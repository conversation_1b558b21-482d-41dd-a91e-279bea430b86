#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Lua代码优化器
专门针对unluac反编译输出进行优化
"""

import re
import os
import sys
from typing import Dict, List, Tuple, Optional


class SimpleLuaOptimizer:
    """简化版Lua优化器"""
    
    def __init__(self):
        self.direct_assignments = {}  # 直接赋值映射 L0_1 = require -> L0_1 -> require
        self.function_calls = {}      # 函数调用映射
        
    def optimize_file(self, input_file: str, output_file: str = None) -> str:
        """优化Lua文件"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
            
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        optimized_content = self.optimize_code(content)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            print(f"优化完成，输出文件: {output_file}")
        
        return optimized_content
    
    def optimize_code(self, code: str) -> str:
        """优化Lua代码"""
        lines = code.split('\n')
        
        # 第一步：分析并优化变量赋值
        lines = self._optimize_variable_assignments(lines)
        
        # 第二步：优化简单的goto语句
        lines = self._optimize_simple_goto(lines)
        
        # 第三步：清理格式
        lines = self._cleanup_code(lines)
        
        return '\n'.join(lines)
    
    def _optimize_variable_assignments(self, lines: List[str]) -> List[str]:
        """优化变量赋值"""
        result = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检测连续的赋值模式：L0_1 = func, L1_1 = arg, L0_1(L1_1)
            pattern_result = self._detect_call_pattern(i, lines)
            if pattern_result:
                result.append(pattern_result['optimized_line'])
                i = pattern_result['skip_to']
                continue
            
            # 检测简单的require模式
            require_result = self._optimize_require_pattern(i, lines)
            if require_result:
                result.append(require_result['optimized_line'])
                i = require_result['skip_to']
                continue
            
            # 检测函数调用返回值赋值
            return_result = self._optimize_return_assignment(i, lines)
            if return_result:
                result.extend(return_result['optimized_lines'])
                i = return_result['skip_to']
                continue
            
            result.append(line)
            i += 1
        
        return result
    
    def _detect_call_pattern(self, start: int, lines: List[str]) -> Optional[Dict]:
        """检测函数调用模式"""
        if start + 2 >= len(lines):
            return None
        
        line1 = lines[start].strip()
        line2 = lines[start + 1].strip()
        line3 = lines[start + 2].strip()
        
        # 模式：L0_1 = func, L1_1 = arg, L0_1(L1_1)
        match1 = re.match(r'(L\d+_\d+)\s*=\s*(\w+)$', line1)
        match2 = re.match(r'(L\d+_\d+)\s*=\s*(.+)$', line2)
        match3 = re.match(r'(L\d+_\d+)\((L\d+_\d+)\)$', line3)
        
        if match1 and match2 and match3:
            func_var = match1.group(1)
            func_name = match1.group(2)
            arg_var = match2.group(1)
            arg_value = match2.group(2)
            call_func = match3.group(1)
            call_arg = match3.group(2)
            
            if func_var == call_func and arg_var == call_arg:
                # 获取原始缩进
                indent = self._get_indent(lines[start])
                optimized = f"{indent}{func_name}({arg_value})"
                
                return {
                    'optimized_line': optimized,
                    'skip_to': start + 3
                }
        
        return None
    
    def _optimize_require_pattern(self, start: int, lines: List[str]) -> Optional[Dict]:
        """优化require模式"""
        if start + 1 >= len(lines):
            return None
        
        line1 = lines[start].strip()
        line2 = lines[start + 1].strip()
        
        # 模式：L0_1 = require, L1_1 = "module", L0_1(L1_1)
        if (re.match(r'L\d+_\d+\s*=\s*require$', line1) and 
            re.match(r'L\d+_\d+\s*=\s*"[^"]*"$', line2)):
            
            if start + 2 < len(lines):
                line3 = lines[start + 2].strip()
                if re.match(r'L\d+_\d+\(L\d+_\d+\)$', line3):
                    # 提取模块名
                    module_match = re.search(r'"([^"]*)"', line2)
                    if module_match:
                        module_name = module_match.group(1)
                        indent = self._get_indent(lines[start])
                        optimized = f"{indent}require(\"{module_name}\")"
                        
                        return {
                            'optimized_line': optimized,
                            'skip_to': start + 3
                        }
        
        return None
    
    def _optimize_return_assignment(self, start: int, lines: List[str]) -> Optional[Dict]:
        """优化返回值赋值"""
        if start + 1 >= len(lines):
            return None
        
        line1 = lines[start].strip()
        line2 = lines[start + 1].strip()
        
        # 模式：L0_1 = func, L0_1, L1_1 = L0_1(), var1 = L1_1, var2 = L0_1
        func_match = re.match(r'(L\d+_\d+)\s*=\s*(\w+)$', line1)
        call_match = re.match(r'(L\d+_\d+),\s*(L\d+_\d+)\s*=\s*(L\d+_\d+)\(\)$', line2)
        
        if func_match and call_match:
            func_var = func_match.group(1)
            func_name = func_match.group(2)
            ret_var1 = call_match.group(1)
            ret_var2 = call_match.group(2)
            call_var = call_match.group(3)
            
            if func_var == call_var:
                # 查找后续的赋值
                assignments = []
                i = start + 2
                while i < len(lines) and i < start + 6:  # 限制搜索范围
                    line = lines[i].strip()
                    assign_match = re.match(r'(\w+)\s*=\s*(L\d+_\d+)$', line)
                    if assign_match:
                        var_name = assign_match.group(1)
                        temp_var = assign_match.group(2)
                        if temp_var == ret_var1 or temp_var == ret_var2:
                            assignments.append((var_name, temp_var))
                            i += 1
                            continue
                    break
                
                if assignments:
                    # 构建优化后的代码
                    indent = self._get_indent(lines[start])
                    var_names = [assign[0] for assign in assignments]
                    optimized = f"{indent}{', '.join(var_names)} = {func_name}()"
                    
                    return {
                        'optimized_lines': [optimized],
                        'skip_to': start + 2 + len(assignments)
                    }
        
        return None
    
    def _optimize_simple_goto(self, lines: List[str]) -> List[str]:
        """优化简单的goto语句"""
        result = []
        skip_lines = set()
        
        for i, line in enumerate(lines):
            if i in skip_lines:
                continue
            
            # 移除孤立的goto标签
            if re.match(r'\s*::\w+::\s*$', line):
                continue
            
            # 优化简单的if-goto模式
            if_goto_result = self._optimize_if_goto(i, lines)
            if if_goto_result:
                result.extend(if_goto_result['lines'])
                skip_lines.update(if_goto_result['skip_lines'])
                continue
            
            result.append(line)
        
        return result
    
    def _optimize_if_goto(self, start: int, lines: List[str]) -> Optional[Dict]:
        """优化if-goto模式"""
        if start >= len(lines):
            return None
        
        line = lines[start].strip()
        
        # 简单的条件反转：if condition then goto label -> if not condition then
        if_goto_match = re.match(r'if\s+(.+?)\s+then\s*goto\s+\w+', line)
        if if_goto_match:
            condition = if_goto_match.group(1)
            
            # 查找else分支内容（下一行到标签之间）
            else_content = []
            i = start + 1
            while i < len(lines) and i < start + 10:  # 限制搜索范围
                if re.match(r'\s*::\w+::', lines[i]):
                    break
                if lines[i].strip():
                    else_content.append(lines[i])
                i += 1
            
            if else_content:
                # 反转条件
                reversed_condition = self._reverse_condition(condition)
                indent = self._get_indent(lines[start])
                
                result_lines = [f"{indent}if {reversed_condition} then"]
                result_lines.extend([indent + "  " + line.lstrip() for line in else_content])
                result_lines.append(f"{indent}end")
                
                return {
                    'lines': result_lines,
                    'skip_lines': set(range(start, i + 1))
                }
        
        return None
    
    def _reverse_condition(self, condition: str) -> str:
        """反转条件"""
        condition = condition.strip()
        
        if ' == ' in condition:
            return condition.replace(' == ', ' ~= ')
        elif ' ~= ' in condition:
            return condition.replace(' ~= ', ' == ')
        elif condition.startswith('not '):
            return condition[4:].strip()
        else:
            return f"not ({condition})"
    
    def _cleanup_code(self, lines: List[str]) -> List[str]:
        """清理代码格式"""
        result = []
        prev_empty = False
        
        for line in lines:
            # 移除多余空行
            if not line.strip():
                if not prev_empty:
                    result.append(line)
                    prev_empty = True
            else:
                result.append(line)
                prev_empty = False
        
        return result
    
    def _get_indent(self, line: str) -> str:
        """获取行缩进"""
        return line[:len(line) - len(line.lstrip())]


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python simple_lua_optimizer.py <输入文件> [输出文件]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not output_file:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_simple_optimized.lua"
    
    optimizer = SimpleLuaOptimizer()
    
    try:
        optimizer.optimize_file(input_file, output_file)
        print("优化完成！")
    except Exception as e:
        print(f"优化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
