local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1, L16_1, L17_1, L18_1, L19_1, L20_1
L0_1 = require
L1_1 = "math"
L0_1 = L0_1(L1_1)
L1_1 = require
L2_1 = "string"
L1_1 = L1_1(L2_1)
L2_1 = require
L3_1 = "table"
L2_1 = L2_1(L3_1)
L3_1 = {}
L4_1 = {}
L5_1 = {}
L3_1.EMPTY_ARRAY = L5_1
L5_1 = {}
L3_1.EMPTY_OBJECT = L5_1
L5_1 = nil
L6_1 = nil
L7_1 = nil
L8_1 = nil
L9_1 = nil
L10_1 = nil
L11_1 = nil
L12_1 = nil
L13_1 = nil
L14_1 = nil

function L15_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  if A0_2 == nil then
    L1_2 = "null"
    return L1_2
  end
  L1_2 = type
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  if L1_2 == "string" then
    L2_2 = "\""
    L3_2 = L4_1.encodeString
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    L4_2 = "\""
    L2_2 = L2_2 .. L3_2 .. L4_2
    return L2_2
  end
  if L1_2 == "number" or L1_2 == "boolean" then
    L2_2 = tostring
    L3_2 = A0_2
    return L2_2(L3_2)
  end
  if L1_2 == "table" then
    L2_2 = {}
    L3_2 = L13_1
    L4_2 = A0_2
    L3_2, L4_2 = L3_2(L4_2)
    if L3_2 then
      L5_2 = 1
      L6_2 = L4_2
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = L2_1.insert
        L10_2 = L2_2
        L11_2 = L3_1.encode
        L12_2 = A0_2[L8_2]
        L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2(L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      end
    else
      L5_2 = pairs
      L6_2 = A0_2
      L5_2, L6_2, L7_2 = L5_2(L6_2)
      for L8_2, L9_2 in L5_2, L6_2, L7_2 do
        L10_2 = L14_1
        L11_2 = L8_2
        L10_2 = L10_2(L11_2)
        if L10_2 then
          L10_2 = L14_1
          L11_2 = L9_2
          L10_2 = L10_2(L11_2)
          if L10_2 then
            L10_2 = L2_1.insert
            L11_2 = L2_2
            L12_2 = "\""
            L13_2 = L4_1.encodeString
            L14_2 = L8_2
            L13_2 = L13_2(L14_2)
            L14_2 = "\":"
            L15_2 = L3_1.encode
            L16_2 = L9_2
            L15_2 = L15_2(L16_2)
            L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2
            L10_2(L11_2, L12_2)
          end
        end
      end
    end
    if L3_2 then
      L5_2 = "["
      L6_2 = L2_1.concat
      L7_2 = L2_2
      L8_2 = ","
      L6_2 = L6_2(L7_2, L8_2)
      L7_2 = "]"
      L5_2 = L5_2 .. L6_2 .. L7_2
      return L5_2
    else
      L5_2 = "{"
      L6_2 = L2_1.concat
      L7_2 = L2_2
      L8_2 = ","
      L6_2 = L6_2(L7_2, L8_2)
      L7_2 = "}"
      L5_2 = L5_2 .. L6_2 .. L7_2
      return L5_2
    end
  end
  if L1_2 == "function" then
    L2_2 = L3_1.null
    if A0_2 == L2_2 then
      L2_2 = "null"
      return L2_2
    end
  end
  L2_2 = assert
  L3_2 = false
  L4_2 = "encode attempt to encode unsupported type "
  L5_2 = L1_2
  L6_2 = ":"
  L7_2 = tostring
  L8_2 = A0_2
  L7_2 = L7_2(L8_2)
  L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2
  L2_2(L3_2, L4_2)
end

L3_1.encode = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  if not A1_2 or not A1_2 then
    A1_2 = 1
  end
  L2_2 = L11_1
  L3_2 = A0_2
  L4_2 = A1_2
  L2_2 = L2_2(L3_2, L4_2)
  A1_2 = L2_2
  L2_2 = assert
  L3_2 = L1_1.len
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L3_2 = A1_2 <= L3_2
  L4_2 = "Unterminated JSON encoded object found at position in ["
  L5_2 = A0_2
  L6_2 = "]"
  L4_2 = L4_2 .. L5_2 .. L6_2
  L2_2(L3_2, L4_2)
  L2_2 = L1_1.sub
  L3_2 = A0_2
  L4_2 = A1_2
  L5_2 = A1_2
  L2_2 = L2_2(L3_2, L4_2, L5_2)
  if L2_2 == "{" then
    L3_2 = L9_1
    L4_2 = A0_2
    L5_2 = A1_2
    return L3_2(L4_2, L5_2)
  end
  if L2_2 == "[" then
    L3_2 = L5_1
    L4_2 = A0_2
    L5_2 = A1_2
    return L3_2(L4_2, L5_2)
  end
  L3_2 = L1_1.find
  L4_2 = "+-0123456789.e"
  L5_2 = L2_2
  L6_2 = 1
  L7_2 = true
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2)
  if L3_2 then
    L3_2 = L8_1
    L4_2 = A0_2
    L5_2 = A1_2
    return L3_2(L4_2, L5_2)
  end
  if L2_2 == "\"" or L2_2 == "'" then
    L3_2 = L10_1
    L4_2 = A0_2
    L5_2 = A1_2
    return L3_2(L4_2, L5_2)
  end
  L3_2 = L1_1.sub
  L4_2 = A0_2
  L5_2 = A1_2
  L6_2 = A1_2 + 1
  L3_2 = L3_2(L4_2, L5_2, L6_2)
  if L3_2 == "/*" then
    L3_2 = L3_1.decode
    L4_2 = A0_2
    L5_2 = L6_1
    L6_2 = A0_2
    L7_2 = A1_2
    L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
    return L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
  end
  L3_2 = L7_1
  L4_2 = A0_2
  L5_2 = A1_2
  return L3_2(L4_2, L5_2)
end

L3_1.decode = L15_1

function L15_1()
  local L0_2, L1_2, L2_2
  L0_2 = L3_1.null
  return L0_2
end

L3_1.null = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = {}
  L3_2 = L1_1.len
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = assert
  L5_2 = L1_1.sub
  L6_2 = A0_2
  L7_2 = A1_2
  L8_2 = A1_2
  L5_2 = L5_2(L6_2, L7_2, L8_2)
  L5_2 = L5_2 == "["
  L6_2 = "decode_scanArray called but array does not start at position "
  L7_2 = A1_2
  L8_2 = " in string:\n"
  L9_2 = A0_2
  L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2
  L4_2(L5_2, L6_2)
  A1_2 = A1_2 + 1
  L4_2 = 1
  repeat
    L5_2 = L11_1
    L6_2 = A0_2
    L7_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2)
    A1_2 = L5_2
    L5_2 = assert
    L6_2 = L3_2 >= A1_2
    L7_2 = "JSON String ended unexpectedly scanning array."
    L5_2(L6_2, L7_2)
    L5_2 = L1_1.sub
    L6_2 = A0_2
    L7_2 = A1_2
    L8_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2, L8_2)
    if L5_2 == "]" then
      L6_2 = L2_2
      L7_2 = A1_2 + 1
      return L6_2, L7_2
    end
    if L5_2 == "," then
      L6_2 = L11_1
      L7_2 = A0_2
      L8_2 = A1_2 + 1
      L6_2 = L6_2(L7_2, L8_2)
      A1_2 = L6_2
    end
    L6_2 = assert
    L7_2 = L3_2 >= A1_2
    L8_2 = "JSON String ended unexpectedly scanning array."
    L6_2(L7_2, L8_2)
    L6_2 = L3_1.decode
    L7_2 = A0_2
    L8_2 = A1_2
    L6_2, L7_2 = L6_2(L7_2, L8_2)
    A1_2 = L7_2
    object = L6_2
    L6_2 = object
    L2_2[L4_2] = L6_2
    L4_2 = L4_2 + 1
    L6_2 = false
  until L6_2
end

L5_1 = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = assert
  L3_2 = L1_1.sub
  L4_2 = A0_2
  L5_2 = A1_2
  L6_2 = A1_2 + 1
  L3_2 = L3_2(L4_2, L5_2, L6_2)
  L3_2 = L3_2 == "/*"
  L4_2 = "decode_scanComment called but comment does not start at position "
  L5_2 = A1_2
  L4_2 = L4_2 .. L5_2
  L2_2(L3_2, L4_2)
  L2_2 = L1_1.find
  L3_2 = A0_2
  L4_2 = "*/"
  L5_2 = A1_2 + 2
  L2_2 = L2_2(L3_2, L4_2, L5_2)
  L3_2 = assert
  L4_2 = L2_2 ~= nil
  L5_2 = "Unterminated comment in string at "
  L6_2 = A1_2
  L5_2 = L5_2 .. L6_2
  L3_2(L4_2, L5_2)
  L3_2 = L2_2 + 2
  return L3_2
end

L6_1 = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L2_2 = {}
  L2_2["true"] = true
  L2_2["false"] = false
  L2_2.null = nil
  L3_2 = {}
  L4_2 = "true"
  L5_2 = "false"
  L6_2 = "null"
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L4_2 = pairs
  L5_2 = L3_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = L1_1.sub
    L10_2 = A0_2
    L11_2 = A1_2
    L12_2 = L1_1.len
    L13_2 = L8_2
    L12_2 = L12_2(L13_2)
    L12_2 = A1_2 + L12_2
    L12_2 = L12_2 - 1
    L9_2 = L9_2(L10_2, L11_2, L12_2)
    if L9_2 == L8_2 then
      L9_2 = L2_2[L8_2]
      L10_2 = L1_1.len
      L11_2 = L8_2
      L10_2 = L10_2(L11_2)
      L10_2 = A1_2 + L10_2
      return L9_2, L10_2
    end
  end
  L4_2 = assert
  L5_2 = nil
  L6_2 = "Failed to scan constant from string "
  L7_2 = A0_2
  L8_2 = " at starting position "
  L9_2 = A1_2
  L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2
  L4_2(L5_2, L6_2)
end

L7_1 = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L2_2 = A1_2 + 1
  L3_2 = L1_1.len
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = "+-0123456789.e"
  while true do
    L5_2 = L1_1.find
    L6_2 = L4_2
    L7_2 = L1_1.sub
    L8_2 = A0_2
    L9_2 = L2_2
    L10_2 = L2_2
    L7_2 = L7_2(L8_2, L9_2, L10_2)
    L8_2 = 1
    L9_2 = true
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2)
    if not (L5_2 and L2_2 <= L3_2) then
      break
    end
    L2_2 = L2_2 + 1
  end
  L5_2 = "return "
  L6_2 = L1_1.sub
  L7_2 = A0_2
  L8_2 = A1_2
  L9_2 = L2_2 - 1
  L6_2 = L6_2(L7_2, L8_2, L9_2)
  L5_2 = L5_2 .. L6_2
  L6_2 = load
  L7_2 = L5_2
  L6_2 = L6_2(L7_2)
  L7_2 = assert
  L8_2 = L6_2
  L9_2 = "Failed to scan number [ "
  L10_2 = L5_2
  L11_2 = "] in JSON string at position "
  L12_2 = A1_2
  L13_2 = " : "
  L14_2 = L2_2
  L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
  L7_2(L8_2, L9_2)
  L7_2 = L6_2
  L7_2 = L7_2()
  L8_2 = L2_2
  return L7_2, L8_2
end

L8_1 = L15_1

function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = {}
  L3_2 = L1_1.len
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = nil
  L5_2 = nil
  L6_2 = assert
  L7_2 = L1_1.sub
  L8_2 = A0_2
  L9_2 = A1_2
  L10_2 = A1_2
  L7_2 = L7_2(L8_2, L9_2, L10_2)
  L7_2 = L7_2 == "{"
  L8_2 = "decode_scanObject called but object does not start at position "
  L9_2 = A1_2
  L10_2 = " in string:\n"
  L11_2 = A0_2
  L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2
  L6_2(L7_2, L8_2)
  A1_2 = A1_2 + 1
  repeat
    L6_2 = L11_1
    L7_2 = A0_2
    L8_2 = A1_2
    L6_2 = L6_2(L7_2, L8_2)
    A1_2 = L6_2
    L6_2 = assert
    L7_2 = L3_2 >= A1_2
    L8_2 = "JSON string ended unexpectedly while scanning object."
    L6_2(L7_2, L8_2)
    L6_2 = L1_1.sub
    L7_2 = A0_2
    L8_2 = A1_2
    L9_2 = A1_2
    L6_2 = L6_2(L7_2, L8_2, L9_2)
    if L6_2 == "}" then
      L7_2 = L2_2
      L8_2 = A1_2 + 1
      return L7_2, L8_2
    end
    if L6_2 == "," then
      L7_2 = L11_1
      L8_2 = A0_2
      L9_2 = A1_2 + 1
      L7_2 = L7_2(L8_2, L9_2)
      A1_2 = L7_2
    end
    L7_2 = assert
    L8_2 = L3_2 >= A1_2
    L9_2 = "JSON string ended unexpectedly scanning object."
    L7_2(L8_2, L9_2)
    L7_2 = L3_1.decode
    L8_2 = A0_2
    L9_2 = A1_2
    L7_2, L8_2 = L7_2(L8_2, L9_2)
    A1_2 = L8_2
    L4_2 = L7_2
    L7_2 = assert
    L8_2 = L3_2 >= A1_2
    L9_2 = "JSON string ended unexpectedly searching for value of key "
    L10_2 = L4_2
    L9_2 = L9_2 .. L10_2
    L7_2(L8_2, L9_2)
    L7_2 = L11_1
    L8_2 = A0_2
    L9_2 = A1_2
    L7_2 = L7_2(L8_2, L9_2)
    A1_2 = L7_2
    L7_2 = assert
    L8_2 = L3_2 >= A1_2
    L9_2 = "JSON string ended unexpectedly searching for value of key "
    L10_2 = L4_2
    L9_2 = L9_2 .. L10_2
    L7_2(L8_2, L9_2)
    L7_2 = assert
    L8_2 = L1_1.sub
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A1_2
    L8_2 = L8_2(L9_2, L10_2, L11_2)
    L8_2 = L8_2 == ":"
    L9_2 = "JSON object key-value assignment mal-formed at "
    L10_2 = A1_2
    L9_2 = L9_2 .. L10_2
    L7_2(L8_2, L9_2)
    L7_2 = L11_1
    L8_2 = A0_2
    L9_2 = A1_2 + 1
    L7_2 = L7_2(L8_2, L9_2)
    A1_2 = L7_2
    L7_2 = assert
    L8_2 = L3_2 >= A1_2
    L9_2 = "JSON string ended unexpectedly searching for value of key "
    L10_2 = L4_2
    L9_2 = L9_2 .. L10_2
    L7_2(L8_2, L9_2)
    L7_2 = L3_1.decode
    L8_2 = A0_2
    L9_2 = A1_2
    L7_2, L8_2 = L7_2(L8_2, L9_2)
    A1_2 = L8_2
    L5_2 = L7_2
    L2_2[L4_2] = L5_2
    L7_2 = false
  until L7_2
end

L9_1 = L15_1
L15_1 = {}
L15_1["\\t"] = "\t"
L15_1["\\f"] = "\f"
L15_1["\\r"] = "\r"
L15_1["\\n"] = "\n"
L15_1["\\b"] = "\b"
L16_1 = setmetatable
L17_1 = L15_1
L18_1 = {}

function L19_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = L1_1.sub
  L3_2 = A1_2
  L4_2 = 2
  return L2_2(L3_2, L4_2)
end

L18_1.__index = L19_1
L16_1(L17_1, L18_1)

function L16_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L2_2 = assert
  L3_2 = A1_2
  L4_2 = "decode_scanString(..) called without start position"
  L2_2(L3_2, L4_2)
  L2_2 = L1_1.sub
  L3_2 = A0_2
  L4_2 = A1_2
  L5_2 = A1_2
  L2_2 = L2_2(L3_2, L4_2, L5_2)
  L3_2 = assert
  L4_2 = L2_2 == "\"" or L2_2 == "'"
  L5_2 = "decode_scanString called for a non-string"
  L3_2(L4_2, L5_2)
  L3_2 = {}
  L4_2 = A1_2
  L5_2 = A1_2
  while true do
    L6_2 = L1_1.find
    L7_2 = A0_2
    L8_2 = L2_2
    L9_2 = L5_2 + 1
    L6_2 = L6_2(L7_2, L8_2, L9_2)
    L7_2 = L5_2 + 1
    if L6_2 == L7_2 then
      break
    end
    L6_2 = L5_2
    L7_2 = L1_1.find
    L8_2 = A0_2
    L9_2 = "\\."
    L10_2 = L5_2 + 1
    L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2)
    L5_2 = L8_2
    L4_2 = L7_2
    L7_2 = L1_1.find
    L8_2 = A0_2
    L9_2 = L2_2
    L10_2 = L6_2 + 1
    L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2)
    if not L4_2 or L4_2 > L7_2 then
      L9_2 = L7_2
      L5_2 = L8_2 - 1
      L4_2 = L9_2
    end
    L9_2 = L2_1.insert
    L10_2 = L3_2
    L11_2 = L1_1.sub
    L12_2 = A0_2
    L13_2 = L6_2 + 1
    L14_2 = L4_2 - 1
    L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2 = L11_2(L12_2, L13_2, L14_2)
    L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
    L9_2 = L1_1.sub
    L10_2 = A0_2
    L11_2 = L4_2
    L12_2 = L5_2
    L9_2 = L9_2(L10_2, L11_2, L12_2)
    if L9_2 == "\\u" then
      L9_2 = L1_1.sub
      L10_2 = A0_2
      L11_2 = L5_2 + 1
      L12_2 = L5_2 + 4
      L9_2 = L9_2(L10_2, L11_2, L12_2)
      L5_2 = L5_2 + 4
      L10_2 = tonumber
      L11_2 = L9_2
      L12_2 = 16
      L10_2 = L10_2(L11_2, L12_2)
      L11_2 = assert
      L12_2 = L10_2
      L13_2 = "String decoding failed: bad Unicode escape "
      L14_2 = L9_2
      L15_2 = " at position "
      L16_2 = L4_2
      L17_2 = " : "
      L18_2 = L5_2
      L13_2 = L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2
      L11_2(L12_2, L13_2)
      L11_2 = nil
      if L10_2 < 128 then
        L12_2 = L1_1.char
        L13_2 = L10_2 % 128
        L12_2 = L12_2(L13_2)
        L11_2 = L12_2
      elseif L10_2 < 2048 then
        L12_2 = L1_1.char
        L13_2 = L0_1.floor
        L14_2 = L10_2 / 64
        L13_2 = L13_2(L14_2)
        L13_2 = L13_2 % 32
        L13_2 = 192 + L13_2
        L14_2 = L10_2 % 64
        L14_2 = 128 + L14_2
        L12_2 = L12_2(L13_2, L14_2)
        L11_2 = L12_2
      else
        L12_2 = L1_1.char
        L13_2 = L0_1.floor
        L14_2 = L10_2 / 4096
        L13_2 = L13_2(L14_2)
        L13_2 = L13_2 % 16
        L13_2 = 224 + L13_2
        L14_2 = L0_1.floor
        L15_2 = L10_2 / 64
        L14_2 = L14_2(L15_2)
        L14_2 = L14_2 % 64
        L14_2 = 128 + L14_2
        L15_2 = L10_2 % 64
        L15_2 = 128 + L15_2
        L12_2 = L12_2(L13_2, L14_2, L15_2)
        L11_2 = L12_2
      end
      L12_2 = L2_1.insert
      L13_2 = L3_2
      L14_2 = L11_2
      L12_2(L13_2, L14_2)
    else
      L9_2 = L2_1.insert
      L10_2 = L3_2
      L11_2 = L1_1.sub
      L12_2 = A0_2
      L13_2 = L4_2
      L14_2 = L5_2
      L11_2 = L11_2(L12_2, L13_2, L14_2)
      L11_2 = L15_1[L11_2]
      L9_2(L10_2, L11_2)
    end
  end
  L6_2 = L2_1.insert
  L7_2 = L3_2
  L8_2 = L1_1.sub
  L9_2 = L5_2
  L10_2 = L5_2 + 1
  L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2 = L8_2(L9_2, L10_2)
  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
  L6_2 = assert
  L7_2 = L1_1.find
  L8_2 = A0_2
  L9_2 = L2_2
  L10_2 = L5_2 + 1
  L7_2 = L7_2(L8_2, L9_2, L10_2)
  L8_2 = "String decoding failed: missing closing "
  L9_2 = L2_2
  L10_2 = " at position "
  L11_2 = L5_2
  L12_2 = "(for string at position "
  L13_2 = A1_2
  L14_2 = ")"
  L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
  L6_2(L7_2, L8_2)
  L6_2 = L2_1.concat
  L7_2 = L3_2
  L8_2 = ""
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = L5_2 + 2
  return L6_2, L7_2
end

L10_1 = L16_1

function L16_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = " \n\r\t"
  L3_2 = L1_1.len
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  while true do
    L4_2 = L1_1.find
    L5_2 = L2_2
    L6_2 = L1_1.sub
    L7_2 = A0_2
    L8_2 = A1_2
    L9_2 = A1_2
    L6_2 = L6_2(L7_2, L8_2, L9_2)
    L7_2 = 1
    L8_2 = true
    L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2)
    if not (L4_2 and A1_2 <= L3_2) then
      break
    end
    A1_2 = A1_2 + 1
  end
  return A1_2
end

L11_1 = L16_1
L16_1 = {}
L16_1["\""] = "\\\""
L16_1["\\"] = "\\\\"
L16_1["/"] = "\\/"
L16_1["\b"] = "\\b"
L16_1["\f"] = "\\f"
L16_1["\n"] = "\\n"
L16_1["\r"] = "\\r"
L16_1["\t"] = "\\t"

function L17_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L1_2 = tostring
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  L3_2 = L1_2
  L2_2 = L1_2.gsub
  L4_2 = "."
  
  function L5_2(A0_3)
    local L1_3, L2_3
    L1_3 = L16_1[A0_3]
    return L1_3
  end
  
  return L2_2(L3_2, L4_2, L5_2)
end

L4_1.encodeString = L17_1

function L17_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = L3_1.EMPTY_ARRAY
  if A0_2 == L1_2 then
    L1_2 = true
    L2_2 = 0
    return L1_2, L2_2
  end
  L1_2 = L3_1.EMPTY_OBJECT
  if A0_2 == L1_2 then
    L1_2 = false
    return L1_2
  end
  L1_2 = 0
  L2_2 = pairs
  L3_2 = A0_2
  L2_2, L3_2, L4_2 = L2_2(L3_2)
  for L5_2, L6_2 in L2_2, L3_2, L4_2 do
    L7_2 = type
    L8_2 = L5_2
    L7_2 = L7_2(L8_2)
    if L7_2 == "number" then
      L7_2 = L0_1.floor
      L8_2 = L5_2
      L7_2 = L7_2(L8_2)
      if L7_2 == L5_2 and 1 <= L5_2 then
        L7_2 = L14_1
        L8_2 = L6_2
        L7_2 = L7_2(L8_2)
        if not L7_2 then
          L7_2 = false
          return L7_2
        end
        L7_2 = L0_1.max
        L8_2 = L1_2
        L9_2 = L5_2
        L7_2 = L7_2(L8_2, L9_2)
        L1_2 = L7_2
    end
    elseif L5_2 == "n" then
      L7_2 = A0_2.n
      if not L7_2 then
        L7_2 = #A0_2
      end
      if L6_2 ~= L7_2 then
        L7_2 = false
        return L7_2
      end
    else
      L7_2 = L14_1
      L8_2 = L6_2
      L7_2 = L7_2(L8_2)
      if L7_2 then
        L7_2 = false
        return L7_2
      end
    end
  end
  L2_2 = true
  L3_2 = L1_2
  return L2_2, L3_2
end

L13_1 = L17_1

function L17_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = type
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  L2_2 = L1_2 == "string" or L1_2 == "boolean" or L1_2 == "number" or L1_2 == "nil" or L1_2 == "table"
  return L2_2
end

L14_1 = L17_1
return L3_1
