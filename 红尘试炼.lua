local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1
flags = true
done = false
_ENV["升级"] = false
_ENV["鬼王"] = -1
Times = 0

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = true
  L2_2 = os
  L2_2 = L2_2.time
  L2_2 = L2_2()
  if A0_2 ~= "null" then
    L3_2 = type
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    if L3_2 ~= "number" then
      goto lbl_17
    end
  end
  L3_2 = _ENV["通用功能"]
  L3_2 = L3_2["叉叉"]
  L3_2()
  L3_2 = supplement
  L3_2()
  ::lbl_17::
  while true do
    L3_2 = getColour
    L4_2 = _ENV["红尘试炼"]
    L5_2 = "打开背包"
    L3_2 = L3_2(L4_2, L5_2)
    if L3_2 then
      L3_2 = randomClick
      L4_2 = 2
      L5_2 = 500
      L6_2 = 1604
      L7_2 = 84
      L3_2(L4_2, L5_2, L6_2, L7_2)
    else
      L3_2 = getColour
      L4_2 = _ENV["桃源村一别"]
      L5_2 = "关闭对话框"
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        L3_2 = randomClick
        L4_2 = 2
        L5_2 = 300
        L6_2 = 1873
        L7_2 = 797
        L3_2(L4_2, L5_2, L6_2, L7_2)
      else
        L3_2 = wordStock
        L3_2 = L3_2["门派名称"]
        L4_2 = A0_2
        L3_2 = L3_2(L4_2)
        if L3_2 then
          L3_2 = writeFileString
          L4_2 = userPath
          L4_2 = L4_2()
          L5_2 = "/res/任务节点.txt"
          L4_2 = L4_2 .. L5_2
          L5_2 = tostring
          L6_2 = A0_2
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L5_2(L6_2)
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
          L3_2 = true
          return L3_2
        elseif L1_2 then
          L3_2 = _find
          L4_2 = Color
          L4_2 = L4_2["起号"]
          L4_2 = L4_2["快捷技能栏"]
          L3_2 = L3_2(L4_2)
          if L3_2 == false then
            L3_2 = randomClick
            L4_2 = 2
            L5_2 = 444
            L6_2 = 1577
            L7_2 = 990
            L3_2(L4_2, L5_2, L6_2, L7_2)
            L3_2 = _ENV["_随机延时"]
            L4_2 = 411
            L3_2(L4_2)
          end
          L3_2 = _ENV["仙灵买药"]
          if L3_2 then
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 500
            L6_2 = 1665
            L7_2 = 830
            L8_2 = 1711
            L9_2 = 883
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          else
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 500
            L6_2 = 1587
            L7_2 = 817
            L8_2 = 1649
            L9_2 = 874
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          end
          L1_2 = false
        else
          L3_2 = os
          L3_2 = L3_2.time
          L3_2 = L3_2()
          L3_2 = L3_2 - L2_2
          if 2 <= L3_2 then
            L3_2 = wordStock
            L3_2 = L3_2["门派名称"]
            L3_2 = L3_2()
            if not L3_2 then
              L1_2 = true
              L3_2 = os
              L3_2 = L3_2.time
              L3_2 = L3_2()
              L2_2 = L3_2
            end
          end
        end
      end
    end
  end
end

_ENV["返回师门"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = true
  L1_2 = os
  L1_2 = L1_2.time
  L1_2 = L1_2()
  L2_2 = 0
  L3_2 = 0
  L4_2 = os
  L4_2 = L4_2.time
  L4_2 = L4_2()
  L5_2 = _ENV["通用功能"]
  L5_2 = L5_2["叉叉"]
  L5_2()
  L5_2 = supplement
  L5_2()
  L5_2 = _ENV["门派名字"]
  if L5_2 ~= "" then
    while true do
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["购买"]
      L6_2 = L6_2["佛手"]
      L6_2 = L6_2["物品界面"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = _cmp
        L6_2 = Color
        L6_2 = L6_2["购买"]
        L6_2 = L6_2["佛手"]
        L6_2 = L6_2["关闭界面"]
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = _Sleep
          L6_2 = 500
          L7_2 = 1000
          L5_2(L6_2, L7_2)
        end
      end
      L5_2 = _cmp_tb
      L6_2 = Color
      L6_2 = L6_2["地图"]
      L7_2 = "到了"
      L8_2 = _ENV["门派名字"]
      L7_2 = L7_2 .. L8_2
      L6_2 = L6_2[L7_2]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = _print
        L6_2 = "到师门!"
        L5_2(L6_2)
        return
      end
      L5_2 = _find
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["快捷技能栏"]
      L5_2 = L5_2(L6_2)
      if L5_2 == false then
        L5_2 = randomClick
        L6_2 = 2
        L7_2 = 111
        L8_2 = 1582
        L9_2 = 990
        L5_2(L6_2, L7_2, L8_2, L9_2)
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["快捷技能栏"]
      L7_2 = {}
      L8_2 = 20
      L9_2 = 40
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L3_2 = L3_2 + 1
        L5_2 = _ENV["仙灵买药"]
        if L5_2 then
          L5_2 = randomClick
          L6_2 = 0
          L7_2 = 100
          L8_2 = 1665
          L9_2 = 830
          L10_2 = 1711
          L11_2 = 883
          L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
        else
          L5_2 = randomClick
          L6_2 = 0
          L7_2 = 100
          L8_2 = 1587
          L9_2 = 817
          L10_2 = 1649
          L11_2 = 874
          L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
        end
      end
      L5_2 = _cmp_tb_cx
      L6_2 = Color
      L6_2 = L6_2["地图"]
      L7_2 = "到了"
      L8_2 = _ENV["门派名字"]
      L7_2 = L7_2 .. L8_2
      L6_2 = L6_2[L7_2]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 30
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = _print
        L6_2 = "到师门!"
        L5_2(L6_2)
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L4_2
      if 10 < L5_2 then
        L5_2 = _find
        L6_2 = Color
        L6_2 = L6_2["起号"]
        L6_2 = L6_2["快捷技能栏"]
        L5_2 = L5_2(L6_2)
        if not L5_2 then
          L5_2 = os
          L5_2 = L5_2.time
          L5_2 = L5_2()
          L4_2 = L5_2
          L5_2 = tap
          L6_2 = 1582
          L7_2 = 990
          L5_2(L6_2, L7_2)
        end
      end
      if 10 < L3_2 then
        L5_2 = supplement
        L5_2()
        L3_2 = 0
      end
    end
  end
  while true do
    L5_2 = _cmp
    L6_2 = Color
    L6_2 = L6_2["购买"]
    L6_2 = L6_2["佛手"]
    L6_2 = L6_2["物品界面"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["购买"]
      L6_2 = L6_2["佛手"]
      L6_2 = L6_2["关闭界面"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = _Sleep
        L6_2 = 500
        L7_2 = 1000
        L5_2(L6_2, L7_2)
      end
    end
    L5_2 = printLog
    L6_2 = "返回师门"
    L5_2(L6_2)
    L5_2 = _ENV["到达师门"]
    L5_2 = L5_2()
    if L5_2 then
      return
    elseif L0_2 then
      L5_2 = _find
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["快捷技能栏"]
      L5_2 = L5_2(L6_2)
      if L5_2 == false then
        L5_2 = randomClick
        L6_2 = 2
        L7_2 = 233
        L8_2 = 1577
        L9_2 = 990
        L5_2(L6_2, L7_2, L8_2, L9_2)
        L5_2 = mSleep
        L6_2 = 200
        L5_2(L6_2)
      end
      L5_2 = _ENV["仙灵买药"]
      if L5_2 then
        L5_2 = randomClick
        L6_2 = 0
        L7_2 = 333
        L8_2 = 1665
        L9_2 = 830
        L10_2 = 1711
        L11_2 = 883
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      else
        L5_2 = randomClick
        L6_2 = 0
        L7_2 = 333
        L8_2 = 1587
        L9_2 = 817
        L10_2 = 1649
        L11_2 = 874
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      end
      L0_2 = false
    else
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 1 <= L5_2 then
        L5_2 = _ENV["到达师门"]
        L5_2 = L5_2()
        if not L5_2 then
          if 5 < L2_2 then
            L5_2 = _ENV["师门任务返回"]
            return L5_2()
          else
            L2_2 = L2_2 + 1
            L0_2 = true
            L5_2 = os
            L5_2 = L5_2.time
            L5_2 = L5_2()
            L1_2 = L5_2
          end
        end
      end
    end
  end
end

_ENV["师门任务返回"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _cmp_tb
  L1_2 = Color
  L1_2 = L1_2["地图"]
  L1_2 = L1_2["到达普陀山"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    _ENV["门派名字"] = "普陀山"
    L0_2 = true
    return L0_2
  else
    L0_2 = _cmp_tb
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达潮音洞"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      _ENV["门派名字"] = "普陀山"
      L0_2 = true
      return L0_2
    else
      L0_2 = _cmp_tb
      L1_2 = Color
      L1_2 = L1_2["地图"]
      L1_2 = L1_2["到达天宫"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        _ENV["门派名字"] = "天宫"
        L0_2 = true
        return L0_2
      else
        L0_2 = _cmp_tb
        L1_2 = Color
        L1_2 = L1_2["地图"]
        L1_2 = L1_2["到达凌霄宝殿"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          _ENV["门派名字"] = "天宫"
          L0_2 = true
          return L0_2
        else
          L0_2 = _cmp_tb
          L1_2 = Color
          L1_2 = L1_2["地图"]
          L1_2 = L1_2["到达龙宫"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            _ENV["门派名字"] = "龙宫"
            L0_2 = true
            return L0_2
          else
            L0_2 = _cmp_tb
            L1_2 = Color
            L1_2 = L1_2["地图"]
            L1_2 = L1_2["到达水晶宫"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              _ENV["门派名字"] = "龙宫"
              L0_2 = true
              return L0_2
            else
              L0_2 = _cmp_tb
              L1_2 = Color
              L1_2 = L1_2["地图"]
              L1_2 = L1_2["到达凌波城"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                _ENV["门派名字"] = "凌波城"
                L0_2 = true
                return L0_2
              else
                L0_2 = _cmp_tb
                L1_2 = Color
                L1_2 = L1_2["地图"]
                L1_2 = L1_2["到达幻境花果山"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  _ENV["门派名字"] = "花果山"
                  L0_2 = true
                  return L0_2
                else
                  L0_2 = _cmp_tb
                  L1_2 = Color
                  L1_2 = L1_2["地图"]
                  L1_2 = L1_2["到达五庄观"]
                  L0_2 = L0_2(L1_2)
                  if L0_2 then
                    _ENV["门派名字"] = "五庄观"
                    L0_2 = true
                    return L0_2
                  else
                    L0_2 = _cmp_tb
                    L1_2 = Color
                    L1_2 = L1_2["地图"]
                    L1_2 = L1_2["到达乾坤殿"]
                    L0_2 = L0_2(L1_2)
                    if L0_2 then
                      _ENV["门派名字"] = "五庄观"
                      L0_2 = true
                      return L0_2
                    else
                      L0_2 = _cmp_tb
                      L1_2 = Color
                      L1_2 = L1_2["地图"]
                      L1_2 = L1_2["到达盘丝洞"]
                      L0_2 = L0_2(L1_2)
                      if L0_2 then
                        _ENV["门派名字"] = "盘丝洞"
                        L0_2 = true
                        return L0_2
                      else
                        L0_2 = _cmp_tb
                        L1_2 = Color
                        L1_2 = L1_2["地图"]
                        L1_2 = L1_2["到达盘丝岭"]
                        L0_2 = L0_2(L1_2)
                        if L0_2 then
                          _ENV["门派名字"] = "盘丝洞"
                          L0_2 = true
                          return L0_2
                        else
                          L0_2 = _cmp_tb
                          L1_2 = Color
                          L1_2 = L1_2["地图"]
                          L1_2 = L1_2["到达魔王寨"]
                          L0_2 = L0_2(L1_2)
                          if L0_2 then
                            _ENV["门派名字"] = "魔王寨"
                            L0_2 = true
                            return L0_2
                          else
                            L0_2 = _cmp_tb
                            L1_2 = Color
                            L1_2 = L1_2["地图"]
                            L1_2 = L1_2["到达魔王居"]
                            L0_2 = L0_2(L1_2)
                            if L0_2 then
                              _ENV["门派名字"] = "魔王寨"
                              L0_2 = true
                              return L0_2
                            else
                              L0_2 = _cmp_tb
                              L1_2 = Color
                              L1_2 = L1_2["地图"]
                              L1_2 = L1_2["到达琉璃殿"]
                              L0_2 = L0_2(L1_2)
                              if L0_2 then
                                _ENV["门派名字"] = "无底洞"
                                L0_2 = true
                                return L0_2
                              else
                                L0_2 = _cmp_tb
                                L1_2 = Color
                                L1_2 = L1_2["地图"]
                                L1_2 = L1_2["到达狮驼岭"]
                                L0_2 = L0_2(L1_2)
                                if L0_2 then
                                  _ENV["门派名字"] = "狮驼岭"
                                  L0_2 = true
                                  return L0_2
                                else
                                  L0_2 = _cmp_tb
                                  L1_2 = Color
                                  L1_2 = L1_2["地图"]
                                  L1_2 = L1_2["到达狮王洞"]
                                  L0_2 = L0_2(L1_2)
                                  if L0_2 then
                                    _ENV["门派名字"] = "狮驼岭"
                                    L0_2 = true
                                    return L0_2
                                  else
                                    L0_2 = _cmp_tb
                                    L1_2 = Color
                                    L1_2 = L1_2["地图"]
                                    L1_2 = L1_2["到达地府"]
                                    L0_2 = L0_2(L1_2)
                                    if L0_2 then
                                      _ENV["门派名字"] = "地府"
                                      L0_2 = true
                                      return L0_2
                                    else
                                      L0_2 = _cmp_tb
                                      L1_2 = Color
                                      L1_2 = L1_2["地图"]
                                      L1_2 = L1_2["到达地藏王府"]
                                      L0_2 = L0_2(L1_2)
                                      if L0_2 then
                                        _ENV["门派名字"] = "地府"
                                        L0_2 = true
                                        return L0_2
                                      else
                                        L0_2 = _cmp_tb
                                        L1_2 = Color
                                        L1_2 = L1_2["地图"]
                                        L1_2 = L1_2["到达地府"]
                                        L0_2 = L0_2(L1_2)
                                        if L0_2 then
                                          _ENV["门派名字"] = "地府"
                                          L0_2 = true
                                          return L0_2
                                        else
                                          L0_2 = _cmp_tb
                                          L1_2 = Color
                                          L1_2 = L1_2["地图"]
                                          L1_2 = L1_2["到达地藏王府"]
                                          L0_2 = L0_2(L1_2)
                                          if L0_2 then
                                            _ENV["门派名字"] = "地府"
                                            L0_2 = true
                                            return L0_2
                                          else
                                            L0_2 = _cmp_tb
                                            L1_2 = Color
                                            L1_2 = L1_2["地图"]
                                            L1_2 = L1_2["到达化生寺"]
                                            L0_2 = L0_2(L1_2)
                                            if L0_2 then
                                              _ENV["门派名字"] = "化生寺"
                                              L0_2 = true
                                              return L0_2
                                            else
                                              L0_2 = _cmp_tb
                                              L1_2 = Color
                                              L1_2 = L1_2["地图"]
                                              L1_2 = L1_2["到达藏经阁"]
                                              L0_2 = L0_2(L1_2)
                                              if L0_2 then
                                                _ENV["门派名字"] = "化生寺"
                                                L0_2 = true
                                                return L0_2
                                              else
                                                L0_2 = _cmp_tb
                                                L1_2 = Color
                                                L1_2 = L1_2["地图"]
                                                L1_2 = L1_2["到达大唐官府"]
                                                L0_2 = L0_2(L1_2)
                                                if L0_2 then
                                                  _ENV["门派名字"] = "大唐官府"
                                                  L0_2 = true
                                                  return L0_2
                                                else
                                                  L0_2 = _cmp_tb
                                                  L1_2 = Color
                                                  L1_2 = L1_2["地图"]
                                                  L1_2 = L1_2["到达程咬金府"]
                                                  L0_2 = L0_2(L1_2)
                                                  if L0_2 then
                                                    _ENV["门派名字"] = "大唐官符"
                                                    L0_2 = true
                                                    return L0_2
                                                  else
                                                    L0_2 = _cmp_tb
                                                    L1_2 = Color
                                                    L1_2 = L1_2["地图"]
                                                    L1_2 = L1_2["到达神木林"]
                                                    L0_2 = L0_2(L1_2)
                                                    if L0_2 then
                                                      _ENV["门派名字"] = "神木林"
                                                      L0_2 = true
                                                      return L0_2
                                                    else
                                                      L0_2 = _cmp_tb
                                                      L1_2 = Color
                                                      L1_2 = L1_2["地图"]
                                                      L1_2 = L1_2["到达神木屋"]
                                                      L0_2 = L0_2(L1_2)
                                                      if L0_2 then
                                                        _ENV["门派名字"] = "神木林"
                                                        L0_2 = true
                                                        return L0_2
                                                      else
                                                        L0_2 = _cmp_tb
                                                        L1_2 = Color
                                                        L1_2 = L1_2["地图"]
                                                        L1_2 = L1_2["到达女儿村"]
                                                        L0_2 = L0_2(L1_2)
                                                        if L0_2 then
                                                          _ENV["门派名字"] = "女儿村"
                                                          L0_2 = true
                                                          return L0_2
                                                        end
                                                      end
                                                    end
                                                  end
                                                end
                                              end
                                            end
                                          end
                                        end
                                      end
                                    end
                                  end
                                end
                              end
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  L0_2 = false
  return L0_2
end

_ENV["新到达师门"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = pairs
  L1_2 = _ENV["师门洞府"]
  L0_2, L1_2, L2_2 = L0_2(L1_2)
  for L3_2, L4_2 in L0_2, L1_2, L2_2 do
    L5_2 = getWordStock
    L6_2 = _ENV["师门洞府"]
    L7_2 = L3_2
    L8_2 = 148
    L9_2 = 35
    L10_2 = 316
    L11_2 = 80
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
    if L5_2 then
      _ENV["门派名字"] = L3_2
      L5_2 = true
      return L5_2
    else
      L5_2 = _ENV["当前门派"]
      L6_2 = L3_2
      L5_2 = L5_2(L6_2)
      if L5_2 then
        _ENV["门派名字"] = L3_2
        L5_2 = true
        return L5_2
      end
    end
  end
  L0_2 = false
  return L0_2
end

_ENV["到达师门"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = A0_2 or nil
  if not A0_2 then
    L1_2 = 1
  end
  L2_2 = 0
  L3_2 = 0
  L4_2 = os
  L4_2 = L4_2.time
  L4_2 = L4_2()
  _ENV["回蓝时间"] = L4_2
  L4_2 = os
  L4_2 = L4_2.time
  L4_2 = L4_2()
  _ENV["回血时间"] = L4_2
  L4_2 = _ENV["选择项目"]
  if L4_2 ~= "新手任务" then
    L4_2 = _ENV["选择项目"]
    if L4_2 ~= "红尘试炼" then
      L4_2 = _ENV["选择项目"]
      if L4_2 ~= "场景挂机" then
        goto lbl_25
      end
    end
  end
  L3_2 = 1
  goto lbl_26
  ::lbl_25::
  L3_2 = 3
  ::lbl_26::
  while true do
    if L2_2 < L3_2 then
      L4_2 = getColour
      L5_2 = ZS
      L5_2 = L5_2["人血"]
      L6_2 = "40%"
      L4_2 = L4_2(L5_2, L6_2)
      if not L4_2 then
        L4_2 = true
        while L4_2 do
          L5_2 = getColour
          L6_2 = CJColorList
          L7_2 = "人物加血"
          L5_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            L5_2 = randomClick
            L6_2 = 0
            L7_2 = 500
            L8_2 = 1401
            L9_2 = 122
            L10_2 = 1603
            L11_2 = 196
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            L2_2 = L2_2 + 1
            L5_2 = 1
            L6_2 = 20
            L7_2 = 1
            for L8_2 = L5_2, L6_2, L7_2 do
              L9_2 = getColour
              L10_2 = ZS
              L10_2 = L10_2["人血"]
              L11_2 = "40%"
              L9_2 = L9_2(L10_2, L11_2)
              if L9_2 then
                L4_2 = false
                goto lbl_100
              else
                L9_2 = mSleep
                L10_2 = 100
                L9_2(L10_2)
              end
            end
          else
            if not (3 <= L2_2) then
              L5_2 = os
              L5_2 = L5_2.time
              L5_2 = L5_2()
              L6_2 = _ENV["回血时间"]
              L5_2 = L5_2 - L6_2
              if 10 < L5_2 then
                L5_2 = supplement
                L5_2()
                L5_2 = os
                L5_2 = L5_2.time
                L5_2 = L5_2()
                _ENV["回血时间"] = L5_2
                goto lbl_130
            end
            else
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1818
              L9_2 = 10
              L10_2 = 1915
              L11_2 = 67
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              L5_2 = mSleep
              L6_2 = 500
              L5_2(L6_2)
            end
          end
          ::lbl_100::
        end
    end
    elseif 3 <= L2_2 and L1_2 == 1 then
      L4_2 = isItemExist
      L5_2 = "包子"
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _ENV["回血回蓝"]
        L5_2 = L1_2
        return L4_2(L5_2)
      else
        L4_2 = _ENV["仙灵买药"]
        if L4_2 then
          L4_2 = _ENV["仙灵买包子"]
          return L4_2()
        else
          L4_2 = _ENV["前往买包子"]
          L5_2 = false
          return L4_2(L5_2)
        end
      end
    else
      L2_2 = 0
      break
    end
    ::lbl_130::
  end
  while true do
    if L3_2 > L2_2 then
      L4_2 = getColour
      L5_2 = ZS
      L5_2 = L5_2["人蓝"]
      L6_2 = "40%"
      L4_2 = L4_2(L5_2, L6_2)
      if not L4_2 then
        L4_2 = true
        while L4_2 do
          L5_2 = getColour
          L6_2 = CJColorList
          L7_2 = "人物加血"
          L5_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            L5_2 = randomClick
            L6_2 = 0
            L7_2 = 500
            L8_2 = 1669
            L9_2 = 119
            L10_2 = 1877
            L11_2 = 199
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            L2_2 = L2_2 + 1
            L5_2 = 1
            L6_2 = 20
            L7_2 = 1
            for L8_2 = L5_2, L6_2, L7_2 do
              L9_2 = getColour
              L10_2 = ZS
              L10_2 = L10_2["人蓝"]
              L11_2 = "40%"
              L9_2 = L9_2(L10_2, L11_2)
              if L9_2 then
                L4_2 = false
                goto lbl_202
              else
                L9_2 = mSleep
                L10_2 = 100
                L9_2(L10_2)
              end
            end
          else
            if not (3 <= L2_2) then
              L5_2 = os
              L5_2 = L5_2.time
              L5_2 = L5_2()
              L6_2 = _ENV["回蓝时间"]
              L5_2 = L5_2 - L6_2
              if 10 < L5_2 then
                L5_2 = supplement
                L5_2()
                L5_2 = os
                L5_2 = L5_2.time
                L5_2 = L5_2()
                _ENV["回蓝时间"] = L5_2
                goto lbl_225
            end
            else
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1818
              L9_2 = 10
              L10_2 = 1915
              L11_2 = 67
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            end
          end
          ::lbl_202::
        end
    end
    elseif 3 <= L2_2 and L1_2 == 1 then
      L4_2 = isItemExist
      L5_2 = "佛手"
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _ENV["回血回蓝"]
        L5_2 = L1_2
        return L4_2(L5_2)
      else
        L4_2 = _ENV["前往买佛手"]
        L5_2 = false
        return L4_2(L5_2)
      end
    else
      L2_2 = 0
      break
    end
    ::lbl_225::
  end
end

_ENV["回血回蓝"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  repeat
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["屏蔽"]
    L1_2 = L1_2["取消"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["屏蔽"]
    L1_2 = L1_2["取消"]
    L0_2 = L0_2(L1_2)
  until L0_2 == false
end

_ENV["屏蔽取消"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  repeat
    L0_2 = _ENV["通用功能"]
    L0_2 = L0_2["关闭"]
    L0_2()
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["背包"]
    L1_2 = "open"
    L0_2(L1_2)
    L0_2 = _ENV["服务器高精度文字识别"]
    L1_2 = {}
    L2_2 = {}
    L3_2 = 337
    L4_2 = 756
    L5_2 = 523
    L6_2 = 790
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L2_2[3] = L5_2
    L2_2[4] = L6_2
    L1_2[1] = L2_2
    L2_2 = false
    L0_2 = L0_2(L1_2, L2_2)
  until L0_2 ~= nil and L0_2 ~= ""
  L1_2 = string
  L1_2 = L1_2.match
  L2_2 = L0_2
  L3_2 = "%d+"
  L1_2 = L1_2(L2_2, L3_2)
  L0_2 = L1_2
  L1_2 = _ENV["预留金额"]
  L1_2 = L0_2 - L1_2
  _ENV["丢钱金额"] = L1_2
  L1_2 = _ENV["丢钱金额"]
  if 0 < L1_2 then
    L1_2 = _ENV["丢钱"]
    L1_2()
  else
    _ENV["金钱不足"] = true
    L1_2 = _ENV["通用功能"]
    L1_2 = L1_2["叉叉"]
    L1_2()
  end
end

_ENV["检测钱数"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
  L0_2 = _ENV["_返回"]
  L0_2 = L0_2["地图"]
  L1_2 = "建邺城"
  L0_2 = L0_2(L1_2)
  if L0_2 == false then
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["建邺城"]
    L0_2()
  end
  L0_2 = _ENV["UI_丢钱ID"]
  _ENV["指定ID"] = L0_2
  L0_2 = _ENV["是否需要加丢钱好友"]
  if L0_2 == "加好友" then
    L0_2 = _ENV["加好友丢钱"]
    L0_2()
  else
    L0_2 = mSleep
    L1_2 = 200
    L0_2(L1_2)
  end
  L0_2 = {}
  L1_2 = {}
  L2_2 = 1452
  L3_2 = 952
  L4_2 = 14477028
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L2_2 = {}
  L3_2 = 1455
  L4_2 = 974
  L5_2 = 9808040
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L3_2 = {}
  L4_2 = 1482
  L5_2 = 965
  L6_2 = 3233897
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L4_2 = {}
  L5_2 = 1427
  L6_2 = 961
  L7_2 = 3693680
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L5_2 = {}
  L6_2 = 1425
  L7_2 = 949
  L8_2 = 4221056
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L6_2 = {}
  L7_2 = 1448
  L8_2 = 985
  L9_2 = 6983057
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L7_2 = {}
  L8_2 = 1463
  L9_2 = 981
  L10_2 = 7903384
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L8_2 = {}
  L9_2 = 1469
  L10_2 = 961
  L11_2 = 3693421
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L0_2[5] = L5_2
  L0_2[6] = L6_2
  L0_2[7] = L7_2
  L0_2[8] = L8_2
  L1_2 = {}
  L2_2 = {}
  L3_2 = 1858
  L4_2 = 211
  L5_2 = 4284777
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L3_2 = {}
  L4_2 = 1856
  L5_2 = 240
  L6_2 = 2700601
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L4_2 = {}
  L5_2 = 1841
  L6_2 = 225
  L7_2 = 3624533
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L5_2 = {}
  L6_2 = 1872
  L7_2 = 226
  L8_2 = 3755353
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L6_2 = {}
  L7_2 = 1849
  L8_2 = 220
  L9_2 = 16383481
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L7_2 = {}
  L8_2 = 1857
  L9_2 = 226
  L10_2 = 16383481
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L1_2[5] = L6_2
  L1_2[6] = L7_2
  L2_2 = {}
  L3_2 = {}
  L4_2 = 1834
  L5_2 = 346
  L6_2 = 7961001
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L4_2 = {}
  L5_2 = 1842
  L6_2 = 360
  L7_2 = 7961001
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L5_2 = {}
  L6_2 = 1847
  L7_2 = 365
  L8_2 = 7961001
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L6_2 = {}
  L7_2 = 1838
  L8_2 = 379
  L9_2 = 7961001
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L7_2 = {}
  L8_2 = 1834
  L9_2 = 383
  L10_2 = 7961001
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L8_2 = {}
  L9_2 = 1831
  L10_2 = 363
  L11_2 = 10985679
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L9_2 = {}
  L10_2 = 1860
  L11_2 = 364
  L12_2 = 10985679
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L9_2[3] = L12_2
  L10_2 = {}
  L11_2 = 1822
  L12_2 = 363
  L13_2 = 10985679
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L10_2[3] = L13_2
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L2_2[5] = L7_2
  L2_2[6] = L8_2
  L2_2[7] = L9_2
  L2_2[8] = L10_2
  L3_2 = {}
  L4_2 = {}
  L5_2 = 1366
  L6_2 = 620
  L7_2 = 15922421
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L5_2 = {}
  L6_2 = 1370
  L7_2 = 623
  L8_2 = 9609644
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L6_2 = {}
  L7_2 = 1385
  L8_2 = 642
  L9_2 = 3167336
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L7_2 = {}
  L8_2 = 1388
  L9_2 = 632
  L10_2 = 4481909
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L8_2 = {}
  L9_2 = 1400
  L10_2 = 632
  L11_2 = 3363944
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L9_2 = {}
  L10_2 = 1420
  L11_2 = 627
  L12_2 = 3691624
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L9_2[3] = L12_2
  L10_2 = {}
  L11_2 = 1431
  L12_2 = 621
  L13_2 = 3691624
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L10_2[3] = L13_2
  L11_2 = {}
  L12_2 = 1439
  L13_2 = 636
  L14_2 = 3167336
  L11_2[1] = L12_2
  L11_2[2] = L13_2
  L11_2[3] = L14_2
  L12_2 = {}
  L13_2 = 1428
  L14_2 = 646
  L15_2 = 7966877
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L12_2[3] = L15_2
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L3_2[5] = L8_2
  L3_2[6] = L9_2
  L3_2[7] = L10_2
  L3_2[8] = L11_2
  L3_2[9] = L12_2
  L4_2 = {}
  L5_2 = {}
  L6_2 = 1466
  L7_2 = 43
  L8_2 = 10067891
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L6_2 = {}
  L7_2 = 1467
  L8_2 = 52
  L9_2 = 15264496
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L7_2 = {}
  L8_2 = 1475
  L9_2 = 62
  L10_2 = 14540773
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L8_2 = {}
  L9_2 = 1485
  L10_2 = 64
  L11_2 = 2635896
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L9_2 = {}
  L10_2 = 1498
  L11_2 = 53
  L12_2 = 2635888
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L9_2[3] = L12_2
  L10_2 = {}
  L11_2 = 1497
  L12_2 = 49
  L13_2 = 4213877
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L10_2[3] = L13_2
  L11_2 = {}
  L12_2 = 1484
  L13_2 = 52
  L14_2 = 11120838
  L11_2[1] = L12_2
  L11_2[2] = L13_2
  L11_2[3] = L14_2
  L12_2 = {}
  L13_2 = 1490
  L14_2 = 53
  L15_2 = 14080740
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L12_2[3] = L15_2
  L13_2 = {}
  L14_2 = 1506
  L15_2 = 55
  L16_2 = 2305627
  L13_2[1] = L14_2
  L13_2[2] = L15_2
  L13_2[3] = L16_2
  L14_2 = {}
  L15_2 = 1539
  L16_2 = 55
  L17_2 = 11712716
  L14_2[1] = L15_2
  L14_2[2] = L16_2
  L14_2[3] = L17_2
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L4_2[8] = L12_2
  L4_2[9] = L13_2
  L4_2[10] = L14_2
  L5_2 = {}
  L6_2 = math
  L6_2 = L6_2.random
  L7_2 = 1810
  L8_2 = 1850
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = 859
  L9_2 = 891
  L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L7_2(L8_2, L9_2)
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L5_2[4] = L9_2
  L5_2[5] = L10_2
  L5_2[6] = L11_2
  L5_2[7] = L12_2
  L5_2[8] = L13_2
  L5_2[9] = L14_2
  L5_2[10] = L15_2
  L5_2[11] = L16_2
  L5_2[12] = L17_2
  L5_2[13] = L18_2
  L5_2[14] = L19_2
  L5_2[15] = L20_2
  L5_2[16] = L21_2
  L5_2[17] = L22_2
  L5_2[18] = L23_2
  L6_2 = {}
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = 1466
  L9_2 = 1808
  L7_2 = L7_2(L8_2, L9_2)
  L8_2 = math
  L8_2 = L8_2.random
  L9_2 = 215
  L10_2 = 242
  L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L8_2(L9_2, L10_2)
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L6_2[4] = L10_2
  L6_2[5] = L11_2
  L6_2[6] = L12_2
  L6_2[7] = L13_2
  L6_2[8] = L14_2
  L6_2[9] = L15_2
  L6_2[10] = L16_2
  L6_2[11] = L17_2
  L6_2[12] = L18_2
  L6_2[13] = L19_2
  L6_2[14] = L20_2
  L6_2[15] = L21_2
  L6_2[16] = L22_2
  L6_2[17] = L23_2
  L7_2 = {}
  L8_2 = math
  L8_2 = L8_2.random
  L9_2 = 1844
  L10_2 = 1873
  L8_2 = L8_2(L9_2, L10_2)
  L9_2 = math
  L9_2 = L9_2.random
  L10_2 = 213
  L11_2 = 247
  L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L9_2(L10_2, L11_2)
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L7_2[5] = L12_2
  L7_2[6] = L13_2
  L7_2[7] = L14_2
  L7_2[8] = L15_2
  L7_2[9] = L16_2
  L7_2[10] = L17_2
  L7_2[11] = L18_2
  L7_2[12] = L19_2
  L7_2[13] = L20_2
  L7_2[14] = L21_2
  L7_2[15] = L22_2
  L7_2[16] = L23_2
  L8_2 = {}
  L9_2 = math
  L9_2 = L9_2.random
  L10_2 = 1823
  L11_2 = 1859
  L9_2 = L9_2(L10_2, L11_2)
  L10_2 = math
  L10_2 = L10_2.random
  L11_2 = 344
  L12_2 = 382
  L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L10_2(L11_2, L12_2)
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L8_2[4] = L12_2
  L8_2[5] = L13_2
  L8_2[6] = L14_2
  L8_2[7] = L15_2
  L8_2[8] = L16_2
  L8_2[9] = L17_2
  L8_2[10] = L18_2
  L8_2[11] = L19_2
  L8_2[12] = L20_2
  L8_2[13] = L21_2
  L8_2[14] = L22_2
  L8_2[15] = L23_2
  L9_2 = {}
  L10_2 = math
  L10_2 = L10_2.random
  L11_2 = 1346
  L12_2 = 1471
  L10_2 = L10_2(L11_2, L12_2)
  L11_2 = math
  L11_2 = L11_2.random
  L12_2 = 599
  L13_2 = 662
  L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L11_2(L12_2, L13_2)
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L9_2[3] = L12_2
  L9_2[4] = L13_2
  L9_2[5] = L14_2
  L9_2[6] = L15_2
  L9_2[7] = L16_2
  L9_2[8] = L17_2
  L9_2[9] = L18_2
  L9_2[10] = L19_2
  L9_2[11] = L20_2
  L9_2[12] = L21_2
  L9_2[13] = L22_2
  L9_2[14] = L23_2
  L10_2 = {}
  L11_2 = math
  L11_2 = L11_2.random
  L12_2 = 1426
  L13_2 = 1595
  L11_2 = L11_2(L12_2, L13_2)
  L12_2 = math
  L12_2 = L12_2.random
  L13_2 = 968
  L14_2 = 1013
  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L12_2(L13_2, L14_2)
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L10_2[3] = L13_2
  L10_2[4] = L14_2
  L10_2[5] = L15_2
  L10_2[6] = L16_2
  L10_2[7] = L17_2
  L10_2[8] = L18_2
  L10_2[9] = L19_2
  L10_2[10] = L20_2
  L10_2[11] = L21_2
  L10_2[12] = L22_2
  L10_2[13] = L23_2
  repeat
    repeat
      repeat
        L11_2 = tap
        L12_2 = 1832
        L13_2 = 851
        L11_2(L12_2, L13_2)
        L11_2 = _cmp_cx
        L12_2 = Color
        L12_2 = L12_2["购买"]
        L12_2 = L12_2["出现系统消息"]
        L13_2 = {}
        L14_2 = 10
        L15_2 = 30
        L13_2[1] = L14_2
        L13_2[2] = L15_2
        L11_2 = L11_2(L12_2, L13_2)
        if L11_2 then
        end
        L11_2 = _cmp_cx
        L12_2 = Color
        L12_2 = L12_2["购买"]
        L12_2 = L12_2["出现好友消息"]
        L13_2 = {}
        L14_2 = 10
        L15_2 = 30
        L13_2[1] = L14_2
        L13_2[2] = L15_2
        L11_2 = L11_2(L12_2, L13_2)
        if L11_2 then
        end
        L11_2 = _cmp
        L12_2 = Color
        L12_2 = L12_2["购买"]
        L12_2 = L12_2["发现好友界面"]
        L11_2 = L11_2(L12_2)
      until L11_2
      L11_2 = _find
      L12_2 = Color
      L12_2 = L12_2["红尘"]
      L12_2 = L12_2["系统消息"]
      L11_2 = L11_2(L12_2)
    until L11_2 == false
    L11_2 = _find
    L12_2 = Color
    L12_2 = L12_2["红尘"]
    L12_2 = L12_2["好友消息"]
    L11_2 = L11_2(L12_2)
  until L11_2 == false
  L11_2 = 1
  L12_2 = 2
  L13_2 = 1
  for L14_2 = L11_2, L12_2, L13_2 do
    L15_2 = mSleep
    L16_2 = 100
    L15_2(L16_2)
    L15_2 = multiColor
    L16_2 = L1_2
    L17_2 = 80
    L18_2 = false
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    if L15_2 == true then
      L15_2 = randomTap
      L16_2 = L7_2[1]
      L17_2 = L7_2[2]
      L18_2 = 2
      L15_2(L16_2, L17_2, L18_2)
      L15_2 = mSleep
      L16_2 = 500
      L15_2(L16_2)
      break
    end
  end
  L11_2 = randomTap
  L12_2 = L6_2[1]
  L13_2 = L6_2[2]
  L14_2 = 2
  L11_2(L12_2, L13_2, L14_2)
  L11_2 = mSleep
  L12_2 = _ENV["卡顿掉帧"]
  L11_2(L12_2)
  L11_2 = mSleep
  L12_2 = 500
  L11_2(L12_2)
  L11_2 = tap
  L12_2 = 155
  L13_2 = 40
  L11_2(L12_2, L13_2)
  L11_2 = mSleep
  L12_2 = 500
  L11_2(L12_2)
  L11_2 = inputText
  L12_2 = ""
  L11_2(L12_2)
  L11_2 = mSleep
  L12_2 = 500
  L11_2(L12_2)
  L11_2 = os
  L11_2 = L11_2.execute
  L12_2 = "input text "
  L13_2 = _ENV["指定ID"]
  L12_2 = L12_2 .. L13_2
  L11_2(L12_2)
  L11_2 = mSleep
  L12_2 = 400
  L11_2(L12_2)
  L11_2 = randomTap
  L12_2 = 1639
  L13_2 = 546
  L14_2 = 2
  L11_2(L12_2, L13_2, L14_2)
  L11_2 = 1
  L12_2 = 50
  L13_2 = 1
  for L14_2 = L11_2, L12_2, L13_2 do
    L15_2 = mSleep
    L16_2 = 100
    L15_2(L16_2)
    L15_2 = multiColor
    L16_2 = L1_2
    L17_2 = 80
    L18_2 = false
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    if L15_2 == true then
      L15_2 = mSleep
      L16_2 = 500
      L15_2(L16_2)
      break
    end
  end
  L11_2 = 1
  L12_2 = 22
  L13_2 = 1
  for L14_2 = L11_2, L12_2, L13_2 do
    L15_2 = mSleep
    L16_2 = 100
    L15_2(L16_2)
    L15_2 = multiColor
    L16_2 = L2_2
    L17_2 = 80
    L18_2 = false
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    if L15_2 == true then
      break
    end
    L15_2 = dialog
    L16_2 = "没有添加此id为好友！"
    L17_2 = time
    L15_2(L16_2, L17_2)
    L15_2 = lua_exit
    L15_2()
  end
  L11_2 = 1
  L12_2 = 99
  L13_2 = 1
  for L14_2 = L11_2, L12_2, L13_2 do
    L15_2 = multiColor
    L16_2 = L2_2
    L17_2 = 80
    L18_2 = false
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    if L15_2 == true then
      L15_2 = mSleep
      L16_2 = math
      L16_2 = L16_2.random
      L17_2 = 100
      L18_2 = 200
      L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L16_2(L17_2, L18_2)
      L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
      L15_2 = randomTap
      L16_2 = L8_2[1]
      L17_2 = L8_2[2]
      L18_2 = 2
      L15_2(L16_2, L17_2, L18_2)
      L15_2 = mSleep
      L16_2 = math
      L16_2 = L16_2.random
      L17_2 = 300
      L18_2 = 800
      L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2 = L16_2(L17_2, L18_2)
      L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
      L15_2 = 1
      L16_2 = 30
      L17_2 = 1
      for L18_2 = L15_2, L16_2, L17_2 do
        L19_2 = mSleep
        L20_2 = 100
        L19_2(L20_2)
        L19_2 = multiColor
        L20_2 = L3_2
        L21_2 = 80
        L22_2 = false
        L19_2 = L19_2(L20_2, L21_2, L22_2)
        if L19_2 == true then
          L19_2 = mSleep
          L20_2 = 100
          L19_2(L20_2)
          L19_2 = randomTap
          L20_2 = L9_2[1]
          L21_2 = L9_2[2]
          L22_2 = 2
          L19_2(L20_2, L21_2, L22_2)
          break
        end
      end
      L15_2 = 1
      L16_2 = 50
      L17_2 = 1
      for L18_2 = L15_2, L16_2, L17_2 do
        L19_2 = mSleep
        L20_2 = 100
        L19_2(L20_2)
        L19_2 = multiColor
        L20_2 = L4_2
        L21_2 = 80
        L22_2 = false
        L19_2 = L19_2(L20_2, L21_2, L22_2)
        if L19_2 == true then
          break
        end
      end
    end
    L15_2 = multiColor
    L16_2 = L4_2
    L17_2 = 80
    L18_2 = false
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    if L15_2 == true then
      break
    end
  end
  L11_2 = {}
  L12_2 = {}
  L13_2 = 1672
  L14_2 = 858
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2["X输入口"] = L12_2
  L12_2 = {}
  L13_2 = 1328
  L14_2 = 417
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X1 = L12_2
  L12_2 = {}
  L13_2 = 1482
  L14_2 = 418
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X2 = L12_2
  L12_2 = {}
  L13_2 = 1632
  L14_2 = 413
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X3 = L12_2
  L12_2 = {}
  L13_2 = 1330
  L14_2 = 565
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X4 = L12_2
  L12_2 = {}
  L13_2 = 1478
  L14_2 = 564
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X5 = L12_2
  L12_2 = {}
  L13_2 = 1633
  L14_2 = 565
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X6 = L12_2
  L12_2 = {}
  L13_2 = 1329
  L14_2 = 712
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X7 = L12_2
  L12_2 = {}
  L13_2 = 1481
  L14_2 = 714
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X8 = L12_2
  L12_2 = {}
  L13_2 = 1634
  L14_2 = 712
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X9 = L12_2
  L12_2 = {}
  L13_2 = 1784
  L14_2 = 567
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.X0 = L12_2
  L12_2 = {}
  L13_2 = 1789
  L14_2 = 420
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2["X删除"] = L12_2
  L12_2 = {}
  L13_2 = 1784
  L14_2 = 714
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L11_2.Xok = L12_2
  _ENV["金额小键盘"] = L11_2
  repeat
    L11_2 = tap
    L12_2 = 1727
    L13_2 = 852
    L11_2(L12_2, L13_2)
    L11_2 = mSleep
    L12_2 = 500
    L11_2(L12_2)
    L11_2 = _cmp_cx
    L12_2 = Color
    L12_2 = L12_2["购买"]
    L12_2 = L12_2["发现金额键盘"]
    L13_2 = {}
    L14_2 = 50
    L15_2 = 10
    L13_2[1] = L14_2
    L13_2[2] = L15_2
    L11_2 = L11_2(L12_2, L13_2)
  until L11_2
  L11_2 = _cmp_cx
  L12_2 = Color
  L12_2 = L12_2["购买"]
  L12_2 = L12_2["发现金额键盘"]
  L13_2 = {}
  L14_2 = 50
  L15_2 = 60
  L13_2[1] = L14_2
  L13_2[2] = L15_2
  L11_2 = L11_2(L12_2, L13_2)
  if L11_2 then
    L11_2 = mSleep
    L12_2 = 500
    L11_2(L12_2)
    L11_2 = tonumber
    L12_2 = _ENV["丢钱金额"]
    L11_2 = L11_2(L12_2)
    data = L11_2
    L11_2 = 1
    L12_2 = string
    L12_2 = L12_2.len
    L13_2 = _ENV["丢钱金额"]
    L12_2 = L12_2(L13_2)
    L13_2 = 1
    for L14_2 = L11_2, L12_2, L13_2 do
      L15_2 = "X"
      L16_2 = string
      L16_2 = L16_2.sub
      L17_2 = data
      L18_2 = L14_2
      L19_2 = L14_2
      L16_2 = L16_2(L17_2, L18_2, L19_2)
      L15_2 = L15_2 .. L16_2
      L16_2 = mSleep
      L17_2 = 400
      L16_2(L17_2)
      L16_2 = randomTap
      L17_2 = _ENV["金额小键盘"]
      L17_2 = L17_2[L15_2]
      L17_2 = L17_2[1]
      L18_2 = _ENV["金额小键盘"]
      L18_2 = L18_2[L15_2]
      L18_2 = L18_2[2]
      L16_2(L17_2, L18_2)
    end
    L11_2 = randomTap
    L12_2 = _ENV["金额小键盘"]
    L12_2 = L12_2.Xok
    L12_2 = L12_2[1]
    L13_2 = _ENV["金额小键盘"]
    L13_2 = L13_2.Xok
    L13_2 = L13_2[2]
    L11_2(L12_2, L13_2)
    L11_2 = mSleep
    L12_2 = 1000
    L11_2(L12_2)
    L11_2 = 1
    L12_2 = 3
    L13_2 = 1
    for L14_2 = L11_2, L12_2, L13_2 do
      L15_2 = _find
      L16_2 = Color
      L16_2 = L16_2["百级师门"]
      L16_2 = L16_2["丢钱给予"]
      L15_2 = L15_2(L16_2)
      if L15_2 then
        L15_2 = _ENV["_随机延时"]
        L16_2 = 811
        L15_2(L16_2)
      end
      L15_2 = mSleep
      L16_2 = 200
      L15_2(L16_2)
    end
  end
  L11_2 = _ENV["关闭道具栏或好友栏"]
  L11_2()
end

_ENV["丢钱"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  repeat
    L0_2 = _ENV["通用功能"]
    L0_2 = L0_2["叉叉"]
    L0_2()
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["背包"]
    L1_2 = "open"
    L0_2(L1_2)
    L0_2 = _ENV["服务器高精度文字识别"]
    L1_2 = {}
    L2_2 = {}
    L3_2 = 337
    L4_2 = 756
    L5_2 = 523
    L6_2 = 790
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L2_2[3] = L5_2
    L2_2[4] = L6_2
    L1_2[1] = L2_2
    L2_2 = false
    L0_2 = L0_2(L1_2, L2_2)
  until L0_2 ~= nil and L0_2 ~= ""
  L1_2 = string
  L1_2 = L1_2.match
  L2_2 = L0_2
  L3_2 = "%d+"
  L1_2 = L1_2(L2_2, L3_2)
  L0_2 = L1_2
  L1_2 = _ENV["预留金额"]
  L1_2 = L0_2 - L1_2
  _ENV["存钱金额"] = L1_2
  L1_2 = _ENV["存钱金额"]
  if 0 < L1_2 then
    L1_2 = _ENV["存钱"]
    L1_2()
  else
    L1_2 = _ENV["通用功能"]
    L1_2 = L1_2["叉叉"]
    L1_2()
  end
end

_ENV["检测钱数存钱"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = _ENV["_使用"]
  L0_2 = L0_2["飞行符"]
  L1_2 = "长安城"
  L0_2(L1_2)
  while true do
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["固定坐标"]
    L1_2 = "长安城"
    L2_2 = 1562
    L3_2 = 398
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["进入光圈"]
    L1_2 = 1261
    L2_2 = 255
    L0_2(L1_2, L2_2)
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达长安城钱庄"]
    L2_2 = {}
    L3_2 = 50
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      break
    end
    L0_2 = mSleep
    L1_2 = 500
    L0_2(L1_2)
  end
  L0_2 = _ENV["押镖屏蔽"]
  L0_2()
  repeat
    repeat
      L0_2 = _ENV["起号"]
      L0_2 = L0_2["坐标"]
      L0_2, L1_2 = L0_2()
      y = L1_2
      x = L0_2
      L0_2 = x
      if not (L0_2 <= 30) then
        L0_2 = y
        if not (L0_2 <= 10) then
          goto lbl_60
        end
      end
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 500
      L3_2 = 1028
      L4_2 = 507
      L5_2 = 1127
      L6_2 = 614
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
      L0_2 = _ENV["通用功能"]
      L0_2 = L0_2["叉叉"]
      L0_2()
      L0_2 = mSleep
      L1_2 = 3000
      L0_2(L1_2)
      L0_2 = _ENV["_卡区延迟"]
      L0_2()
      ::lbl_60::
      L0_2 = _ENV["起号"]
      L0_2 = L0_2["坐标"]
      L0_2, L1_2 = L0_2()
      y = L1_2
      x = L0_2
      L0_2 = x
    until 22 < L0_2
    L0_2 = y
  until 11 <= L0_2
  while "老板坐标x" do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    y = L1_2
    x = L0_2
    L0_2 = _ENV["_计算"]
    L0_2 = L0_2["取目标屏幕坐标"]
    L1_2 = "长安城钱庄"
    L2_2 = x
    L3_2 = y
    L4_2 = 31
    L5_2 = 15
    L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
    _ENV["老板坐标y"] = L1_2
    _ENV["老板坐标x"] = L0_2
    L0_2 = tap
    L1_2 = _ENV["老板坐标x"]
    L2_2 = _ENV["老板坐标y"]
    L0_2(L1_2, L2_2)
    L0_2 = mSleep
    L1_2 = 3000
    L0_2(L1_2)
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["百级师门"]
    L1_2 = L1_2["存钱"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_卡区延迟"]
      L0_2()
      L0_2 = _cmp_cx
      L1_2 = Color
      L1_2 = L1_2["百级师门"]
      L1_2 = L1_2["存钱界面"]
      L2_2 = {}
      L3_2 = 30
      L4_2 = 66
      L2_2[1] = L3_2
      L2_2[2] = L4_2
      L0_2 = L0_2(L1_2, L2_2)
      if L0_2 then
        break
      end
    end
    L0_2 = _cmp_cx
    L1_2 = Color
    L1_2 = L1_2["百级师门"]
    L1_2 = L1_2["存钱界面"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 22
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      break
    end
  end
  L0_2 = {}
  L1_2 = {}
  L2_2 = 1672
  L3_2 = 858
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2["X输入口"] = L1_2
  L1_2 = {}
  L2_2 = 766
  L3_2 = 378
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X1 = L1_2
  L1_2 = {}
  L2_2 = 922
  L3_2 = 377
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X2 = L1_2
  L1_2 = {}
  L2_2 = 1077
  L3_2 = 376
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X3 = L1_2
  L1_2 = {}
  L2_2 = 769
  L3_2 = 529
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X4 = L1_2
  L1_2 = {}
  L2_2 = 919
  L3_2 = 523
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X5 = L1_2
  L1_2 = {}
  L2_2 = 1076
  L3_2 = 524
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X6 = L1_2
  L1_2 = {}
  L2_2 = 767
  L3_2 = 675
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X7 = L1_2
  L1_2 = {}
  L2_2 = 920
  L3_2 = 674
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X8 = L1_2
  L1_2 = {}
  L2_2 = 1074
  L3_2 = 673
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X9 = L1_2
  L1_2 = {}
  L2_2 = 1227
  L3_2 = 523
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.X0 = L1_2
  L1_2 = {}
  L2_2 = 1226
  L3_2 = 373
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2["X删除"] = L1_2
  L1_2 = {}
  L2_2 = 1232
  L3_2 = 678
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L0_2.Xok = L1_2
  _ENV["金额小键盘"] = L0_2
  repeat
    L0_2 = tap
    L1_2 = 1527
    L2_2 = 633
    L0_2(L1_2, L2_2)
    L0_2 = mSleep
    L1_2 = 1500
    L0_2(L1_2)
    L0_2 = _cmp_cx
    L1_2 = Color
    L1_2 = L1_2["百级师门"]
    L1_2 = L1_2["存钱键盘"]
    L2_2 = {}
    L3_2 = 11
    L4_2 = 10
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
  until L0_2
  L0_2 = _cmp_cx
  L1_2 = Color
  L1_2 = L1_2["百级师门"]
  L1_2 = L1_2["存钱键盘"]
  L2_2 = {}
  L3_2 = 50
  L4_2 = 60
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 then
    L0_2 = mSleep
    L1_2 = 500
    L0_2(L1_2)
    L0_2 = tonumber
    L1_2 = _ENV["存钱金额"]
    L0_2 = L0_2(L1_2)
    data = L0_2
    L0_2 = 1
    L1_2 = string
    L1_2 = L1_2.len
    L2_2 = _ENV["存钱金额"]
    L1_2 = L1_2(L2_2)
    L2_2 = 1
    for L3_2 = L0_2, L1_2, L2_2 do
      L4_2 = "X"
      L5_2 = string
      L5_2 = L5_2.sub
      L6_2 = data
      L7_2 = L3_2
      L8_2 = L3_2
      L5_2 = L5_2(L6_2, L7_2, L8_2)
      L4_2 = L4_2 .. L5_2
      L5_2 = mSleep
      L6_2 = 400
      L5_2(L6_2)
      L5_2 = randomTap
      L6_2 = _ENV["金额小键盘"]
      L6_2 = L6_2[L4_2]
      L6_2 = L6_2[1]
      L7_2 = _ENV["金额小键盘"]
      L7_2 = L7_2[L4_2]
      L7_2 = L7_2[2]
      L5_2(L6_2, L7_2)
    end
    L0_2 = randomTap
    L1_2 = _ENV["金额小键盘"]
    L1_2 = L1_2.Xok
    L1_2 = L1_2[1]
    L2_2 = _ENV["金额小键盘"]
    L2_2 = L2_2.Xok
    L2_2 = L2_2[2]
    L0_2(L1_2, L2_2)
    L0_2 = mSleep
    L1_2 = 1000
    L0_2(L1_2)
    L0_2 = 1
    L1_2 = 3
    L2_2 = 1
    for L3_2 = L0_2, L1_2, L2_2 do
      L4_2 = _cmp
      L5_2 = Color
      L5_2 = L5_2["百级师门"]
      L5_2 = L5_2["存钱进入"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _ENV["_随机延时"]
        L5_2 = 811
        L4_2(L5_2)
      end
      L4_2 = mSleep
      L5_2 = 200
      L4_2(L5_2)
    end
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["存钱"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  _ENV["可购买宝图数量"] = 0
  repeat
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["背包"]
    L1_2 = "open"
    L0_2(L1_2)
    L0_2 = _ENV["服务器高精度文字识别"]
    L1_2 = {}
    L2_2 = {}
    L3_2 = 337
    L4_2 = 756
    L5_2 = 523
    L6_2 = 790
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L2_2[3] = L5_2
    L2_2[4] = L6_2
    L1_2[1] = L2_2
    L2_2 = false
    L0_2 = L0_2(L1_2, L2_2)
  until L0_2 ~= nil and L0_2 ~= ""
  L1_2 = string
  L1_2 = L1_2.match
  L2_2 = L0_2
  L3_2 = "%d+"
  L1_2 = L1_2(L2_2, L3_2)
  L2_2 = _ENV["预留金额"]
  L2_2 = L1_2 - L2_2
  if 0 < L2_2 then
    L3_2 = tonumber
    L4_2 = L2_2
    L3_2 = L3_2(L4_2)
    L4_2 = tonumber
    L5_2 = _ENV["UI_goumai_宝图_price"]
    L4_2 = L4_2(L5_2)
    L3_2 = L3_2 / L4_2
    L4_2 = math
    L4_2 = L4_2.modf
    L5_2 = L3_2
    L4_2, L5_2 = L4_2(L5_2)
    if 0 < L4_2 then
      L6_2 = true
      L7_2 = L4_2
      return L6_2, L7_2
    else
      L6_2 = _ENV["通用功能"]
      L6_2 = L6_2["关闭"]
      L6_2()
      L6_2 = false
      return L6_2
    end
  else
    L3_2 = _ENV["通用功能"]
    L3_2 = L3_2["关闭"]
    L3_2()
    L3_2 = false
    return L3_2
  end
end

_ENV["检测钱数买图"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L0_2 = nil
  L1_2 = nil
  L2_2 = nil
  L3_2 = nil
  L4_2 = nil
  L5_2 = _ENV["检测钱数买图"]
  L5_2, L6_2 = L5_2()
  L1_2 = L6_2
  L0_2 = L5_2
  if L0_2 then
    L5_2 = _ENV["_功能"]
    L5_2 = L5_2["背包"]
    L6_2 = "open"
    L5_2(L6_2)
    L5_2 = _ENV["_卖图"]
    L5_2 = L5_2["获取背包物品数量"]
    L5_2 = L5_2()
    L2_2 = L5_2
    L5_2 = #L2_2
    L4_2 = 20 - L5_2
    L5_2 = math
    L5_2 = L5_2.min
    L6_2 = L4_2
    L7_2 = L1_2
    L5_2 = L5_2(L6_2, L7_2)
    L3_2 = L5_2
    L5_2 = _ENV["UI_goumai_宝图_la"]
    if L5_2 == "长安城" then
      L5_2 = _ENV["_使用"]
      L5_2 = L5_2["飞行符"]
      L6_2 = "长安城"
      L5_2(L6_2)
    else
      L5_2 = _ENV["_使用"]
      L5_2 = L5_2["飞行符"]
      L6_2 = "建邺城"
      L5_2(L6_2)
    end
    L5_2 = _ENV["_前往"]
    L5_2 = L5_2["地图坐标"]
    L6_2 = _ENV["UI_goumai_宝图_la"]
    L7_2 = tonumber
    L8_2 = _ENV["UI_goumai_宝图_X"]
    L7_2 = L7_2(L8_2)
    L8_2 = tonumber
    L9_2 = _ENV["UI_goumai_宝图_Y"]
    L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2 = L8_2(L9_2)
    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    while true do
      L5_2 = _ENV["_返回"]
      L5_2 = L5_2["坐标"]
      L5_2, L6_2 = L5_2()
      L7_2 = tonumber
      L8_2 = _ENV["UI_goumai_宝图_X"]
      L7_2 = L7_2(L8_2)
      L7_2 = L5_2 - L7_2
      L8_2 = tonumber
      L9_2 = _ENV["UI_goumai_宝图_Y"]
      L8_2 = L8_2(L9_2)
      L8_2 = L6_2 - L8_2
      if -10 <= L7_2 and L7_2 <= 10 and -10 <= L8_2 and L8_2 <= 10 then
        break
      end
      L9_2 = _ENV["_前往"]
      L9_2 = L9_2["地图坐标"]
      L10_2 = _ENV["UI_goumai_宝图_la"]
      L11_2 = tonumber
      L12_2 = _ENV["UI_goumai_宝图_X"]
      L11_2 = L11_2(L12_2)
      L12_2 = tonumber
      L13_2 = _ENV["UI_goumai_宝图_Y"]
      L12_2, L13_2, L14_2 = L12_2(L13_2)
      L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
    end
    L5_2 = _ENV["_购买"]
    L5_2 = L5_2["购买宝图流程"]
    L6_2 = L3_2
    L5_2(L6_2)
    L5_2 = true
    return L5_2
  else
    L5_2 = false
    return L5_2
  end
end

_ENV["购买宝图全部流程"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  repeat
    L0_2 = ""
    L1_2 = ""
    L2_2 = ""
    L3_2 = ""
    L4_2 = ""
    L5_2 = ""
    L6_2 = ""
    L7_2 = ""
    L8_2 = ""
    L9_2 = ""
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1345
    L16_2 = 340
    L17_2 = 1431
    L18_2 = 383
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L0_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 384
    L17_2 = 1424
    L18_2 = 423
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L1_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 427
    L17_2 = 1425
    L18_2 = 465
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L2_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 467
    L17_2 = 1419
    L18_2 = 505
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L3_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1350
    L16_2 = 510
    L17_2 = 1418
    L18_2 = 545
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = L10_2
    L10_2 = tonumber
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L5_2 = L10_2
    L10_2 = tonumber
    L11_2 = L1_2
    L10_2 = L10_2(L11_2)
    L6_2 = L10_2
    L10_2 = tonumber
    L11_2 = L2_2
    L10_2 = L10_2(L11_2)
    L7_2 = L10_2
    L10_2 = tonumber
    L11_2 = L3_2
    L10_2 = L10_2(L11_2)
    L8_2 = L10_2
    L10_2 = tonumber
    L11_2 = L4_2
    L10_2 = L10_2(L11_2)
    L9_2 = L10_2
  until L5_2 ~= nil and L6_2 ~= nil and L7_2 ~= nil and L8_2 ~= nil and L9_2 ~= nil
  L10_2 = nLog
  L11_2 = L5_2
  L12_2 = ","
  L13_2 = L6_2
  L14_2 = ","
  L15_2 = L7_2
  L16_2 = ","
  L17_2 = L8_2
  L18_2 = ","
  L19_2 = L9_2
  L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2
  L10_2(L11_2)
  while true do
    if L5_2 > L6_2 and L5_2 > L7_2 and L5_2 > L8_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 > L6_2 and L5_2 > L7_2 and L5_2 > L9_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 > L6_2 and L5_2 > L8_2 and L5_2 > L9_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 > L7_2 and L5_2 > L8_2 and L5_2 > L9_2 then
      L10_2 = true
      return L10_2
    else
      L10_2 = false
      return L10_2
    end
  end
end

_ENV["身强体壮"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  repeat
    L0_2 = ""
    L1_2 = ""
    L2_2 = ""
    L3_2 = ""
    L4_2 = ""
    L5_2 = ""
    L6_2 = ""
    L7_2 = ""
    L8_2 = ""
    L9_2 = ""
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1345
    L16_2 = 340
    L17_2 = 1431
    L18_2 = 383
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L0_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 384
    L17_2 = 1424
    L18_2 = 423
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L1_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 427
    L17_2 = 1425
    L18_2 = 465
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L2_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 467
    L17_2 = 1419
    L18_2 = 505
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L3_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1350
    L16_2 = 510
    L17_2 = 1418
    L18_2 = 545
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = L10_2
    L10_2 = tonumber
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L5_2 = L10_2
    L10_2 = tonumber
    L11_2 = L1_2
    L10_2 = L10_2(L11_2)
    L6_2 = L10_2
    L10_2 = tonumber
    L11_2 = L2_2
    L10_2 = L10_2(L11_2)
    L7_2 = L10_2
    L10_2 = tonumber
    L11_2 = L3_2
    L10_2 = L10_2(L11_2)
    L8_2 = L10_2
    L10_2 = tonumber
    L11_2 = L4_2
    L10_2 = L10_2(L11_2)
    L9_2 = L10_2
  until L5_2 ~= nil and L6_2 ~= nil and L7_2 ~= nil and L8_2 ~= nil and L9_2 ~= nil
  L10_2 = nLog
  L11_2 = L5_2
  L12_2 = ","
  L13_2 = L6_2
  L14_2 = ","
  L15_2 = L7_2
  L16_2 = ","
  L17_2 = L8_2
  L18_2 = ","
  L19_2 = L9_2
  L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2
  L10_2(L11_2)
  while true do
    if L5_2 < L6_2 and L6_2 > L7_2 and L6_2 > L8_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 < L6_2 and L6_2 > L7_2 and L6_2 > L9_2 then
      L10_2 = true
      return L10_2
    elseif L6_2 > L9_2 and L6_2 > L8_2 and L6_2 > L7_2 then
      L10_2 = true
      return L10_2
    elseif L6_2 > L9_2 and L6_2 > L8_2 and L5_2 < L6_2 then
      L10_2 = true
      return L10_2
    else
      L10_2 = false
      return L10_2
    end
  end
end

_ENV["法力高深"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  repeat
    L0_2 = ""
    L1_2 = ""
    L2_2 = ""
    L3_2 = ""
    L4_2 = ""
    L5_2 = ""
    L6_2 = ""
    L7_2 = ""
    L8_2 = ""
    L9_2 = ""
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1345
    L16_2 = 340
    L17_2 = 1431
    L18_2 = 383
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L0_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 384
    L17_2 = 1424
    L18_2 = 423
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L1_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 427
    L17_2 = 1425
    L18_2 = 465
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L2_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 467
    L17_2 = 1419
    L18_2 = 505
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L3_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1350
    L16_2 = 510
    L17_2 = 1418
    L18_2 = 545
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = L10_2
    L10_2 = tonumber
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L5_2 = L10_2
    L10_2 = tonumber
    L11_2 = L1_2
    L10_2 = L10_2(L11_2)
    L6_2 = L10_2
    L10_2 = tonumber
    L11_2 = L2_2
    L10_2 = L10_2(L11_2)
    L7_2 = L10_2
    L10_2 = tonumber
    L11_2 = L3_2
    L10_2 = L10_2(L11_2)
    L8_2 = L10_2
    L10_2 = tonumber
    L11_2 = L4_2
    L10_2 = L10_2(L11_2)
    L9_2 = L10_2
  until L5_2 ~= nil and L6_2 ~= nil and L7_2 ~= nil and L8_2 ~= nil and L9_2 ~= nil
  L10_2 = nLog
  L11_2 = L5_2
  L12_2 = ","
  L13_2 = L6_2
  L14_2 = ","
  L15_2 = L7_2
  L16_2 = ","
  L17_2 = L8_2
  L18_2 = ","
  L19_2 = L9_2
  L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2
  L10_2(L11_2)
  while true do
    if L5_2 < L7_2 and L6_2 < L7_2 and L7_2 > L8_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 < L7_2 and L6_2 < L7_2 and L7_2 > L9_2 then
      L10_2 = true
      return L10_2
    elseif L7_2 > L9_2 and L7_2 > L8_2 and L6_2 < L7_2 then
      L10_2 = true
      return L10_2
    elseif L7_2 > L9_2 and L7_2 > L8_2 and L5_2 < L7_2 then
      L10_2 = true
      return L10_2
    else
      L10_2 = false
      return L10_2
    end
  end
end

_ENV["武力超群"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  repeat
    L0_2 = ""
    L1_2 = ""
    L2_2 = ""
    L3_2 = ""
    L4_2 = ""
    L5_2 = ""
    L6_2 = ""
    L7_2 = ""
    L8_2 = ""
    L9_2 = ""
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1345
    L16_2 = 340
    L17_2 = 1431
    L18_2 = 383
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L0_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 384
    L17_2 = 1424
    L18_2 = 423
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L1_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 427
    L17_2 = 1425
    L18_2 = 465
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L2_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 467
    L17_2 = 1419
    L18_2 = 505
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L3_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1350
    L16_2 = 510
    L17_2 = 1418
    L18_2 = 545
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = L10_2
    L10_2 = tonumber
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L11_2 = tonumber
    L12_2 = L1_2
    L11_2 = L11_2(L12_2)
    L12_2 = tonumber
    L13_2 = L2_2
    L12_2 = L12_2(L13_2)
    L13_2 = tonumber
    L14_2 = L3_2
    L13_2 = L13_2(L14_2)
    L14_2 = tonumber
    L15_2 = L4_2
    L14_2 = L14_2(L15_2)
  until L10_2 ~= nil and L11_2 ~= nil and L12_2 ~= nil and L13_2 ~= nil and L14_2 ~= nil
  L15_2 = nLog
  L16_2 = L10_2
  L17_2 = ","
  L18_2 = L11_2
  L19_2 = ","
  L20_2 = L12_2
  L21_2 = ","
  L22_2 = L13_2
  L23_2 = ","
  L24_2 = L14_2
  L16_2 = L16_2 .. L17_2 .. L18_2 .. L19_2 .. L20_2 .. L21_2 .. L22_2 .. L23_2 .. L24_2
  L15_2(L16_2)
  while true do
    if L10_2 < L13_2 and L11_2 < L13_2 and L13_2 > L14_2 then
      L15_2 = true
      return L15_2
    elseif L10_2 < L13_2 and L11_2 < L13_2 and L12_2 < L13_2 then
      L15_2 = true
      return L15_2
    elseif L13_2 > L14_2 and L12_2 < L13_2 and L11_2 < L13_2 then
      L15_2 = true
      return L15_2
    elseif L13_2 > L14_2 and L12_2 < L13_2 and L10_2 < L13_2 then
      L15_2 = true
      return L15_2
    else
      L15_2 = false
      return L15_2
    end
  end
end

_ENV["刀枪不入"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  repeat
    L0_2 = ""
    L1_2 = ""
    L2_2 = ""
    L3_2 = ""
    L4_2 = ""
    L5_2 = ""
    L6_2 = ""
    L7_2 = ""
    L8_2 = ""
    L9_2 = ""
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1345
    L16_2 = 340
    L17_2 = 1431
    L18_2 = 383
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L0_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 384
    L17_2 = 1424
    L18_2 = 423
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L1_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 427
    L17_2 = 1425
    L18_2 = 465
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L2_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1348
    L16_2 = 467
    L17_2 = 1419
    L18_2 = 505
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L3_2 = L10_2
    L10_2 = _ENV["字库识别属性"]
    L11_2 = _ENV["字库"]
    L12_2 = "识别"
    L13_2 = "召唤兽属性"
    L14_2 = _ENV["召唤兽属性"]
    L15_2 = 1350
    L16_2 = 510
    L17_2 = 1418
    L18_2 = 545
    L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = L10_2
    L10_2 = tonumber
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L5_2 = L10_2
    L10_2 = tonumber
    L11_2 = L1_2
    L10_2 = L10_2(L11_2)
    L6_2 = L10_2
    L10_2 = tonumber
    L11_2 = L2_2
    L10_2 = L10_2(L11_2)
    L7_2 = L10_2
    L10_2 = tonumber
    L11_2 = L3_2
    L10_2 = L10_2(L11_2)
    L8_2 = L10_2
    L10_2 = tonumber
    L11_2 = L4_2
    L10_2 = L10_2(L11_2)
    L9_2 = L10_2
  until L5_2 ~= nil and L6_2 ~= nil and L7_2 ~= nil and L8_2 ~= nil and L9_2 ~= nil
  L10_2 = nLog
  L11_2 = L5_2
  L12_2 = ","
  L13_2 = L6_2
  L14_2 = ","
  L15_2 = L7_2
  L16_2 = ","
  L17_2 = L8_2
  L18_2 = ","
  L19_2 = L9_2
  L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2
  L10_2(L11_2)
  while true do
    if L5_2 < L9_2 and L6_2 < L9_2 and L7_2 < L9_2 then
      L10_2 = true
      return L10_2
    elseif L5_2 < L9_2 and L6_2 < L9_2 and L8_2 < L9_2 then
      L10_2 = true
      return L10_2
    elseif L8_2 < L9_2 and L7_2 < L9_2 and L5_2 < L9_2 then
      L10_2 = true
      return L10_2
    elseif L8_2 < L9_2 and L7_2 < L9_2 and L6_2 < L9_2 then
      L10_2 = true
      return L10_2
    else
      L10_2 = false
      return L10_2
    end
  end
end

_ENV["身手敏捷"] = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = 124
L3_1 = 232
L4_1 = 446
L5_1 = 334
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["1号格子"] = L1_1
L1_1 = {}
L2_1 = 474
L3_1 = 226
L4_1 = 812
L5_1 = 340
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["2号格子"] = L1_1
L1_1 = {}
L2_1 = 112
L3_1 = 372
L4_1 = 446
L5_1 = 484
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["3号格子"] = L1_1
L1_1 = {}
L2_1 = 471
L3_1 = 370
L4_1 = 816
L5_1 = 488
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["4号格子"] = L1_1
L1_1 = {}
L2_1 = 108
L3_1 = 518
L4_1 = 450
L5_1 = 632
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["5号格子"] = L1_1
L1_1 = {}
L2_1 = 472
L3_1 = 518
L4_1 = 814
L5_1 = 636
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["6号格子"] = L1_1
L1_1 = {}
L2_1 = 110
L3_1 = 662
L4_1 = 450
L5_1 = 764
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["7号格子"] = L1_1
L1_1 = {}
L2_1 = 470
L3_1 = 660
L4_1 = 814
L5_1 = 764
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L0_1["8号格子"] = L1_1
_ENV["商会位置格子"] = L0_1
L0_1 = {}
L1_1 = _ENV["身强体壮"]
L0_1["身强体壮"] = L1_1
L1_1 = _ENV["法力高深"]
L0_1["法力高深"] = L1_1
L1_1 = _ENV["武力超群"]
L0_1["武力超群"] = L1_1
L1_1 = _ENV["刀枪不入"]
L0_1["刀枪不入"] = L1_1
L1_1 = _ENV["身手敏捷"]
L0_1["身手敏捷"] = L1_1
_ENV["宠物特性"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2
  L2_2 = false
  L3_2 = false
  L4_2 = false
  if A0_2 == "大海龟" or A0_2 == "巨蛙" or A0_2 == "野猪" or A0_2 == "树怪" or A0_2 == "强盗" or A0_2 == "赌徒" or A0_2 == "山贼" or A0_2 == "老虎" or A0_2 == "黑熊" or A0_2 == "花妖" or A0_2 == "羊头怪" or A0_2 == "蛤蟆精" or A0_2 == "骷髅怪" or A0_2 == "狐狸精" then
    L2_2 = true
  elseif A0_2 == "狼" or A0_2 == "小龙女" or A0_2 == "虾兵" or A0_2 == "蟹将" or A0_2 == "龟丞相" or A0_2 == "牛头" or A0_2 == "马面" or A0_2 == "牛妖" or A0_2 == "僵尸" or A0_2 == "野鬼" or A0_2 == "兔子怪" or A0_2 == "蜘蛛精" or A0_2 == "黑熊精" then
    L3_2 = true
  elseif A0_2 == "蝴蝶仙子" or A0_2 == "雷鸟人" or A0_2 == "古代瑞兽" or A0_2 == "白熊" or A0_2 == "黑山老妖" or A0_2 == "天将" or A0_2 == "天兵" or A0_2 == "风伯" or A0_2 == "地狱战神" or A0_2 == "蛟龙" or A0_2 == "凤凰" or A0_2 == "雨师" or A0_2 == "巡游天神" or A0_2 == "星灵仙子" or A0_2 == "芙蓉仙子" or A0_2 == "如意仙子" then
    L4_2 = true
  end
  L5_2 = 0
  L6_2 = {}
  L7_2 = {}
  L8_2 = 1129
  L9_2 = 488
  L10_2 = 1249
  L11_2 = 527
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L6_2["二药"] = L7_2
  L7_2 = {}
  L8_2 = 1124
  L9_2 = 385
  L10_2 = 1252
  L11_2 = 428
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L6_2["烹饪"] = L7_2
  L7_2 = {}
  L8_2 = 1394
  L9_2 = 288
  L10_2 = 1530
  L11_2 = 327
  L12_2 = 1363
  L13_2 = 776
  L14_2 = 1528
  L15_2 = 813
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L7_2[5] = L12_2
  L7_2[6] = L13_2
  L7_2[7] = L14_2
  L7_2[8] = L15_2
  L6_2["宠物"] = L7_2
  L7_2 = {}
  L8_2 = 1085
  L9_2 = 476
  L10_2 = 1294
  L11_2 = 535
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L6_2["三药"] = L7_2
  L7_2 = {}
  L8_2 = 819
  L9_2 = 478
  L10_2 = 1021
  L11_2 = 528
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L6_2["家具"] = L7_2
  L7_2 = A0_2
  while true do
    L8_2 = printLog
    L9_2 = "转换名店龟速查找:"
    L10_2 = A0_2
    L9_2 = L9_2 .. L10_2
    L8_2(L9_2)
    if L5_2 == 0 then
      L8_2 = pairs
      L9_2 = BuyToType
      L8_2, L9_2, L10_2 = L8_2(L9_2)
      for L11_2, L12_2 in L8_2, L9_2, L10_2 do
        L13_2 = 1
        L14_2 = #L12_2
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = L12_2[L16_2]
          if A0_2 == L17_2 then
            L7_2 = L11_2
          end
        end
      end
      L8_2 = randomClick
      L9_2 = 0
      L10_2 = 500
      L11_2 = 1115
      L12_2 = 155
      L13_2 = 1238
      L14_2 = 185
      L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "家具" or L7_2 == "三药" then
        L8_2 = randomClick
        L9_2 = 0
        L10_2 = 500
        L11_2 = L6_2[L7_2]
        L11_2 = L11_2[1]
        L12_2 = L6_2[L7_2]
        L12_2 = L12_2[2]
        L13_2 = L6_2[L7_2]
        L13_2 = L13_2[3]
        L14_2 = L6_2[L7_2]
        L14_2 = L14_2[4]
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      else
        L8_2 = randomClick
        L9_2 = 0
        L10_2 = 500
        L11_2 = L6_2["宠物"]
        L11_2 = L11_2[1]
        L12_2 = L6_2["宠物"]
        L12_2 = L12_2[2]
        L13_2 = L6_2["宠物"]
        L13_2 = L13_2[3]
        L14_2 = L6_2["宠物"]
        L14_2 = L14_2[4]
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        L8_2 = randomClick
        L9_2 = 0
        L10_2 = 500
        L11_2 = L6_2["宠物"]
        L11_2 = L11_2[5]
        L12_2 = L6_2["宠物"]
        L12_2 = L12_2[6]
        L13_2 = L6_2["宠物"]
        L13_2 = L13_2[7]
        L14_2 = L6_2["宠物"]
        L14_2 = L14_2[8]
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      end
      L5_2 = 1
      L8_2 = ""
      L9_2 = _ENV["商会不买前几页"]
      if L9_2 == "0" then
      else
        L9_2 = tonumber
        L10_2 = _ENV["商会不买前几页"]
        L9_2 = L9_2(L10_2)
        L8_2 = L9_2
        L9_2 = 1
        L10_2 = L8_2
        L11_2 = 1
        for L12_2 = L9_2, L10_2, L11_2 do
          L13_2 = randomClick
          L14_2 = 2
          L15_2 = 888
          L16_2 = 600
          L17_2 = 970
          L13_2(L14_2, L15_2, L16_2, L17_2)
          L13_2 = mSleep
          L14_2 = 300
          L13_2(L14_2)
        end
      end
    elseif L5_2 == 1 then
      L8_2 = {}
      if L2_2 then
        while true do
          L9_2 = _find_ex
          L10_2 = Color
          L10_2 = L10_2["师门"]
          L10_2 = L10_2["商会低级"]
          L11_2 = {}
          L12_2 = 222
          L13_2 = 318
          L14_2 = 1316
          L15_2 = 895
          L11_2[1] = L12_2
          L11_2[2] = L13_2
          L11_2[3] = L14_2
          L11_2[4] = L15_2
          L12_2 = {}
          L13_2 = 10
          L14_2 = 50
          L12_2[1] = L13_2
          L12_2[2] = L14_2
          L9_2 = L9_2(L10_2, L11_2, L12_2)
          L8_2 = L9_2
          L9_2 = #L8_2
          if L9_2 ~= 0 then
            L9_2 = 1
            L10_2 = #L8_2
            L11_2 = 1
            for L12_2 = L9_2, L10_2, L11_2 do
              L13_2 = true
              while true do
                L14_2 = getColour
                L15_2 = colorList
                L16_2 = "商会店铺"
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  L14_2 = 1
                  L15_2 = 10
                  L16_2 = 1
                  for L17_2 = L14_2, L15_2, L16_2 do
                    L18_2 = false
                    L19_2 = -1
                    L20_2 = -1
                    L21_2 = false
                    L22_2 = false
                    L23_2 = 1
                    L24_2 = 10
                    L25_2 = 1
                    for L26_2 = L23_2, L24_2, L25_2 do
                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "家具" or L7_2 == "三药" then
                        if A0_2 == "豆斋果" or A0_2 == "醉生梦死" or A0_2 == "长寿面" or A0_2 == "佛跳墙" or A0_2 == "烤鸭" or A0_2 == "珍露酒" or A0_2 == "臭豆腐" or A0_2 == "烤肉" or A0_2 == "桂花丸" or A0_2 == "翡翠豆腐" or A0_2 == "梅花酒" or A0_2 == "百味酒" or A0_2 == "蛇胆酒" then
                          L27_2 = _ENV["烹饪价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["烹饪价格"]
                            if L27_2 ~= nil then
                              goto lbl_326
                            end
                          end
                          _ENV["烹饪价格"] = 99999
                          ::lbl_326::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["烹饪价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            if L27_2 ~= nil then
                              goto lbl_367
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_367::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            if L27_2 ~= nil then
                              goto lbl_408
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_408::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "黑麻垂曼帘" or A0_2 == "漆花竹凳" or A0_2 == "桦木圆桌" or A0_2 == "桦木立柜" or A0_2 == "草编地毯" or A0_2 == "榛木床" or A0_2 == "印花屏风" or A0_2 == "文竹" or A0_2 == "君子兰" or A0_2 == "蝴蝶兰" or A0_2 == "水仙" or A0_2 == "仙人掌" or A0_2 == "银烛台" or A0_2 == "玉瓷画瓶" or A0_2 == "踏春图" or A0_2 == "漆花地板" then
                          L27_2 = _ENV["一家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["一家价格"]
                            if L27_2 ~= nil then
                              goto lbl_459
                            end
                          end
                          _ENV["一家价格"] = 99999
                          ::lbl_459::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["一家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "白鹤展翅帘" or A0_2 == "桦木地板" or A0_2 == "夕阳山水图" or A0_2 == "蓝绸绣花帘" or A0_2 == "红木八仙桌" or A0_2 == "镶玉虎纹桌" or A0_2 == "双鱼吉庆柜" or A0_2 == "彩绘立柜" or A0_2 == "兽皮地毯" or A0_2 == "麻布地毯" or A0_2 == "桦木靠背椅" or A0_2 == "神仙帐" or A0_2 == "月牙凳" or A0_2 == "八卦镇邪塌" or A0_2 == "狮子图屏风" or A0_2 == "花鸟图屏风" or A0_2 == "天山云雪" or A0_2 == "龟鹤延年灯" or A0_2 == "长信宫灯" or A0_2 == "雕花马桶" or A0_2 == "彩绘花瓶" then
                          L27_2 = _ENV["二家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二家价格"]
                            if L27_2 ~= nil then
                              goto lbl_520
                            end
                          end
                          _ENV["二家价格"] = 99999
                          ::lbl_520::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "天不老" or A0_2 == "紫石英" or A0_2 == "凤凰尾巴" or A0_2 == "硫磺草" or A0_2 == "龙之心屑" or A0_2 == "月星子" or A0_2 == "血珊瑚" or A0_2 == "火凤之眼" or A0_2 == "孔雀红" or A0_2 == "鹿茸" or A0_2 == "仙狐涎" or A0_2 == "天龙水" or A0_2 == "地狱灵芝" or A0_2 == "六道轮回" or A0_2 == "白露为霜" or A0_2 == "餐风饮露" then
                          L27_2 = _ENV["二药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二药价格"]
                            if L27_2 ~= nil then
                              goto lbl_571
                            end
                          end
                          _ENV["二药价格"] = 99999
                          ::lbl_571::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        else
                          L27_2 = getMultiColor
                          L28_2 = propsList
                          L29_2 = A0_2
                          L30_2 = 85
                          L31_2 = 117
                          L32_2 = 224
                          L33_2 = 803
                          L34_2 = 752
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                        end
                      else
                        L27_2 = _ENV["_卡区延迟"]
                        L27_2()
                        L27_2 = _ENV["_随机延时"]
                        L28_2 = 300
                        L27_2(L28_2)
                        L27_2 = getWordStock
                        L28_2 = PetList
                        L28_2 = L28_2["低于一万"]
                        L29_2 = A0_2
                        L30_2 = 176
                        L31_2 = 250
                        L32_2 = 735
                        L33_2 = 773
                        L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                        L20_2 = L29_2
                        L19_2 = L28_2
                        L21_2 = L27_2
                        if not L21_2 then
                          L27_2 = getWordStock
                          L28_2 = PetList
                          L28_2 = L28_2["超出一万"]
                          L29_2 = A0_2
                          L30_2 = 176
                          L31_2 = 250
                          L32_2 = 735
                          L33_2 = 773
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L22_2 = L27_2
                        end
                        if L21_2 then
                          L21_2 = false
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["低于一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y1 = L33_2
                            x1 = L32_2
                            boolean1 = L31_2
                            L31_2 = boolean1
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x1
                              L33_2 = y1
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L22_2 then
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["超出一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y2 = L33_2
                            x2 = L32_2
                            boolean2 = L31_2
                            L31_2 = boolean2
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x2
                              L33_2 = y2
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L18_2 and A0_2 == "黑熊" then
                          L27_2 = tsFindText
                          L28_2 = _ENV["黑熊精黑熊区分"]
                          L29_2 = "黑熊精"
                          L30_2 = 187
                          L31_2 = 253
                          L32_2 = 744
                          L33_2 = 754
                          L34_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                          L35_2 = 90
                          L27_2, L28_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                          y11 = L28_2
                          x11 = L27_2
                          L27_2 = x11
                          if 0 < L27_2 then
                            L18_2 = false
                            repeat
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会店铺关闭店铺"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = mSleep
                                L28_2 = 250
                                L27_2(L28_2)
                              end
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["弹出召唤兽"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = randomClick
                                L28_2 = 0
                                L29_2 = 300
                                L30_2 = 1665
                                L31_2 = 5
                                L32_2 = 1689
                                L33_2 = 36
                                L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                              end
                              L27_2 = mSleep
                              L28_2 = 250
                              L27_2(L28_2)
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会主界面"]
                              L27_2 = L27_2(L28_2)
                            until L27_2
                            break
                          end
                        end
                      end
                      if L18_2 then
                        L27_2 = true
                        L28_2 = 0
                        L29_2 = nil
                        while true do
                          L30_2 = getColour
                          L31_2 = colorList
                          L32_2 = "提示信息"
                          L30_2 = L30_2(L31_2, L32_2)
                          if L30_2 or L29_2 then
                            L30_2 = printLog
                            L31_2 = "完成购买:"
                            L32_2 = A0_2
                            L31_2 = L31_2 .. L32_2
                            L30_2(L31_2)
                            while true do
                              L30_2 = getColour
                              L31_2 = colorList
                              L32_2 = "商会店铺"
                              L30_2 = L30_2(L31_2, L32_2)
                              if L30_2 then
                                L30_2 = randomClick
                                L31_2 = 2
                                L32_2 = 100
                                L33_2 = 848
                                L34_2 = 49
                                L30_2(L31_2, L32_2, L33_2, L34_2)
                              else
                                L30_2 = getColour
                                L31_2 = colorList
                                L32_2 = "商会列表"
                                L30_2 = L30_2(L31_2, L32_2)
                                if L30_2 then
                                  L30_2 = randomClick
                                  L31_2 = 2
                                  L32_2 = 300
                                  L33_2 = 1705
                                  L34_2 = 66
                                  L30_2(L31_2, L32_2, L33_2, L34_2)
                                else
                                  L30_2 = getColour
                                  L31_2 = colorList
                                  L32_2 = "宠物属性页面"
                                  L30_2 = L30_2(L31_2, L32_2)
                                  if L30_2 then
                                    L30_2 = randomClick
                                    L31_2 = 2
                                    L32_2 = 300
                                    L33_2 = 1705
                                    L34_2 = 76
                                    L35_2 = 20
                                    L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                                  else
                                    L30_2 = getColour
                                    L31_2 = colorList
                                    L32_2 = "人物属性页面"
                                    L30_2 = L30_2(L31_2, L32_2)
                                    if L30_2 then
                                      L30_2 = randomClick
                                      L31_2 = 2
                                      L32_2 = 300
                                      L33_2 = 1603
                                      L34_2 = 86
                                      L30_2(L31_2, L32_2, L33_2, L34_2)
                                    else
                                      L30_2 = getColour
                                      L31_2 = colorList
                                      L32_2 = "动作按钮"
                                      L30_2 = L30_2(L31_2, L32_2)
                                      if not L30_2 then
                                        L30_2 = getColour
                                        L31_2 = colorList
                                        L32_2 = "设置按钮"
                                        L30_2 = L30_2(L31_2, L32_2)
                                        if not L30_2 then
                                          L30_2 = getColour
                                          L31_2 = colorList
                                          L32_2 = "道具按钮"
                                          L30_2 = L30_2(L31_2, L32_2)
                                          if not L30_2 then
                                            goto lbl_916
                                          end
                                        end
                                      end
                                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                                        L30_2 = false
                                        return L30_2
                                      else
                                        L30_2 = true
                                        return L30_2
                                      end
                                    end
                                  end
                                end
                              end
                              ::lbl_916::
                            end
                          elseif L27_2 then
                            L27_2 = false
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L28_2 = L30_2
                            if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                              L30_2 = randomClick
                              L31_2 = 2
                              L32_2 = 300
                              L33_2 = L19_2
                              L34_2 = L20_2 + 20
                              L35_2 = 20
                              L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                            end
                            L30_2 = randomClick
                            L31_2 = 0
                            L32_2 = 300
                            L33_2 = 627
                            L34_2 = 944
                            L35_2 = 779
                            L36_2 = 991
                            L30_2(L31_2, L32_2, L33_2, L34_2, L35_2, L36_2)
                            L29_2 = true
                          else
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L30_2 = L30_2 - L28_2
                            if 3 < L30_2 and L28_2 ~= 0 then
                              L27_2 = true
                            end
                          end
                        end
                      else
                        L27_2 = mSleep
                        L28_2 = 100
                        L27_2(L28_2)
                      end
                      L27_2 = false
                      if L21_2 == L27_2 then
                        break
                      end
                      L27_2 = false
                      if L22_2 == L27_2 then
                        break
                      end
                    end
                    L23_2 = randomClick
                    L24_2 = 2
                    L25_2 = 1000
                    L26_2 = 445
                    L27_2 = 962
                    L23_2(L24_2, L25_2, L26_2, L27_2)
                  end
                  while true do
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会列表"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      goto lbl_1025
                    end
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会店铺"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      L14_2 = randomClick
                      L15_2 = 2
                      L16_2 = 200
                      L17_2 = 848
                      L18_2 = 49
                      L14_2(L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                  break
                elseif L13_2 then
                  L13_2 = false
                  L14_2 = randomClick
                  L15_2 = 1
                  L16_2 = 300
                  L17_2 = L8_2[L12_2]
                  L18_2 = "x"
                  L17_2 = L17_2[L18_2]
                  L18_2 = L8_2[L12_2]
                  L19_2 = "y"
                  L18_2 = L18_2[L19_2]
                  L14_2(L15_2, L16_2, L17_2, L18_2)
                  L14_2 = randomClick
                  L15_2 = 0
                  L16_2 = 888
                  L17_2 = 1486
                  L18_2 = 943
                  L19_2 = 1683
                  L20_2 = 1000
                  L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                end
              end
              ::lbl_1025::
            end
          else
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 888
            L12_2 = 600
            L13_2 = 970
            L9_2(L10_2, L11_2, L12_2, L13_2)
          end
          L9_2 = randomClick
          L10_2 = 2
          L11_2 = 888
          L12_2 = 600
          L13_2 = 970
          L9_2(L10_2, L11_2, L12_2, L13_2)
        end
      elseif L3_2 then
        while true do
          L9_2 = _find_ex
          L10_2 = Color
          L10_2 = L10_2["师门"]
          L11_2 = "商会中级"
          L10_2 = L10_2[L11_2]
          L11_2 = {}
          L12_2 = 222
          L13_2 = 318
          L14_2 = 1316
          L15_2 = 895
          L11_2[1] = L12_2
          L11_2[2] = L13_2
          L11_2[3] = L14_2
          L11_2[4] = L15_2
          L12_2 = {}
          L13_2 = 10
          L14_2 = 50
          L12_2[1] = L13_2
          L12_2[2] = L14_2
          L9_2 = L9_2(L10_2, L11_2, L12_2)
          L8_2 = L9_2
          L9_2 = #L8_2
          if L9_2 ~= 0 then
            L9_2 = 1
            L10_2 = #L8_2
            L11_2 = 1
            for L12_2 = L9_2, L10_2, L11_2 do
              L13_2 = true
              while true do
                L14_2 = getColour
                L15_2 = colorList
                L16_2 = "商会店铺"
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  L14_2 = 1
                  L15_2 = 10
                  L16_2 = 1
                  for L17_2 = L14_2, L15_2, L16_2 do
                    L18_2 = false
                    L19_2 = -1
                    L20_2 = -1
                    L21_2 = false
                    L22_2 = false
                    L23_2 = 1
                    L24_2 = 10
                    L25_2 = 1
                    for L26_2 = L23_2, L24_2, L25_2 do
                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "家具" or L7_2 == "三药" then
                        if A0_2 == "豆斋果" or A0_2 == "醉生梦死" or A0_2 == "长寿面" or A0_2 == "佛跳墙" or A0_2 == "烤鸭" or A0_2 == "珍露酒" or A0_2 == "臭豆腐" or A0_2 == "烤肉" or A0_2 == "桂花丸" or A0_2 == "翡翠豆腐" or A0_2 == "梅花酒" or A0_2 == "百味酒" or A0_2 == "蛇胆酒" then
                          L27_2 = _ENV["烹饪价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["烹饪价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1129
                            end
                          end
                          _ENV["烹饪价格"] = 99999
                          ::lbl_1129::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["烹饪价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1171
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_1171::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1213
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_1213::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "黑麻垂曼帘" or A0_2 == "漆花竹凳" or A0_2 == "桦木圆桌" or A0_2 == "桦木立柜" or A0_2 == "草编地毯" or A0_2 == "榛木床" or A0_2 == "印花屏风" or A0_2 == "文竹" or A0_2 == "君子兰" or A0_2 == "蝴蝶兰" or A0_2 == "水仙" or A0_2 == "仙人掌" or A0_2 == "银烛台" or A0_2 == "玉瓷画瓶" or A0_2 == "踏春图" or A0_2 == "漆花地板" then
                          L27_2 = _ENV["一家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["一家价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1265
                            end
                          end
                          _ENV["一家价格"] = 99999
                          ::lbl_1265::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["一家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "白鹤展翅帘" or A0_2 == "桦木地板" or A0_2 == "夕阳山水图" or A0_2 == "蓝绸绣花帘" or A0_2 == "红木八仙桌" or A0_2 == "镶玉虎纹桌" or A0_2 == "双鱼吉庆柜" or A0_2 == "彩绘立柜" or A0_2 == "兽皮地毯" or A0_2 == "麻布地毯" or A0_2 == "桦木靠背椅" or A0_2 == "神仙帐" or A0_2 == "月牙凳" or A0_2 == "八卦镇邪塌" or A0_2 == "狮子图屏风" or A0_2 == "花鸟图屏风" or A0_2 == "天山云雪" or A0_2 == "龟鹤延年灯" or A0_2 == "长信宫灯" or A0_2 == "雕花马桶" or A0_2 == "彩绘花瓶" then
                          L27_2 = _ENV["二家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二家价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1327
                            end
                          end
                          _ENV["二家价格"] = 99999
                          ::lbl_1327::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "天不老" or A0_2 == "紫石英" or A0_2 == "凤凰尾巴" or A0_2 == "硫磺草" or A0_2 == "龙之心屑" or A0_2 == "月星子" or A0_2 == "血珊瑚" or A0_2 == "火凤之眼" or A0_2 == "孔雀红" or A0_2 == "鹿茸" or A0_2 == "仙狐涎" or A0_2 == "天龙水" or A0_2 == "地狱灵芝" or A0_2 == "六道轮回" or A0_2 == "白露为霜" or A0_2 == "餐风饮露" then
                          L27_2 = _ENV["二药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1379
                            end
                          end
                          _ENV["二药价格"] = 99999
                          ::lbl_1379::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        else
                          L27_2 = getMultiColor
                          L28_2 = propsList
                          L29_2 = A0_2
                          L30_2 = 85
                          L31_2 = 117
                          L32_2 = 224
                          L33_2 = 803
                          L34_2 = 752
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                        end
                      else
                        L27_2 = _ENV["_卡区延迟"]
                        L27_2()
                        L27_2 = _ENV["_随机延时"]
                        L28_2 = 300
                        L27_2(L28_2)
                        L27_2 = getWordStock
                        L28_2 = PetList
                        L28_2 = L28_2["低于一万"]
                        L29_2 = A0_2
                        L30_2 = 176
                        L31_2 = 250
                        L32_2 = 735
                        L33_2 = 773
                        L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                        L20_2 = L29_2
                        L19_2 = L28_2
                        L21_2 = L27_2
                        if not L21_2 then
                          L27_2 = getWordStock
                          L28_2 = PetList
                          L28_2 = L28_2["超出一万"]
                          L29_2 = A0_2
                          L30_2 = 176
                          L31_2 = 250
                          L32_2 = 735
                          L33_2 = 773
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L22_2 = L27_2
                        end
                        if L21_2 then
                          L21_2 = false
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["低于一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y1 = L33_2
                            x1 = L32_2
                            boolean1 = L31_2
                            L31_2 = boolean1
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x1
                              L33_2 = y1
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L22_2 then
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["超出一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y2 = L33_2
                            x2 = L32_2
                            boolean2 = L31_2
                            L31_2 = boolean2
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x2
                              L33_2 = y2
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L18_2 and A0_2 == "黑熊" then
                          L27_2 = tsFindText
                          L28_2 = _ENV["黑熊精黑熊区分"]
                          L29_2 = "黑熊精"
                          L30_2 = 187
                          L31_2 = 253
                          L32_2 = 744
                          L33_2 = 754
                          L34_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                          L35_2 = 90
                          L27_2, L28_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                          y11 = L28_2
                          x11 = L27_2
                          L27_2 = x11
                          if 0 < L27_2 then
                            L18_2 = false
                            repeat
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会店铺关闭店铺"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = mSleep
                                L28_2 = 250
                                L27_2(L28_2)
                              end
                              L27_2 = mSleep
                              L28_2 = 250
                              L27_2(L28_2)
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["弹出召唤兽"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = randomClick
                                L28_2 = 0
                                L29_2 = 300
                                L30_2 = 1665
                                L31_2 = 5
                                L32_2 = 1689
                                L33_2 = 36
                                L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                              end
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会主界面"]
                              L27_2 = L27_2(L28_2)
                            until L27_2
                            break
                          end
                        end
                      end
                      if L18_2 then
                        L27_2 = true
                        L28_2 = 0
                        L29_2 = nil
                        while true do
                          L30_2 = getColour
                          L31_2 = colorList
                          L32_2 = "提示信息"
                          L30_2 = L30_2(L31_2, L32_2)
                          if L30_2 or L29_2 then
                            L30_2 = printLog
                            L31_2 = "完成购买:"
                            L32_2 = A0_2
                            L31_2 = L31_2 .. L32_2
                            L30_2(L31_2)
                            while true do
                              L30_2 = getColour
                              L31_2 = colorList
                              L32_2 = "商会店铺"
                              L30_2 = L30_2(L31_2, L32_2)
                              if L30_2 then
                                L30_2 = randomClick
                                L31_2 = 2
                                L32_2 = 100
                                L33_2 = 848
                                L34_2 = 49
                                L30_2(L31_2, L32_2, L33_2, L34_2)
                              else
                                L30_2 = getColour
                                L31_2 = colorList
                                L32_2 = "商会列表"
                                L30_2 = L30_2(L31_2, L32_2)
                                if L30_2 then
                                  L30_2 = randomClick
                                  L31_2 = 2
                                  L32_2 = 300
                                  L33_2 = 1705
                                  L34_2 = 66
                                  L30_2(L31_2, L32_2, L33_2, L34_2)
                                else
                                  L30_2 = getColour
                                  L31_2 = colorList
                                  L32_2 = "宠物属性页面"
                                  L30_2 = L30_2(L31_2, L32_2)
                                  if L30_2 then
                                    L30_2 = randomClick
                                    L31_2 = 2
                                    L32_2 = 300
                                    L33_2 = 1705
                                    L34_2 = 76
                                    L35_2 = 20
                                    L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                                  else
                                    L30_2 = getColour
                                    L31_2 = colorList
                                    L32_2 = "人物属性页面"
                                    L30_2 = L30_2(L31_2, L32_2)
                                    if L30_2 then
                                      L30_2 = randomClick
                                      L31_2 = 2
                                      L32_2 = 300
                                      L33_2 = 1603
                                      L34_2 = 86
                                      L30_2(L31_2, L32_2, L33_2, L34_2)
                                    else
                                      L30_2 = getColour
                                      L31_2 = colorList
                                      L32_2 = "动作按钮"
                                      L30_2 = L30_2(L31_2, L32_2)
                                      if not L30_2 then
                                        L30_2 = getColour
                                        L31_2 = colorList
                                        L32_2 = "设置按钮"
                                        L30_2 = L30_2(L31_2, L32_2)
                                        if not L30_2 then
                                          L30_2 = getColour
                                          L31_2 = colorList
                                          L32_2 = "道具按钮"
                                          L30_2 = L30_2(L31_2, L32_2)
                                          if not L30_2 then
                                            goto lbl_1724
                                          end
                                        end
                                      end
                                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                                        L30_2 = false
                                        return L30_2
                                      else
                                        L30_2 = true
                                        return L30_2
                                      end
                                    end
                                  end
                                end
                              end
                              ::lbl_1724::
                            end
                          elseif L27_2 then
                            L27_2 = false
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L28_2 = L30_2
                            if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                              L30_2 = randomClick
                              L31_2 = 2
                              L32_2 = 300
                              L33_2 = L19_2
                              L34_2 = L20_2 + 20
                              L35_2 = 20
                              L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                            end
                            L30_2 = randomClick
                            L31_2 = 0
                            L32_2 = 300
                            L33_2 = 627
                            L34_2 = 944
                            L35_2 = 779
                            L36_2 = 991
                            L30_2(L31_2, L32_2, L33_2, L34_2, L35_2, L36_2)
                            L29_2 = true
                          else
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L30_2 = L30_2 - L28_2
                            if 3 < L30_2 and L28_2 ~= 0 then
                              L27_2 = true
                            end
                          end
                        end
                      else
                        L27_2 = mSleep
                        L28_2 = 100
                        L27_2(L28_2)
                      end
                      L27_2 = false
                      if L21_2 == L27_2 then
                        break
                      end
                      L27_2 = false
                      if L22_2 == L27_2 then
                        break
                      end
                    end
                    L23_2 = randomClick
                    L24_2 = 2
                    L25_2 = 1000
                    L26_2 = 445
                    L27_2 = 962
                    L23_2(L24_2, L25_2, L26_2, L27_2)
                  end
                  while true do
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会列表"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      goto lbl_1833
                    end
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会店铺"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      L14_2 = randomClick
                      L15_2 = 2
                      L16_2 = 200
                      L17_2 = 848
                      L18_2 = 49
                      L14_2(L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                  break
                elseif L13_2 then
                  L13_2 = false
                  L14_2 = randomClick
                  L15_2 = 1
                  L16_2 = 300
                  L17_2 = L8_2[L12_2]
                  L18_2 = "x"
                  L17_2 = L17_2[L18_2]
                  L18_2 = L8_2[L12_2]
                  L19_2 = "y"
                  L18_2 = L18_2[L19_2]
                  L14_2(L15_2, L16_2, L17_2, L18_2)
                  L14_2 = randomClick
                  L15_2 = 0
                  L16_2 = 888
                  L17_2 = 1486
                  L18_2 = 943
                  L19_2 = 1683
                  L20_2 = 1000
                  L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                end
              end
              ::lbl_1833::
            end
          else
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 888
            L12_2 = 600
            L13_2 = 970
            L9_2(L10_2, L11_2, L12_2, L13_2)
          end
          L9_2 = randomClick
          L10_2 = 2
          L11_2 = 888
          L12_2 = 600
          L13_2 = 970
          L9_2(L10_2, L11_2, L12_2, L13_2)
        end
      elseif L4_2 then
        while true do
          L9_2 = _find_ex
          L10_2 = Color
          L10_2 = L10_2["师门"]
          L11_2 = "商会龙凤"
          L10_2 = L10_2[L11_2]
          L11_2 = {}
          L12_2 = 222
          L13_2 = 318
          L14_2 = 1316
          L15_2 = 895
          L11_2[1] = L12_2
          L11_2[2] = L13_2
          L11_2[3] = L14_2
          L11_2[4] = L15_2
          L12_2 = {}
          L13_2 = 10
          L14_2 = 50
          L12_2[1] = L13_2
          L12_2[2] = L14_2
          L9_2 = L9_2(L10_2, L11_2, L12_2)
          L8_2 = L9_2
          L9_2 = #L8_2
          if L9_2 ~= 0 then
            L9_2 = 1
            L10_2 = #L8_2
            L11_2 = 1
            for L12_2 = L9_2, L10_2, L11_2 do
              L13_2 = true
              while true do
                L14_2 = getColour
                L15_2 = colorList
                L16_2 = "商会店铺"
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  L14_2 = 1
                  L15_2 = 10
                  L16_2 = 1
                  for L17_2 = L14_2, L15_2, L16_2 do
                    L18_2 = false
                    L19_2 = -1
                    L20_2 = -1
                    L21_2 = false
                    L22_2 = false
                    L23_2 = 1
                    L24_2 = 10
                    L25_2 = 1
                    for L26_2 = L23_2, L24_2, L25_2 do
                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "家具" or L7_2 == "三药" then
                        if A0_2 == "豆斋果" or A0_2 == "醉生梦死" or A0_2 == "长寿面" or A0_2 == "佛跳墙" or A0_2 == "烤鸭" or A0_2 == "珍露酒" or A0_2 == "臭豆腐" or A0_2 == "烤肉" or A0_2 == "桂花丸" or A0_2 == "翡翠豆腐" or A0_2 == "梅花酒" or A0_2 == "百味酒" or A0_2 == "蛇胆酒" then
                          L27_2 = _ENV["烹饪价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["烹饪价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1937
                            end
                          end
                          _ENV["烹饪价格"] = 99999
                          ::lbl_1937::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["烹饪价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_1979
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_1979::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                          L27_2 = _ENV["三药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["三药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_2021
                            end
                          end
                          _ENV["三药价格"] = 99999
                          ::lbl_2021::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["三药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "黑麻垂曼帘" or A0_2 == "漆花竹凳" or A0_2 == "桦木圆桌" or A0_2 == "桦木立柜" or A0_2 == "草编地毯" or A0_2 == "榛木床" or A0_2 == "印花屏风" or A0_2 == "文竹" or A0_2 == "君子兰" or A0_2 == "蝴蝶兰" or A0_2 == "水仙" or A0_2 == "仙人掌" or A0_2 == "银烛台" or A0_2 == "玉瓷画瓶" or A0_2 == "踏春图" or A0_2 == "漆花地板" then
                          L27_2 = _ENV["一家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["一家价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_2073
                            end
                          end
                          _ENV["一家价格"] = 99999
                          ::lbl_2073::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["一家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "白鹤展翅帘" or A0_2 == "桦木地板" or A0_2 == "夕阳山水图" or A0_2 == "蓝绸绣花帘" or A0_2 == "红木八仙桌" or A0_2 == "镶玉虎纹桌" or A0_2 == "双鱼吉庆柜" or A0_2 == "彩绘立柜" or A0_2 == "兽皮地毯" or A0_2 == "麻布地毯" or A0_2 == "桦木靠背椅" or A0_2 == "神仙帐" or A0_2 == "月牙凳" or A0_2 == "八卦镇邪塌" or A0_2 == "狮子图屏风" or A0_2 == "花鸟图屏风" or A0_2 == "天山云雪" or A0_2 == "龟鹤延年灯" or A0_2 == "长信宫灯" or A0_2 == "雕花马桶" or A0_2 == "彩绘花瓶" then
                          L27_2 = _ENV["二家价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二家价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_2135
                            end
                          end
                          _ENV["二家价格"] = 99999
                          ::lbl_2135::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二家价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        elseif A0_2 == "天不老" or A0_2 == "紫石英" or A0_2 == "凤凰尾巴" or A0_2 == "硫磺草" or A0_2 == "龙之心屑" or A0_2 == "月星子" or A0_2 == "血珊瑚" or A0_2 == "火凤之眼" or A0_2 == "孔雀红" or A0_2 == "鹿茸" or A0_2 == "仙狐涎" or A0_2 == "天龙水" or A0_2 == "地狱灵芝" or A0_2 == "六道轮回" or A0_2 == "白露为霜" or A0_2 == "餐风饮露" then
                          L27_2 = _ENV["二药价格"]
                          if L27_2 ~= "" then
                            L27_2 = _ENV["二药价格"]
                            L28_2 = nil
                            if L27_2 ~= L28_2 then
                              goto lbl_2187
                            end
                          end
                          _ENV["二药价格"] = 99999
                          ::lbl_2187::
                          L27_2 = _ENV["价格筛选"]
                          L28_2 = tonumber
                          L29_2 = _ENV["二药价格"]
                          L28_2 = L28_2(L29_2)
                          L29_2 = A0_2
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                          if not L18_2 then
                            break
                          end
                        else
                          L27_2 = getMultiColor
                          L28_2 = propsList
                          L29_2 = A0_2
                          L30_2 = 85
                          L31_2 = 117
                          L32_2 = 224
                          L33_2 = 803
                          L34_2 = 752
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L18_2 = L27_2
                        end
                      else
                        L27_2 = _ENV["_卡区延迟"]
                        L27_2()
                        L27_2 = _ENV["_随机延时"]
                        L28_2 = 300
                        L27_2(L28_2)
                        L27_2 = getWordStock
                        L28_2 = PetList
                        L28_2 = L28_2["低于一万"]
                        L29_2 = A0_2
                        L30_2 = 176
                        L31_2 = 250
                        L32_2 = 735
                        L33_2 = 773
                        L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                        L20_2 = L29_2
                        L19_2 = L28_2
                        L21_2 = L27_2
                        if not L21_2 then
                          L27_2 = getWordStock
                          L28_2 = PetList
                          L28_2 = L28_2["超出一万"]
                          L29_2 = A0_2
                          L30_2 = 176
                          L31_2 = 250
                          L32_2 = 735
                          L33_2 = 773
                          L27_2, L28_2, L29_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                          L20_2 = L29_2
                          L19_2 = L28_2
                          L22_2 = L27_2
                        end
                        if L21_2 then
                          L21_2 = false
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["低于一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y1 = L33_2
                            x1 = L32_2
                            boolean1 = L31_2
                            L31_2 = boolean1
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x1
                              L33_2 = y1
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L22_2 then
                          L22_2 = false
                          L27_2 = 1
                          L28_2 = 8
                          L29_2 = 1
                          for L30_2 = L27_2, L28_2, L29_2 do
                            L31_2 = getWordStock
                            L32_2 = PetList
                            L32_2 = L32_2["超出一万"]
                            L33_2 = A0_2
                            L34_2 = _ENV["商会位置格子"]
                            L35_2 = L30_2
                            L36_2 = "号格子"
                            L35_2 = L35_2 .. L36_2
                            L34_2 = L34_2[L35_2]
                            L34_2 = L34_2[1]
                            L35_2 = _ENV["商会位置格子"]
                            L36_2 = L30_2
                            L37_2 = "号格子"
                            L36_2 = L36_2 .. L37_2
                            L35_2 = L35_2[L36_2]
                            L35_2 = L35_2[2]
                            L36_2 = _ENV["商会位置格子"]
                            L37_2 = L30_2
                            L38_2 = "号格子"
                            L37_2 = L37_2 .. L38_2
                            L36_2 = L36_2[L37_2]
                            L36_2 = L36_2[3]
                            L37_2 = _ENV["商会位置格子"]
                            L38_2 = L30_2
                            L39_2 = "号格子"
                            L38_2 = L38_2 .. L39_2
                            L37_2 = L37_2[L38_2]
                            L37_2 = L37_2[4]
                            L31_2, L32_2, L33_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                            y2 = L33_2
                            x2 = L32_2
                            boolean2 = L31_2
                            L31_2 = boolean2
                            if L31_2 then
                              L31_2 = tap
                              L32_2 = x2
                              L33_2 = y2
                              L33_2 = L33_2 + 20
                              L31_2(L32_2, L33_2)
                              L31_2 = _ENV["_随机延时"]
                              L32_2 = 1400
                              L31_2(L32_2)
                              L31_2 = _ENV["_卡区延迟"]
                              L31_2()
                              L31_2 = _ENV["宠物特性"]
                              L31_2 = L31_2[A1_2]
                              L31_2 = L31_2()
                              if L31_2 then
                                L18_2 = true
                                break
                              else
                                L32_2 = _ENV["_随机延时"]
                                L33_2 = 400
                                L32_2(L33_2)
                                L18_2 = false
                              end
                            end
                          end
                        end
                        if L18_2 and A0_2 == "黑熊" then
                          L27_2 = tsFindText
                          L28_2 = _ENV["黑熊精黑熊区分"]
                          L29_2 = "黑熊精"
                          L30_2 = 187
                          L31_2 = 253
                          L32_2 = 744
                          L33_2 = 754
                          L34_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                          L35_2 = 90
                          L27_2, L28_2 = L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                          y11 = L28_2
                          x11 = L27_2
                          L27_2 = x11
                          if 0 < L27_2 then
                            L18_2 = false
                            repeat
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会店铺关闭店铺"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = mSleep
                                L28_2 = 250
                                L27_2(L28_2)
                              end
                              L27_2 = mSleep
                              L28_2 = 250
                              L27_2(L28_2)
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["弹出召唤兽"]
                              L27_2 = L27_2(L28_2)
                              if L27_2 then
                                L27_2 = randomClick
                                L28_2 = 0
                                L29_2 = 300
                                L30_2 = 1665
                                L31_2 = 5
                                L32_2 = 1689
                                L33_2 = 36
                                L27_2(L28_2, L29_2, L30_2, L31_2, L32_2, L33_2)
                              end
                              L27_2 = _find
                              L28_2 = Color
                              L28_2 = L28_2["师门"]
                              L28_2 = L28_2["商会主界面"]
                              L27_2 = L27_2(L28_2)
                            until L27_2
                            break
                          end
                        end
                      end
                      if L18_2 then
                        L27_2 = true
                        L28_2 = 0
                        L29_2 = nil
                        while true do
                          L30_2 = getColour
                          L31_2 = colorList
                          L32_2 = "提示信息"
                          L30_2 = L30_2(L31_2, L32_2)
                          if L30_2 or L29_2 then
                            L30_2 = printLog
                            L31_2 = "完成购买:"
                            L32_2 = A0_2
                            L31_2 = L31_2 .. L32_2
                            L30_2(L31_2)
                            while true do
                              L30_2 = getColour
                              L31_2 = colorList
                              L32_2 = "商会店铺"
                              L30_2 = L30_2(L31_2, L32_2)
                              if L30_2 then
                                L30_2 = randomClick
                                L31_2 = 2
                                L32_2 = 100
                                L33_2 = 848
                                L34_2 = 49
                                L30_2(L31_2, L32_2, L33_2, L34_2)
                              else
                                L30_2 = getColour
                                L31_2 = colorList
                                L32_2 = "商会列表"
                                L30_2 = L30_2(L31_2, L32_2)
                                if L30_2 then
                                  L30_2 = randomClick
                                  L31_2 = 2
                                  L32_2 = 300
                                  L33_2 = 1705
                                  L34_2 = 66
                                  L30_2(L31_2, L32_2, L33_2, L34_2)
                                else
                                  L30_2 = getColour
                                  L31_2 = colorList
                                  L32_2 = "宠物属性页面"
                                  L30_2 = L30_2(L31_2, L32_2)
                                  if L30_2 then
                                    L30_2 = randomClick
                                    L31_2 = 2
                                    L32_2 = 300
                                    L33_2 = 1705
                                    L34_2 = 76
                                    L35_2 = 20
                                    L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                                  else
                                    L30_2 = getColour
                                    L31_2 = colorList
                                    L32_2 = "人物属性页面"
                                    L30_2 = L30_2(L31_2, L32_2)
                                    if L30_2 then
                                      L30_2 = randomClick
                                      L31_2 = 2
                                      L32_2 = 300
                                      L33_2 = 1603
                                      L34_2 = 86
                                      L30_2(L31_2, L32_2, L33_2, L34_2)
                                    else
                                      L30_2 = getColour
                                      L31_2 = colorList
                                      L32_2 = "动作按钮"
                                      L30_2 = L30_2(L31_2, L32_2)
                                      if not L30_2 then
                                        L30_2 = getColour
                                        L31_2 = colorList
                                        L32_2 = "设置按钮"
                                        L30_2 = L30_2(L31_2, L32_2)
                                        if not L30_2 then
                                          L30_2 = getColour
                                          L31_2 = colorList
                                          L32_2 = "道具按钮"
                                          L30_2 = L30_2(L31_2, L32_2)
                                          if not L30_2 then
                                            goto lbl_2532
                                          end
                                        end
                                      end
                                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                                        L30_2 = false
                                        return L30_2
                                      else
                                        L30_2 = true
                                        return L30_2
                                      end
                                    end
                                  end
                                end
                              end
                              ::lbl_2532::
                            end
                          elseif L27_2 then
                            L27_2 = false
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L28_2 = L30_2
                            if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                              L30_2 = randomClick
                              L31_2 = 2
                              L32_2 = 300
                              L33_2 = L19_2
                              L34_2 = L20_2 + 20
                              L35_2 = 20
                              L30_2(L31_2, L32_2, L33_2, L34_2, L35_2)
                            end
                            L30_2 = randomClick
                            L31_2 = 0
                            L32_2 = 300
                            L33_2 = 627
                            L34_2 = 944
                            L35_2 = 779
                            L36_2 = 991
                            L30_2(L31_2, L32_2, L33_2, L34_2, L35_2, L36_2)
                            L29_2 = true
                          else
                            L30_2 = "os"
                            L30_2 = _ENV[L30_2]
                            L31_2 = "time"
                            L30_2 = L30_2[L31_2]
                            L30_2 = L30_2()
                            L30_2 = L30_2 - L28_2
                            if 3 < L30_2 and L28_2 ~= 0 then
                              L27_2 = true
                            end
                          end
                        end
                      else
                        L27_2 = mSleep
                        L28_2 = 100
                        L27_2(L28_2)
                      end
                      L27_2 = false
                      if L21_2 == L27_2 then
                        break
                      end
                      L27_2 = false
                      if L22_2 == L27_2 then
                        break
                      end
                    end
                    L23_2 = randomClick
                    L24_2 = 2
                    L25_2 = 1000
                    L26_2 = 445
                    L27_2 = 962
                    L23_2(L24_2, L25_2, L26_2, L27_2)
                  end
                  while true do
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会列表"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      goto lbl_2641
                    end
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "商会店铺"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      L14_2 = randomClick
                      L15_2 = 2
                      L16_2 = 200
                      L17_2 = 848
                      L18_2 = 49
                      L14_2(L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                  break
                elseif L13_2 then
                  L13_2 = false
                  L14_2 = randomClick
                  L15_2 = 1
                  L16_2 = 300
                  L17_2 = L8_2[L12_2]
                  L18_2 = "x"
                  L17_2 = L17_2[L18_2]
                  L18_2 = L8_2[L12_2]
                  L19_2 = "y"
                  L18_2 = L18_2[L19_2]
                  L14_2(L15_2, L16_2, L17_2, L18_2)
                  L14_2 = randomClick
                  L15_2 = 0
                  L16_2 = 888
                  L17_2 = 1486
                  L18_2 = 943
                  L19_2 = 1683
                  L20_2 = 1000
                  L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                end
              end
              ::lbl_2641::
            end
          else
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 888
            L12_2 = 600
            L13_2 = 970
            L9_2(L10_2, L11_2, L12_2, L13_2)
          end
          L9_2 = randomClick
          L10_2 = 2
          L11_2 = 888
          L12_2 = 600
          L13_2 = 970
          L9_2(L10_2, L11_2, L12_2, L13_2)
        end
      end
      L9_2 = {}
      L10_2 = {}
      L11_2 = 394
      L12_2 = 341
      L13_2 = 498
      L14_2 = 425
      L10_2[1] = L11_2
      L10_2[2] = L12_2
      L10_2[3] = L13_2
      L10_2[4] = L14_2
      L11_2 = {}
      L12_2 = 968
      L13_2 = 342
      L14_2 = 1066
      L15_2 = 427
      L11_2[1] = L12_2
      L11_2[2] = L13_2
      L11_2[3] = L14_2
      L11_2[4] = L15_2
      L12_2 = {}
      L13_2 = 422
      L14_2 = 495
      L15_2 = 523
      L16_2 = 580
      L12_2[1] = L13_2
      L12_2[2] = L14_2
      L12_2[3] = L15_2
      L12_2[4] = L16_2
      L13_2 = {}
      L14_2 = 956
      L15_2 = 500
      L16_2 = 1038
      L17_2 = 580
      L13_2[1] = L14_2
      L13_2[2] = L15_2
      L13_2[3] = L16_2
      L13_2[4] = L17_2
      L14_2 = {}
      L15_2 = 398
      L16_2 = 641
      L17_2 = 504
      L18_2 = 733
      L14_2[1] = L15_2
      L14_2[2] = L16_2
      L14_2[3] = L17_2
      L14_2[4] = L18_2
      L15_2 = {}
      L16_2 = 964
      L17_2 = 633
      L18_2 = 1065
      L19_2 = 735
      L15_2[1] = L16_2
      L15_2[2] = L17_2
      L15_2[3] = L18_2
      L15_2[4] = L19_2
      L16_2 = {}
      L17_2 = 377
      L18_2 = 784
      L19_2 = 544
      L20_2 = 880
      L16_2[1] = L17_2
      L16_2[2] = L18_2
      L16_2[3] = L19_2
      L16_2[4] = L20_2
      L17_2 = {}
      L18_2 = 938
      L19_2 = 789
      L20_2 = 1052
      L21_2 = 879
      L17_2[1] = L18_2
      L17_2[2] = L19_2
      L17_2[3] = L20_2
      L17_2[4] = L21_2
      L9_2[1] = L10_2
      L9_2[2] = L11_2
      L9_2[3] = L12_2
      L9_2[4] = L13_2
      L9_2[5] = L14_2
      L9_2[6] = L15_2
      L9_2[7] = L16_2
      L9_2[8] = L17_2
      L10_2 = 1
      L11_2 = #L9_2
      L12_2 = 1
      for L13_2 = L10_2, L11_2, L12_2 do
        L14_2 = true
        while true do
          L15_2 = getColour
          L16_2 = colorList
          L17_2 = "商会店铺"
          L15_2 = L15_2(L16_2, L17_2)
          if L15_2 then
            L15_2 = 1
            L16_2 = 10
            L17_2 = 1
            for L18_2 = L15_2, L16_2, L17_2 do
              L19_2 = false
              L20_2 = -1
              L21_2 = -1
              L22_2 = false
              L23_2 = false
              L24_2 = 1
              L25_2 = 10
              L26_2 = 1
              for L27_2 = L24_2, L25_2, L26_2 do
                if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "家具" or L7_2 == "三药" then
                  if A0_2 == "豆斋果" or A0_2 == "醉生梦死" or A0_2 == "长寿面" or A0_2 == "佛跳墙" or A0_2 == "烤鸭" or A0_2 == "珍露酒" or A0_2 == "臭豆腐" or A0_2 == "烤肉" or A0_2 == "桂花丸" or A0_2 == "翡翠豆腐" or A0_2 == "梅花酒" or A0_2 == "百味酒" or A0_2 == "蛇胆酒" then
                    L28_2 = _ENV["烹饪价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["烹饪价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_2772
                      end
                    end
                    _ENV["烹饪价格"] = 99999
                    ::lbl_2772::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["烹饪价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                    L28_2 = _ENV["三药价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["三药价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_2814
                      end
                    end
                    _ENV["三药价格"] = 99999
                    ::lbl_2814::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["三药价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  elseif A0_2 == "金香玉" or A0_2 == "小还丹" or A0_2 == "千年保心丹" or A0_2 == "风水混元丹" or A0_2 == "佛光舍利子" or A0_2 == "定神香" or A0_2 == "蛇蝎美人" or A0_2 == "红雪散" or A0_2 == "九转回魂丹" or A0_2 == "五龙丹" or A0_2 == "十香返生丸" then
                    L28_2 = _ENV["三药价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["三药价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_2856
                      end
                    end
                    _ENV["三药价格"] = 99999
                    ::lbl_2856::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["三药价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  elseif A0_2 == "黑麻垂曼帘" or A0_2 == "漆花竹凳" or A0_2 == "桦木圆桌" or A0_2 == "桦木立柜" or A0_2 == "草编地毯" or A0_2 == "榛木床" or A0_2 == "印花屏风" or A0_2 == "文竹" or A0_2 == "君子兰" or A0_2 == "蝴蝶兰" or A0_2 == "水仙" or A0_2 == "仙人掌" or A0_2 == "银烛台" or A0_2 == "玉瓷画瓶" or A0_2 == "踏春图" or A0_2 == "漆花地板" then
                    L28_2 = _ENV["一家价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["一家价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_2908
                      end
                    end
                    _ENV["一家价格"] = 99999
                    ::lbl_2908::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["一家价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  elseif A0_2 == "白鹤展翅帘" or A0_2 == "桦木地板" or A0_2 == "夕阳山水图" or A0_2 == "蓝绸绣花帘" or A0_2 == "红木八仙桌" or A0_2 == "镶玉虎纹桌" or A0_2 == "双鱼吉庆柜" or A0_2 == "彩绘立柜" or A0_2 == "兽皮地毯" or A0_2 == "麻布地毯" or A0_2 == "桦木靠背椅" or A0_2 == "神仙帐" or A0_2 == "月牙凳" or A0_2 == "八卦镇邪塌" or A0_2 == "狮子图屏风" or A0_2 == "花鸟图屏风" or A0_2 == "天山云雪" or A0_2 == "龟鹤延年灯" or A0_2 == "长信宫灯" or A0_2 == "雕花马桶" or A0_2 == "彩绘花瓶" then
                    L28_2 = _ENV["二家价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["二家价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_2970
                      end
                    end
                    _ENV["二家价格"] = 99999
                    ::lbl_2970::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["二家价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  elseif A0_2 == "天不老" or A0_2 == "紫石英" or A0_2 == "凤凰尾巴" or A0_2 == "硫磺草" or A0_2 == "龙之心屑" or A0_2 == "月星子" or A0_2 == "血珊瑚" or A0_2 == "火凤之眼" or A0_2 == "孔雀红" or A0_2 == "鹿茸" or A0_2 == "仙狐涎" or A0_2 == "天龙水" or A0_2 == "地狱灵芝" or A0_2 == "六道轮回" or A0_2 == "白露为霜" or A0_2 == "餐风饮露" then
                    L28_2 = _ENV["二药价格"]
                    if L28_2 ~= "" then
                      L28_2 = _ENV["二药价格"]
                      L29_2 = nil
                      if L28_2 ~= L29_2 then
                        goto lbl_3022
                      end
                    end
                    _ENV["二药价格"] = 99999
                    ::lbl_3022::
                    L28_2 = _ENV["价格筛选"]
                    L29_2 = tonumber
                    L30_2 = _ENV["二药价格"]
                    L29_2 = L29_2(L30_2)
                    L30_2 = A0_2
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                    if not L19_2 then
                      break
                    end
                  else
                    L28_2 = getMultiColor
                    L29_2 = propsList
                    L30_2 = A0_2
                    L31_2 = 85
                    L32_2 = 117
                    L33_2 = 224
                    L34_2 = 803
                    L35_2 = 752
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L19_2 = L28_2
                  end
                else
                  L28_2 = _ENV["_卡区延迟"]
                  L28_2()
                  L28_2 = _ENV["_随机延时"]
                  L29_2 = 300
                  L28_2(L29_2)
                  L28_2 = getWordStock
                  L29_2 = PetList
                  L29_2 = L29_2["低于一万"]
                  L30_2 = A0_2
                  L31_2 = 176
                  L32_2 = 250
                  L33_2 = 735
                  L34_2 = 773
                  L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                  L21_2 = L30_2
                  L20_2 = L29_2
                  L22_2 = L28_2
                  if not L22_2 then
                    L28_2 = getWordStock
                    L29_2 = PetList
                    L29_2 = L29_2["超出一万"]
                    L30_2 = A0_2
                    L31_2 = 176
                    L32_2 = 250
                    L33_2 = 735
                    L34_2 = 773
                    L28_2, L29_2, L30_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                    L21_2 = L30_2
                    L20_2 = L29_2
                    L23_2 = L28_2
                  end
                  if L22_2 then
                    L22_2 = false
                    L23_2 = false
                    L28_2 = 1
                    L29_2 = 8
                    L30_2 = 1
                    for L31_2 = L28_2, L29_2, L30_2 do
                      L32_2 = getWordStock
                      L33_2 = PetList
                      L33_2 = L33_2["低于一万"]
                      L34_2 = A0_2
                      L35_2 = _ENV["商会位置格子"]
                      L36_2 = L31_2
                      L37_2 = "号格子"
                      L36_2 = L36_2 .. L37_2
                      L35_2 = L35_2[L36_2]
                      L35_2 = L35_2[1]
                      L36_2 = _ENV["商会位置格子"]
                      L37_2 = L31_2
                      L38_2 = "号格子"
                      L37_2 = L37_2 .. L38_2
                      L36_2 = L36_2[L37_2]
                      L36_2 = L36_2[2]
                      L37_2 = _ENV["商会位置格子"]
                      L38_2 = L31_2
                      L39_2 = "号格子"
                      L38_2 = L38_2 .. L39_2
                      L37_2 = L37_2[L38_2]
                      L37_2 = L37_2[3]
                      L38_2 = _ENV["商会位置格子"]
                      L39_2 = L31_2
                      L40_2 = "号格子"
                      L39_2 = L39_2 .. L40_2
                      L38_2 = L38_2[L39_2]
                      L38_2 = L38_2[4]
                      L32_2, L33_2, L34_2 = L32_2(L33_2, L34_2, L35_2, L36_2, L37_2, L38_2)
                      y1 = L34_2
                      x1 = L33_2
                      boolean1 = L32_2
                      L32_2 = boolean1
                      if L32_2 then
                        L32_2 = tap
                        L33_2 = x1
                        L34_2 = y1
                        L34_2 = L34_2 + 20
                        L32_2(L33_2, L34_2)
                        L32_2 = _ENV["_随机延时"]
                        L33_2 = 1400
                        L32_2(L33_2)
                        L32_2 = _ENV["_卡区延迟"]
                        L32_2()
                        L32_2 = _ENV["宠物特性"]
                        L32_2 = L32_2[A1_2]
                        L32_2 = L32_2()
                        if L32_2 then
                          L19_2 = true
                          break
                        else
                          L33_2 = _ENV["_随机延时"]
                          L34_2 = 400
                          L33_2(L34_2)
                          L19_2 = false
                        end
                      end
                    end
                  end
                  if L23_2 then
                    L23_2 = false
                    L28_2 = 1
                    L29_2 = 8
                    L30_2 = 1
                    for L31_2 = L28_2, L29_2, L30_2 do
                      L32_2 = getWordStock
                      L33_2 = PetList
                      L33_2 = L33_2["超出一万"]
                      L34_2 = A0_2
                      L35_2 = _ENV["商会位置格子"]
                      L36_2 = L31_2
                      L37_2 = "号格子"
                      L36_2 = L36_2 .. L37_2
                      L35_2 = L35_2[L36_2]
                      L35_2 = L35_2[1]
                      L36_2 = _ENV["商会位置格子"]
                      L37_2 = L31_2
                      L38_2 = "号格子"
                      L37_2 = L37_2 .. L38_2
                      L36_2 = L36_2[L37_2]
                      L36_2 = L36_2[2]
                      L37_2 = _ENV["商会位置格子"]
                      L38_2 = L31_2
                      L39_2 = "号格子"
                      L38_2 = L38_2 .. L39_2
                      L37_2 = L37_2[L38_2]
                      L37_2 = L37_2[3]
                      L38_2 = _ENV["商会位置格子"]
                      L39_2 = L31_2
                      L40_2 = "号格子"
                      L39_2 = L39_2 .. L40_2
                      L38_2 = L38_2[L39_2]
                      L38_2 = L38_2[4]
                      L32_2, L33_2, L34_2 = L32_2(L33_2, L34_2, L35_2, L36_2, L37_2, L38_2)
                      y2 = L34_2
                      x2 = L33_2
                      boolean2 = L32_2
                      L32_2 = boolean2
                      if L32_2 then
                        L32_2 = tap
                        L33_2 = x2
                        L34_2 = y2
                        L34_2 = L34_2 + 20
                        L32_2(L33_2, L34_2)
                        L32_2 = _ENV["_随机延时"]
                        L33_2 = 1400
                        L32_2(L33_2)
                        L32_2 = _ENV["_卡区延迟"]
                        L32_2()
                        L32_2 = _ENV["宠物特性"]
                        L32_2 = L32_2[A1_2]
                        L32_2 = L32_2()
                        if L32_2 then
                          L19_2 = true
                          break
                        else
                          L33_2 = _ENV["_随机延时"]
                          L34_2 = 400
                          L33_2(L34_2)
                          L19_2 = false
                        end
                      end
                    end
                  end
                  if L19_2 and A0_2 == "黑熊" then
                    L28_2 = tsFindText
                    L29_2 = _ENV["黑熊精黑熊区分"]
                    L30_2 = "黑熊精"
                    L31_2 = 187
                    L32_2 = 253
                    L33_2 = 744
                    L34_2 = 754
                    L35_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                    L36_2 = 90
                    L28_2, L29_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2)
                    y11 = L29_2
                    x11 = L28_2
                    L28_2 = x11
                    if 0 < L28_2 then
                      L19_2 = false
                      repeat
                        L28_2 = _find
                        L29_2 = Color
                        L29_2 = L29_2["师门"]
                        L29_2 = L29_2["商会店铺关闭店铺"]
                        L28_2 = L28_2(L29_2)
                        if L28_2 then
                          L28_2 = mSleep
                          L29_2 = 250
                          L28_2(L29_2)
                        end
                        L28_2 = mSleep
                        L29_2 = 250
                        L28_2(L29_2)
                        L28_2 = _find
                        L29_2 = Color
                        L29_2 = L29_2["师门"]
                        L29_2 = L29_2["弹出召唤兽"]
                        L28_2 = L28_2(L29_2)
                        if L28_2 then
                          L28_2 = randomClick
                          L29_2 = 0
                          L30_2 = 300
                          L31_2 = 1665
                          L32_2 = 5
                          L33_2 = 1689
                          L34_2 = 36
                          L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                        end
                        L28_2 = _find
                        L29_2 = Color
                        L29_2 = L29_2["师门"]
                        L29_2 = L29_2["商会主界面"]
                        L28_2 = L28_2(L29_2)
                      until L28_2
                      break
                    end
                  end
                end
                if L19_2 then
                  L28_2 = true
                  L29_2 = 0
                  L30_2 = nil
                  while true do
                    L31_2 = getColour
                    L32_2 = colorList
                    L33_2 = "提示信息"
                    L31_2 = L31_2(L32_2, L33_2)
                    if L31_2 or L30_2 then
                      L31_2 = printLog
                      L32_2 = "完成购买:"
                      L33_2 = A0_2
                      L32_2 = L32_2 .. L33_2
                      L31_2(L32_2)
                      while true do
                        L31_2 = getColour
                        L32_2 = colorList
                        L33_2 = "商会店铺"
                        L31_2 = L31_2(L32_2, L33_2)
                        if L31_2 then
                          L31_2 = randomClick
                          L32_2 = 2
                          L33_2 = 100
                          L34_2 = 848
                          L35_2 = 49
                          L31_2(L32_2, L33_2, L34_2, L35_2)
                        else
                          L31_2 = getColour
                          L32_2 = colorList
                          L33_2 = "商会列表"
                          L31_2 = L31_2(L32_2, L33_2)
                          if L31_2 then
                            L31_2 = randomClick
                            L32_2 = 2
                            L33_2 = 300
                            L34_2 = 1705
                            L35_2 = 66
                            L31_2(L32_2, L33_2, L34_2, L35_2)
                          else
                            L31_2 = getColour
                            L32_2 = colorList
                            L33_2 = "宠物属性页面"
                            L31_2 = L31_2(L32_2, L33_2)
                            if L31_2 then
                              L31_2 = randomClick
                              L32_2 = 2
                              L33_2 = 300
                              L34_2 = 1705
                              L35_2 = 76
                              L36_2 = 20
                              L31_2(L32_2, L33_2, L34_2, L35_2, L36_2)
                            else
                              L31_2 = getColour
                              L32_2 = colorList
                              L33_2 = "人物属性页面"
                              L31_2 = L31_2(L32_2, L33_2)
                              if L31_2 then
                                L31_2 = randomClick
                                L32_2 = 2
                                L33_2 = 300
                                L34_2 = 1603
                                L35_2 = 86
                                L31_2(L32_2, L33_2, L34_2, L35_2)
                              else
                                L31_2 = getColour
                                L32_2 = colorList
                                L33_2 = "动作按钮"
                                L31_2 = L31_2(L32_2, L33_2)
                                if not L31_2 then
                                  L31_2 = getColour
                                  L32_2 = colorList
                                  L33_2 = "设置按钮"
                                  L31_2 = L31_2(L32_2, L33_2)
                                  if not L31_2 then
                                    L31_2 = getColour
                                    L32_2 = colorList
                                    L33_2 = "道具按钮"
                                    L31_2 = L31_2(L32_2, L33_2)
                                    if not L31_2 then
                                      goto lbl_3367
                                    end
                                  end
                                end
                                if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                                  L31_2 = false
                                  return L31_2
                                else
                                  L31_2 = true
                                  return L31_2
                                end
                              end
                            end
                          end
                        end
                        ::lbl_3367::
                      end
                    elseif L28_2 then
                      L28_2 = false
                      L31_2 = "os"
                      L31_2 = _ENV[L31_2]
                      L32_2 = "time"
                      L31_2 = L31_2[L32_2]
                      L31_2 = L31_2()
                      L29_2 = L31_2
                      if L7_2 == "二药" or L7_2 == "烹饪" or L7_2 == "三药" or L7_2 == "家具" then
                        L31_2 = randomClick
                        L32_2 = 2
                        L33_2 = 300
                        L34_2 = L20_2
                        L35_2 = L21_2 + 20
                        L36_2 = 20
                        L31_2(L32_2, L33_2, L34_2, L35_2, L36_2)
                      end
                      L31_2 = randomClick
                      L32_2 = 0
                      L33_2 = 300
                      L34_2 = 627
                      L35_2 = 944
                      L36_2 = 779
                      L37_2 = 991
                      L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                      L30_2 = true
                    else
                      L31_2 = "os"
                      L31_2 = _ENV[L31_2]
                      L32_2 = "time"
                      L31_2 = L31_2[L32_2]
                      L31_2 = L31_2()
                      L31_2 = L31_2 - L29_2
                      if 3 < L31_2 and L29_2 ~= 0 then
                        L28_2 = true
                      end
                    end
                  end
                else
                  L28_2 = mSleep
                  L29_2 = 10
                  L28_2(L29_2)
                end
                L28_2 = false
                if L22_2 == L28_2 then
                  break
                end
                L28_2 = false
                if L23_2 == L28_2 then
                  break
                end
              end
              L24_2 = _find
              L25_2 = Color
              L25_2 = L25_2["师门"]
              L26_2 = "商会最后一页"
              L25_2 = L25_2[L26_2]
              L24_2 = L24_2(L25_2)
              if L24_2 then
                break
              end
              L24_2 = "价格不合适"
              L24_2 = _ENV[L24_2]
              if L24_2 then
                break
              end
              L24_2 = randomClick
              L25_2 = 2
              L26_2 = 1000
              L27_2 = 445
              L28_2 = 962
              L24_2(L25_2, L26_2, L27_2, L28_2)
            end
            while true do
              L15_2 = getColour
              L16_2 = colorList
              L17_2 = "商会列表"
              L15_2 = L15_2(L16_2, L17_2)
              if L15_2 then
                goto lbl_3490
              end
              L15_2 = getColour
              L16_2 = colorList
              L17_2 = "商会店铺"
              L15_2 = L15_2(L16_2, L17_2)
              if L15_2 then
                L15_2 = randomClick
                L16_2 = 2
                L17_2 = 200
                L18_2 = 848
                L19_2 = 49
                L15_2(L16_2, L17_2, L18_2, L19_2)
              end
            end
            break
          elseif L14_2 then
            L14_2 = false
            L15_2 = randomClick
            L16_2 = 0
            L17_2 = 300
            L18_2 = L9_2[L13_2]
            L18_2 = L18_2[1]
            L19_2 = L9_2[L13_2]
            L19_2 = L19_2[2]
            L20_2 = L9_2[L13_2]
            L20_2 = L20_2[3]
            L21_2 = L9_2[L13_2]
            L21_2 = L21_2[4]
            L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
            L15_2 = randomClick
            L16_2 = 0
            L17_2 = 888
            L18_2 = 1486
            L19_2 = 943
            L20_2 = 1683
            L21_2 = 1000
            L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          end
        end
        ::lbl_3490::
      end
      L10_2 = randomClick
      L11_2 = 2
      L12_2 = 888
      L13_2 = 600
      L14_2 = 970
      L10_2(L11_2, L12_2, L13_2, L14_2)
    end
  end
end

_ENV["百级龟速识别"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2
  L4_2 = false
  L5_2 = A1_2
  L6_2 = pairs
  L7_2 = BuyToType
  L6_2, L7_2, L8_2 = L6_2(L7_2)
  for L9_2, L10_2 in L6_2, L7_2, L8_2 do
    L11_2 = 1
    L12_2 = #L10_2
    L13_2 = 1
    for L14_2 = L11_2, L12_2, L13_2 do
      L15_2 = L10_2[L14_2]
      if A1_2 == L15_2 then
        L5_2 = L9_2
      end
    end
  end
  L6_2 = strSplit
  L7_2 = A0_2
  L8_2 = "+"
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = 1
  L8_2 = #L6_2
  L9_2 = 1
  for L10_2 = L7_2, L8_2, L9_2 do
    L11_2 = 0
    L12_2 = strSplit
    L13_2 = L6_2[L10_2]
    L14_2 = "#"
    L12_2 = L12_2(L13_2, L14_2)
    L13_2 = 1
    L14_2 = #L5_2
    L14_2 = L14_2 + 1
    L15_2 = 1
    L16_2 = L12_2[2]
    L16_2 = #L16_2
    L17_2 = 1
    for L18_2 = L15_2, L16_2, L17_2 do
      L19_2 = true
      L20_2 = string
      L20_2 = L20_2.sub
      L21_2 = L12_2[2]
      L22_2 = L13_2
      L23_2 = L14_2
      L20_2 = L20_2(L21_2, L22_2, L23_2)
      L13_2 = L13_2 + 1
      L14_2 = L14_2 + 1
      if A1_2 == "黑熊" then
        L21_2 = string
        L21_2 = L21_2.find
        L22_2 = L20_2
        L23_2 = L5_2
        L21_2 = L21_2(L22_2, L23_2)
        if L21_2 then
          L21_2 = string
          L21_2 = L21_2.sub
          L22_2 = L12_2[2]
          L23_2 = L13_2 - 1
          L24_2 = L14_2 + 2
          L21_2 = L21_2(L22_2, L23_2, L24_2)
          L22_2 = string
          L22_2 = L22_2.find
          L23_2 = L21_2
          L24_2 = "黑熊精"
          L22_2 = L22_2(L23_2, L24_2)
          if L22_2 then
            L19_2 = false
          end
        end
      end
      if L18_2 >= L11_2 then
        L21_2 = string
        L21_2 = L21_2.find
        L22_2 = L20_2
        L23_2 = L5_2
        L21_2 = L21_2(L22_2, L23_2)
        if L21_2 and L11_2 ~= -1 and L19_2 then
          L21_2 = printLog
          L22_2 = "获取商品信息:"
          L23_2 = L20_2
          L22_2 = L22_2 .. L23_2
          L21_2(L22_2)
          L21_2 = true
          L22_2 = false
          L23_2 = 0
          L24_2 = tonumber
          L25_2 = string
          L25_2 = L25_2.sub
          L26_2 = L12_2[2]
          L27_2 = L13_2 - 1
          L28_2 = L13_2 - 1
          L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2 = L25_2(L26_2, L27_2, L28_2)
          L24_2 = L24_2(L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2)
          if L24_2 == nil then
            break
          end
          while true do
            L25_2 = _ENV["弹窗查看详情"]
            L25_2()
            if L22_2 then
              break
            end
            if L4_2 then
              L25_2 = printLog
              L26_2 = "退出商会"
              L25_2(L26_2)
              L25_2 = getColour
              L26_2 = colorList
              L27_2 = "商会店铺"
              L25_2 = L25_2(L26_2, L27_2)
              if L25_2 then
                L25_2 = randomClick
                L26_2 = 2
                L27_2 = 100
                L28_2 = 848
                L29_2 = 49
                L25_2(L26_2, L27_2, L28_2, L29_2)
              else
                L25_2 = getColour
                L26_2 = colorList
                L27_2 = "商会列表"
                L25_2 = L25_2(L26_2, L27_2)
                if L25_2 then
                  L25_2 = randomClick
                  L26_2 = 2
                  L27_2 = 300
                  L28_2 = 1705
                  L29_2 = 66
                  L25_2(L26_2, L27_2, L28_2, L29_2)
                else
                  L25_2 = getColour
                  L26_2 = colorList
                  L27_2 = "宠物属性页面"
                  L25_2 = L25_2(L26_2, L27_2)
                  if L25_2 then
                    L25_2 = randomClick
                    L26_2 = 2
                    L27_2 = 300
                    L28_2 = 1705
                    L29_2 = 76
                    L30_2 = 20
                    L25_2(L26_2, L27_2, L28_2, L29_2, L30_2)
                  else
                    L25_2 = getColour
                    L26_2 = colorList
                    L27_2 = "召唤兽购买"
                    L25_2 = L25_2(L26_2, L27_2)
                    if L25_2 then
                      L25_2 = randomClick
                      L26_2 = 2
                      L27_2 = 1300
                      L28_2 = 1670
                      L29_2 = 46
                      L25_2(L26_2, L27_2, L28_2, L29_2)
                    else
                      L25_2 = _find
                      L26_2 = Color
                      L26_2 = L26_2["师门"]
                      L26_2 = L26_2["弹出成就对话框"]
                      L25_2 = L25_2(L26_2)
                      if L25_2 then
                        L25_2 = _ENV["通用功能"]
                        L25_2 = L25_2["关闭"]
                        L25_2()
                      else
                        L25_2 = getColour
                        L26_2 = colorList
                        L27_2 = "人物属性页面"
                        L25_2 = L25_2(L26_2, L27_2)
                        if L25_2 then
                          L25_2 = randomClick
                          L26_2 = 2
                          L27_2 = 300
                          L28_2 = 1603
                          L29_2 = 86
                          L25_2(L26_2, L27_2, L28_2, L29_2)
                        else
                          L25_2 = getColour
                          L26_2 = colorList
                          L27_2 = "动作按钮"
                          L25_2 = L25_2(L26_2, L27_2)
                          if not L25_2 then
                            L25_2 = getColour
                            L26_2 = colorList
                            L27_2 = "设置按钮"
                            L25_2 = L25_2(L26_2, L27_2)
                            if not L25_2 then
                              L25_2 = getColour
                              L26_2 = colorList
                              L27_2 = "道具按钮"
                              L25_2 = L25_2(L26_2, L27_2)
                            end
                          end
                          if L25_2 then
                            L25_2 = printLog
                            L26_2 = "返回师门"
                            L25_2(L26_2)
                            if A2_2 == "物品" then
                              L25_2 = false
                              return L25_2
                            else
                              L25_2 = true
                              return L25_2
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            else
              if L21_2 then
                L25_2 = getColour
                L26_2 = colorList
                L27_2 = "号码键盘"
                L25_2 = L25_2(L26_2, L27_2)
                if L25_2 then
                  L25_2 = {}
                  L26_2 = {}
                  L27_2 = 562
                  L28_2 = 337
                  L26_2[1] = L27_2
                  L26_2[2] = L28_2
                  L27_2 = {}
                  L28_2 = 718
                  L29_2 = 337
                  L27_2[1] = L28_2
                  L27_2[2] = L29_2
                  L28_2 = {}
                  L29_2 = 866
                  L30_2 = 335
                  L28_2[1] = L29_2
                  L28_2[2] = L30_2
                  L29_2 = {}
                  L30_2 = 563
                  L31_2 = 485
                  L29_2[1] = L30_2
                  L29_2[2] = L31_2
                  L30_2 = {}
                  L31_2 = 714
                  L32_2 = 487
                  L30_2[1] = L31_2
                  L30_2[2] = L32_2
                  L31_2 = {}
                  L32_2 = 866
                  L33_2 = 486
                  L31_2[1] = L32_2
                  L31_2[2] = L33_2
                  L32_2 = {}
                  L33_2 = 564
                  L34_2 = 632
                  L32_2[1] = L33_2
                  L32_2[2] = L34_2
                  L33_2 = {}
                  L34_2 = 716
                  L35_2 = 632
                  L33_2[1] = L34_2
                  L33_2[2] = L35_2
                  L34_2 = {}
                  L35_2 = 868
                  L36_2 = 634
                  L34_2[1] = L35_2
                  L34_2[2] = L36_2
                  L35_2 = {}
                  L36_2 = 1020
                  L37_2 = 485
                  L35_2[1] = L36_2
                  L35_2[2] = L37_2
                  L25_2[1] = L26_2
                  L25_2[2] = L27_2
                  L25_2[3] = L28_2
                  L25_2[4] = L29_2
                  L25_2[5] = L30_2
                  L25_2[6] = L31_2
                  L25_2[7] = L32_2
                  L25_2[8] = L33_2
                  L25_2[9] = L34_2
                  L25_2[10] = L35_2
                  L26_2 = L12_2[1]
                  L27_2 = 1
                  L28_2 = #L26_2
                  L29_2 = 1
                  for L30_2 = L27_2, L28_2, L29_2 do
                    L31_2 = tonumber
                    L32_2 = string
                    L32_2 = L32_2.sub
                    L33_2 = L26_2
                    L34_2 = L30_2
                    L35_2 = L30_2
                    L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2 = L32_2(L33_2, L34_2, L35_2)
                    L31_2 = L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2)
                    if L31_2 == 0 then
                      L31_2 = 10
                    end
                    L32_2 = 1
                    L33_2 = 10
                    L34_2 = 1
                    for L35_2 = L32_2, L33_2, L34_2 do
                      if L31_2 == L35_2 then
                        L36_2 = randomClick
                        L37_2 = 2
                        L38_2 = 200
                        L39_2 = L25_2[L35_2]
                        L39_2 = L39_2[1]
                        L40_2 = L25_2[L35_2]
                        L40_2 = L40_2[2]
                        L41_2 = 20
                        L36_2(L37_2, L38_2, L39_2, L40_2, L41_2)
                        break
                      end
                    end
                  end
                  L21_2 = false
              end
              else
                if L23_2 ~= 0 then
                  L25_2 = os
                  L25_2 = L25_2.time
                  L25_2 = L25_2()
                  L25_2 = L25_2 - L23_2
                  if 3 < L25_2 then
                    L25_2 = getColour
                    L26_2 = colorList
                    L27_2 = "号码键盘"
                    L25_2 = L25_2(L26_2, L27_2)
                    if not L25_2 then
                      L25_2 = randomClick
                      L26_2 = 0
                      L27_2 = 300
                      L28_2 = 275
                      L29_2 = 153
                      L30_2 = 377
                      L31_2 = 196
                      L25_2(L26_2, L27_2, L28_2, L29_2, L30_2, L31_2)
                      break
                  end
                end
                else
                  L25_2 = getColour
                  L26_2 = colorList
                  L27_2 = "商会店铺"
                  L25_2 = L25_2(L26_2, L27_2)
                  if L25_2 then
                    L25_2 = true
                    L26_2 = 0
                    L27_2 = true
                    L28_2 = {}
                    L29_2 = {}
                    L30_2 = 172
                    L31_2 = 160
                    L32_2 = 230
                    L33_2 = 217
                    L29_2[1] = L30_2
                    L29_2[2] = L31_2
                    L29_2[3] = L32_2
                    L29_2[4] = L33_2
                    L30_2 = {}
                    L31_2 = 320
                    L32_2 = 159
                    L33_2 = 381
                    L34_2 = 219
                    L30_2[1] = L31_2
                    L30_2[2] = L32_2
                    L30_2[3] = L33_2
                    L30_2[4] = L34_2
                    L31_2 = {}
                    L32_2 = 170
                    L33_2 = 307
                    L34_2 = 238
                    L35_2 = 374
                    L31_2[1] = L32_2
                    L31_2[2] = L33_2
                    L31_2[3] = L34_2
                    L31_2[4] = L35_2
                    L32_2 = {}
                    L33_2 = 316
                    L34_2 = 311
                    L35_2 = 388
                    L36_2 = 375
                    L32_2[1] = L33_2
                    L32_2[2] = L34_2
                    L32_2[3] = L35_2
                    L32_2[4] = L36_2
                    L33_2 = {}
                    L34_2 = 171
                    L35_2 = 465
                    L36_2 = 235
                    L37_2 = 529
                    L33_2[1] = L34_2
                    L33_2[2] = L35_2
                    L33_2[3] = L36_2
                    L33_2[4] = L37_2
                    L34_2 = {}
                    L35_2 = 320
                    L36_2 = 468
                    L37_2 = 390
                    L38_2 = 530
                    L34_2[1] = L35_2
                    L34_2[2] = L36_2
                    L34_2[3] = L37_2
                    L34_2[4] = L38_2
                    L35_2 = {}
                    L36_2 = 172
                    L37_2 = 620
                    L38_2 = 237
                    L39_2 = 680
                    L35_2[1] = L36_2
                    L35_2[2] = L37_2
                    L35_2[3] = L38_2
                    L35_2[4] = L39_2
                    L36_2 = {}
                    L37_2 = 316
                    L38_2 = 621
                    L39_2 = 388
                    L40_2 = 685
                    L36_2[1] = L37_2
                    L36_2[2] = L38_2
                    L36_2[3] = L39_2
                    L36_2[4] = L40_2
                    L37_2 = {}
                    L38_2 = 165
                    L39_2 = 769
                    L40_2 = 234
                    L41_2 = 835
                    L37_2[1] = L38_2
                    L37_2[2] = L39_2
                    L37_2[3] = L40_2
                    L37_2[4] = L41_2
                    L38_2 = {}
                    L39_2 = 323
                    L40_2 = 776
                    L41_2 = 383
                    L42_2 = 835
                    L38_2[1] = L39_2
                    L38_2[2] = L40_2
                    L38_2[3] = L41_2
                    L38_2[4] = L42_2
                    L28_2[1] = L29_2
                    L28_2[2] = L30_2
                    L28_2[3] = L31_2
                    L28_2[4] = L32_2
                    L28_2[5] = L33_2
                    L28_2[6] = L34_2
                    L28_2[7] = L35_2
                    L28_2[8] = L36_2
                    L28_2[9] = L37_2
                    L28_2[10] = L38_2
                    while true do
                      if L24_2 == 1 and L25_2 then
                        L29_2 = printLog
                        L30_2 = "第1间铺面查找:"
                        L31_2 = A1_2
                        L30_2 = L30_2 .. L31_2
                        L29_2(L30_2)
                        L25_2 = false
                      else
                        if L25_2 and L27_2 then
                          L29_2 = getColour
                          L30_2 = colorList
                          L31_2 = "商会号码"
                          L29_2 = L29_2(L30_2, L31_2)
                          if L29_2 then
                            L25_2 = false
                            if L24_2 == 0 then
                              L24_2 = 10
                              L29_2 = printLog
                              L30_2 = "第10间铺面查找:"
                              L31_2 = A1_2
                              L30_2 = L30_2 .. L31_2
                              L29_2(L30_2)
                            else
                              L29_2 = printLog
                              L30_2 = "第"
                              L31_2 = L24_2
                              L32_2 = "间铺面查找:"
                              L33_2 = A1_2
                              L30_2 = L30_2 .. L31_2 .. L32_2 .. L33_2
                              L29_2(L30_2)
                            end
                            L29_2 = _ENV["_卡区延迟"]
                            L29_2()
                            L29_2 = getColors
                            L30_2 = colorList
                            L31_2 = "灰色号码"
                            L32_2 = L28_2[L24_2]
                            L32_2 = L32_2[1]
                            L33_2 = L28_2[L24_2]
                            L33_2 = L33_2[2]
                            L34_2 = L28_2[L24_2]
                            L34_2 = L34_2[3]
                            L35_2 = L28_2[L24_2]
                            L35_2 = L35_2[4]
                            L29_2 = L29_2(L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                            if L29_2 then
                              L29_2 = printLog
                              L30_2 = "店铺未开:"
                              L31_2 = L26_2
                              L30_2 = L30_2 .. L31_2
                              L29_2(L30_2)
                              while true do
                                L29_2 = getColour
                                L30_2 = colorList
                                L31_2 = "商会列表"
                                L29_2 = L29_2(L30_2, L31_2)
                                if L29_2 then
                                  L29_2 = randomClick
                                  L30_2 = 0
                                  L31_2 = 300
                                  L32_2 = 275
                                  L33_2 = 153
                                  L34_2 = 377
                                  L35_2 = 196
                                  L29_2(L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                                  break
                                else
                                  L29_2 = getColour
                                  L30_2 = colorList
                                  L31_2 = "商会店铺"
                                  L29_2 = L29_2(L30_2, L31_2)
                                  if L29_2 then
                                    L29_2 = randomClick
                                    L30_2 = 2
                                    L31_2 = 200
                                    L32_2 = 848
                                    L33_2 = 49
                                    L29_2(L30_2, L31_2, L32_2, L33_2)
                                  end
                                end
                              end
                              L22_2 = true
                              L11_2 = -1
                              goto lbl_1299
                            else
                              L27_2 = false
                              L29_2 = randomClick
                              L30_2 = 0
                              L31_2 = 120
                              L32_2 = L28_2[L24_2]
                              L32_2 = L32_2[1]
                              L33_2 = L28_2[L24_2]
                              L33_2 = L33_2[2]
                              L34_2 = L28_2[L24_2]
                              L34_2 = L34_2[3]
                              L35_2 = L28_2[L24_2]
                              L35_2 = L35_2[4]
                              L29_2(L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                              L29_2 = _find_cx
                              L30_2 = Color
                              L30_2 = L30_2["师门"]
                              L30_2 = L30_2["商会店铺可购买"]
                              L31_2 = {}
                              L32_2 = 30
                              L33_2 = 30
                              L31_2[1] = L32_2
                              L31_2[2] = L33_2
                              L29_2 = L29_2(L30_2, L31_2)
                              if L29_2 then
                              end
                              L29_2 = _ENV["屏蔽取消"]
                              L29_2()
                              L29_2 = _ENV["_卡区延迟"]
                              L29_2()
                            end
                        end
                        elseif not L25_2 then
                          L29_2 = _ENV["_卡区延迟"]
                          L29_2()
                          L29_2 = 1
                          L30_2 = 20
                          L31_2 = 1
                          for L32_2 = L29_2, L30_2, L31_2 do
                            L33_2 = false
                            L34_2 = -1
                            L35_2 = -1
                            if A2_2 == "物品" then
                              L36_2 = mSleep
                              L37_2 = math
                              L37_2 = L37_2.random
                              L38_2 = 500
                              L39_2 = 800
                              L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2 = L37_2(L38_2, L39_2)
                              L36_2(L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2)
                              if A1_2 == "豆斋果" or A1_2 == "醉生梦死" or A1_2 == "长寿面" or A1_2 == "佛跳墙" or A1_2 == "烤鸭" or A1_2 == "珍露酒" or A1_2 == "臭豆腐" or A1_2 == "烤肉" or A1_2 == "桂花丸" or A1_2 == "翡翠豆腐" or A1_2 == "梅花酒" or A1_2 == "百味酒" or A1_2 == "蛇胆酒" then
                                L36_2 = _ENV["烹饪价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["烹饪价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_572
                                  end
                                end
                                _ENV["烹饪价格"] = 99999
                                ::lbl_572::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["烹饪价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              elseif A1_2 == "金香玉" or A1_2 == "小还丹" or A1_2 == "千年保心丹" or A1_2 == "风水混元丹" or A1_2 == "佛光舍利子" or A1_2 == "定神香" or A1_2 == "蛇蝎美人" or A1_2 == "红雪散" or A1_2 == "九转回魂丹" or A1_2 == "五龙丹" or A1_2 == "十香返生丸" then
                                L36_2 = _ENV["三药价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["三药价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_613
                                  end
                                end
                                _ENV["三药价格"] = 99999
                                ::lbl_613::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["三药价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              elseif A1_2 == "金香玉" or A1_2 == "小还丹" or A1_2 == "千年保心丹" or A1_2 == "风水混元丹" or A1_2 == "佛光舍利子" or A1_2 == "定神香" or A1_2 == "蛇蝎美人" or A1_2 == "红雪散" or A1_2 == "九转回魂丹" or A1_2 == "五龙丹" or A1_2 == "十香返生丸" then
                                L36_2 = _ENV["三药价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["三药价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_654
                                  end
                                end
                                _ENV["三药价格"] = 99999
                                ::lbl_654::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["三药价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              elseif A1_2 == "黑麻垂曼帘" or A1_2 == "漆花竹凳" or A1_2 == "桦木圆桌" or A1_2 == "桦木立柜" or A1_2 == "草编地毯" or A1_2 == "榛木床" or A1_2 == "印花屏风" or A1_2 == "文竹" or A1_2 == "君子兰" or A1_2 == "蝴蝶兰" or A1_2 == "水仙" or A1_2 == "仙人掌" or A1_2 == "银烛台" or A1_2 == "玉瓷画瓶" or A1_2 == "踏春图" or A1_2 == "漆花地板" then
                                L36_2 = _ENV["一家价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["一家价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_705
                                  end
                                end
                                _ENV["一家价格"] = 99999
                                ::lbl_705::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["一家价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              elseif A1_2 == "白鹤展翅帘" or A1_2 == "桦木地板" or A1_2 == "夕阳山水图" or A1_2 == "蓝绸绣花帘" or A1_2 == "红木八仙桌" or A1_2 == "镶玉虎纹桌" or A1_2 == "双鱼吉庆柜" or A1_2 == "彩绘立柜" or A1_2 == "兽皮地毯" or A1_2 == "麻布地毯" or A1_2 == "桦木靠背椅" or A1_2 == "神仙帐" or A1_2 == "月牙凳" or A1_2 == "八卦镇邪塌" or A1_2 == "狮子图屏风" or A1_2 == "花鸟图屏风" or A1_2 == "天山云雪" or A1_2 == "龟鹤延年灯" or A1_2 == "长信宫灯" or A1_2 == "雕花马桶" or A1_2 == "彩绘花瓶" then
                                L36_2 = _ENV["二家价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["二家价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_766
                                  end
                                end
                                _ENV["二家价格"] = 99999
                                ::lbl_766::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["二家价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              elseif A1_2 == "天不老" or A1_2 == "紫石英" or A1_2 == "凤凰尾巴" or A1_2 == "硫磺草" or A1_2 == "龙之心屑" or A1_2 == "月星子" or A1_2 == "血珊瑚" or A1_2 == "火凤之眼" or A1_2 == "孔雀红" or A1_2 == "鹿茸" or A1_2 == "仙狐涎" or A1_2 == "天龙水" or A1_2 == "地狱灵芝" or A1_2 == "六道轮回" or A1_2 == "白露为霜" or A1_2 == "餐风饮露" then
                                L36_2 = _ENV["二药价格"]
                                if L36_2 ~= "" then
                                  L36_2 = _ENV["二药价格"]
                                  if L36_2 ~= nil then
                                    goto lbl_817
                                  end
                                end
                                _ENV["二药价格"] = 99999
                                ::lbl_817::
                                L36_2 = _ENV["价格筛选"]
                                L37_2 = tonumber
                                L38_2 = _ENV["二药价格"]
                                L37_2 = L37_2(L38_2)
                                L38_2 = A1_2
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                                if not L33_2 then
                                  break
                                end
                              else
                                L36_2 = getMultiColor
                                L37_2 = propsList
                                L38_2 = A1_2
                                L39_2 = 85
                                L40_2 = 117
                                L41_2 = 224
                                L42_2 = 803
                                L43_2 = 752
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                L33_2 = L36_2
                              end
                            else
                              L36_2 = mSleep
                              L37_2 = 600
                              L36_2(L37_2)
                              L36_2 = _ENV["_卡区延迟"]
                              L36_2()
                              L36_2 = getWordStock
                              L37_2 = PetList
                              L37_2 = L37_2["低于一万"]
                              L38_2 = A1_2
                              L39_2 = 176
                              L40_2 = 250
                              L41_2 = 735
                              L42_2 = 773
                              L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2, L39_2, L40_2, L41_2, L42_2)
                              L35_2 = L38_2
                              L34_2 = L37_2
                              _ENV["指定宠物1"] = L36_2
                              L36_2 = _ENV["指定宠物1"]
                              if not L36_2 then
                                L36_2 = getWordStock
                                L37_2 = PetList
                                L37_2 = L37_2["超出一万"]
                                L38_2 = A1_2
                                L39_2 = 176
                                L40_2 = 250
                                L41_2 = 735
                                L42_2 = 773
                                L36_2, L37_2, L38_2 = L36_2(L37_2, L38_2, L39_2, L40_2, L41_2, L42_2)
                                L35_2 = L38_2
                                L34_2 = L37_2
                                _ENV["指定宠物2"] = L36_2
                              end
                              L36_2 = _ENV["指定宠物1"]
                              if L36_2 then
                                _ENV["指定宠物1"] = false
                                _ENV["指定宠物2"] = false
                                L36_2 = 1
                                L37_2 = 8
                                L38_2 = 1
                                for L39_2 = L36_2, L37_2, L38_2 do
                                  L40_2 = getWordStock
                                  L41_2 = PetList
                                  L41_2 = L41_2["低于一万"]
                                  L42_2 = A1_2
                                  L43_2 = _ENV["商会位置格子"]
                                  L44_2 = L39_2
                                  L45_2 = "号格子"
                                  L44_2 = L44_2 .. L45_2
                                  L43_2 = L43_2[L44_2]
                                  L43_2 = L43_2[1]
                                  L44_2 = _ENV["商会位置格子"]
                                  L45_2 = L39_2
                                  L46_2 = "号格子"
                                  L45_2 = L45_2 .. L46_2
                                  L44_2 = L44_2[L45_2]
                                  L44_2 = L44_2[2]
                                  L45_2 = _ENV["商会位置格子"]
                                  L46_2 = L39_2
                                  L47_2 = "号格子"
                                  L46_2 = L46_2 .. L47_2
                                  L45_2 = L45_2[L46_2]
                                  L45_2 = L45_2[3]
                                  L46_2 = _ENV["商会位置格子"]
                                  L47_2 = L39_2
                                  L48_2 = "号格子"
                                  L47_2 = L47_2 .. L48_2
                                  L46_2 = L46_2[L47_2]
                                  L46_2 = L46_2[4]
                                  L40_2, L41_2, L42_2 = L40_2(L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
                                  y1 = L42_2
                                  x1 = L41_2
                                  boolean1 = L40_2
                                  L40_2 = boolean1
                                  if L40_2 then
                                    L40_2 = tap
                                    L41_2 = x1
                                    L42_2 = y1
                                    L42_2 = L42_2 + 20
                                    L40_2(L41_2, L42_2)
                                    L40_2 = _ENV["_随机延时"]
                                    L41_2 = 1400
                                    L40_2(L41_2)
                                    L40_2 = _ENV["_卡区延迟"]
                                    L40_2()
                                    L40_2 = _ENV["宠物特性"]
                                    L40_2 = L40_2[A3_2]
                                    L40_2 = L40_2()
                                    if L40_2 then
                                      L33_2 = true
                                      break
                                    else
                                      L41_2 = _ENV["_随机延时"]
                                      L42_2 = 400
                                      L41_2(L42_2)
                                      L33_2 = false
                                    end
                                  end
                                end
                              end
                              L36_2 = _ENV["指定宠物2"]
                              if L36_2 then
                                _ENV["指定宠物2"] = false
                                L36_2 = 1
                                L37_2 = 8
                                L38_2 = 1
                                for L39_2 = L36_2, L37_2, L38_2 do
                                  L40_2 = getWordStock
                                  L41_2 = PetList
                                  L41_2 = L41_2["超出一万"]
                                  L42_2 = A1_2
                                  L43_2 = _ENV["商会位置格子"]
                                  L44_2 = L39_2
                                  L45_2 = "号格子"
                                  L44_2 = L44_2 .. L45_2
                                  L43_2 = L43_2[L44_2]
                                  L43_2 = L43_2[1]
                                  L44_2 = _ENV["商会位置格子"]
                                  L45_2 = L39_2
                                  L46_2 = "号格子"
                                  L45_2 = L45_2 .. L46_2
                                  L44_2 = L44_2[L45_2]
                                  L44_2 = L44_2[2]
                                  L45_2 = _ENV["商会位置格子"]
                                  L46_2 = L39_2
                                  L47_2 = "号格子"
                                  L46_2 = L46_2 .. L47_2
                                  L45_2 = L45_2[L46_2]
                                  L45_2 = L45_2[3]
                                  L46_2 = _ENV["商会位置格子"]
                                  L47_2 = L39_2
                                  L48_2 = "号格子"
                                  L47_2 = L47_2 .. L48_2
                                  L46_2 = L46_2[L47_2]
                                  L46_2 = L46_2[4]
                                  L40_2, L41_2, L42_2 = L40_2(L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
                                  y2 = L42_2
                                  x2 = L41_2
                                  boolean2 = L40_2
                                  L40_2 = boolean2
                                  if L40_2 then
                                    L40_2 = tap
                                    L41_2 = x2
                                    L42_2 = y2
                                    L42_2 = L42_2 + 20
                                    L40_2(L41_2, L42_2)
                                    L40_2 = _ENV["_随机延时"]
                                    L41_2 = 1400
                                    L40_2(L41_2)
                                    L40_2 = _ENV["_卡区延迟"]
                                    L40_2()
                                    L40_2 = _ENV["宠物特性"]
                                    L40_2 = L40_2[A3_2]
                                    L40_2 = L40_2()
                                    if L40_2 then
                                      L33_2 = true
                                      break
                                    else
                                      L41_2 = _ENV["_随机延时"]
                                      L42_2 = 400
                                      L41_2(L42_2)
                                      L33_2 = false
                                    end
                                  end
                                end
                              end
                            end
                            if L33_2 then
                              L36_2 = true
                              L37_2 = 0
                              L38_2 = 0
                              L39_2 = false
                              while true do
                                L40_2 = getColour
                                L41_2 = colorList
                                L42_2 = "提示信息"
                                L40_2 = L40_2(L41_2, L42_2)
                                if L40_2 or L39_2 then
                                  L40_2 = printLog
                                  L41_2 = "完成购买:"
                                  L42_2 = A1_2
                                  L41_2 = L41_2 .. L42_2
                                  L40_2(L41_2)
                                  L4_2 = true
                                  break
                                elseif L38_2 < 3 and L36_2 then
                                  L36_2 = false
                                  L40_2 = os
                                  L40_2 = L40_2.time
                                  L40_2 = L40_2()
                                  L37_2 = L40_2
                                  if A2_2 == "物品" then
                                    L40_2 = randomClick
                                    L41_2 = 2
                                    L42_2 = 300
                                    L43_2 = L34_2
                                    L44_2 = L35_2 + 20
                                    L45_2 = 20
                                    L40_2(L41_2, L42_2, L43_2, L44_2, L45_2)
                                  end
                                  L40_2 = randomClick
                                  L41_2 = 0
                                  L42_2 = 300
                                  L43_2 = 627
                                  L44_2 = 944
                                  L45_2 = 779
                                  L46_2 = 991
                                  L40_2(L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
                                  L39_2 = true
                                elseif 3 <= L38_2 then
                                  L4_2 = false
                                  break
                                else
                                  L40_2 = os
                                  L40_2 = L40_2.time
                                  L40_2 = L40_2()
                                  L40_2 = L40_2 - L37_2
                                  L41_2 = 5
                                  if L40_2 > L41_2 and L37_2 ~= 0 then
                                    L38_2 = L38_2 + 1
                                    L36_2 = true
                                  end
                                end
                              end
                            end
                            if L4_2 then
                              break
                            end
                            L36_2 = mSleep
                            L37_2 = 100
                            L36_2(L37_2)
                            L36_2 = _ENV["指定宠物1"]
                            L37_2 = false
                            if L36_2 == L37_2 then
                              break
                            end
                            L36_2 = _ENV["指定宠物2"]
                            L37_2 = false
                            if L36_2 == L37_2 then
                              break
                            end
                          end
                          if not L4_2 then
                            L29_2 = 0
                            L30_2 = 1
                            L31_2 = #L5_2
                            L31_2 = L31_2 + 1
                            L32_2 = #L5_2
                            L32_2 = L18_2 + L32_2
                            L11_2 = L32_2 + 1
                            L32_2 = 1
                            L33_2 = L12_2[2]
                            L33_2 = #L33_2
                            L34_2 = 1
                            for L35_2 = L32_2, L33_2, L34_2 do
                              L36_2 = string
                              L36_2 = L36_2.sub
                              L37_2 = L12_2[2]
                              L38_2 = L30_2
                              L39_2 = L31_2
                              L36_2 = L36_2(L37_2, L38_2, L39_2)
                              L30_2 = L30_2 + 1
                              L31_2 = L31_2 + 1
                              if L35_2 >= L11_2 then
                                L37_2 = string
                                L37_2 = L37_2.find
                                L38_2 = L36_2
                                L39_2 = L5_2
                                L37_2 = L37_2(L38_2, L39_2)
                                if L37_2 then
                                  L25_2 = true
                                  L37_2 = tonumber
                                  L38_2 = string
                                  L38_2 = L38_2.sub
                                  L39_2 = L12_2[2]
                                  L40_2 = L30_2 - 1
                                  L41_2 = L30_2 - 1
                                  L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2 = L38_2(L39_2, L40_2, L41_2)
                                  L37_2 = L37_2(L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2)
                                  L29_2 = L37_2
                                  if L29_2 == 0 then
                                    L29_2 = 10
                                  end
                                  break
                                end
                              end
                            end
                            if A1_2 == "黑熊精" or A1_2 == "黑熊" then
                              L29_2 = 0
                            end
                            if L29_2 == 0 then
                              while true do
                                L32_2 = getColour
                                L33_2 = colorList
                                L34_2 = "商会列表"
                                L32_2 = L32_2(L33_2, L34_2)
                                if L32_2 then
                                  L32_2 = randomClick
                                  L33_2 = 0
                                  L34_2 = 300
                                  L35_2 = 275
                                  L36_2 = 153
                                  L37_2 = 377
                                  L38_2 = 196
                                  L32_2(L33_2, L34_2, L35_2, L36_2, L37_2, L38_2)
                                  break
                                else
                                  L32_2 = getColour
                                  L33_2 = colorList
                                  L34_2 = "商会店铺"
                                  L32_2 = L32_2(L33_2, L34_2)
                                  if L32_2 then
                                    L32_2 = randomClick
                                    L33_2 = 2
                                    L34_2 = 200
                                    L35_2 = 848
                                    L36_2 = 49
                                    L32_2(L33_2, L34_2, L35_2, L36_2)
                                  end
                                end
                              end
                              L22_2 = true
                              goto lbl_1299
                          end
                          else
                            L22_2 = true
                            goto lbl_1299
                            goto lbl_1250
                            goto lbl_1299
                          end
                        else
                          L29_2 = 5
                          if L26_2 > L29_2 and L25_2 then
                            L29_2 = getColour
                            L30_2 = colorList
                            L31_2 = "商会号码"
                            L29_2 = L29_2(L30_2, L31_2)
                            if not L29_2 then
                              L29_2 = printLog
                              L30_2 = "店铺未开:"
                              L31_2 = L26_2
                              L30_2 = L30_2 .. L31_2
                              L29_2(L30_2)
                              while true do
                                L29_2 = getColour
                                L30_2 = colorList
                                L31_2 = "商会列表"
                                L29_2 = L29_2(L30_2, L31_2)
                                if L29_2 then
                                  L29_2 = randomClick
                                  L30_2 = 0
                                  L31_2 = 300
                                  L32_2 = 275
                                  L33_2 = 153
                                  L34_2 = 377
                                  L35_2 = 196
                                  L29_2(L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                                  break
                                else
                                  L29_2 = getColour
                                  L30_2 = colorList
                                  L31_2 = "商会店铺"
                                  L29_2 = L29_2(L30_2, L31_2)
                                  if L29_2 then
                                    L29_2 = randomClick
                                    L30_2 = 2
                                    L31_2 = 200
                                    L32_2 = 848
                                    L33_2 = 49
                                    L29_2(L30_2, L31_2, L32_2, L33_2)
                                  end
                                end
                              end
                              L22_2 = true
                              L11_2 = -1
                              goto lbl_1299
                          end
                          elseif L25_2 then
                            L26_2 = L26_2 + 1
                            L29_2 = randomClick
                            L30_2 = 0
                            L31_2 = 100
                            L32_2 = 233
                            L33_2 = 946
                            L34_2 = 322
                            L35_2 = 979
                            L29_2(L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
                            L29_2 = _find_cx
                            L30_2 = Color
                            L30_2 = L30_2["师门"]
                            L31_2 = "商会店铺小键盘"
                            L30_2 = L30_2[L31_2]
                            L31_2 = {}
                            L32_2 = 30
                            L33_2 = 30
                            L31_2[1] = L32_2
                            L31_2[2] = L33_2
                            L29_2 = L29_2(L30_2, L31_2)
                            if L29_2 then
                            end
                          end
                        end
                      end
                      ::lbl_1250::
                    end
                  elseif L23_2 == 0 and not L21_2 then
                    L25_2 = randomClick
                    L26_2 = 0
                    L27_2 = 1300
                    L28_2 = 989
                    L29_2 = 600
                    L30_2 = 1054
                    L31_2 = 666
                    L25_2(L26_2, L27_2, L28_2, L29_2, L30_2, L31_2)
                    L25_2 = os
                    L25_2 = L25_2.time
                    L25_2 = L25_2()
                    L23_2 = L25_2
                  else
                    L25_2 = randomClick
                    L26_2 = 0
                    L27_2 = 300
                    L28_2 = 587
                    L29_2 = 149
                    L30_2 = 711
                    L31_2 = 200
                    L25_2(L26_2, L27_2, L28_2, L29_2, L30_2, L31_2)
                    L25_2 = "_cmp"
                    L25_2 = _ENV[L25_2]
                    L26_2 = Color
                    L27_2 = "百级师门"
                    L26_2 = L26_2[L27_2]
                    L27_2 = "误点搜索"
                    L26_2 = L26_2[L27_2]
                    L25_2 = L25_2(L26_2)
                    if L25_2 then
                      L25_2 = "_Sleep"
                      L25_2 = _ENV[L25_2]
                      L26_2 = 300
                      L27_2 = 500
                      L25_2(L26_2, L27_2)
                      L25_2 = "_tap"
                      L25_2 = _ENV[L25_2]
                      L26_2 = 322
                      L27_2 = 180
                      L28_2 = 1
                      L29_2 = 10
                      L25_2(L26_2, L27_2, L28_2, L29_2)
                    end
                  end
                end
              end
            end
            ::lbl_1299::
          end
        end
      end
    end
  end
  L7_2 = "大海龟"
  if A1_2 ~= L7_2 then
    L7_2 = "巨蛙"
    if A1_2 ~= L7_2 then
      L7_2 = "野猪"
      if A1_2 ~= L7_2 then
        L7_2 = "树怪"
        if A1_2 ~= L7_2 then
          L7_2 = "强盗"
          if A1_2 ~= L7_2 then
            L7_2 = "赌徒"
            if A1_2 ~= L7_2 then
              L7_2 = "山贼"
              if A1_2 ~= L7_2 then
                L7_2 = "老虎"
                if A1_2 ~= L7_2 and A1_2 ~= "黑熊" then
                  L7_2 = "花妖"
                  if A1_2 ~= L7_2 then
                    L7_2 = "羊头怪"
                    if A1_2 ~= L7_2 then
                      L7_2 = "蛤蟆精"
                      if A1_2 ~= L7_2 then
                        L7_2 = "骷髅怪"
                        if A1_2 ~= L7_2 then
                          L7_2 = "狐狸精"
                          if A1_2 ~= L7_2 then
                            L7_2 = "狼"
                            if A1_2 ~= L7_2 then
                              L7_2 = "小龙女"
                              if A1_2 ~= L7_2 then
                                L7_2 = "虾兵"
                                if A1_2 ~= L7_2 then
                                  L7_2 = "蟹将"
                                  if A1_2 ~= L7_2 then
                                    L7_2 = "龟丞相"
                                    if A1_2 ~= L7_2 then
                                      L7_2 = "牛头"
                                      if A1_2 ~= L7_2 then
                                        L7_2 = "马面"
                                        if A1_2 ~= L7_2 then
                                          L7_2 = "牛妖"
                                          if A1_2 ~= L7_2 then
                                            L7_2 = "僵尸"
                                            if A1_2 ~= L7_2 then
                                              L7_2 = "野鬼"
                                              if A1_2 ~= L7_2 then
                                                L7_2 = "兔子怪"
                                                if A1_2 ~= L7_2 then
                                                  L7_2 = "蜘蛛精"
                                                  if A1_2 ~= L7_2 and A1_2 ~= "黑熊精" then
                                                    L7_2 = "蝴蝶仙子"
                                                    if A1_2 ~= L7_2 then
                                                      L7_2 = "雷鸟人"
                                                      if A1_2 ~= L7_2 then
                                                        L7_2 = "古代瑞兽"
                                                        if A1_2 ~= L7_2 then
                                                          L7_2 = "白熊"
                                                          if A1_2 ~= L7_2 then
                                                            L7_2 = "黑山老妖"
                                                            if A1_2 ~= L7_2 then
                                                              L7_2 = "天将"
                                                              if A1_2 ~= L7_2 then
                                                                L7_2 = "天兵"
                                                                if A1_2 ~= L7_2 then
                                                                  L7_2 = "风伯"
                                                                  if A1_2 ~= L7_2 then
                                                                    L7_2 = "地狱战神"
                                                                    if A1_2 ~= L7_2 then
                                                                      L7_2 = "蛟龙"
                                                                      if A1_2 ~= L7_2 then
                                                                        L7_2 = "凤凰"
                                                                        if A1_2 ~= L7_2 then
                                                                          L7_2 = "雨师"
                                                                          if A1_2 ~= L7_2 then
                                                                            L7_2 = "巡游天神"
                                                                            if A1_2 ~= L7_2 then
                                                                              L7_2 = "星灵仙子"
                                                                              if A1_2 ~= L7_2 then
                                                                                L7_2 = "芙蓉仙子"
                                                                                if A1_2 ~= L7_2 then
                                                                                  L7_2 = "如意仙子"
                                                                                  if A1_2 ~= L7_2 then
                                                                                    goto lbl_1436
                                                                                  end
                                                                                end
                                                                              end
                                                                            end
                                                                          end
                                                                        end
                                                                      end
                                                                    end
                                                                  end
                                                                end
                                                              end
                                                            end
                                                          end
                                                        end
                                                      end
                                                    end
                                                  end
                                                end
                                              end
                                            end
                                          end
                                        end
                                      end
                                    end
                                  end
                                end
                              end
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  L7_2 = "百级龟速识别宠物"
  L7_2 = _ENV[L7_2]
  L8_2 = A1_2
  L9_2 = A3_2
  do return L7_2(L8_2, L9_2) end
  goto lbl_1442
  ::lbl_1436::
  L7_2 = "百级龟速识别"
  L7_2 = _ENV[L7_2]
  L8_2 = A1_2
  L9_2 = A3_2
  do return L7_2(L8_2, L9_2) end
  ::lbl_1442::
end

_ENV["百级查找对于物品"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2
  L3_2 = A1_2
  L4_2 = _ENV["门派传送"]
  if L4_2 == "是" then
    L4_2 = _ENV["长安传送NPC"]
    L5_2 = A0_2
    L4_2(L5_2)
  else
    L4_2 = _ENV["_使用"]
    L4_2 = L4_2["旗子"]
    L5_2 = "长安城"
    L6_2 = "长安_商会d"
    L4_2(L5_2, L6_2)
  end
  L4_2 = _find
  L5_2 = Color
  L5_2 = L5_2["红尘"]
  L5_2 = L5_2["没点了"]
  L4_2 = L4_2(L5_2)
  if L4_2 then
    L4_2 = _ENV["没点了下号"]
    return L4_2()
  end
  L4_2 = _ENV["_返回"]
  L4_2 = L4_2["地图"]
  L5_2 = "长安城"
  L4_2 = L4_2(L5_2)
  if L4_2 then
    while true do
      L4_2 = _ENV["通用功能"]
      L4_2 = L4_2["叉叉"]
      L4_2()
      L4_2 = _ENV["起号"]
      L4_2 = L4_2["坐标"]
      L4_2, L5_2 = L4_2()
      L6_2 = 326 - L4_2
      L7_2 = 18 - L5_2
      L8_2 = math
      L8_2 = L8_2.abs
      L9_2 = L6_2
      L8_2 = L8_2(L9_2)
      if L8_2 <= 5 then
        L8_2 = math
        L8_2 = L8_2.abs
        L9_2 = L7_2
        L8_2 = L8_2(L9_2)
        if L8_2 <= 5 then
          L8_2 = _ENV["_功能"]
          L8_2 = L8_2["屏蔽"]
          L9_2 = 4
          L8_2(L9_2)
          L8_2 = _ENV["_计算"]
          L8_2 = L8_2["取目标屏幕坐标"]
          L9_2 = "长安城"
          L10_2 = L4_2
          L11_2 = L5_2
          L12_2 = 326
          L13_2 = 18
          L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2)
          L5_2 = L9_2
          L4_2 = L8_2
          L8_2 = mSleep
          L9_2 = 500
          L8_2(L9_2)
          L8_2 = tap
          L9_2 = L4_2
          L10_2 = L5_2
          L8_2(L9_2, L10_2)
          L8_2 = _Sleep
          L9_2 = 500
          L10_2 = 1188
          L8_2(L9_2, L10_2)
          L8_2 = 1
          L9_2 = 40
          L10_2 = 1
          for L11_2 = L8_2, L9_2, L10_2 do
            L12_2 = _find_tb
            L13_2 = Color
            L13_2 = L13_2["出现重叠"]
            L12_2, L13_2, L14_2 = L12_2(L13_2)
            if L12_2 then
              L15_2 = L13_2 - 165
              L16_2 = L14_2 + 31
              L17_2 = L13_2 - 53
              L18_2 = L14_2 + 540
              L19_2 = _find_tb
              L20_2 = Color
              L20_2 = L20_2["回复"]
              L20_2 = L20_2["商会重叠"]
              L21_2 = {}
              L22_2 = 0
              L23_2 = 0
              L21_2[1] = L22_2
              L21_2[2] = L23_2
              L22_2 = {}
              L23_2 = L15_2
              L24_2 = L16_2
              L25_2 = L17_2
              L26_2 = L18_2
              L22_2[1] = L23_2
              L22_2[2] = L24_2
              L22_2[3] = L25_2
              L22_2[4] = L26_2
              L19_2 = L19_2(L20_2, L21_2, L22_2)
              if L19_2 then
                L19_2 = mSleep
                L20_2 = 1000
                L19_2(L20_2)
              end
            end
            L15_2 = _find_cx
            L16_2 = Color
            L16_2 = L16_2["百级师门"]
            L16_2 = L16_2["我来买些东西"]
            L17_2 = {}
            L18_2 = 30
            L19_2 = 50
            L17_2[1] = L18_2
            L17_2[2] = L19_2
            L15_2 = L15_2(L16_2, L17_2)
            if L15_2 then
              L15_2 = _cmp_cx
              L16_2 = Color
              L16_2 = L16_2["购买"]
              L16_2 = L16_2["商会界面"]
              L17_2 = {}
              L18_2 = 50
              L19_2 = 50
              L17_2[1] = L18_2
              L17_2[2] = L19_2
              L15_2 = L15_2(L16_2, L17_2)
              if L15_2 then
                L15_2 = _ENV["屏蔽取消"]
                L15_2()
                L15_2 = _ENV["宠物店"]
                if L15_2 ~= nil then
                  L15_2 = _ENV["宠物店"]
                  if L15_2 ~= "" then
                    goto lbl_145
                  end
                end
                L15_2 = _ENV["百级龟速识别宠物"]
                L16_2 = L3_2
                L17_2 = A2_2
                do return L15_2(L16_2, L17_2) end
                goto lbl_152
                ::lbl_145::
                L15_2 = _ENV["百级查找对于物品"]
                L16_2 = _ENV["宠物店"]
                L17_2 = A1_2
                L18_2 = "宠物"
                L19_2 = A2_2
                return L15_2(L16_2, L17_2, L18_2, L19_2)
              end
            end
            ::lbl_152::
          end
      end
      else
        L8_2 = _ENV["_前往"]
        L8_2 = L8_2["固定坐标"]
        L9_2 = "长安城"
        L10_2 = 1095
        L11_2 = 850
        L8_2(L9_2, L10_2, L11_2)
      end
    end
  end
end

_ENV["百级帮师傅抓到宠物"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["门派传送"]
  if L2_2 == "是" then
    L2_2 = _ENV["长安传送NPC"]
    L3_2 = A0_2
    L2_2(L3_2)
  else
    L2_2 = _ENV["_使用"]
    L2_2 = L2_2["旗子"]
    L3_2 = "长安城"
    L4_2 = "长安_商会d"
    L2_2(L3_2, L4_2)
  end
  L2_2 = _find
  L3_2 = Color
  L3_2 = L3_2["红尘"]
  L3_2 = L3_2["没点了"]
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = _ENV["没点了下号"]
    return L2_2()
  end
  L2_2 = _ENV["_返回"]
  L2_2 = L2_2["地图"]
  L3_2 = "长安城"
  L2_2 = L2_2(L3_2)
  if L2_2 then
    while true do
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["叉叉"]
      L2_2()
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      L4_2 = 326 - L2_2
      L5_2 = 18 - L3_2
      L6_2 = math
      L6_2 = L6_2.abs
      L7_2 = L4_2
      L6_2 = L6_2(L7_2)
      if L6_2 <= 5 then
        L6_2 = math
        L6_2 = L6_2.abs
        L7_2 = L5_2
        L6_2 = L6_2(L7_2)
        if L6_2 <= 5 then
          L6_2 = _ENV["_功能"]
          L6_2 = L6_2["屏蔽"]
          L7_2 = 4
          L6_2(L7_2)
          L6_2 = _ENV["_计算"]
          L6_2 = L6_2["取目标屏幕坐标"]
          L7_2 = "长安城"
          L8_2 = L2_2
          L9_2 = L3_2
          L10_2 = 326
          L11_2 = 18
          L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
          L3_2 = L7_2
          L2_2 = L6_2
          L6_2 = mSleep
          L7_2 = 500
          L6_2(L7_2)
          L6_2 = tap
          L7_2 = L2_2
          L8_2 = L3_2
          L6_2(L7_2, L8_2)
          L6_2 = _Sleep
          L7_2 = 500
          L8_2 = 1188
          L6_2(L7_2, L8_2)
          L6_2 = 1
          L7_2 = 40
          L8_2 = 1
          for L9_2 = L6_2, L7_2, L8_2 do
            L10_2 = _find_tb
            L11_2 = Color
            L11_2 = L11_2["出现重叠"]
            L10_2, L11_2, L12_2 = L10_2(L11_2)
            if L10_2 then
              L13_2 = L11_2 - 165
              L14_2 = L12_2 + 31
              L15_2 = L11_2 - 53
              L16_2 = L12_2 + 540
              L17_2 = _find_tb
              L18_2 = Color
              L18_2 = L18_2["回复"]
              L18_2 = L18_2["商会重叠"]
              L19_2 = {}
              L20_2 = 0
              L21_2 = 0
              L19_2[1] = L20_2
              L19_2[2] = L21_2
              L20_2 = {}
              L21_2 = L13_2
              L22_2 = L14_2
              L23_2 = L15_2
              L24_2 = L16_2
              L20_2[1] = L21_2
              L20_2[2] = L22_2
              L20_2[3] = L23_2
              L20_2[4] = L24_2
              L17_2 = L17_2(L18_2, L19_2, L20_2)
              if L17_2 then
                L17_2 = mSleep
                L18_2 = 1000
                L17_2(L18_2)
              end
            end
            L13_2 = _find_cx
            L14_2 = Color
            L14_2 = L14_2["百级师门"]
            L14_2 = L14_2["我来买些东西"]
            L15_2 = {}
            L16_2 = 30
            L17_2 = 50
            L15_2[1] = L16_2
            L15_2[2] = L17_2
            L13_2 = L13_2(L14_2, L15_2)
            if L13_2 then
              L13_2 = _cmp_cx
              L14_2 = Color
              L14_2 = L14_2["购买"]
              L14_2 = L14_2["商会界面"]
              L15_2 = {}
              L16_2 = 50
              L17_2 = 50
              L15_2[1] = L16_2
              L15_2[2] = L17_2
              L13_2 = L13_2(L14_2, L15_2)
              if L13_2 then
                L13_2 = _ENV["屏蔽取消"]
                L13_2()
                L13_2 = _ENV["物品店"]
                if L13_2 ~= nil then
                  L13_2 = _ENV["物品店"]
                  if L13_2 ~= "" then
                    goto lbl_150
                  end
                end
                L13_2 = _ENV["百级龟速识别"]
                L14_2 = A1_2
                do return L13_2(L14_2) end
                goto lbl_156
                ::lbl_150::
                L13_2 = _ENV["百级查找对于物品"]
                L14_2 = _ENV["物品店"]
                L15_2 = A1_2
                L16_2 = "物品"
                return L13_2(L14_2, L15_2, L16_2)
              end
            end
            ::lbl_156::
          end
          L6_2 = mSleep
          L7_2 = 50
          L6_2(L7_2)
      end
      else
        L6_2 = _ENV["_前往"]
        L6_2 = L6_2["固定坐标"]
        L7_2 = "长安城"
        L8_2 = 1095
        L9_2 = 850
        L6_2(L7_2, L8_2, L9_2)
      end
      L6_2 = mSleep
      L7_2 = 100
      L6_2(L7_2)
    end
  end
end

_ENV["百级购买师门物品"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2
  L2_2 = ""
  L3_2 = _ENV["商会不买前几页"]
  if L3_2 == "0" then
  else
    L3_2 = tonumber
    L4_2 = _ENV["商会不买前几页"]
    L3_2 = L3_2(L4_2)
    L2_2 = L3_2
    L3_2 = 1
    L4_2 = L2_2
    L5_2 = 1
    for L6_2 = L3_2, L4_2, L5_2 do
      L7_2 = randomClick
      L8_2 = 2
      L9_2 = 888
      L10_2 = 600
      L11_2 = 970
      L7_2(L8_2, L9_2, L10_2, L11_2)
      L7_2 = mSleep
      L8_2 = 300
      L7_2(L8_2)
    end
  end
  L3_2 = ""
  L4_2 = ""
  if A0_2 == "大海龟" then
    L4_2 = "龟"
    L3_2 = "低级"
  elseif A0_2 == "巨蛙" then
    L4_2 = "蛙"
    L3_2 = "低级"
  elseif A0_2 == "野猪" then
    L4_2 = "猪"
    L3_2 = "低级"
  elseif A0_2 == "树怪" then
    L4_2 = "树"
    L3_2 = "低级"
  elseif A0_2 == "强盗" then
    L4_2 = "强"
    L3_2 = "低级"
  elseif A0_2 == "赌徒" then
    L4_2 = "赌"
    L3_2 = "低级"
  elseif A0_2 == "山贼" then
    L4_2 = "贼"
    L3_2 = "低级"
  elseif A0_2 == "老虎" then
    L4_2 = "虎"
    L3_2 = "低级"
  elseif A0_2 == "黑熊" then
    L4_2 = "熊"
    L3_2 = "低级"
  elseif A0_2 == "花妖" then
    L4_2 = "花"
    L3_2 = "低级"
  elseif A0_2 == "羊头怪" then
    L4_2 = "羊"
    L3_2 = "低级"
  elseif A0_2 == "蛤蟆精" then
    L4_2 = "蛤"
    L3_2 = "低级"
  elseif A0_2 == "骷髅怪" then
    L4_2 = "骷"
    L3_2 = "低级"
  elseif A0_2 == "狐狸精" then
    L4_2 = "狐"
    L3_2 = "低级"
  elseif A0_2 == "狼" then
    L4_2 = "狼"
    L3_2 = "中级"
  elseif A0_2 == "小龙女" then
    L4_2 = "女"
    L3_2 = "中级"
  elseif A0_2 == "虾兵" then
    L4_2 = "虾"
    L3_2 = "中级"
  elseif A0_2 == "蟹将" then
    L4_2 = "蟹"
    L3_2 = "中级"
  elseif A0_2 == "龟丞相" then
    L4_2 = "丞"
    L3_2 = "中级"
  elseif A0_2 == "牛头" then
    L4_2 = "牛"
    L3_2 = "中级"
  elseif A0_2 == "马面" then
    L4_2 = "马"
    L3_2 = "中级"
  elseif A0_2 == "牛妖" then
    L4_2 = "妖"
    L3_2 = "中级"
  elseif A0_2 == "僵尸" then
    L4_2 = "僵"
    L3_2 = "中级"
  elseif A0_2 == "野鬼" then
    L4_2 = "野"
    L3_2 = "中级"
  elseif A0_2 == "兔子怪" then
    L4_2 = "兔"
    L3_2 = "中级"
  elseif A0_2 == "蜘蛛精" then
    L4_2 = "蜘"
    L3_2 = "中级"
  elseif A0_2 == "黑熊精" then
    L4_2 = "熊"
    L3_2 = "中级"
  elseif A0_2 == "雷鸟人" then
    L4_2 = "狮"
    L3_2 = "龙凤"
  elseif A0_2 == "蝴蝶仙子" then
    L4_2 = "狮"
    L3_2 = "龙凤"
  elseif A0_2 == "白熊" then
    L4_2 = "白"
    L3_2 = "龙凤"
  elseif A0_2 == "天将" then
    L4_2 = "将"
    L3_2 = "龙凤"
  elseif A0_2 == "古代瑞兽" then
    L4_2 = "瑞"
    L3_2 = "龙凤"
  elseif A0_2 == "黑山老妖" then
    L4_2 = "黑"
    L3_2 = "龙凤"
  elseif A0_2 == "风伯" then
    L4_2 = "伯"
    L3_2 = "龙凤"
  elseif A0_2 == "天兵" then
    L4_2 = "兵"
    L3_2 = "龙凤"
  elseif A0_2 == "蛟龙" then
    L4_2 = "龙"
    L3_2 = "龙凤"
  elseif A0_2 == "地狱战神" then
    L4_2 = "战"
    L3_2 = "龙凤"
  elseif A0_2 == "凤凰" then
    L4_2 = "凤"
    L3_2 = "龙凤"
  elseif A0_2 == "雨师" then
    L4_2 = "雨"
    L3_2 = "龙凤"
  elseif A0_2 == "巡游天神" then
    L4_2 = "巡"
    L3_2 = "龙凤"
  elseif A0_2 == "芙蓉仙子" then
    L4_2 = "芙"
    L3_2 = "龙凤"
  elseif A0_2 == "如意仙子" then
    L4_2 = "如"
    L3_2 = "龙凤"
  elseif A0_2 == "星灵仙子" then
    L4_2 = "星"
    L3_2 = "龙凤"
  end
  while true do
    L5_2 = _find
    L6_2 = Color
    L6_2 = L6_2["师门"]
    L6_2 = L6_2["商会主界面"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L5_2 = _find
      L6_2 = Color
      L6_2 = L6_2["师门"]
      L6_2 = L6_2["弹出召唤兽"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = randomClick
        L6_2 = 0
        L7_2 = 300
        L8_2 = 1665
        L9_2 = 5
        L10_2 = 1689
        L11_2 = 36
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      end
      L5_2 = false
      while true do
        L6_2 = 1
        L7_2 = shfw
        L7_2 = #L7_2
        L8_2 = 1
        for L9_2 = L6_2, L7_2, L8_2 do
          L10_2 = tsOcrText
          L11_2 = _ENV["商会店铺类型字库"]
          L12_2 = shfw
          L12_2 = L12_2[L9_2]
          L12_2 = L12_2[1]
          L13_2 = shfw
          L13_2 = L13_2[L9_2]
          L13_2 = L13_2[2]
          L14_2 = shfw
          L14_2 = L14_2[L9_2]
          L14_2 = L14_2[3]
          L15_2 = shfw
          L15_2 = L15_2[L9_2]
          L15_2 = L15_2[4]
          L16_2 = "263152 , 121011 # 848283 , 06050A "
          L17_2 = 90
          L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          if L10_2 == L3_2 then
            L11_2 = randomClick
            L12_2 = 0
            L13_2 = 300
            L14_2 = shfw
            L14_2 = L14_2[L9_2]
            L14_2 = L14_2[1]
            L15_2 = shfw
            L15_2 = L15_2[L9_2]
            L15_2 = L15_2[2]
            L16_2 = shfw
            L16_2 = L16_2[L9_2]
            L16_2 = L16_2[3]
            L17_2 = shfw
            L17_2 = L17_2[L9_2]
            L17_2 = L17_2[4]
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            L11_2 = _ENV["识别店铺所售召唤兽"]
            L12_2 = L4_2
            L11_2, L12_2 = L11_2(L12_2)
            _ENV["召唤兽所在货架"] = L12_2
            L5_2 = L11_2
            if L5_2 then
              L11_2 = 1
              L12_2 = 5
              L13_2 = 1
              for L14_2 = L11_2, L12_2, L13_2 do
                L15_2 = _find
                L16_2 = Color
                L16_2 = L16_2["师门"]
                L16_2 = L16_2["商会主界面进入商店"]
                L15_2 = L15_2(L16_2)
                if L15_2 then
                end
                L15_2 = _find_cx
                L16_2 = Color
                L16_2 = L16_2["师门"]
                L16_2 = L16_2["商会店铺界面"]
                L17_2 = {}
                L18_2 = 30
                L19_2 = 60
                L17_2[1] = L18_2
                L17_2[2] = L19_2
                L15_2 = L15_2(L16_2, L17_2)
                if L15_2 then
                  L15_2 = mSleep
                  L16_2 = 350
                  L15_2(L16_2)
                  break
                end
              end
              L11_2 = 1
              L12_2 = 5
              L13_2 = 1
              for L14_2 = L11_2, L12_2, L13_2 do
                L15_2 = randomClick
                L16_2 = 0
                L17_2 = 100
                L18_2 = 239
                L19_2 = 951
                L20_2 = 330
                L21_2 = 974
                L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
                L15_2 = _find_cx
                L16_2 = Color
                L16_2 = L16_2["师门"]
                L16_2 = L16_2["商会店铺小键盘"]
                L17_2 = {}
                L18_2 = 30
                L19_2 = 30
                L17_2[1] = L18_2
                L17_2[2] = L19_2
                L15_2 = L15_2(L16_2, L17_2)
                if L15_2 then
                  break
                end
              end
              L11_2 = 1
              L12_2 = 3
              L13_2 = 1
              for L14_2 = L11_2, L12_2, L13_2 do
                L15_2 = _find
                L16_2 = Color
                L16_2 = L16_2["师门"]
                L16_2 = L16_2["商会店铺小键盘"]
                L15_2 = L15_2(L16_2)
                if L15_2 then
                  L15_2 = "X"
                  L16_2 = _ENV["召唤兽所在货架"]
                  L15_2 = L15_2 .. L16_2
                  L16_2 = randomTap
                  L17_2 = _ENV["货架"]
                  L17_2 = L17_2[L15_2]
                  L17_2 = L17_2[1]
                  L18_2 = _ENV["货架"]
                  L18_2 = L18_2[L15_2]
                  L18_2 = L18_2[2]
                  L16_2(L17_2, L18_2)
                  L16_2 = mSleep
                  L17_2 = 400
                  L16_2(L17_2)
                end
                L15_2 = _find_cx
                L16_2 = Color
                L16_2 = L16_2["师门"]
                L16_2 = L16_2["商会店铺可购买"]
                L17_2 = {}
                L18_2 = 20
                L19_2 = 30
                L17_2[1] = L18_2
                L17_2[2] = L19_2
                L15_2 = L15_2(L16_2, L17_2)
                if L15_2 then
                  break
                end
                L15_2 = _ENV["屏蔽取消"]
                L15_2()
              end
              L11_2 = false
              L12_2 = false
              L13_2 = false
              L14_2 = getWordStock
              L15_2 = PetList
              L15_2 = L15_2["低于一万"]
              L16_2 = A0_2
              L17_2 = 176
              L18_2 = 250
              L19_2 = 735
              L20_2 = 773
              L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
              y = L16_2
              x = L15_2
              L12_2 = L14_2
              if not L12_2 then
                L14_2 = _ENV["买召唤兽循环"]
                if L14_2 == 2 then
                  L14_2 = getWordStock
                  L15_2 = PetList
                  L15_2 = L15_2["超出一万"]
                  L16_2 = A0_2
                  L17_2 = 176
                  L18_2 = 250
                  L19_2 = 735
                  L20_2 = 773
                  L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                  y = L16_2
                  x = L15_2
                  L13_2 = L14_2
                end
              end
              if L12_2 then
                L14_2 = 1
                L15_2 = 8
                L16_2 = 1
                for L17_2 = L14_2, L15_2, L16_2 do
                  L18_2 = getWordStock
                  L19_2 = PetList
                  L19_2 = L19_2["低于一万"]
                  L20_2 = A0_2
                  L21_2 = _ENV["商会位置格子"]
                  L22_2 = L17_2
                  L23_2 = "号格子"
                  L22_2 = L22_2 .. L23_2
                  L21_2 = L21_2[L22_2]
                  L21_2 = L21_2[1]
                  L22_2 = _ENV["商会位置格子"]
                  L23_2 = L17_2
                  L24_2 = "号格子"
                  L23_2 = L23_2 .. L24_2
                  L22_2 = L22_2[L23_2]
                  L22_2 = L22_2[2]
                  L23_2 = _ENV["商会位置格子"]
                  L24_2 = L17_2
                  L25_2 = "号格子"
                  L24_2 = L24_2 .. L25_2
                  L23_2 = L23_2[L24_2]
                  L23_2 = L23_2[3]
                  L24_2 = _ENV["商会位置格子"]
                  L25_2 = L17_2
                  L26_2 = "号格子"
                  L25_2 = L25_2 .. L26_2
                  L24_2 = L24_2[L25_2]
                  L24_2 = L24_2[4]
                  L18_2, L19_2, L20_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
                  y1 = L20_2
                  x1 = L19_2
                  boolean1 = L18_2
                  L18_2 = boolean1
                  if L18_2 then
                    L18_2 = tap
                    L19_2 = x1
                    L20_2 = y1
                    L20_2 = L20_2 + 20
                    L18_2(L19_2, L20_2)
                    L18_2 = _ENV["_随机延时"]
                    L19_2 = 1400
                    L18_2(L19_2)
                    L18_2 = _ENV["_卡区延迟"]
                    L18_2()
                    L18_2 = _ENV["宠物特性"]
                    L18_2 = L18_2[A1_2]
                    L18_2 = L18_2()
                    if L18_2 then
                      L11_2 = true
                      break
                    else
                      L19_2 = _ENV["_随机延时"]
                      L20_2 = 400
                      L19_2(L20_2)
                      L11_2 = false
                    end
                  end
                end
              end
              if L13_2 then
                L14_2 = 1
                L15_2 = 8
                L16_2 = 1
                for L17_2 = L14_2, L15_2, L16_2 do
                  L18_2 = getWordStock
                  L19_2 = PetList
                  L19_2 = L19_2["超出一万"]
                  L20_2 = A0_2
                  L21_2 = _ENV["商会位置格子"]
                  L22_2 = L17_2
                  L23_2 = "号格子"
                  L22_2 = L22_2 .. L23_2
                  L21_2 = L21_2[L22_2]
                  L21_2 = L21_2[1]
                  L22_2 = _ENV["商会位置格子"]
                  L23_2 = L17_2
                  L24_2 = "号格子"
                  L23_2 = L23_2 .. L24_2
                  L22_2 = L22_2[L23_2]
                  L22_2 = L22_2[2]
                  L23_2 = _ENV["商会位置格子"]
                  L24_2 = L17_2
                  L25_2 = "号格子"
                  L24_2 = L24_2 .. L25_2
                  L23_2 = L23_2[L24_2]
                  L23_2 = L23_2[3]
                  L24_2 = _ENV["商会位置格子"]
                  L25_2 = L17_2
                  L26_2 = "号格子"
                  L25_2 = L25_2 .. L26_2
                  L24_2 = L24_2[L25_2]
                  L24_2 = L24_2[4]
                  L18_2, L19_2, L20_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
                  y2 = L20_2
                  x2 = L19_2
                  boolean2 = L18_2
                  L18_2 = boolean2
                  if L18_2 then
                    L18_2 = tap
                    L19_2 = x2
                    L20_2 = y2
                    L20_2 = L20_2 + 20
                    L18_2(L19_2, L20_2)
                    L18_2 = _ENV["_随机延时"]
                    L19_2 = 1400
                    L18_2(L19_2)
                    L18_2 = _ENV["_卡区延迟"]
                    L18_2()
                    L18_2 = _ENV["宠物特性"]
                    L18_2 = L18_2[A1_2]
                    L18_2 = L18_2()
                    if L18_2 then
                      L11_2 = true
                      break
                    else
                      L19_2 = _ENV["_随机延时"]
                      L20_2 = 400
                      L19_2(L20_2)
                      L11_2 = false
                    end
                  end
                end
              end
              if L11_2 and A0_2 == "黑熊" then
                L14_2 = tsFindText
                L15_2 = _ENV["黑熊精黑熊区分"]
                L16_2 = "黑熊精"
                L17_2 = 187
                L18_2 = 253
                L19_2 = 744
                L20_2 = 754
                L21_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                L22_2 = 90
                L14_2, L15_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                y11 = L15_2
                x11 = L14_2
                L14_2 = x11
                if 0 < L14_2 then
                  L11_2 = false
                  repeat
                    L14_2 = _find
                    L15_2 = Color
                    L15_2 = L15_2["师门"]
                    L15_2 = L15_2["商会店铺关闭店铺"]
                    L14_2 = L14_2(L15_2)
                    if L14_2 then
                      L14_2 = mSleep
                      L15_2 = 250
                      L14_2(L15_2)
                    end
                    L14_2 = _find
                    L15_2 = Color
                    L15_2 = L15_2["师门"]
                    L15_2 = L15_2["弹出召唤兽"]
                    L14_2 = L14_2(L15_2)
                    if L14_2 then
                      L14_2 = randomClick
                      L15_2 = 0
                      L16_2 = 300
                      L17_2 = 1665
                      L18_2 = 5
                      L19_2 = 1689
                      L20_2 = 36
                      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    end
                    L14_2 = mSleep
                    L15_2 = 250
                    L14_2(L15_2)
                    L14_2 = _find
                    L15_2 = Color
                    L15_2 = L15_2["师门"]
                    L15_2 = L15_2["商会主界面"]
                    L14_2 = L14_2(L15_2)
                  until L14_2
                  break
                end
              end
              if L11_2 then
                L14_2 = 1
                L15_2 = 2
                L16_2 = 1
                for L17_2 = L14_2, L15_2, L16_2 do
                  L18_2 = _find
                  L19_2 = Color
                  L19_2 = L19_2["师门"]
                  L19_2 = L19_2["商会店铺购买"]
                  L18_2 = L18_2(L19_2)
                  if L18_2 then
                    L18_2 = mSleep
                    L19_2 = math
                    L19_2 = L19_2.random
                    L20_2 = 222
                    L21_2 = 333
                    L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2 = L19_2(L20_2, L21_2)
                    L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                  end
                  L18_2 = _find_cx
                  L19_2 = Color
                  L19_2 = L19_2["提示框"]
                  L20_2 = {}
                  L21_2 = 30
                  L22_2 = 30
                  L20_2[1] = L21_2
                  L20_2[2] = L22_2
                  L18_2 = L18_2(L19_2, L20_2)
                  if L18_2 then
                    break
                  end
                end
                L14_2 = 1
                L15_2 = 3
                L16_2 = 1
                for L17_2 = L14_2, L15_2, L16_2 do
                  L18_2 = _find
                  L19_2 = Color
                  L19_2 = L19_2["师门"]
                  L19_2 = L19_2["商会店铺界面"]
                  L18_2 = L18_2(L19_2)
                  if L18_2 then
                    L18_2 = randomClick
                    L19_2 = 2
                    L20_2 = 300
                    L21_2 = 852
                    L22_2 = 66
                    L23_2 = 15
                    L18_2(L19_2, L20_2, L21_2, L22_2, L23_2)
                  end
                end
                L14_2 = _ENV["通用功能"]
                L14_2 = L14_2["关闭"]
                L14_2()
                L14_2 = true
                return L14_2
              end
              repeat
                L14_2 = _find
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会店铺关闭店铺"]
                L14_2 = L14_2(L15_2)
                if L14_2 then
                  L14_2 = mSleep
                  L15_2 = 250
                  L14_2(L15_2)
                end
                L14_2 = _find
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["弹出召唤兽"]
                L14_2 = L14_2(L15_2)
                if L14_2 then
                  L14_2 = randomClick
                  L15_2 = 0
                  L16_2 = 300
                  L17_2 = 1665
                  L18_2 = 5
                  L19_2 = 1689
                  L20_2 = 36
                  L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                end
                L14_2 = mSleep
                L15_2 = 250
                L14_2(L15_2)
                L14_2 = _find
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会主界面"]
                L14_2 = L14_2(L15_2)
              until L14_2
            end
          end
        end
        L6_2 = _find
        L7_2 = Color
        L7_2 = L7_2["师门"]
        L7_2 = L7_2["商会主界面下一页"]
        L6_2 = L6_2(L7_2)
        if not L6_2 then
          break
        end
        L6_2 = mSleep
        L7_2 = 1000
        L6_2(L7_2)
        L6_2 = mSleep
        L7_2 = _ENV["卡顿掉帧"]
        L6_2(L7_2)
      end
    end
  end
end

_ENV["百级龟速识别宠物"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L1_2 = ""
  L2_2 = _ENV["商会不买前几页"]
  if L2_2 == "0" then
  else
    L2_2 = tonumber
    L3_2 = _ENV["商会不买前几页"]
    L2_2 = L2_2(L3_2)
    L1_2 = L2_2
    L2_2 = 1
    L3_2 = L1_2
    L4_2 = 1
    for L5_2 = L2_2, L3_2, L4_2 do
      L6_2 = randomClick
      L7_2 = 2
      L8_2 = 888
      L9_2 = 600
      L10_2 = 970
      L6_2(L7_2, L8_2, L9_2, L10_2)
      L6_2 = mSleep
      L7_2 = 300
      L6_2(L7_2)
    end
  end
  L2_2 = ""
  L3_2 = ""
  if A0_2 == "大海龟" then
    L3_2 = "龟"
    L2_2 = "低级"
  elseif A0_2 == "巨蛙" then
    L3_2 = "蛙"
    L2_2 = "低级"
  elseif A0_2 == "野猪" then
    L3_2 = "猪"
    L2_2 = "低级"
  elseif A0_2 == "树怪" then
    L3_2 = "树"
    L2_2 = "低级"
  elseif A0_2 == "强盗" then
    L3_2 = "强"
    L2_2 = "低级"
  elseif A0_2 == "赌徒" then
    L3_2 = "赌"
    L2_2 = "低级"
  elseif A0_2 == "山贼" then
    L3_2 = "贼"
    L2_2 = "低级"
  elseif A0_2 == "老虎" then
    L3_2 = "虎"
    L2_2 = "低级"
  elseif A0_2 == "黑熊" then
    L3_2 = "熊"
    L2_2 = "低级"
  elseif A0_2 == "花妖" then
    L3_2 = "花"
    L2_2 = "低级"
  elseif A0_2 == "羊头怪" then
    L3_2 = "羊"
    L2_2 = "低级"
  elseif A0_2 == "蛤蟆精" then
    L3_2 = "蛤"
    L2_2 = "低级"
  elseif A0_2 == "骷髅怪" then
    L3_2 = "骷"
    L2_2 = "低级"
  elseif A0_2 == "狐狸精" then
    L3_2 = "狐"
    L2_2 = "低级"
  elseif A0_2 == "狼" then
    L3_2 = "狼"
    L2_2 = "中级"
  elseif A0_2 == "小龙女" then
    L3_2 = "女"
    L2_2 = "中级"
  elseif A0_2 == "虾兵" then
    L3_2 = "虾"
    L2_2 = "中级"
  elseif A0_2 == "蟹将" then
    L3_2 = "蟹"
    L2_2 = "中级"
  elseif A0_2 == "龟丞相" then
    L3_2 = "丞"
    L2_2 = "中级"
  elseif A0_2 == "牛头" then
    L3_2 = "牛"
    L2_2 = "中级"
  elseif A0_2 == "马面" then
    L3_2 = "马"
    L2_2 = "中级"
  elseif A0_2 == "牛妖" then
    L3_2 = "妖"
    L2_2 = "中级"
  elseif A0_2 == "僵尸" then
    L3_2 = "僵"
    L2_2 = "中级"
  elseif A0_2 == "野鬼" then
    L3_2 = "野"
    L2_2 = "中级"
  elseif A0_2 == "兔子怪" then
    L3_2 = "兔"
    L2_2 = "中级"
  elseif A0_2 == "蜘蛛精" then
    L3_2 = "蜘"
    L2_2 = "中级"
  elseif A0_2 == "黑熊精" then
    L3_2 = "熊"
    L2_2 = "中级"
  end
  while true do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["师门"]
    L5_2 = L5_2["商会主界面"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = false
      while true do
        L5_2 = 1
        L6_2 = shfw
        L6_2 = #L6_2
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = tsOcrText
          L10_2 = _ENV["商会店铺类型字库"]
          L11_2 = shfw
          L11_2 = L11_2[L8_2]
          L11_2 = L11_2[1]
          L12_2 = shfw
          L12_2 = L12_2[L8_2]
          L12_2 = L12_2[2]
          L13_2 = shfw
          L13_2 = L13_2[L8_2]
          L13_2 = L13_2[3]
          L14_2 = shfw
          L14_2 = L14_2[L8_2]
          L14_2 = L14_2[4]
          L15_2 = "263152 , 121011 # 848283 , 06050A "
          L16_2 = 90
          L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          if L9_2 == L2_2 then
            L10_2 = randomClick
            L11_2 = 0
            L12_2 = 300
            L13_2 = shfw
            L13_2 = L13_2[L8_2]
            L13_2 = L13_2[1]
            L14_2 = shfw
            L14_2 = L14_2[L8_2]
            L14_2 = L14_2[2]
            L15_2 = shfw
            L15_2 = L15_2[L8_2]
            L15_2 = L15_2[3]
            L16_2 = shfw
            L16_2 = L16_2[L8_2]
            L16_2 = L16_2[4]
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            L10_2 = _ENV["识别店铺所售召唤兽"]
            L11_2 = L3_2
            L10_2, L11_2 = L10_2(L11_2)
            _ENV["召唤兽所在货架"] = L11_2
            L4_2 = L10_2
            if L4_2 then
              L10_2 = 1
              L11_2 = 5
              L12_2 = 1
              for L13_2 = L10_2, L11_2, L12_2 do
                L14_2 = _find
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会主界面进入商店"]
                L14_2 = L14_2(L15_2)
                if L14_2 then
                end
                L14_2 = _find_cx
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会店铺界面"]
                L16_2 = {}
                L17_2 = 30
                L18_2 = 60
                L16_2[1] = L17_2
                L16_2[2] = L18_2
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  L14_2 = mSleep
                  L15_2 = 350
                  L14_2(L15_2)
                  break
                end
              end
              L10_2 = 1
              L11_2 = 5
              L12_2 = 1
              for L13_2 = L10_2, L11_2, L12_2 do
                L14_2 = randomClick
                L15_2 = 0
                L16_2 = 100
                L17_2 = 239
                L18_2 = 951
                L19_2 = 330
                L20_2 = 974
                L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                L14_2 = _find_cx
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会店铺小键盘"]
                L16_2 = {}
                L17_2 = 30
                L18_2 = 30
                L16_2[1] = L17_2
                L16_2[2] = L18_2
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  break
                end
              end
              L10_2 = 1
              L11_2 = 3
              L12_2 = 1
              for L13_2 = L10_2, L11_2, L12_2 do
                L14_2 = _find
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会店铺小键盘"]
                L14_2 = L14_2(L15_2)
                if L14_2 then
                  L14_2 = "X"
                  L15_2 = _ENV["召唤兽所在货架"]
                  L14_2 = L14_2 .. L15_2
                  L15_2 = randomTap
                  L16_2 = _ENV["货架"]
                  L16_2 = L16_2[L14_2]
                  L16_2 = L16_2[1]
                  L17_2 = _ENV["货架"]
                  L17_2 = L17_2[L14_2]
                  L17_2 = L17_2[2]
                  L15_2(L16_2, L17_2)
                  L15_2 = mSleep
                  L16_2 = 400
                  L15_2(L16_2)
                end
                L14_2 = _find_cx
                L15_2 = Color
                L15_2 = L15_2["师门"]
                L15_2 = L15_2["商会店铺可购买"]
                L16_2 = {}
                L17_2 = 20
                L18_2 = 30
                L16_2[1] = L17_2
                L16_2[2] = L18_2
                L14_2 = L14_2(L15_2, L16_2)
                if L14_2 then
                  break
                end
                L14_2 = _ENV["屏蔽取消"]
                L14_2()
              end
              L10_2 = false
              L11_2 = 1
              L12_2 = _ENV["买召唤兽循环"]
              L13_2 = 1
              for L14_2 = L11_2, L12_2, L13_2 do
                if 1 < L14_2 then
                  L15_2 = getWordStock
                  L16_2 = PetList
                  L16_2 = L16_2["超出一万"]
                  L17_2 = A0_2
                  L18_2 = 176
                  L19_2 = 250
                  L20_2 = 735
                  L21_2 = 773
                  L15_2, L16_2, L17_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
                  y = L17_2
                  x = L16_2
                  L10_2 = L15_2
                else
                  L15_2 = getWordStock
                  L16_2 = PetList
                  L16_2 = L16_2["低于一万"]
                  L17_2 = A0_2
                  L18_2 = 176
                  L19_2 = 250
                  L20_2 = 735
                  L21_2 = 773
                  L15_2, L16_2, L17_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
                  y = L17_2
                  x = L16_2
                  L10_2 = L15_2
                end
                if L10_2 and A0_2 == "黑熊" then
                  L15_2 = tsFindText
                  L16_2 = _ENV["黑熊精黑熊区分"]
                  L17_2 = "黑熊精"
                  L18_2 = 187
                  L19_2 = 253
                  L20_2 = 744
                  L21_2 = 754
                  L22_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                  L23_2 = 90
                  L15_2, L16_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
                  y11 = L16_2
                  x11 = L15_2
                  L15_2 = x11
                  if 0 < L15_2 then
                    L10_2 = false
                    repeat
                      L15_2 = _find
                      L16_2 = Color
                      L16_2 = L16_2["师门"]
                      L16_2 = L16_2["商会店铺关闭店铺"]
                      L15_2 = L15_2(L16_2)
                      if L15_2 then
                        L15_2 = mSleep
                        L16_2 = 250
                        L15_2(L16_2)
                      end
                      L15_2 = mSleep
                      L16_2 = 250
                      L15_2(L16_2)
                      L15_2 = _find
                      L16_2 = Color
                      L16_2 = L16_2["师门"]
                      L16_2 = L16_2["商会主界面"]
                      L15_2 = L15_2(L16_2)
                    until L15_2
                    break
                  end
                end
                if L10_2 then
                  L15_2 = randomClick
                  L16_2 = 2
                  L17_2 = 150
                  L18_2 = x
                  L19_2 = y
                  L19_2 = L19_2 + 20
                  L20_2 = 20
                  L15_2(L16_2, L17_2, L18_2, L19_2, L20_2)
                  L15_2 = randomClick
                  L16_2 = 2
                  L17_2 = 330
                  L18_2 = x
                  L19_2 = y
                  L19_2 = L19_2 + 20
                  L20_2 = 20
                  L15_2(L16_2, L17_2, L18_2, L19_2, L20_2)
                  L15_2 = 1
                  L16_2 = 2
                  L17_2 = 1
                  for L18_2 = L15_2, L16_2, L17_2 do
                    L19_2 = _find
                    L20_2 = Color
                    L20_2 = L20_2["师门"]
                    L20_2 = L20_2["商会店铺购买"]
                    L19_2 = L19_2(L20_2)
                    if L19_2 then
                      L19_2 = mSleep
                      L20_2 = math
                      L20_2 = L20_2.random
                      L21_2 = 222
                      L22_2 = 333
                      L20_2, L21_2, L22_2, L23_2, L24_2, L25_2 = L20_2(L21_2, L22_2)
                      L19_2(L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                    end
                    L19_2 = _find_cx
                    L20_2 = Color
                    L20_2 = L20_2["提示框"]
                    L21_2 = {}
                    L22_2 = 20
                    L23_2 = 30
                    L21_2[1] = L22_2
                    L21_2[2] = L23_2
                    L19_2 = L19_2(L20_2, L21_2)
                    if L19_2 then
                      break
                    end
                  end
                  L15_2 = 1
                  L16_2 = 2
                  L17_2 = 1
                  for L18_2 = L15_2, L16_2, L17_2 do
                    L19_2 = _find
                    L20_2 = Color
                    L20_2 = L20_2["师门"]
                    L20_2 = L20_2["商会店铺界面"]
                    L19_2 = L19_2(L20_2)
                    if L19_2 then
                      L19_2 = randomClick
                      L20_2 = 2
                      L21_2 = 300
                      L22_2 = 852
                      L23_2 = 66
                      L24_2 = 15
                      L19_2(L20_2, L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                  L15_2 = _ENV["通用功能"]
                  L15_2 = L15_2["关闭"]
                  L15_2()
                  L15_2 = true
                  return L15_2
                end
                L15_2 = _ENV["买召唤兽循环"]
                if L14_2 == L15_2 then
                  repeat
                    L15_2 = _find
                    L16_2 = Color
                    L16_2 = L16_2["师门"]
                    L16_2 = L16_2["商会店铺关闭店铺"]
                    L15_2 = L15_2(L16_2)
                    if L15_2 then
                      L15_2 = mSleep
                      L16_2 = 250
                      L15_2(L16_2)
                    end
                    L15_2 = mSleep
                    L16_2 = 250
                    L15_2(L16_2)
                    L15_2 = _find
                    L16_2 = Color
                    L16_2 = L16_2["师门"]
                    L16_2 = L16_2["商会主界面"]
                    L15_2 = L15_2(L16_2)
                  until L15_2
                end
              end
            end
          end
        end
        L5_2 = _find
        L6_2 = Color
        L6_2 = L6_2["师门"]
        L6_2 = L6_2["商会主界面下一页"]
        L5_2 = L5_2(L6_2)
        if not L5_2 then
          break
        end
        L5_2 = mSleep
        L6_2 = 200
        L5_2(L6_2)
      end
    end
  end
end

_ENV["低级龟速识别"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2
  L1_2 = 0
  L2_2 = {}
  L3_2 = {}
  L4_2 = 1129
  L5_2 = 488
  L6_2 = 1249
  L7_2 = 527
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L2_2["二药"] = L3_2
  L3_2 = {}
  L4_2 = 1124
  L5_2 = 385
  L6_2 = 1252
  L7_2 = 428
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L2_2["烹饪"] = L3_2
  L3_2 = {}
  L4_2 = 1394
  L5_2 = 288
  L6_2 = 1530
  L7_2 = 327
  L8_2 = 1363
  L9_2 = 776
  L10_2 = 1528
  L11_2 = 813
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L3_2[5] = L8_2
  L3_2[6] = L9_2
  L3_2[7] = L10_2
  L3_2[8] = L11_2
  L2_2["宠物"] = L3_2
  L3_2 = A0_2
  while true do
    L4_2 = printLog
    L5_2 = "转换名店龟速查找:"
    L6_2 = A0_2
    L5_2 = L5_2 .. L6_2
    L4_2(L5_2)
    if L1_2 == 0 then
      L4_2 = pairs
      L5_2 = BuyToType
      L4_2, L5_2, L6_2 = L4_2(L5_2)
      for L7_2, L8_2 in L4_2, L5_2, L6_2 do
        L9_2 = 1
        L10_2 = #L8_2
        L11_2 = 1
        for L12_2 = L9_2, L10_2, L11_2 do
          L13_2 = L8_2[L12_2]
          if A0_2 == L13_2 then
            L3_2 = L7_2
          end
        end
      end
      L4_2 = randomClick
      L5_2 = 0
      L6_2 = 500
      L7_2 = 1115
      L8_2 = 155
      L9_2 = 1238
      L10_2 = 185
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      if L3_2 == "二药" or L3_2 == "烹饪" then
        L4_2 = randomClick
        L5_2 = 0
        L6_2 = 500
        L7_2 = L2_2[L3_2]
        L7_2 = L7_2[1]
        L8_2 = L2_2[L3_2]
        L8_2 = L8_2[2]
        L9_2 = L2_2[L3_2]
        L9_2 = L9_2[3]
        L10_2 = L2_2[L3_2]
        L10_2 = L10_2[4]
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      else
        L4_2 = randomClick
        L5_2 = 0
        L6_2 = 500
        L7_2 = L2_2["宠物"]
        L7_2 = L7_2[1]
        L8_2 = L2_2["宠物"]
        L8_2 = L8_2[2]
        L9_2 = L2_2["宠物"]
        L9_2 = L9_2[3]
        L10_2 = L2_2["宠物"]
        L10_2 = L10_2[4]
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
        L4_2 = randomClick
        L5_2 = 0
        L6_2 = 500
        L7_2 = L2_2["宠物"]
        L7_2 = L7_2[5]
        L8_2 = L2_2["宠物"]
        L8_2 = L8_2[6]
        L9_2 = L2_2["宠物"]
        L9_2 = L9_2[7]
        L10_2 = L2_2["宠物"]
        L10_2 = L10_2[8]
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      end
      L1_2 = 1
    elseif L1_2 == 1 then
      L4_2 = {}
      L5_2 = {}
      L6_2 = 394
      L7_2 = 341
      L8_2 = 498
      L9_2 = 425
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L5_2[3] = L8_2
      L5_2[4] = L9_2
      L6_2 = {}
      L7_2 = 968
      L8_2 = 342
      L9_2 = 1066
      L10_2 = 427
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L6_2[3] = L9_2
      L6_2[4] = L10_2
      L7_2 = {}
      L8_2 = 422
      L9_2 = 495
      L10_2 = 523
      L11_2 = 580
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L7_2[3] = L10_2
      L7_2[4] = L11_2
      L8_2 = {}
      L9_2 = 956
      L10_2 = 500
      L11_2 = 1038
      L12_2 = 580
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L8_2[3] = L11_2
      L8_2[4] = L12_2
      L9_2 = {}
      L10_2 = 398
      L11_2 = 641
      L12_2 = 504
      L13_2 = 733
      L9_2[1] = L10_2
      L9_2[2] = L11_2
      L9_2[3] = L12_2
      L9_2[4] = L13_2
      L10_2 = {}
      L11_2 = 964
      L12_2 = 633
      L13_2 = 1065
      L14_2 = 735
      L10_2[1] = L11_2
      L10_2[2] = L12_2
      L10_2[3] = L13_2
      L10_2[4] = L14_2
      L11_2 = {}
      L12_2 = 377
      L13_2 = 784
      L14_2 = 544
      L15_2 = 880
      L11_2[1] = L12_2
      L11_2[2] = L13_2
      L11_2[3] = L14_2
      L11_2[4] = L15_2
      L12_2 = {}
      L13_2 = 938
      L14_2 = 789
      L15_2 = 1052
      L16_2 = 879
      L12_2[1] = L13_2
      L12_2[2] = L14_2
      L12_2[3] = L15_2
      L12_2[4] = L16_2
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L4_2[3] = L7_2
      L4_2[4] = L8_2
      L4_2[5] = L9_2
      L4_2[6] = L10_2
      L4_2[7] = L11_2
      L4_2[8] = L12_2
      L5_2 = 1
      L6_2 = #L4_2
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = true
        while true do
          L10_2 = getColour
          L11_2 = colorList
          L12_2 = "商会店铺"
          L10_2 = L10_2(L11_2, L12_2)
          if L10_2 then
            L10_2 = 1
            L11_2 = 10
            L12_2 = 1
            for L13_2 = L10_2, L11_2, L12_2 do
              L14_2 = false
              L15_2 = -1
              L16_2 = -1
              L17_2 = 1
              L18_2 = 4
              L19_2 = 1
              for L20_2 = L17_2, L18_2, L19_2 do
                if L3_2 == "二药" or L3_2 == "烹饪" then
                  if A0_2 == "豆斋果" or A0_2 == "醉生梦死" or A0_2 == "长寿面" or A0_2 == "佛跳墙" or A0_2 == "烤鸭" or A0_2 == "珍露酒" or A0_2 == "臭豆腐" or A0_2 == "烤肉" or A0_2 == "桂花丸" or A0_2 == "翡翠豆腐" or A0_2 == "梅花酒" or A0_2 == "百味酒" or A0_2 == "蛇胆酒" then
                    L21_2 = _ENV["烹饪价格"]
                    if L21_2 ~= "" then
                      L21_2 = _ENV["烹饪价格"]
                      if L21_2 ~= nil then
                        goto lbl_213
                      end
                    end
                    _ENV["烹饪价格"] = 99999
                    ::lbl_213::
                    L21_2 = _ENV["价格筛选"]
                    L22_2 = tonumber
                    L23_2 = _ENV["烹饪价格"]
                    L22_2 = L22_2(L23_2)
                    L23_2 = A0_2
                    L21_2, L22_2, L23_2 = L21_2(L22_2, L23_2)
                    L16_2 = L23_2
                    L15_2 = L22_2
                    L14_2 = L21_2
                    if not L14_2 then
                      break
                    end
                  else
                    L21_2 = getMultiColor
                    L22_2 = propsList
                    L23_2 = A0_2
                    L24_2 = 85
                    L25_2 = 117
                    L26_2 = 224
                    L27_2 = 803
                    L28_2 = 752
                    L21_2, L22_2, L23_2 = L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                    L16_2 = L23_2
                    L15_2 = L22_2
                    L14_2 = L21_2
                  end
                else
                  if 2 < L20_2 then
                    L21_2 = getWordStock
                    L22_2 = PetList
                    L22_2 = L22_2["超出一万"]
                    L23_2 = A0_2
                    L24_2 = 176
                    L25_2 = 250
                    L26_2 = 735
                    L27_2 = 773
                    L21_2, L22_2, L23_2 = L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                    L16_2 = L23_2
                    L15_2 = L22_2
                    L14_2 = L21_2
                  else
                    L21_2 = getWordStock
                    L22_2 = PetList
                    L22_2 = L22_2["低于一万"]
                    L23_2 = A0_2
                    L24_2 = 176
                    L25_2 = 250
                    L26_2 = 735
                    L27_2 = 773
                    L21_2, L22_2, L23_2 = L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                    L16_2 = L23_2
                    L15_2 = L22_2
                    L14_2 = L21_2
                  end
                  if L14_2 and A0_2 == "黑熊" then
                    L21_2 = tsFindText
                    L22_2 = _ENV["黑熊精黑熊区分"]
                    L23_2 = "黑熊精"
                    L24_2 = 187
                    L25_2 = 253
                    L26_2 = 744
                    L27_2 = 754
                    L28_2 = "201F27 , 1F1E26# 1E1DFF , 1D1C07"
                    L29_2 = 90
                    L21_2, L22_2 = L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2)
                    y11 = L22_2
                    x11 = L21_2
                    L21_2 = x11
                    if 0 < L21_2 then
                      L14_2 = false
                      repeat
                        L21_2 = _find
                        L22_2 = Color
                        L22_2 = L22_2["师门"]
                        L22_2 = L22_2["商会店铺关闭店铺"]
                        L21_2 = L21_2(L22_2)
                        if L21_2 then
                          L21_2 = mSleep
                          L22_2 = 250
                          L21_2(L22_2)
                        end
                        L21_2 = mSleep
                        L22_2 = 250
                        L21_2(L22_2)
                        L21_2 = _find
                        L22_2 = Color
                        L22_2 = L22_2["师门"]
                        L22_2 = L22_2["商会主界面"]
                        L21_2 = L21_2(L22_2)
                      until L21_2
                      break
                    end
                  end
                end
                if L14_2 then
                  L21_2 = true
                  L22_2 = 0
                  L23_2 = false
                  while true do
                    L24_2 = getColour
                    L25_2 = colorList
                    L26_2 = "提示信息"
                    L24_2 = L24_2(L25_2, L26_2)
                    if L24_2 or L23_2 then
                      L24_2 = printLog
                      L25_2 = "完成购买:"
                      L26_2 = A0_2
                      L25_2 = L25_2 .. L26_2
                      L24_2(L25_2)
                      while true do
                        L24_2 = getColour
                        L25_2 = colorList
                        L26_2 = "商会店铺"
                        L24_2 = L24_2(L25_2, L26_2)
                        if L24_2 then
                          L24_2 = randomClick
                          L25_2 = 2
                          L26_2 = 100
                          L27_2 = 848
                          L28_2 = 49
                          L24_2(L25_2, L26_2, L27_2, L28_2)
                        else
                          L24_2 = getColour
                          L25_2 = colorList
                          L26_2 = "商会列表"
                          L24_2 = L24_2(L25_2, L26_2)
                          if L24_2 then
                            L24_2 = randomClick
                            L25_2 = 2
                            L26_2 = 300
                            L27_2 = 1705
                            L28_2 = 66
                            L24_2(L25_2, L26_2, L27_2, L28_2)
                          else
                            L24_2 = getColour
                            L25_2 = colorList
                            L26_2 = "宠物属性页面"
                            L24_2 = L24_2(L25_2, L26_2)
                            if L24_2 then
                              L24_2 = randomClick
                              L25_2 = 2
                              L26_2 = 300
                              L27_2 = 1705
                              L28_2 = 76
                              L29_2 = 20
                              L24_2(L25_2, L26_2, L27_2, L28_2, L29_2)
                            else
                              L24_2 = getColour
                              L25_2 = colorList
                              L26_2 = "人物属性页面"
                              L24_2 = L24_2(L25_2, L26_2)
                              if L24_2 then
                                L24_2 = randomClick
                                L25_2 = 2
                                L26_2 = 300
                                L27_2 = 1603
                                L28_2 = 86
                                L24_2(L25_2, L26_2, L27_2, L28_2)
                              else
                                L24_2 = getColour
                                L25_2 = colorList
                                L26_2 = "动作按钮"
                                L24_2 = L24_2(L25_2, L26_2)
                                if not L24_2 then
                                  L24_2 = getColour
                                  L25_2 = colorList
                                  L26_2 = "设置按钮"
                                  L24_2 = L24_2(L25_2, L26_2)
                                  if not L24_2 then
                                    L24_2 = getColour
                                    L25_2 = colorList
                                    L26_2 = "道具按钮"
                                    L24_2 = L24_2(L25_2, L26_2)
                                    if not L24_2 then
                                      goto lbl_404
                                    end
                                  end
                                end
                                if L3_2 == "二药" or L3_2 == "烹饪" then
                                  L24_2 = false
                                  return L24_2
                                else
                                  L24_2 = true
                                  return L24_2
                                end
                              end
                            end
                          end
                        end
                        ::lbl_404::
                      end
                    elseif L21_2 then
                      L21_2 = false
                      L24_2 = os
                      L24_2 = L24_2.time
                      L24_2 = L24_2()
                      L22_2 = L24_2
                      L24_2 = randomClick
                      L25_2 = 2
                      L26_2 = 300
                      L27_2 = L15_2
                      L28_2 = L16_2 + 20
                      L29_2 = 20
                      L24_2(L25_2, L26_2, L27_2, L28_2, L29_2)
                      L24_2 = randomClick
                      L25_2 = 0
                      L26_2 = 300
                      L27_2 = 627
                      L28_2 = 944
                      L29_2 = 779
                      L30_2 = 991
                      L24_2(L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
                      L23_2 = true
                    else
                      L24_2 = os
                      L24_2 = L24_2.time
                      L24_2 = L24_2()
                      L24_2 = L24_2 - L22_2
                      if 3 < L24_2 and L22_2 ~= 0 then
                        L21_2 = true
                      end
                    end
                  end
                else
                  L21_2 = mSleep
                  L22_2 = 30
                  L21_2(L22_2)
                end
              end
              L17_2 = randomClick
              L18_2 = 2
              L19_2 = 1000
              L20_2 = 445
              L21_2 = 962
              L17_2(L18_2, L19_2, L20_2, L21_2)
            end
            while true do
              L10_2 = getColour
              L11_2 = colorList
              L12_2 = "商会列表"
              L10_2 = L10_2(L11_2, L12_2)
              if L10_2 then
                goto lbl_497
              end
              L10_2 = getColour
              L11_2 = colorList
              L12_2 = "商会店铺"
              L10_2 = L10_2(L11_2, L12_2)
              if L10_2 then
                L10_2 = randomClick
                L11_2 = 2
                L12_2 = 200
                L13_2 = 848
                L14_2 = 49
                L10_2(L11_2, L12_2, L13_2, L14_2)
              end
            end
            break
          elseif L9_2 then
            L9_2 = false
            L10_2 = randomClick
            L11_2 = 0
            L12_2 = 300
            L13_2 = L4_2[L8_2]
            L13_2 = L13_2[1]
            L14_2 = L4_2[L8_2]
            L14_2 = L14_2[2]
            L15_2 = L4_2[L8_2]
            L15_2 = L15_2[3]
            L16_2 = L4_2[L8_2]
            L16_2 = L16_2[4]
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            L10_2 = randomClick
            L11_2 = 0
            L12_2 = 888
            L13_2 = 1486
            L14_2 = 943
            L15_2 = 1683
            L16_2 = 1000
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          end
        end
        ::lbl_497::
      end
      L5_2 = randomClick
      L6_2 = 2
      L7_2 = 888
      L8_2 = 600
      L9_2 = 970
      L5_2(L6_2, L7_2, L8_2, L9_2)
    end
  end
end

_ENV["原低级龟速识别"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2
  L3_2 = false
  L4_2 = A1_2
  L5_2 = pairs
  L6_2 = BuyToType
  L5_2, L6_2, L7_2 = L5_2(L6_2)
  for L8_2, L9_2 in L5_2, L6_2, L7_2 do
    L10_2 = 1
    L11_2 = #L9_2
    L12_2 = 1
    for L13_2 = L10_2, L11_2, L12_2 do
      L14_2 = L9_2[L13_2]
      if A1_2 == L14_2 then
        L4_2 = L8_2
      end
    end
  end
  L5_2 = strSplit
  L6_2 = A0_2
  L7_2 = "+"
  L5_2 = L5_2(L6_2, L7_2)
  L6_2 = 1
  L7_2 = #L5_2
  L8_2 = 1
  for L9_2 = L6_2, L7_2, L8_2 do
    L10_2 = 0
    L11_2 = strSplit
    L12_2 = L5_2[L9_2]
    L13_2 = "#"
    L11_2 = L11_2(L12_2, L13_2)
    L12_2 = 1
    L13_2 = #L4_2
    L13_2 = L13_2 + 1
    L14_2 = 1
    L15_2 = L11_2[2]
    L15_2 = #L15_2
    L16_2 = 1
    for L17_2 = L14_2, L15_2, L16_2 do
      L18_2 = true
      L19_2 = string
      L19_2 = L19_2.sub
      L20_2 = L11_2[2]
      L21_2 = L12_2
      L22_2 = L13_2
      L19_2 = L19_2(L20_2, L21_2, L22_2)
      L12_2 = L12_2 + 1
      L13_2 = L13_2 + 1
      if A1_2 == "黑熊" then
        L20_2 = string
        L20_2 = L20_2.find
        L21_2 = L19_2
        L22_2 = L4_2
        L20_2 = L20_2(L21_2, L22_2)
        if L20_2 then
          L20_2 = string
          L20_2 = L20_2.sub
          L21_2 = L11_2[2]
          L22_2 = L12_2 - 1
          L23_2 = L13_2 + 2
          L20_2 = L20_2(L21_2, L22_2, L23_2)
          L21_2 = string
          L21_2 = L21_2.find
          L22_2 = L20_2
          L23_2 = "黑熊精"
          L21_2 = L21_2(L22_2, L23_2)
          if L21_2 then
            L18_2 = false
          end
        end
      end
      if L17_2 >= L10_2 then
        L20_2 = string
        L20_2 = L20_2.find
        L21_2 = L19_2
        L22_2 = L4_2
        L20_2 = L20_2(L21_2, L22_2)
        if L20_2 and L10_2 ~= -1 and L18_2 then
          L20_2 = printLog
          L21_2 = "获取商品信息:"
          L22_2 = L19_2
          L21_2 = L21_2 .. L22_2
          L20_2(L21_2)
          L20_2 = true
          L21_2 = false
          L22_2 = 0
          L23_2 = tonumber
          L24_2 = string
          L24_2 = L24_2.sub
          L25_2 = L11_2[2]
          L26_2 = L12_2 - 1
          L27_2 = L12_2 - 1
          L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2 = L24_2(L25_2, L26_2, L27_2)
          L23_2 = L23_2(L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
          if L23_2 == nil then
            break
          end
          while true do
            L24_2 = _ENV["弹窗查看详情"]
            L24_2()
            if L21_2 then
              break
            end
            if L3_2 then
              L24_2 = printLog
              L25_2 = "退出商会"
              L24_2(L25_2)
              L24_2 = getColour
              L25_2 = colorList
              L26_2 = "商会店铺"
              L24_2 = L24_2(L25_2, L26_2)
              if L24_2 then
                L24_2 = randomClick
                L25_2 = 2
                L26_2 = 100
                L27_2 = 848
                L28_2 = 49
                L24_2(L25_2, L26_2, L27_2, L28_2)
              else
                L24_2 = getColour
                L25_2 = colorList
                L26_2 = "商会列表"
                L24_2 = L24_2(L25_2, L26_2)
                if L24_2 then
                  L24_2 = randomClick
                  L25_2 = 2
                  L26_2 = 300
                  L27_2 = 1705
                  L28_2 = 66
                  L24_2(L25_2, L26_2, L27_2, L28_2)
                else
                  L24_2 = getColour
                  L25_2 = colorList
                  L26_2 = "宠物属性页面"
                  L24_2 = L24_2(L25_2, L26_2)
                  if L24_2 then
                    L24_2 = randomClick
                    L25_2 = 2
                    L26_2 = 300
                    L27_2 = 1705
                    L28_2 = 76
                    L29_2 = 20
                    L24_2(L25_2, L26_2, L27_2, L28_2, L29_2)
                  else
                    L24_2 = getColour
                    L25_2 = colorList
                    L26_2 = "召唤兽购买"
                    L24_2 = L24_2(L25_2, L26_2)
                    if L24_2 then
                      L24_2 = randomClick
                      L25_2 = 2
                      L26_2 = 1300
                      L27_2 = 1670
                      L28_2 = 46
                      L24_2(L25_2, L26_2, L27_2, L28_2)
                    else
                      L24_2 = _find
                      L25_2 = Color
                      L25_2 = L25_2["师门"]
                      L25_2 = L25_2["弹出成就对话框"]
                      L24_2 = L24_2(L25_2)
                      if L24_2 then
                        L24_2 = _ENV["通用功能"]
                        L24_2 = L24_2["关闭"]
                        L24_2()
                      else
                        L24_2 = getColour
                        L25_2 = colorList
                        L26_2 = "人物属性页面"
                        L24_2 = L24_2(L25_2, L26_2)
                        if L24_2 then
                          L24_2 = randomClick
                          L25_2 = 2
                          L26_2 = 300
                          L27_2 = 1603
                          L28_2 = 86
                          L24_2(L25_2, L26_2, L27_2, L28_2)
                        else
                          L24_2 = getColour
                          L25_2 = colorList
                          L26_2 = "动作按钮"
                          L24_2 = L24_2(L25_2, L26_2)
                          if not L24_2 then
                            L24_2 = getColour
                            L25_2 = colorList
                            L26_2 = "设置按钮"
                            L24_2 = L24_2(L25_2, L26_2)
                            if not L24_2 then
                              L24_2 = getColour
                              L25_2 = colorList
                              L26_2 = "道具按钮"
                              L24_2 = L24_2(L25_2, L26_2)
                            end
                          end
                          if L24_2 then
                            L24_2 = printLog
                            L25_2 = "返回师门"
                            L24_2(L25_2)
                            if A2_2 == "物品" then
                              L24_2 = false
                              return L24_2
                            else
                              L24_2 = true
                              return L24_2
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            else
              if L20_2 then
                L24_2 = getColour
                L25_2 = colorList
                L26_2 = "号码键盘"
                L24_2 = L24_2(L25_2, L26_2)
                if L24_2 then
                  L24_2 = {}
                  L25_2 = {}
                  L26_2 = 562
                  L27_2 = 337
                  L25_2[1] = L26_2
                  L25_2[2] = L27_2
                  L26_2 = {}
                  L27_2 = 718
                  L28_2 = 337
                  L26_2[1] = L27_2
                  L26_2[2] = L28_2
                  L27_2 = {}
                  L28_2 = 866
                  L29_2 = 335
                  L27_2[1] = L28_2
                  L27_2[2] = L29_2
                  L28_2 = {}
                  L29_2 = 563
                  L30_2 = 485
                  L28_2[1] = L29_2
                  L28_2[2] = L30_2
                  L29_2 = {}
                  L30_2 = 714
                  L31_2 = 487
                  L29_2[1] = L30_2
                  L29_2[2] = L31_2
                  L30_2 = {}
                  L31_2 = 866
                  L32_2 = 486
                  L30_2[1] = L31_2
                  L30_2[2] = L32_2
                  L31_2 = {}
                  L32_2 = 564
                  L33_2 = 632
                  L31_2[1] = L32_2
                  L31_2[2] = L33_2
                  L32_2 = {}
                  L33_2 = 716
                  L34_2 = 632
                  L32_2[1] = L33_2
                  L32_2[2] = L34_2
                  L33_2 = {}
                  L34_2 = 868
                  L35_2 = 634
                  L33_2[1] = L34_2
                  L33_2[2] = L35_2
                  L34_2 = {}
                  L35_2 = 1020
                  L36_2 = 485
                  L34_2[1] = L35_2
                  L34_2[2] = L36_2
                  L24_2[1] = L25_2
                  L24_2[2] = L26_2
                  L24_2[3] = L27_2
                  L24_2[4] = L28_2
                  L24_2[5] = L29_2
                  L24_2[6] = L30_2
                  L24_2[7] = L31_2
                  L24_2[8] = L32_2
                  L24_2[9] = L33_2
                  L24_2[10] = L34_2
                  L25_2 = L11_2[1]
                  L26_2 = 1
                  L27_2 = #L25_2
                  L28_2 = 1
                  for L29_2 = L26_2, L27_2, L28_2 do
                    L30_2 = tonumber
                    L31_2 = string
                    L31_2 = L31_2.sub
                    L32_2 = L25_2
                    L33_2 = L29_2
                    L34_2 = L29_2
                    L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2 = L31_2(L32_2, L33_2, L34_2)
                    L30_2 = L30_2(L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
                    if L30_2 == 0 then
                      L30_2 = 10
                    end
                    L31_2 = 1
                    L32_2 = 10
                    L33_2 = 1
                    for L34_2 = L31_2, L32_2, L33_2 do
                      if L30_2 == L34_2 then
                        L35_2 = randomClick
                        L36_2 = 2
                        L37_2 = 200
                        L38_2 = L24_2[L34_2]
                        L38_2 = L38_2[1]
                        L39_2 = L24_2[L34_2]
                        L39_2 = L39_2[2]
                        L40_2 = 20
                        L35_2(L36_2, L37_2, L38_2, L39_2, L40_2)
                        break
                      end
                    end
                  end
                  L20_2 = false
              end
              else
                if L22_2 ~= 0 then
                  L24_2 = os
                  L24_2 = L24_2.time
                  L24_2 = L24_2()
                  L24_2 = L24_2 - L22_2
                  if 3 < L24_2 then
                    L24_2 = getColour
                    L25_2 = colorList
                    L26_2 = "号码键盘"
                    L24_2 = L24_2(L25_2, L26_2)
                    if not L24_2 then
                      L24_2 = randomClick
                      L25_2 = 0
                      L26_2 = 300
                      L27_2 = 275
                      L28_2 = 153
                      L29_2 = 377
                      L30_2 = 196
                      L24_2(L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
                      break
                  end
                end
                else
                  L24_2 = getColour
                  L25_2 = colorList
                  L26_2 = "商会店铺"
                  L24_2 = L24_2(L25_2, L26_2)
                  if L24_2 then
                    L24_2 = true
                    L25_2 = 0
                    L26_2 = true
                    L27_2 = {}
                    L28_2 = {}
                    L29_2 = 172
                    L30_2 = 160
                    L31_2 = 230
                    L32_2 = 217
                    L28_2[1] = L29_2
                    L28_2[2] = L30_2
                    L28_2[3] = L31_2
                    L28_2[4] = L32_2
                    L29_2 = {}
                    L30_2 = 320
                    L31_2 = 159
                    L32_2 = 381
                    L33_2 = 219
                    L29_2[1] = L30_2
                    L29_2[2] = L31_2
                    L29_2[3] = L32_2
                    L29_2[4] = L33_2
                    L30_2 = {}
                    L31_2 = 170
                    L32_2 = 307
                    L33_2 = 238
                    L34_2 = 374
                    L30_2[1] = L31_2
                    L30_2[2] = L32_2
                    L30_2[3] = L33_2
                    L30_2[4] = L34_2
                    L31_2 = {}
                    L32_2 = 316
                    L33_2 = 311
                    L34_2 = 388
                    L35_2 = 375
                    L31_2[1] = L32_2
                    L31_2[2] = L33_2
                    L31_2[3] = L34_2
                    L31_2[4] = L35_2
                    L32_2 = {}
                    L33_2 = 171
                    L34_2 = 465
                    L35_2 = 235
                    L36_2 = 529
                    L32_2[1] = L33_2
                    L32_2[2] = L34_2
                    L32_2[3] = L35_2
                    L32_2[4] = L36_2
                    L33_2 = {}
                    L34_2 = 320
                    L35_2 = 468
                    L36_2 = 390
                    L37_2 = 530
                    L33_2[1] = L34_2
                    L33_2[2] = L35_2
                    L33_2[3] = L36_2
                    L33_2[4] = L37_2
                    L34_2 = {}
                    L35_2 = 172
                    L36_2 = 620
                    L37_2 = 237
                    L38_2 = 680
                    L34_2[1] = L35_2
                    L34_2[2] = L36_2
                    L34_2[3] = L37_2
                    L34_2[4] = L38_2
                    L35_2 = {}
                    L36_2 = 316
                    L37_2 = 621
                    L38_2 = 388
                    L39_2 = 685
                    L35_2[1] = L36_2
                    L35_2[2] = L37_2
                    L35_2[3] = L38_2
                    L35_2[4] = L39_2
                    L36_2 = {}
                    L37_2 = 165
                    L38_2 = 769
                    L39_2 = 234
                    L40_2 = 835
                    L36_2[1] = L37_2
                    L36_2[2] = L38_2
                    L36_2[3] = L39_2
                    L36_2[4] = L40_2
                    L37_2 = {}
                    L38_2 = 323
                    L39_2 = 776
                    L40_2 = 383
                    L41_2 = 835
                    L37_2[1] = L38_2
                    L37_2[2] = L39_2
                    L37_2[3] = L40_2
                    L37_2[4] = L41_2
                    L27_2[1] = L28_2
                    L27_2[2] = L29_2
                    L27_2[3] = L30_2
                    L27_2[4] = L31_2
                    L27_2[5] = L32_2
                    L27_2[6] = L33_2
                    L27_2[7] = L34_2
                    L27_2[8] = L35_2
                    L27_2[9] = L36_2
                    L27_2[10] = L37_2
                    while true do
                      if L23_2 == 1 and L24_2 then
                        L28_2 = printLog
                        L29_2 = "第1间铺面查找:"
                        L30_2 = A1_2
                        L29_2 = L29_2 .. L30_2
                        L28_2(L29_2)
                        L24_2 = false
                      else
                        if L24_2 and L26_2 then
                          L28_2 = getColour
                          L29_2 = colorList
                          L30_2 = "商会号码"
                          L28_2 = L28_2(L29_2, L30_2)
                          if L28_2 then
                            L24_2 = false
                            if L23_2 == 0 then
                              L23_2 = 10
                              L28_2 = printLog
                              L29_2 = "第10间铺面查找:"
                              L30_2 = A1_2
                              L29_2 = L29_2 .. L30_2
                              L28_2(L29_2)
                            else
                              L28_2 = printLog
                              L29_2 = "第"
                              L30_2 = L23_2
                              L31_2 = "间铺面查找:"
                              L32_2 = A1_2
                              L29_2 = L29_2 .. L30_2 .. L31_2 .. L32_2
                              L28_2(L29_2)
                            end
                            L28_2 = _ENV["_卡区延迟"]
                            L28_2()
                            L28_2 = getColors
                            L29_2 = colorList
                            L30_2 = "灰色号码"
                            L31_2 = L27_2[L23_2]
                            L31_2 = L31_2[1]
                            L32_2 = L27_2[L23_2]
                            L32_2 = L32_2[2]
                            L33_2 = L27_2[L23_2]
                            L33_2 = L33_2[3]
                            L34_2 = L27_2[L23_2]
                            L34_2 = L34_2[4]
                            L28_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                            if L28_2 then
                              L28_2 = printLog
                              L29_2 = "店铺未开:"
                              L30_2 = L25_2
                              L29_2 = L29_2 .. L30_2
                              L28_2(L29_2)
                              while true do
                                L28_2 = getColour
                                L29_2 = colorList
                                L30_2 = "商会列表"
                                L28_2 = L28_2(L29_2, L30_2)
                                if L28_2 then
                                  L28_2 = randomClick
                                  L29_2 = 0
                                  L30_2 = 300
                                  L31_2 = 275
                                  L32_2 = 153
                                  L33_2 = 377
                                  L34_2 = 196
                                  L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                                  break
                                else
                                  L28_2 = getColour
                                  L29_2 = colorList
                                  L30_2 = "商会店铺"
                                  L28_2 = L28_2(L29_2, L30_2)
                                  if L28_2 then
                                    L28_2 = randomClick
                                    L29_2 = 2
                                    L30_2 = 200
                                    L31_2 = 848
                                    L32_2 = 49
                                    L28_2(L29_2, L30_2, L31_2, L32_2)
                                  end
                                end
                              end
                              L21_2 = true
                              L10_2 = -1
                              goto lbl_874
                            else
                              L26_2 = false
                              L28_2 = randomClick
                              L29_2 = 0
                              L30_2 = 250
                              L31_2 = L27_2[L23_2]
                              L31_2 = L31_2[1]
                              L32_2 = L27_2[L23_2]
                              L32_2 = L32_2[2]
                              L33_2 = L27_2[L23_2]
                              L33_2 = L33_2[3]
                              L34_2 = L27_2[L23_2]
                              L34_2 = L34_2[4]
                              L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                              L28_2 = _ENV["_卡区延迟"]
                              L28_2()
                            end
                        end
                        elseif not L24_2 then
                          L28_2 = _ENV["_卡区延迟"]
                          L28_2()
                          L28_2 = 1
                          L29_2 = 3
                          L30_2 = 1
                          for L31_2 = L28_2, L29_2, L30_2 do
                            L32_2 = false
                            L33_2 = -1
                            L34_2 = -1
                            if A2_2 == "物品" then
                              if A1_2 == "豆斋果" or A1_2 == "醉生梦死" or A1_2 == "长寿面" or A1_2 == "佛跳墙" or A1_2 == "烤鸭" or A1_2 == "珍露酒" or A1_2 == "臭豆腐" or A1_2 == "烤肉" or A1_2 == "桂花丸" or A1_2 == "翡翠豆腐" or A1_2 == "梅花酒" or A1_2 == "百味酒" or A1_2 == "蛇胆酒" then
                                L35_2 = _ENV["烹饪价格"]
                                if L35_2 ~= "" then
                                  L35_2 = _ENV["烹饪价格"]
                                  if L35_2 ~= nil then
                                    goto lbl_552
                                  end
                                end
                                _ENV["烹饪价格"] = 99999
                                ::lbl_552::
                                L35_2 = _ENV["价格筛选"]
                                L36_2 = tonumber
                                L37_2 = _ENV["烹饪价格"]
                                L36_2 = L36_2(L37_2)
                                L37_2 = A1_2
                                L35_2, L36_2, L37_2 = L35_2(L36_2, L37_2)
                                L34_2 = L37_2
                                L33_2 = L36_2
                                L32_2 = L35_2
                                if not L32_2 then
                                  break
                                end
                              else
                                L35_2 = getMultiColor
                                L36_2 = propsList
                                L37_2 = A1_2
                                L38_2 = 85
                                L39_2 = 117
                                L40_2 = 224
                                L41_2 = 803
                                L42_2 = 752
                                L35_2, L36_2, L37_2 = L35_2(L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2)
                                L34_2 = L37_2
                                L33_2 = L36_2
                                L32_2 = L35_2
                              end
                            elseif 2 < L31_2 then
                              L35_2 = getWordStock
                              L36_2 = PetList
                              L36_2 = L36_2["超出一万"]
                              L37_2 = A1_2
                              L38_2 = 176
                              L39_2 = 250
                              L40_2 = 735
                              L41_2 = 773
                              L35_2, L36_2, L37_2 = L35_2(L36_2, L37_2, L38_2, L39_2, L40_2, L41_2)
                              L34_2 = L37_2
                              L33_2 = L36_2
                              L32_2 = L35_2
                            else
                              L35_2 = getWordStock
                              L36_2 = PetList
                              L36_2 = L36_2["低于一万"]
                              L37_2 = A1_2
                              L38_2 = 176
                              L39_2 = 250
                              L40_2 = 735
                              L41_2 = 773
                              L35_2, L36_2, L37_2 = L35_2(L36_2, L37_2, L38_2, L39_2, L40_2, L41_2)
                              L34_2 = L37_2
                              L33_2 = L36_2
                              L32_2 = L35_2
                            end
                            if L32_2 then
                              L35_2 = true
                              L36_2 = 0
                              L37_2 = 0
                              L38_2 = false
                              while true do
                                L39_2 = getColour
                                L40_2 = colorList
                                L41_2 = "提示信息"
                                L39_2 = L39_2(L40_2, L41_2)
                                if L39_2 or L38_2 then
                                  L39_2 = printLog
                                  L40_2 = "完成购买:"
                                  L41_2 = A1_2
                                  L40_2 = L40_2 .. L41_2
                                  L39_2(L40_2)
                                  L3_2 = true
                                  break
                                elseif L37_2 < 3 and L35_2 then
                                  L35_2 = false
                                  L39_2 = os
                                  L39_2 = L39_2.time
                                  L39_2 = L39_2()
                                  L36_2 = L39_2
                                  L39_2 = randomClick
                                  L40_2 = 2
                                  L41_2 = 300
                                  L42_2 = L33_2
                                  L43_2 = L34_2 + 20
                                  L44_2 = 20
                                  L39_2(L40_2, L41_2, L42_2, L43_2, L44_2)
                                  L39_2 = randomClick
                                  L40_2 = 0
                                  L41_2 = 300
                                  L42_2 = 627
                                  L43_2 = 944
                                  L44_2 = 779
                                  L45_2 = 991
                                  L39_2(L40_2, L41_2, L42_2, L43_2, L44_2, L45_2)
                                  L38_2 = true
                                  L39_2 = printLog
                                  L40_2 = "完成购买:"
                                  L41_2 = A1_2
                                  L40_2 = L40_2 .. L41_2
                                  L39_2(L40_2)
                                  L3_2 = true
                                  break
                                elseif 3 <= L37_2 then
                                  L3_2 = false
                                  break
                                else
                                  L39_2 = os
                                  L39_2 = L39_2.time
                                  L39_2 = L39_2()
                                  L39_2 = L39_2 - L36_2
                                  if 5 < L39_2 and L36_2 ~= 0 then
                                    L37_2 = L37_2 + 1
                                    L35_2 = true
                                  end
                                end
                              end
                            end
                            if L3_2 then
                              break
                            end
                            L35_2 = mSleep
                            L36_2 = 100
                            L35_2(L36_2)
                          end
                          if not L3_2 then
                            L28_2 = 0
                            L29_2 = 1
                            L30_2 = #L4_2
                            L30_2 = L30_2 + 1
                            L31_2 = #L4_2
                            L31_2 = L17_2 + L31_2
                            L10_2 = L31_2 + 1
                            L31_2 = 1
                            L32_2 = L11_2[2]
                            L32_2 = #L32_2
                            L33_2 = 1
                            for L34_2 = L31_2, L32_2, L33_2 do
                              L35_2 = string
                              L35_2 = L35_2.sub
                              L36_2 = L11_2[2]
                              L37_2 = L29_2
                              L38_2 = L30_2
                              L35_2 = L35_2(L36_2, L37_2, L38_2)
                              L29_2 = L29_2 + 1
                              L30_2 = L30_2 + 1
                              if L34_2 >= L10_2 then
                                L36_2 = string
                                L36_2 = L36_2.find
                                L37_2 = L35_2
                                L38_2 = L4_2
                                L36_2 = L36_2(L37_2, L38_2)
                                if L36_2 then
                                  L24_2 = true
                                  L36_2 = tonumber
                                  L37_2 = string
                                  L37_2 = L37_2.sub
                                  L38_2 = L11_2[2]
                                  L39_2 = L29_2 - 1
                                  L40_2 = L29_2 - 1
                                  L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2 = L37_2(L38_2, L39_2, L40_2)
                                  L36_2 = L36_2(L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2)
                                  L28_2 = L36_2
                                  if L28_2 == 0 then
                                    L28_2 = 10
                                  end
                                  break
                                end
                              end
                            end
                            if A1_2 == "黑熊精" or A1_2 == "黑熊" then
                              L28_2 = 0
                            end
                            if L28_2 == 0 then
                              while true do
                                L31_2 = getColour
                                L32_2 = colorList
                                L33_2 = "商会列表"
                                L31_2 = L31_2(L32_2, L33_2)
                                if L31_2 then
                                  L31_2 = randomClick
                                  L32_2 = 0
                                  L33_2 = 300
                                  L34_2 = 275
                                  L35_2 = 153
                                  L36_2 = 377
                                  L37_2 = 196
                                  L31_2(L32_2, L33_2, L34_2, L35_2, L36_2, L37_2)
                                  break
                                else
                                  L31_2 = getColour
                                  L32_2 = colorList
                                  L33_2 = "商会店铺"
                                  L31_2 = L31_2(L32_2, L33_2)
                                  if L31_2 then
                                    L31_2 = randomClick
                                    L32_2 = 2
                                    L33_2 = 200
                                    L34_2 = 848
                                    L35_2 = 49
                                    L31_2(L32_2, L33_2, L34_2, L35_2)
                                  end
                                end
                              end
                              L21_2 = true
                              goto lbl_874
                          end
                          else
                            L21_2 = true
                            goto lbl_874
                            goto lbl_830
                            goto lbl_874
                          end
                        else
                          if 5 < L25_2 and L24_2 then
                            L28_2 = getColour
                            L29_2 = colorList
                            L30_2 = "商会号码"
                            L28_2 = L28_2(L29_2, L30_2)
                            if not L28_2 then
                              L28_2 = printLog
                              L29_2 = "店铺未开:"
                              L30_2 = L25_2
                              L29_2 = L29_2 .. L30_2
                              L28_2(L29_2)
                              while true do
                                L28_2 = getColour
                                L29_2 = colorList
                                L30_2 = "商会列表"
                                L28_2 = L28_2(L29_2, L30_2)
                                if L28_2 then
                                  L28_2 = randomClick
                                  L29_2 = 0
                                  L30_2 = 300
                                  L31_2 = 275
                                  L32_2 = 153
                                  L33_2 = 377
                                  L34_2 = 196
                                  L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                                  break
                                else
                                  L28_2 = getColour
                                  L29_2 = colorList
                                  L30_2 = "商会店铺"
                                  L28_2 = L28_2(L29_2, L30_2)
                                  if L28_2 then
                                    L28_2 = randomClick
                                    L29_2 = 2
                                    L30_2 = 200
                                    L31_2 = 848
                                    L32_2 = 49
                                    L28_2(L29_2, L30_2, L31_2, L32_2)
                                  end
                                end
                              end
                              L21_2 = true
                              L10_2 = -1
                              goto lbl_874
                          end
                          elseif L24_2 then
                            L25_2 = L25_2 + 1
                            L28_2 = randomClick
                            L29_2 = 0
                            L30_2 = 300
                            L31_2 = 233
                            L32_2 = 946
                            L33_2 = 322
                            L34_2 = 979
                            L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                          end
                        end
                      end
                      ::lbl_830::
                    end
                  elseif L22_2 == 0 and not L20_2 then
                    L24_2 = randomClick
                    L25_2 = 0
                    L26_2 = 1300
                    L27_2 = 989
                    L28_2 = 600
                    L29_2 = 1054
                    L30_2 = 666
                    L24_2(L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
                    L24_2 = os
                    L24_2 = L24_2.time
                    L24_2 = L24_2()
                    L22_2 = L24_2
                  else
                    L24_2 = randomClick
                    L25_2 = 0
                    L26_2 = 300
                    L27_2 = 587
                    L28_2 = 149
                    L29_2 = 711
                    L30_2 = 200
                    L24_2(L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
                    L24_2 = _cmp
                    L25_2 = Color
                    L25_2 = L25_2["百级师门"]
                    L25_2 = L25_2["误点搜索"]
                    L24_2 = L24_2(L25_2)
                    if L24_2 then
                      L24_2 = _Sleep
                      L25_2 = 300
                      L26_2 = 500
                      L24_2(L25_2, L26_2)
                      L24_2 = _tap
                      L25_2 = 322
                      L26_2 = 180
                      L27_2 = 1
                      L28_2 = 10
                      L24_2(L25_2, L26_2, L27_2, L28_2)
                    end
                  end
                end
              end
            end
            ::lbl_874::
          end
        end
      end
    end
  end
  L6_2 = _ENV["低级龟速识别"]
  L7_2 = A1_2
  return L6_2(L7_2)
end

_ENV["低级查找对于物品"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L2_2 = _ENV["门派传送"]
  if L2_2 == "是" then
    L2_2 = _ENV["长安传送NPC"]
    L3_2 = A0_2
    L2_2(L3_2)
  else
    L2_2 = _ENV["_使用"]
    L2_2 = L2_2["旗子"]
    L3_2 = "长安城"
    L4_2 = "长安_商会d"
    L2_2(L3_2, L4_2)
  end
  L2_2 = _find
  L3_2 = Color
  L3_2 = L3_2["红尘"]
  L3_2 = L3_2["没点了"]
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = _ENV["没点了下号"]
    return L2_2()
  end
  L2_2 = _ENV["_返回"]
  L2_2 = L2_2["地图"]
  L3_2 = "长安城"
  L2_2 = L2_2(L3_2)
  if L2_2 then
    while true do
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["叉叉"]
      L2_2()
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      L4_2 = 326 - L2_2
      L5_2 = 18 - L3_2
      L6_2 = math
      L6_2 = L6_2.abs
      L7_2 = L4_2
      L6_2 = L6_2(L7_2)
      if L6_2 <= 5 then
        L6_2 = math
        L6_2 = L6_2.abs
        L7_2 = L5_2
        L6_2 = L6_2(L7_2)
        if L6_2 <= 5 then
          L6_2 = _ENV["_功能"]
          L6_2 = L6_2["屏蔽"]
          L7_2 = 4
          L6_2(L7_2)
          L6_2 = _ENV["_计算"]
          L6_2 = L6_2["取目标屏幕坐标"]
          L7_2 = "长安城"
          L8_2 = L2_2
          L9_2 = L3_2
          L10_2 = 326
          L11_2 = 18
          L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
          L3_2 = L7_2
          L2_2 = L6_2
          L6_2 = mSleep
          L7_2 = 500
          L6_2(L7_2)
          L6_2 = tap
          L7_2 = L2_2
          L8_2 = L3_2
          L6_2(L7_2, L8_2)
          L6_2 = _Sleep
          L7_2 = 500
          L8_2 = 1188
          L6_2(L7_2, L8_2)
          L6_2 = 1
          L7_2 = 40
          L8_2 = 1
          for L9_2 = L6_2, L7_2, L8_2 do
            L10_2 = _find_tb
            L11_2 = Color
            L11_2 = L11_2["出现重叠"]
            L10_2, L11_2, L12_2 = L10_2(L11_2)
            if L10_2 then
              L13_2 = L11_2 - 165
              L14_2 = L12_2 + 31
              L15_2 = L11_2 - 53
              L16_2 = L12_2 + 540
              L17_2 = _find_tb
              L18_2 = Color
              L18_2 = L18_2["回复"]
              L18_2 = L18_2["商会重叠"]
              L19_2 = {}
              L20_2 = 0
              L21_2 = 0
              L19_2[1] = L20_2
              L19_2[2] = L21_2
              L20_2 = {}
              L21_2 = L13_2
              L22_2 = L14_2
              L23_2 = L15_2
              L24_2 = L16_2
              L20_2[1] = L21_2
              L20_2[2] = L22_2
              L20_2[3] = L23_2
              L20_2[4] = L24_2
              L17_2 = L17_2(L18_2, L19_2, L20_2)
              if L17_2 then
                L17_2 = mSleep
                L18_2 = 1000
                L17_2(L18_2)
              end
            end
            L13_2 = _find
            L14_2 = Color
            L14_2 = L14_2["百级师门"]
            L14_2 = L14_2["我来买些东西"]
            L13_2 = L13_2(L14_2)
            if L13_2 then
              L13_2 = _Sleep
              L14_2 = 500
              L15_2 = 1000
              L13_2(L14_2, L15_2)
              L13_2 = _cmp_cx
              L14_2 = Color
              L14_2 = L14_2["购买"]
              L14_2 = L14_2["商会界面"]
              L15_2 = {}
              L16_2 = 50
              L17_2 = 50
              L15_2[1] = L16_2
              L15_2[2] = L17_2
              L13_2 = L13_2(L14_2, L15_2)
              if L13_2 then
                L13_2 = _ENV["屏蔽取消"]
                L13_2()
                L13_2 = _ENV["宠物店"]
                if L13_2 ~= nil then
                  L13_2 = _ENV["宠物店"]
                  if L13_2 ~= "" then
                    goto lbl_144
                  end
                end
                L13_2 = _ENV["低级龟速识别"]
                L14_2 = A1_2
                L15_2 = _ENV["特性"]
                do return L13_2(L14_2, L15_2) end
                goto lbl_150
                ::lbl_144::
                L13_2 = _ENV["低级查找对于物品"]
                L14_2 = _ENV["宠物店"]
                L15_2 = A1_2
                L16_2 = "宠物"
                return L13_2(L14_2, L15_2, L16_2)
              end
            end
            ::lbl_150::
          end
          L6_2 = mSleep
          L7_2 = 50
          L6_2(L7_2)
      end
      else
        L6_2 = _ENV["_前往"]
        L6_2 = L6_2["固定坐标"]
        L7_2 = "长安城"
        L8_2 = 1095
        L9_2 = 850
        L6_2(L7_2, L8_2, L9_2)
      end
      L6_2 = mSleep
      L7_2 = 100
      L6_2(L7_2)
    end
  end
end

_ENV["低级帮师傅抓到宠物"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = 0
  L3_2 = 0
  if A0_2 == "普陀山" or A0_2 == "狮驼岭" or A0_2 == "盘丝洞" then
    L4_2 = _ENV["_使用"]
    L4_2 = L4_2["摄魂香2"]
    L5_2 = true
    L4_2(L5_2)
  end
  L4_2 = _ENV["长安传送NPC"]
  L5_2 = A0_2
  L6_2 = A1_2
  L4_2(L5_2, L6_2)
  while true do
    L4_2 = _ENV["弹窗查看详情"]
    L4_2()
    L4_2 = getWordStock
    L5_2 = _ENV["师门洞府"]
    L6_2 = A0_2
    L7_2 = 148
    L8_2 = 35
    L9_2 = 316
    L10_2 = 80
    L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    if L4_2 then
      L4_2 = _ENV["长安传送NPC"]
      L5_2 = A0_2
      L6_2 = A1_2
      L4_2(L5_2, L6_2)
    else
      L4_2 = printLog
      L5_2 = "师门巡逻/"
      L6_2 = L2_2
      L5_2 = L5_2 .. L6_2
      L4_2(L5_2)
      if not (2 <= L2_2) then
        L4_2 = getWordStock
        L5_2 = WordStock
        L6_2 = "完成任务"
        L7_2 = 1587
        L8_2 = 368
        L9_2 = 1725
        L10_2 = 407
        L11_2 = 82
        L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
        if not L4_2 then
          goto lbl_68
        end
      end
      L4_2 = _ENV["巡逻次数"]
      L4_2 = L4_2 + 1
      _ENV["巡逻次数"] = L4_2
      L4_2 = printLog
      L5_2 = "完成巡逻前往交任务"
      L4_2(L5_2)
      L4_2 = _ENV["宠物忠诚"]
      L5_2 = false
      L4_2(L5_2)
      L4_2 = _ENV["押镖屏蔽"]
      L4_2()
      L4_2 = _ENV["百级识别师门任务"]
      L5_2 = A0_2
      do return L4_2(L5_2) end
      goto lbl_164
      ::lbl_68::
      L4_2 = getColour
      L5_2 = SmMapData
      L5_2 = L5_2[A0_2]
      L6_2 = 1
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L4_2 = L4_2 - L3_2
        if 60 < L4_2 then
          L4_2 = randomClick
          L5_2 = 2
          L6_2 = 1300
          L7_2 = SmMapData
          L7_2 = L7_2[A0_2]
          L7_2 = L7_2[6]
          L8_2 = SmMapData
          L8_2 = L8_2[A0_2]
          L8_2 = L8_2[7]
          L4_2(L5_2, L6_2, L7_2, L8_2)
        else
          L4_2 = randomClick
          L5_2 = 0
          L6_2 = 2300
          L7_2 = SmMapData
          L7_2 = L7_2[A0_2]
          L7_2 = L7_2[2]
          L8_2 = SmMapData
          L8_2 = L8_2[A0_2]
          L8_2 = L8_2[3]
          L9_2 = SmMapData
          L9_2 = L9_2[A0_2]
          L9_2 = L9_2[4]
          L10_2 = SmMapData
          L10_2 = L10_2[A0_2]
          L10_2 = L10_2[5]
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
        end
      else
        L4_2 = getColour
        L5_2 = colorList
        L6_2 = "宠物属性页面"
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = randomClick
          L5_2 = 2
          L6_2 = 300
          L7_2 = 1705
          L8_2 = 76
          L9_2 = 20
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        else
          L4_2 = getColour
          L5_2 = colorList
          L6_2 = "战斗状态"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 then
            L4_2 = printLog
            L5_2 = "战斗。。。"
            L4_2(L5_2)
            L4_2 = _ENV["示威战斗"]
            L4_2()
            L2_2 = L2_2 + 1
            L4_2 = supplement
            L4_2()
          else
            L4_2 = getColour
            L5_2 = colorList
            L6_2 = "打开朱紫国地图"
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = randomClick
              L5_2 = 2
              L6_2 = 500
              L7_2 = 1563
              L8_2 = 78
              L4_2(L5_2, L6_2, L7_2, L8_2)
              L4_2 = _ENV["师门任务返回"]
              L4_2()
            else
              L4_2 = os
              L4_2 = L4_2.time
              L4_2 = L4_2()
              L3_2 = L4_2
              L4_2 = randomClick
              L5_2 = 2
              L6_2 = 300
              L7_2 = 217
              L8_2 = 54
              L9_2 = 20
              L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
            end
          end
        end
      end
    end
    ::lbl_164::
  end
end

_ENV["低级巡逻"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2
  L3_2 = true
  L4_2 = false
  L5_2 = true
  L6_2 = 0
  L7_2 = ""
  L8_2 = ""
  L9_2 = 0
  L10_2 = true
  if A0_2 == "普陀山" or A0_2 == "狮驼岭" or A0_2 == "盘丝洞" then
    L11_2 = wordStock
    L11_2 = L11_2.useMosquitoRepellentIncense
    L11_2()
  end
  L11_2 = _ENV["师门任务返回"]
  L11_2()
  L11_2 = _ENV["关闭活动指引"]
  L11_2()
  L11_2 = os
  L11_2 = L11_2.time
  L11_2 = L11_2()
  _ENV["卡点时间"] = L11_2
  L11_2 = 0
  L12_2 = true
  L13_2 = ""
  if A2_2 == true then
    while L3_2 do
      L14_2 = getWordStock
      L15_2 = WordStock
      L16_2 = "其他事情"
      L17_2 = 1455
      L18_2 = 480
      L19_2 = 1733
      L20_2 = 556
      L21_2 = 83
      L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
      if L14_2 then
        L17_2 = mSleep
        L18_2 = math
        L18_2 = L18_2.random
        L19_2 = 111
        L20_2 = 333
        L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2 = L18_2(L19_2, L20_2)
        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2)
        L17_2 = randomClick
        L18_2 = 0
        L19_2 = 300
        L20_2 = 1494
        L21_2 = 502
        L22_2 = 1714
        L23_2 = 549
        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
        L17_2 = os
        L17_2 = L17_2.time
        L17_2 = L17_2()
        repeat
          L18_2 = getWordStock
          L19_2 = WordStock
          L20_2 = "任务"
          L21_2 = 1396
          L22_2 = 130
          L23_2 = 1527
          L24_2 = 548
          L25_2 = 83
          L18_2, L19_2, L20_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
          if L18_2 then
            L21_2 = randomClick
            L22_2 = 0
            L23_2 = 300
            L24_2 = L19_2 + 50
            L25_2 = L20_2
            L26_2 = L19_2 + 100
            L27_2 = L20_2 + 50
            L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
          else
            L21_2 = getColour
            L22_2 = _ENV["商人鬼魂"]
            L23_2 = "学习界面"
            L21_2 = L21_2(L22_2, L23_2)
            if L21_2 then
              L21_2 = _ENV["百级关闭错误弹出"]
              L22_2 = A0_2
              L21_2(L22_2)
              L12_2 = true
              break
            else
              L21_2 = os
              L21_2 = L21_2.time
              L21_2 = L21_2()
              L22_2 = _ENV["卡点时间"]
              L21_2 = L21_2 - L22_2
              if 300 < L21_2 then
                L21_2 = _ENV["卡点处理"]
                return L21_2()
              else
                L11_2 = L11_2 + 1
                L21_2 = printLog
                L22_2 = "index = "
                L23_2 = L11_2
                L22_2 = L22_2 .. L23_2
                L21_2(L22_2)
                L21_2 = mSleep
                L22_2 = 100
                L21_2(L22_2)
              end
            end
          end
          L12_2 = false
          L21_2 = getColour
          L22_2 = colorList
          L23_2 = "完成师门任务红"
          L21_2 = L21_2(L22_2, L23_2)
          if L21_2 then
            L21_2 = getColour
            L22_2 = _ENV["商人鬼魂"]
            L23_2 = "关闭对话框"
            L21_2 = L21_2(L22_2, L23_2)
            if L21_2 then
              L21_2 = getColour
              L22_2 = colorList
              L23_2 = "完成师门任务白"
              L21_2 = L21_2(L22_2, L23_2)
              if L21_2 then
                break
              end
            end
          end
          L21_2 = os
          L21_2 = L21_2.time
          L21_2 = L21_2()
          L21_2 = L21_2 - L17_2
        until 20 < L21_2
      else
        L17_2 = os
        L17_2 = L17_2.time
        L17_2 = L17_2()
        L18_2 = _ENV["卡点时间"]
        L17_2 = L17_2 - L18_2
        if 300 < L17_2 then
          L17_2 = _ENV["卡点处理"]
          return L17_2()
        else
          if A0_2 == "地府" then
            L17_2 = monitorAddress
            L18_2 = SmTable
            L18_2 = L18_2[A0_2]
            L18_2 = L18_2[21]
            L19_2 = SmTable
            L19_2 = L19_2[A0_2]
            L19_2 = L19_2[22]
            L17_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L17_2 = randomClick
              L18_2 = 2
              L19_2 = 1300
              L20_2 = SmTable
              L20_2 = L20_2[A0_2]
              L20_2 = L20_2[23]
              L21_2 = SmTable
              L21_2 = L21_2[A0_2]
              L21_2 = L21_2[24]
              L17_2(L18_2, L19_2, L20_2, L21_2)
          end
          else
            if L11_2 ~= 0 then
              if not L12_2 then
                goto lbl_216
              end
              L17_2 = getAddressNum
              L17_2 = L17_2()
              if L13_2 ~= L17_2 or L13_2 == "" then
                goto lbl_216
              end
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "动作按钮"
              L17_2 = L17_2(L18_2, L19_2)
              if not L17_2 then
                goto lbl_216
              end
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "设置按钮"
              L17_2 = L17_2(L18_2, L19_2)
              if not L17_2 then
                goto lbl_216
              end
            end
            if 15 <= L11_2 then
              L17_2 = _ENV["百级识别师门任务"]
              L18_2 = A0_2
              return L17_2(L18_2)
            else
              L17_2 = printLog
              L18_2 = "点击任务栏"
              L17_2(L18_2)
              L17_2 = randomClick
              L18_2 = 0
              L19_2 = 300
              L20_2 = 1673
              L21_2 = 362
              L22_2 = 1770
              L23_2 = 433
              L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
              L11_2 = L11_2 + 1
              goto lbl_401
              ::lbl_216::
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "完成师门任务红"
              L17_2 = L17_2(L18_2, L19_2)
              if L17_2 then
                L17_2 = getColour
                L18_2 = colorList
                L19_2 = "完成师门任务白"
                L17_2 = L17_2(L18_2, L19_2)
                if L17_2 then
                  L17_2 = getColour
                  L18_2 = _ENV["商人鬼魂"]
                  L19_2 = "关闭对话框"
                  L17_2 = L17_2(L18_2, L19_2)
                  if L17_2 then
                    while L3_2 do
                      L17_2 = getColour
                      L18_2 = _ENV["商人鬼魂"]
                      L19_2 = "关闭对话框"
                      L17_2 = L17_2(L18_2, L19_2)
                      if L17_2 then
                        L17_2 = randomClick
                        L18_2 = 2
                        L19_2 = 300
                        L20_2 = 1873
                        L21_2 = 797
                        L17_2(L18_2, L19_2, L20_2, L21_2)
                      else
                        L17_2 = getColour
                        L18_2 = _ENV["商人鬼魂"]
                        L19_2 = "学习界面"
                        L17_2 = L17_2(L18_2, L19_2)
                        if L17_2 then
                          L17_2 = _ENV["百级完成交任务"]
                          L18_2 = A0_2
                          L19_2 = A1_2
                          L20_2 = A2_2
                          L17_2(L18_2, L19_2, L20_2)
                        else
                          L17_2 = getColour
                          L18_2 = colorList
                          L19_2 = "动作按钮"
                          L17_2 = L17_2(L18_2, L19_2)
                          if L17_2 then
                            L17_2 = getColour
                            L18_2 = colorList
                            L19_2 = "设置按钮"
                            L17_2 = L17_2(L18_2, L19_2)
                            if L17_2 then
                              L6_2 = 66
                              A2_2 = true
                              L4_2 = true
                              L3_2 = false
                            end
                          end
                        end
                      end
                    end
                end
              end
              else
                L17_2 = getColour
                L18_2 = colorList
                L19_2 = "梦幻精灵界面"
                L17_2 = L17_2(L18_2, L19_2)
                if L17_2 then
                  L17_2 = getColour
                  L18_2 = colorList
                  L19_2 = "梦幻精灵界面I"
                  L17_2 = L17_2(L18_2, L19_2)
                  if L17_2 then
                    L17_2 = _ENV["百级关闭错误弹出"]
                    L18_2 = A0_2
                    return L17_2(L18_2)
                end
                else
                  L17_2 = getColour
                  L18_2 = _ENV["商人鬼魂"]
                  L19_2 = "学习界面"
                  L17_2 = L17_2(L18_2, L19_2)
                  if L17_2 then
                    while true do
                      L17_2 = getColour
                      L18_2 = _ENV["商人鬼魂"]
                      L19_2 = "学习界面"
                      L17_2 = L17_2(L18_2, L19_2)
                      if L17_2 then
                        L17_2 = randomClick
                        L18_2 = 2
                        L19_2 = 500
                        L20_2 = 1703
                        L21_2 = 63
                        L17_2(L18_2, L19_2, L20_2, L21_2)
                      else
                        break
                      end
                    end
                  else
                    L17_2 = getColour
                    L18_2 = _ENV["商人鬼魂"]
                    L19_2 = "关闭对话框"
                    L17_2 = L17_2(L18_2, L19_2)
                    if L17_2 then
                      L17_2 = BuyTo
                      L17_2 = L17_2[A0_2]
                      L17_2 = L17_2["巡逻"]
                      L18_2 = addTSOcrDictEx
                      L19_2 = L17_2[1]
                      L18_2 = L18_2(L19_2)
                      L19_2 = tsFindText
                      L20_2 = L18_2
                      L21_2 = L17_2[2]
                      L22_2 = L17_2[3]
                      L23_2 = L17_2[4]
                      L24_2 = L17_2[5]
                      L25_2 = L17_2[6]
                      L26_2 = L17_2[7]
                      L27_2 = 84
                      L19_2, L20_2 = L19_2(L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                      if 0 < L19_2 then
                        L21_2 = funcTable
                        L21_2 = L21_2["巡逻"]
                        L22_2 = A0_2
                        L23_2 = "巡逻"
                        L21_2 = L21_2(L22_2, L23_2)
                        if L21_2 == 66 then
                          L22_2 = _ENV["卡点处理"]
                          return L22_2()
                        else
                          L22_2 = _ENV["百级完成交任务"]
                          L23_2 = A0_2
                          L24_2 = "巡逻"
                          L25_2 = L21_2
                          return L22_2(L23_2, L24_2, L25_2)
                        end
                      elseif 15 < L11_2 then
                        L21_2 = 1
                        L22_2 = 10
                        L23_2 = 1
                        for L24_2 = L21_2, L22_2, L23_2 do
                          L25_2 = getColour
                          L26_2 = _ENV["商人鬼魂"]
                          L27_2 = "关闭对话框"
                          L25_2 = L25_2(L26_2, L27_2)
                          if L25_2 then
                            L25_2 = randomClick
                            L26_2 = 2
                            L27_2 = 300
                            L28_2 = 1873
                            L29_2 = 797
                            L25_2(L26_2, L27_2, L28_2, L29_2)
                          else
                            L6_2 = 66
                            A2_2 = true
                            L4_2 = true
                            L3_2 = false
                            break
                          end
                        end
                      else
                        L21_2 = printLog
                        L22_2 = "index = "
                        L23_2 = L11_2
                        L22_2 = L22_2 .. L23_2
                        L21_2(L22_2)
                        L11_2 = L11_2 + 1
                        L21_2 = mSleep
                        L22_2 = 200
                        L21_2(L22_2)
                      end
                    else
                      L17_2 = printLog
                      L18_2 = "index = "
                      L19_2 = L11_2
                      L18_2 = L18_2 .. L19_2
                      L17_2(L18_2)
                    end
                  end
                end
              end
            end
          end
        end
      end
      ::lbl_401::
      L17_2 = getAddressNum
      L17_2 = L17_2()
      L13_2 = L17_2
      L17_2 = mSleep
      L18_2 = math
      L18_2 = L18_2.random
      L19_2 = 666
      L20_2 = 1000
      L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2 = L18_2(L19_2, L20_2)
      L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2)
    end
  else
    while L3_2 do
      L14_2 = getWordStock
      L15_2 = WordStock
      L16_2 = "其他事情"
      L17_2 = 1455
      L18_2 = 480
      L19_2 = 1733
      L20_2 = 556
      L21_2 = 83
      L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
      if L14_2 then
        L17_2 = mSleep
        L18_2 = math
        L18_2 = L18_2.random
        L19_2 = 333
        L20_2 = 444
        L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2 = L18_2(L19_2, L20_2)
        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2)
        L17_2 = randomClick
        L18_2 = 0
        L19_2 = 300
        L20_2 = 1494
        L21_2 = 502
        L22_2 = 1714
        L23_2 = 549
        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
        L17_2 = os
        L17_2 = L17_2.time
        L17_2 = L17_2()
        repeat
          L18_2 = getWordStock
          L19_2 = WordStock
          L20_2 = "任务"
          L21_2 = 1396
          L22_2 = 130
          L23_2 = 1527
          L24_2 = 548
          L25_2 = 83
          L18_2, L19_2, L20_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
          if L18_2 then
            L21_2 = randomClick
            L22_2 = 0
            L23_2 = 300
            L24_2 = L19_2 + 50
            L25_2 = L20_2 + 140
            L26_2 = L19_2 + 100
            L27_2 = L20_2 + 200
            L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
          else
            L21_2 = getColour
            L22_2 = _ENV["商人鬼魂"]
            L23_2 = "学习界面"
            L21_2 = L21_2(L22_2, L23_2)
            if L21_2 then
              L21_2 = _ENV["百级关闭错误弹出"]
              L22_2 = A0_2
              L21_2(L22_2)
              L12_2 = true
              break
            else
              L21_2 = os
              L21_2 = L21_2.time
              L21_2 = L21_2()
              L22_2 = _ENV["卡点时间"]
              L21_2 = L21_2 - L22_2
              if 300 < L21_2 then
                L21_2 = _ENV["卡点处理"]
                return L21_2()
              else
                L11_2 = L11_2 + 1
                L21_2 = printLog
                L22_2 = "index = "
                L23_2 = L11_2
                L22_2 = L22_2 .. L23_2
                L21_2(L22_2)
                L21_2 = mSleep
                L22_2 = 100
                L21_2(L22_2)
              end
            end
          end
          L21_2 = getColour
          L22_2 = _ENV["商人鬼魂"]
          L23_2 = "给予界面"
          L21_2 = L21_2(L22_2, L23_2)
          if L21_2 then
            break
          end
          L21_2 = os
          L21_2 = L21_2.time
          L21_2 = L21_2()
          L21_2 = L21_2 - L17_2
        until 20 < L21_2
      else
        L17_2 = os
        L17_2 = L17_2.time
        L17_2 = L17_2()
        L18_2 = _ENV["卡点时间"]
        L17_2 = L17_2 - L18_2
        if 300 < L17_2 then
          L17_2 = _ENV["卡点处理"]
          return L17_2()
        else
          if A0_2 == "地府" then
            L17_2 = monitorAddress
            L18_2 = SmTable
            L18_2 = L18_2[A0_2]
            L18_2 = L18_2[21]
            L19_2 = SmTable
            L19_2 = L19_2[A0_2]
            L19_2 = L19_2[22]
            L17_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L17_2 = randomClick
              L18_2 = 2
              L19_2 = 1300
              L20_2 = SmTable
              L20_2 = L20_2[A0_2]
              L20_2 = L20_2[23]
              L21_2 = SmTable
              L21_2 = L21_2[A0_2]
              L21_2 = L21_2[24]
              L17_2(L18_2, L19_2, L20_2, L21_2)
          end
          else
            if L11_2 ~= 0 then
              if not L12_2 then
                goto lbl_578
              end
              L17_2 = getAddressNum
              L17_2 = L17_2()
              if L13_2 ~= L17_2 or L13_2 == "" then
                goto lbl_578
              end
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "动作按钮"
              L17_2 = L17_2(L18_2, L19_2)
              if not L17_2 then
                goto lbl_578
              end
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "设置按钮"
              L17_2 = L17_2(L18_2, L19_2)
              if not L17_2 then
                goto lbl_578
              end
            end
            L17_2 = printLog
            L18_2 = "点击快捷任务栏"
            L17_2(L18_2)
            L17_2 = randomClick
            L18_2 = 0
            L19_2 = 300
            L20_2 = 1673
            L21_2 = 362
            L22_2 = 1770
            L23_2 = 433
            L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
            L11_2 = 1
            goto lbl_990
            ::lbl_578::
            if not L12_2 then
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "完成师门任务红"
              L17_2 = L17_2(L18_2, L19_2)
              if L17_2 then
                L17_2 = getColour
                L18_2 = colorList
                L19_2 = "完成师门任务白"
                L17_2 = L17_2(L18_2, L19_2)
                if L17_2 then
                  L17_2 = getColour
                  L18_2 = _ENV["商人鬼魂"]
                  L19_2 = "关闭对话框"
                  L17_2 = L17_2(L18_2, L19_2)
                  if L17_2 then
                    while L3_2 do
                      L17_2 = getColour
                      L18_2 = _ENV["商人鬼魂"]
                      L19_2 = "关闭对话框"
                      L17_2 = L17_2(L18_2, L19_2)
                      if L17_2 then
                        L17_2 = randomClick
                        L18_2 = 2
                        L19_2 = 300
                        L20_2 = 1873
                        L21_2 = 797
                        L17_2(L18_2, L19_2, L20_2, L21_2)
                      else
                        L17_2 = getColour
                        L18_2 = _ENV["商人鬼魂"]
                        L19_2 = "学习界面"
                        L17_2 = L17_2(L18_2, L19_2)
                        if L17_2 then
                          L17_2 = _ENV["百级完成交任务"]
                          L18_2 = A0_2
                          L19_2 = A1_2
                          L20_2 = A2_2
                          L17_2(L18_2, L19_2, L20_2)
                        else
                          L17_2 = getColour
                          L18_2 = colorList
                          L19_2 = "动作按钮"
                          L17_2 = L17_2(L18_2, L19_2)
                          if L17_2 then
                            L17_2 = getColour
                            L18_2 = colorList
                            L19_2 = "设置按钮"
                            L17_2 = L17_2(L18_2, L19_2)
                            if L17_2 then
                              L6_2 = 66
                              A2_2 = true
                              L4_2 = true
                              L3_2 = false
                            end
                          end
                        end
                      end
                    end
                end
              end
            end
            else
              L17_2 = getColour
              L18_2 = colorList
              L19_2 = "梦幻精灵界面"
              L17_2 = L17_2(L18_2, L19_2)
              if L17_2 then
                L17_2 = getColour
                L18_2 = colorList
                L19_2 = "梦幻精灵界面I"
                L17_2 = L17_2(L18_2, L19_2)
                if L17_2 then
                  L17_2 = _ENV["百级关闭错误弹出"]
                  L18_2 = A0_2
                  return L17_2(L18_2)
              end
              else
                L17_2 = getColour
                L18_2 = _ENV["商人鬼魂"]
                L19_2 = "给予界面"
                L17_2 = L17_2(L18_2, L19_2)
                if L17_2 then
                  L17_2 = printLog
                  L18_2 = "进入给予界面"
                  L17_2(L18_2)
                  L17_2 = os
                  L17_2 = L17_2.time
                  L17_2 = L17_2()
                  while L12_2 do
                    L18_2 = findMultiColorInRegionFuzzyByTable
                    L19_2 = propsList
                    L19_2 = L19_2[A1_2]
                    L20_2 = 85
                    L21_2 = 377
                    L22_2 = 525
                    L23_2 = 489
                    L24_2 = 638
                    L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
                    if 0 < L18_2 then
                      L20_2 = printLog
                      L21_2 = "点击给予物品"
                      L20_2(L21_2)
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 500
                      L23_2 = 556
                      L24_2 = 973
                      L25_2 = 20
                      L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                      L20_2 = mSleep
                      L21_2 = 300
                      L20_2(L21_2)
                      L20_2 = 1
                      L21_2 = 5
                      L22_2 = 1
                      for L23_2 = L20_2, L21_2, L22_2 do
                        L24_2 = getColour
                        L25_2 = _ENV["商人鬼魂"]
                        L26_2 = "给予界面"
                        L24_2 = L24_2(L25_2, L26_2)
                        if L24_2 then
                          L24_2 = printLog
                          L25_2 = "点击给予物品"
                          L24_2(L25_2)
                          L24_2 = randomClick
                          L25_2 = 2
                          L26_2 = 500
                          L27_2 = 556
                          L28_2 = 973
                          L29_2 = 20
                          L24_2(L25_2, L26_2, L27_2, L28_2, L29_2)
                        else
                          L24_2 = mSleep
                          L25_2 = 100
                          L24_2(L25_2)
                          L12_2 = false
                        end
                      end
                    else
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L20_2 = L20_2 - L17_2
                      if 25 < L20_2 then
                        L20_2 = getColour
                        L21_2 = _ENV["商人鬼魂"]
                        L22_2 = "给予界面"
                        L20_2 = L20_2(L21_2, L22_2)
                        if L20_2 then
                          L20_2 = randomClick
                          L21_2 = 2
                          L22_2 = 800
                          L23_2 = 1587
                          L24_2 = 267
                          L20_2(L21_2, L22_2, L23_2, L24_2)
                          break
                      end
                      else
                        L20_2 = _ENV["_卡区延迟"]
                        L20_2()
                        L20_2 = _ENV["_随机延时"]
                        L21_2 = 200
                        L20_2(L21_2)
                        L20_2 = {}
                        L21_2 = {}
                        L22_2 = 884
                        L23_2 = 462
                        L24_2 = 992
                        L25_2 = 574
                        L21_2[1] = L22_2
                        L21_2[2] = L23_2
                        L21_2[3] = L24_2
                        L21_2[4] = L25_2
                        L22_2 = {}
                        L23_2 = 1022
                        L24_2 = 465
                        L25_2 = 1130
                        L26_2 = 570
                        L22_2[1] = L23_2
                        L22_2[2] = L24_2
                        L22_2[3] = L25_2
                        L22_2[4] = L26_2
                        L23_2 = {}
                        L24_2 = 1156
                        L25_2 = 466
                        L26_2 = 1263
                        L27_2 = 571
                        L23_2[1] = L24_2
                        L23_2[2] = L25_2
                        L23_2[3] = L26_2
                        L23_2[4] = L27_2
                        L24_2 = {}
                        L25_2 = 1294
                        L26_2 = 465
                        L27_2 = 1402
                        L28_2 = 570
                        L24_2[1] = L25_2
                        L24_2[2] = L26_2
                        L24_2[3] = L27_2
                        L24_2[4] = L28_2
                        L25_2 = {}
                        L26_2 = 1426
                        L27_2 = 462
                        L28_2 = 1542
                        L29_2 = 576
                        L25_2[1] = L26_2
                        L25_2[2] = L27_2
                        L25_2[3] = L28_2
                        L25_2[4] = L29_2
                        L26_2 = {}
                        L27_2 = 881
                        L28_2 = 600
                        L29_2 = 995
                        L30_2 = 709
                        L26_2[1] = L27_2
                        L26_2[2] = L28_2
                        L26_2[3] = L29_2
                        L26_2[4] = L30_2
                        L27_2 = {}
                        L28_2 = 1019
                        L29_2 = 600
                        L30_2 = 1132
                        L31_2 = 708
                        L27_2[1] = L28_2
                        L27_2[2] = L29_2
                        L27_2[3] = L30_2
                        L27_2[4] = L31_2
                        L28_2 = {}
                        L29_2 = 1156
                        L30_2 = 600
                        L31_2 = 1271
                        L32_2 = 709
                        L28_2[1] = L29_2
                        L28_2[2] = L30_2
                        L28_2[3] = L31_2
                        L28_2[4] = L32_2
                        L29_2 = {}
                        L30_2 = 1291
                        L31_2 = 600
                        L32_2 = 1404
                        L33_2 = 710
                        L29_2[1] = L30_2
                        L29_2[2] = L31_2
                        L29_2[3] = L32_2
                        L29_2[4] = L33_2
                        L30_2 = {}
                        L31_2 = 1425
                        L32_2 = 596
                        L33_2 = 1542
                        L34_2 = 710
                        L30_2[1] = L31_2
                        L30_2[2] = L32_2
                        L30_2[3] = L33_2
                        L30_2[4] = L34_2
                        L31_2 = {}
                        L32_2 = 881
                        L33_2 = 735
                        L34_2 = 994
                        L35_2 = 843
                        L31_2[1] = L32_2
                        L31_2[2] = L33_2
                        L31_2[3] = L34_2
                        L31_2[4] = L35_2
                        L32_2 = {}
                        L33_2 = 1017
                        L34_2 = 730
                        L35_2 = 1133
                        L36_2 = 847
                        L32_2[1] = L33_2
                        L32_2[2] = L34_2
                        L32_2[3] = L35_2
                        L32_2[4] = L36_2
                        L33_2 = {}
                        L34_2 = 1158
                        L35_2 = 739
                        L36_2 = 1269
                        L37_2 = 846
                        L33_2[1] = L34_2
                        L33_2[2] = L35_2
                        L33_2[3] = L36_2
                        L33_2[4] = L37_2
                        L34_2 = {}
                        L35_2 = 1288
                        L36_2 = 733
                        L37_2 = 1413
                        L38_2 = 847
                        L34_2[1] = L35_2
                        L34_2[2] = L36_2
                        L34_2[3] = L37_2
                        L34_2[4] = L38_2
                        L35_2 = {}
                        L36_2 = 1427
                        L37_2 = 732
                        L38_2 = 1543
                        L39_2 = 846
                        L35_2[1] = L36_2
                        L35_2[2] = L37_2
                        L35_2[3] = L38_2
                        L35_2[4] = L39_2
                        L36_2 = {}
                        L37_2 = 880
                        L38_2 = 869
                        L39_2 = 997
                        L40_2 = 983
                        L36_2[1] = L37_2
                        L36_2[2] = L38_2
                        L36_2[3] = L39_2
                        L36_2[4] = L40_2
                        L37_2 = {}
                        L38_2 = 1019
                        L39_2 = 870
                        L40_2 = 1136
                        L41_2 = 984
                        L37_2[1] = L38_2
                        L37_2[2] = L39_2
                        L37_2[3] = L40_2
                        L37_2[4] = L41_2
                        L38_2 = {}
                        L39_2 = 1152
                        L40_2 = 870
                        L41_2 = 1272
                        L42_2 = 984
                        L38_2[1] = L39_2
                        L38_2[2] = L40_2
                        L38_2[3] = L41_2
                        L38_2[4] = L42_2
                        L39_2 = {}
                        L40_2 = 1288
                        L41_2 = 870
                        L42_2 = 1410
                        L43_2 = 984
                        L39_2[1] = L40_2
                        L39_2[2] = L41_2
                        L39_2[3] = L42_2
                        L39_2[4] = L43_2
                        L40_2 = {}
                        L41_2 = 1425
                        L42_2 = 868
                        L43_2 = 1544
                        L44_2 = 988
                        L40_2[1] = L41_2
                        L40_2[2] = L42_2
                        L40_2[3] = L43_2
                        L40_2[4] = L44_2
                        L20_2[1] = L21_2
                        L20_2[2] = L22_2
                        L20_2[3] = L23_2
                        L20_2[4] = L24_2
                        L20_2[5] = L25_2
                        L20_2[6] = L26_2
                        L20_2[7] = L27_2
                        L20_2[8] = L28_2
                        L20_2[9] = L29_2
                        L20_2[10] = L30_2
                        L20_2[11] = L31_2
                        L20_2[12] = L32_2
                        L20_2[13] = L33_2
                        L20_2[14] = L34_2
                        L20_2[15] = L35_2
                        L20_2[16] = L36_2
                        L20_2[17] = L37_2
                        L20_2[18] = L38_2
                        L20_2[19] = L39_2
                        L20_2[20] = L40_2
                        L21_2 = 1
                        L22_2 = 20
                        L23_2 = 1
                        for L24_2 = L21_2, L22_2, L23_2 do
                          L25_2 = mSleep
                          L26_2 = 100
                          L25_2(L26_2)
                          L25_2 = findMultiColorInRegionFuzzyByTable
                          L26_2 = propsList
                          L26_2 = L26_2[A1_2]
                          L27_2 = 80
                          L28_2 = L20_2[L24_2]
                          L28_2 = L28_2[1]
                          L29_2 = L20_2[L24_2]
                          L29_2 = L29_2[2]
                          L30_2 = L20_2[L24_2]
                          L30_2 = L30_2[3]
                          L31_2 = L20_2[L24_2]
                          L31_2 = L31_2[4]
                          L25_2, L26_2 = L25_2(L26_2, L27_2, L28_2, L29_2, L30_2, L31_2)
                          L19_2 = L26_2
                          L18_2 = L25_2
                          if 0 < L18_2 then
                            L25_2 = printLog
                            L26_2 = "找到 ”"
                            L27_2 = A1_2
                            L28_2 = "” 给予师父"
                            L26_2 = L26_2 .. L27_2 .. L28_2
                            L25_2(L26_2)
                            L25_2 = randomClick
                            L26_2 = 2
                            L27_2 = 300
                            L28_2 = L18_2
                            L29_2 = L19_2
                            L30_2 = 20
                            L25_2(L26_2, L27_2, L28_2, L29_2, L30_2)
                            L25_2 = randomClick
                            L26_2 = 0
                            L27_2 = 300
                            L28_2 = 344
                            L29_2 = 966
                            L30_2 = 400
                            L31_2 = 1019
                            L25_2(L26_2, L27_2, L28_2, L29_2, L30_2, L31_2)
                            break
                          end
                        end
                      end
                    end
                  end
                else
                  L17_2 = getColour
                  L18_2 = _ENV["商人鬼魂"]
                  L19_2 = "学习界面"
                  L17_2 = L17_2(L18_2, L19_2)
                  if L17_2 then
                    while true do
                      L17_2 = getColour
                      L18_2 = _ENV["商人鬼魂"]
                      L19_2 = "学习界面"
                      L17_2 = L17_2(L18_2, L19_2)
                      if L17_2 then
                        L17_2 = randomClick
                        L18_2 = 2
                        L19_2 = 500
                        L20_2 = 1703
                        L21_2 = 63
                        L17_2(L18_2, L19_2, L20_2, L21_2)
                      else
                        break
                      end
                    end
                  else
                    L17_2 = getColour
                    L18_2 = _ENV["商人鬼魂"]
                    L19_2 = "关闭对话框"
                    L17_2 = L17_2(L18_2, L19_2)
                    if L17_2 then
                      if 10 < L11_2 then
                        L17_2 = 1
                        L18_2 = 10
                        L19_2 = 1
                        for L20_2 = L17_2, L18_2, L19_2 do
                          L21_2 = getColour
                          L22_2 = _ENV["商人鬼魂"]
                          L23_2 = "关闭对话框"
                          L21_2 = L21_2(L22_2, L23_2)
                          if L21_2 then
                            L21_2 = randomClick
                            L22_2 = 2
                            L23_2 = 300
                            L24_2 = 1873
                            L25_2 = 797
                            L21_2(L22_2, L23_2, L24_2, L25_2)
                          else
                            L6_2 = 66
                            A2_2 = true
                            L4_2 = true
                            L3_2 = false
                            break
                          end
                        end
                      else
                        L17_2 = printLog
                        L18_2 = "index = "
                        L19_2 = L11_2
                        L18_2 = L18_2 .. L19_2
                        L17_2(L18_2)
                        L11_2 = L11_2 + 1
                        L17_2 = mSleep
                        L18_2 = 100
                        L17_2(L18_2)
                      end
                    else
                      L17_2 = printLog
                      L18_2 = "index = "
                      L19_2 = L11_2
                      L18_2 = L18_2 .. L19_2
                      L17_2(L18_2)
                    end
                  end
                end
              end
            end
          end
        end
      end
      ::lbl_990::
      L17_2 = getAddressNum
      L17_2 = L17_2()
      L13_2 = L17_2
      L17_2 = mSleep
      L18_2 = math
      L18_2 = L18_2.random
      L19_2 = 666
      L20_2 = 1000
      L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2 = L18_2(L19_2, L20_2)
      L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2)
    end
  end
  while true do
    L14_2 = _ENV["弹窗查看详情"]
    L14_2()
    if L3_2 then
      L6_2 = 1
      L14_2 = printLog
      L15_2 = "A: "
      L16_2 = L6_2
      L15_2 = L15_2 .. L16_2
      L14_2(L15_2)
      L3_2 = false
      L14_2 = randomClick
      L15_2 = 0
      L16_2 = 300
      L17_2 = 1673
      L18_2 = 362
      L19_2 = 1770
      L20_2 = 433
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
    else
      if L6_2 == 1 then
        L14_2 = getAddressNum
        L14_2 = L14_2()
        if L13_2 == L14_2 and L13_2 ~= "" then
          L14_2 = monitorAddress
          L15_2 = SmTable
          L15_2 = L15_2[A0_2]
          L15_2 = L15_2[17]
          L16_2 = SmTable
          L16_2 = L16_2[A0_2]
          L16_2 = L16_2[18]
          L14_2 = L14_2(L15_2, L16_2)
          if not L14_2 and not L4_2 then
            L6_2 = 2
            L7_2 = L13_2
            L14_2 = printLog
            L15_2 = "A: "
            L16_2 = L6_2
            L15_2 = L15_2 .. L16_2
            L14_2(L15_2)
            L14_2 = os
            L14_2 = L14_2.time
            L14_2 = L14_2()
            L9_2 = L14_2
        end
      end
      else
        L14_2 = os
        L14_2 = L14_2.time
        L14_2 = L14_2()
        L14_2 = L14_2 - L9_2
        if 5 < L14_2 and L9_2 ~= 0 then
          L14_2 = getAddressNum
          L14_2 = L14_2()
          if L7_2 == L14_2 then
            L3_2 = true
            L7_2 = ""
            L9_2 = 0
            L14_2 = randomClick
            L15_2 = 0
            L16_2 = 100
            L17_2 = 740
            L18_2 = 279
            L19_2 = 1326
            L20_2 = 504
            L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L14_2 = printLog
            L15_2 = "A: "
            L16_2 = L6_2
            L15_2 = L15_2 .. L16_2
            L14_2(L15_2)
        end
        else
          L14_2 = getColour
          L15_2 = colorList
          L16_2 = "梦幻精灵界面"
          L14_2 = L14_2(L15_2, L16_2)
          if L14_2 then
            L14_2 = getColour
            L15_2 = colorList
            L16_2 = "动作按钮"
            L14_2 = L14_2(L15_2, L16_2)
            if not L14_2 then
              L14_2 = getColour
              L15_2 = colorList
              L16_2 = "设置按钮"
              L14_2 = L14_2(L15_2, L16_2)
              if not L14_2 then
                L14_2 = getColour
                L15_2 = colorList
                L16_2 = "道具按钮"
                L14_2 = L14_2(L15_2, L16_2)
                if not L14_2 then
                  while true do
                    L14_2 = getColour
                    L15_2 = colorList
                    L16_2 = "梦幻精灵界面"
                    L14_2 = L14_2(L15_2, L16_2)
                    if L14_2 then
                      L14_2 = randomClick
                      L15_2 = 2
                      L16_2 = 300
                      L17_2 = 1700
                      L18_2 = 59
                      L14_2(L15_2, L16_2, L17_2, L18_2)
                    else
                      L14_2 = getColour
                      L15_2 = colorList
                      L16_2 = "梦幻精灵界面"
                      L14_2 = L14_2(L15_2, L16_2)
                      if not L14_2 then
                        L14_2 = _ENV["百级识别师门任务"]
                        L15_2 = A0_2
                        return L14_2(L15_2)
                      end
                    end
                  end
              end
            end
          end
          else
            if L6_2 <= 2 and A0_2 == "地府" then
              L14_2 = monitorAddress
              L15_2 = SmTable
              L15_2 = L15_2[A0_2]
              L15_2 = L15_2[21]
              L16_2 = SmTable
              L16_2 = L16_2[A0_2]
              L16_2 = L16_2[22]
              L14_2 = L14_2(L15_2, L16_2)
              if L14_2 then
                L7_2 = "null"
                L14_2 = randomClick
                L15_2 = 2
                L16_2 = 1300
                L17_2 = SmTable
                L17_2 = L17_2[A0_2]
                L17_2 = L17_2[23]
                L18_2 = SmTable
                L18_2 = L18_2[A0_2]
                L18_2 = L18_2[24]
                L14_2(L15_2, L16_2, L17_2, L18_2)
                L10_2 = false
            end
            else
              L14_2 = getColour
              L15_2 = _ENV["商人鬼魂"]
              L16_2 = "学习界面"
              L14_2 = L14_2(L15_2, L16_2)
              if L14_2 then
                while true do
                  L14_2 = getColour
                  L15_2 = _ENV["商人鬼魂"]
                  L16_2 = "学习界面"
                  L14_2 = L14_2(L15_2, L16_2)
                  if L14_2 then
                    L14_2 = randomClick
                    L15_2 = 2
                    L16_2 = 500
                    L17_2 = 1703
                    L18_2 = 63
                    L14_2(L15_2, L16_2, L17_2, L18_2)
                  else
                    break
                  end
                end
              else
                L14_2 = false
                L15_2 = -1
                L16_2 = -1
                if L4_2 == false then
                  L17_2 = getWordStock
                  L18_2 = WordStock
                  L19_2 = "任务"
                  L20_2 = 1396
                  L21_2 = 130
                  L22_2 = 1527
                  L23_2 = 548
                  L24_2 = 83
                  L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
                  L16_2 = L19_2
                  L15_2 = L18_2
                  L14_2 = L17_2
                  L17_2 = printLog
                  L18_2 = "num: "
                  L19_2 = L6_2
                  L20_2 = ", x="
                  L21_2 = L15_2
                  L22_2 = ", y="
                  L23_2 = L16_2
                  L18_2 = L18_2 .. L19_2 .. L20_2 .. L21_2 .. L22_2 .. L23_2
                  L17_2(L18_2)
                else
                  L17_2 = printLog
                  L18_2 = "num: "
                  L19_2 = L6_2
                  L20_2 = ", x="
                  L21_2 = L15_2
                  L22_2 = ", y="
                  L23_2 = L16_2
                  L18_2 = L18_2 .. L19_2 .. L20_2 .. L21_2 .. L22_2 .. L23_2
                  L17_2(L18_2)
                end
                L17_2 = getWordStock
                L18_2 = WordStock
                L19_2 = "其他事情"
                L20_2 = 1455
                L21_2 = 480
                L22_2 = 1733
                L23_2 = 556
                L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
                if L5_2 and L17_2 then
                  L6_2 = 3
                  L5_2 = false
                  L20_2 = mSleep
                  L21_2 = math
                  L21_2 = L21_2.random
                  L22_2 = 111
                  L23_2 = 333
                  L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2 = L21_2(L22_2, L23_2)
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2)
                  L20_2 = randomClick
                  L21_2 = 2
                  L22_2 = 300
                  L23_2 = L18_2
                  L24_2 = L19_2
                  L25_2 = 10
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                  L10_2 = false
                elseif not A2_2 and L14_2 then
                  L20_2 = randomClick
                  L21_2 = 0
                  L22_2 = 300
                  L23_2 = L18_2 + 50
                  L24_2 = L19_2 + 140
                  L25_2 = L18_2 + 100
                  L26_2 = L19_2 + 200
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
                  L6_2 = 4
                  L10_2 = false
                elseif A2_2 and L14_2 then
                  L6_2 = 5
                  L20_2 = randomClick
                  L21_2 = 0
                  L22_2 = 300
                  L23_2 = L18_2 + 50
                  L24_2 = L19_2
                  L25_2 = L18_2 + 100
                  L26_2 = L19_2 + 50
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
                  L4_2 = true
                  L10_2 = false
                else
                  if not L4_2 then
                    L20_2 = getColour
                    L21_2 = colorList
                    L22_2 = "完成师门任务红"
                    L20_2 = L20_2(L21_2, L22_2)
                    if L20_2 then
                      L20_2 = getColour
                      L21_2 = _ENV["商人鬼魂"]
                      L22_2 = "关闭对话框"
                      L20_2 = L20_2(L21_2, L22_2)
                      if L20_2 then
                        L4_2 = true
                        L10_2 = false
                    end
                  end
                  else
                    if L6_2 ~= 5 then
                      L20_2 = getColour
                      L21_2 = _ENV["商人鬼魂"]
                      L22_2 = "给予界面"
                      L20_2 = L20_2(L21_2, L22_2)
                      if L20_2 then
                        L6_2 = 5
                        L10_2 = false
                        L7_2 = ""
                        L20_2 = os
                        L20_2 = L20_2.time
                        L20_2 = L20_2()
                        while true do
                          L21_2 = _ENV["弹窗查看详情"]
                          L21_2()
                          L21_2 = printLog
                          L22_2 = "给予物品: "
                          L23_2 = A1_2
                          L22_2 = L22_2 .. L23_2
                          L21_2(L22_2)
                          L21_2 = findMultiColorInRegionFuzzyByTable
                          L22_2 = propsList
                          L22_2 = L22_2[A1_2]
                          L23_2 = 85
                          L24_2 = 377
                          L25_2 = 525
                          L26_2 = 489
                          L27_2 = 638
                          L21_2, L22_2 = L21_2(L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                          if 0 < L21_2 then
                            L23_2 = randomClick
                            L24_2 = 2
                            L25_2 = 500
                            L26_2 = 556
                            L27_2 = 973
                            L28_2 = 20
                            L23_2(L24_2, L25_2, L26_2, L27_2, L28_2)
                            L4_2 = true
                            A2_2 = true
                            break
                          else
                            if not L4_2 then
                              L23_2 = os
                              L23_2 = L23_2.time
                              L23_2 = L23_2()
                              L23_2 = L23_2 - L20_2
                              if 10 < L23_2 then
                                L23_2 = getColour
                                L24_2 = _ENV["商人鬼魂"]
                                L25_2 = "给予界面"
                                L23_2 = L23_2(L24_2, L25_2)
                                if L23_2 then
                                  L4_2 = true
                                  A2_2 = true
                                  L23_2 = randomClick
                                  L24_2 = 2
                                  L25_2 = 300
                                  L26_2 = 1585
                                  L27_2 = 268
                                  L23_2(L24_2, L25_2, L26_2, L27_2)
                                  break
                              end
                            end
                            else
                              if L4_2 then
                                L23_2 = os
                                L23_2 = L23_2.time
                                L23_2 = L23_2()
                                L23_2 = L23_2 - L20_2
                                if 10 < L23_2 then
                                  L23_2 = getColour
                                  L24_2 = _ENV["商人鬼魂"]
                                  L25_2 = "给予界面"
                                  L23_2 = L23_2(L24_2, L25_2)
                                  if L23_2 then
                                    L23_2 = randomClick
                                    L24_2 = 2
                                    L25_2 = 300
                                    L26_2 = 1585
                                    L27_2 = 268
                                    L23_2(L24_2, L25_2, L26_2, L27_2)
                                    break
                                end
                              end
                              else
                                L23_2 = os
                                L23_2 = L23_2.time
                                L23_2 = L23_2()
                                L24_2 = _ENV["卡点时间"]
                                L23_2 = L23_2 - L24_2
                                if 300 < L23_2 then
                                  L23_2 = _ENV["卡点处理"]
                                  return L23_2()
                                else
                                  L23_2 = {}
                                  L24_2 = {}
                                  L25_2 = 884
                                  L26_2 = 462
                                  L27_2 = 992
                                  L28_2 = 574
                                  L24_2[1] = L25_2
                                  L24_2[2] = L26_2
                                  L24_2[3] = L27_2
                                  L24_2[4] = L28_2
                                  L25_2 = {}
                                  L26_2 = 1022
                                  L27_2 = 465
                                  L28_2 = 1130
                                  L29_2 = 570
                                  L25_2[1] = L26_2
                                  L25_2[2] = L27_2
                                  L25_2[3] = L28_2
                                  L25_2[4] = L29_2
                                  L26_2 = {}
                                  L27_2 = 1156
                                  L28_2 = 466
                                  L29_2 = 1263
                                  L30_2 = 571
                                  L26_2[1] = L27_2
                                  L26_2[2] = L28_2
                                  L26_2[3] = L29_2
                                  L26_2[4] = L30_2
                                  L27_2 = {}
                                  L28_2 = 1294
                                  L29_2 = 465
                                  L30_2 = 1402
                                  L31_2 = 570
                                  L27_2[1] = L28_2
                                  L27_2[2] = L29_2
                                  L27_2[3] = L30_2
                                  L27_2[4] = L31_2
                                  L28_2 = {}
                                  L29_2 = 1426
                                  L30_2 = 462
                                  L31_2 = 1542
                                  L32_2 = 576
                                  L28_2[1] = L29_2
                                  L28_2[2] = L30_2
                                  L28_2[3] = L31_2
                                  L28_2[4] = L32_2
                                  L29_2 = {}
                                  L30_2 = 881
                                  L31_2 = 600
                                  L32_2 = 995
                                  L33_2 = 709
                                  L29_2[1] = L30_2
                                  L29_2[2] = L31_2
                                  L29_2[3] = L32_2
                                  L29_2[4] = L33_2
                                  L30_2 = {}
                                  L31_2 = 1019
                                  L32_2 = 600
                                  L33_2 = 1132
                                  L34_2 = 708
                                  L30_2[1] = L31_2
                                  L30_2[2] = L32_2
                                  L30_2[3] = L33_2
                                  L30_2[4] = L34_2
                                  L31_2 = {}
                                  L32_2 = 1156
                                  L33_2 = 600
                                  L34_2 = 1271
                                  L35_2 = 709
                                  L31_2[1] = L32_2
                                  L31_2[2] = L33_2
                                  L31_2[3] = L34_2
                                  L31_2[4] = L35_2
                                  L32_2 = {}
                                  L33_2 = 1291
                                  L34_2 = 600
                                  L35_2 = 1404
                                  L36_2 = 710
                                  L32_2[1] = L33_2
                                  L32_2[2] = L34_2
                                  L32_2[3] = L35_2
                                  L32_2[4] = L36_2
                                  L33_2 = {}
                                  L34_2 = 1425
                                  L35_2 = 596
                                  L36_2 = 1542
                                  L37_2 = 710
                                  L33_2[1] = L34_2
                                  L33_2[2] = L35_2
                                  L33_2[3] = L36_2
                                  L33_2[4] = L37_2
                                  L34_2 = {}
                                  L35_2 = 881
                                  L36_2 = 735
                                  L37_2 = 994
                                  L38_2 = 843
                                  L34_2[1] = L35_2
                                  L34_2[2] = L36_2
                                  L34_2[3] = L37_2
                                  L34_2[4] = L38_2
                                  L35_2 = {}
                                  L36_2 = 1017
                                  L37_2 = 730
                                  L38_2 = 1133
                                  L39_2 = 847
                                  L35_2[1] = L36_2
                                  L35_2[2] = L37_2
                                  L35_2[3] = L38_2
                                  L35_2[4] = L39_2
                                  L36_2 = {}
                                  L37_2 = 1158
                                  L38_2 = 739
                                  L39_2 = 1269
                                  L40_2 = 846
                                  L36_2[1] = L37_2
                                  L36_2[2] = L38_2
                                  L36_2[3] = L39_2
                                  L36_2[4] = L40_2
                                  L37_2 = {}
                                  L38_2 = 1288
                                  L39_2 = 733
                                  L40_2 = 1413
                                  L41_2 = 847
                                  L37_2[1] = L38_2
                                  L37_2[2] = L39_2
                                  L37_2[3] = L40_2
                                  L37_2[4] = L41_2
                                  L38_2 = {}
                                  L39_2 = 1427
                                  L40_2 = 732
                                  L41_2 = 1543
                                  L42_2 = 846
                                  L38_2[1] = L39_2
                                  L38_2[2] = L40_2
                                  L38_2[3] = L41_2
                                  L38_2[4] = L42_2
                                  L39_2 = {}
                                  L40_2 = 880
                                  L41_2 = 869
                                  L42_2 = 997
                                  L43_2 = 983
                                  L39_2[1] = L40_2
                                  L39_2[2] = L41_2
                                  L39_2[3] = L42_2
                                  L39_2[4] = L43_2
                                  L40_2 = {}
                                  L41_2 = 1019
                                  L42_2 = 870
                                  L43_2 = 1136
                                  L44_2 = 984
                                  L40_2[1] = L41_2
                                  L40_2[2] = L42_2
                                  L40_2[3] = L43_2
                                  L40_2[4] = L44_2
                                  L41_2 = {}
                                  L42_2 = 1152
                                  L43_2 = 870
                                  L44_2 = 1272
                                  L45_2 = 984
                                  L41_2[1] = L42_2
                                  L41_2[2] = L43_2
                                  L41_2[3] = L44_2
                                  L41_2[4] = L45_2
                                  L42_2 = {}
                                  L43_2 = 1288
                                  L44_2 = 870
                                  L45_2 = 1410
                                  L46_2 = 984
                                  L42_2[1] = L43_2
                                  L42_2[2] = L44_2
                                  L42_2[3] = L45_2
                                  L42_2[4] = L46_2
                                  L43_2 = {}
                                  L44_2 = 1425
                                  L45_2 = 868
                                  L46_2 = 1544
                                  L47_2 = 988
                                  L43_2[1] = L44_2
                                  L43_2[2] = L45_2
                                  L43_2[3] = L46_2
                                  L43_2[4] = L47_2
                                  L23_2[1] = L24_2
                                  L23_2[2] = L25_2
                                  L23_2[3] = L26_2
                                  L23_2[4] = L27_2
                                  L23_2[5] = L28_2
                                  L23_2[6] = L29_2
                                  L23_2[7] = L30_2
                                  L23_2[8] = L31_2
                                  L23_2[9] = L32_2
                                  L23_2[10] = L33_2
                                  L23_2[11] = L34_2
                                  L23_2[12] = L35_2
                                  L23_2[13] = L36_2
                                  L23_2[14] = L37_2
                                  L23_2[15] = L38_2
                                  L23_2[16] = L39_2
                                  L23_2[17] = L40_2
                                  L23_2[18] = L41_2
                                  L23_2[19] = L42_2
                                  L23_2[20] = L43_2
                                  L24_2 = 1
                                  L25_2 = 20
                                  L26_2 = 1
                                  for L27_2 = L24_2, L25_2, L26_2 do
                                    L28_2 = findMultiColorInRegionFuzzyByTable
                                    L29_2 = propsList
                                    L29_2 = L29_2[A1_2]
                                    L30_2 = 80
                                    L31_2 = L23_2[L27_2]
                                    L31_2 = L31_2[1]
                                    L32_2 = L23_2[L27_2]
                                    L32_2 = L32_2[2]
                                    L33_2 = L23_2[L27_2]
                                    L33_2 = L33_2[3]
                                    L34_2 = L23_2[L27_2]
                                    L34_2 = L34_2[4]
                                    L28_2, L29_2 = L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                                    L22_2 = L29_2
                                    L21_2 = L28_2
                                    if 0 < L21_2 then
                                      L28_2 = randomClick
                                      L29_2 = 2
                                      L30_2 = 300
                                      L31_2 = L21_2
                                      L32_2 = L22_2
                                      L33_2 = 20
                                      L28_2(L29_2, L30_2, L31_2, L32_2, L33_2)
                                      L28_2 = randomClick
                                      L29_2 = 0
                                      L30_2 = 300
                                      L31_2 = 344
                                      L32_2 = 966
                                      L33_2 = 400
                                      L34_2 = 1019
                                      L28_2(L29_2, L30_2, L31_2, L32_2, L33_2, L34_2)
                                      break
                                    end
                                  end
                                end
                              end
                            end
                          end
                        end
                    end
                    else
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L21_2 = _ENV["卡点时间"]
                      L20_2 = L20_2 - L21_2
                      if 300 < L20_2 then
                        L20_2 = _ENV["卡点处理"]
                        return L20_2()
                      elseif A2_2 and L4_2 and not L14_2 then
                        if L6_2 ~= 66 then
                          L6_2 = 6
                        end
                        L20_2 = mSleep
                        L21_2 = 1000
                        L20_2(L21_2)
                        L20_2 = false
                        L21_2 = 1
                        L22_2 = 50
                        L23_2 = 1
                        for L24_2 = L21_2, L22_2, L23_2 do
                          L25_2 = printLog
                          L26_2 = "接任务: "
                          L27_2 = L24_2
                          L26_2 = L26_2 .. L27_2
                          L25_2(L26_2)
                          if L20_2 then
                            L25_2 = getColour
                            L26_2 = colorList
                            L27_2 = "动作按钮"
                            L25_2 = L25_2(L26_2, L27_2)
                            if L25_2 then
                              goto lbl_1613
                            end
                          end
                          if L20_2 then
                            L25_2 = getColour
                            L26_2 = colorList
                            L27_2 = "设置按钮"
                            L25_2 = L25_2(L26_2, L27_2)
                            if L25_2 then
                              goto lbl_1613
                            end
                          end
                          if L20_2 then
                            L25_2 = getColour
                            L26_2 = colorList
                            L27_2 = "道具按钮"
                            L25_2 = L25_2(L26_2, L27_2)
                            if L25_2 then
                              ::lbl_1613::
                              L25_2 = addSm
                              if L25_2 then
                                while true do
                                  L25_2 = getColour
                                  L26_2 = colorList
                                  L27_2 = "团队副本"
                                  L25_2 = L25_2(L26_2, L27_2)
                                  if L25_2 then
                                    L25_2 = randomClick
                                    L26_2 = 2
                                    L27_2 = 300
                                    L28_2 = 1593
                                    L29_2 = 82
                                    L25_2(L26_2, L27_2, L28_2, L29_2)
                                  else
                                    break
                                  end
                                end
                                addSm = false
                              end
                              L25_2 = _ENV["百级前往领取任务"]
                              L26_2 = A0_2
                              return L25_2(L26_2)
                          end
                          else
                            L25_2 = getColour
                            L26_2 = _ENV["商人鬼魂"]
                            L27_2 = "关闭对话框"
                            L25_2 = L25_2(L26_2, L27_2)
                            if L25_2 then
                              L25_2 = randomClick
                              L26_2 = 2
                              L27_2 = 300
                              L28_2 = 1873
                              L29_2 = 797
                              L25_2(L26_2, L27_2, L28_2, L29_2)
                              L20_2 = true
                            else
                              if 45 <= L24_2 then
                                L25_2 = getColour
                                L26_2 = colorList
                                L27_2 = "动作按钮"
                                L25_2 = L25_2(L26_2, L27_2)
                                if L25_2 then
                                  goto lbl_1675
                                end
                              end
                              if 45 <= L24_2 then
                                L25_2 = getColour
                                L26_2 = colorList
                                L27_2 = "设置按钮"
                                L25_2 = L25_2(L26_2, L27_2)
                                if L25_2 then
                                  goto lbl_1675
                                end
                              end
                              if 45 <= L24_2 then
                                L25_2 = getColour
                                L26_2 = colorList
                                L27_2 = "道具按钮"
                                L25_2 = L25_2(L26_2, L27_2)
                                if L25_2 then
                                  ::lbl_1675::
                                  L20_2 = true
                              end
                              elseif L6_2 == 66 then
                                L20_2 = true
                              else
                                L25_2 = mSleep
                                L26_2 = 100
                                L25_2(L26_2)
                              end
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
    if L10_2 then
      L14_2 = getAddressNum
      L14_2 = L14_2()
      L13_2 = L14_2
      L14_2 = mSleep
      L15_2 = 888
      L14_2(L15_2)
    end
  end
end

_ENV["百级完成交任务原"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["_前往"]
  L2_2 = L2_2["长寿村"]
  L2_2()
  while true do
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["固定坐标"]
    L3_2 = "长寿村"
    L4_2 = 1099
    L5_2 = 690
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["进入光圈"]
    L3_2 = 1093
    L4_2 = 488
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = 1500
    L2_2(L3_2)
    L2_2 = _ENV["起号"]
    L2_2 = L2_2["坐标"]
    L2_2, L3_2 = L2_2()
    y = L3_2
    x = L2_2
    L2_2 = y
    if L2_2 < 30 then
      break
    end
  end
  L2_2 = _ENV["_功能"]
  L2_2 = L2_2["屏蔽"]
  L3_2 = 4
  L2_2(L3_2)
  L2_2 = nil
  while true do
    if L2_2 ~= nil then
      L3_2 = os
      L3_2 = L3_2.time
      L3_2 = L3_2()
      L3_2 = L3_2 - L2_2
      if not (3 < L3_2) then
        goto lbl_61
      end
    end
    L3_2 = _tap
    L4_2 = 1094
    L5_2 = 751
    L6_2 = 1
    L7_2 = 1
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = _Sleep
    L4_2 = 500
    L5_2 = 1000
    L3_2(L4_2, L5_2)
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L2_2 = L3_2
    ::lbl_61::
    L3_2 = _find_tb
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2["购买按钮"]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _cmp_cx
      L4_2 = Color
      L4_2 = L4_2["师门"]
      L4_2 = L4_2[A1_2]
      L4_2 = L4_2["物品界面"]
      L5_2 = {}
      L6_2 = 30
      L7_2 = 100
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        break
      end
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
  while true do
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2[A1_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
    end
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L5_2 = "选中"
    L6_2 = A1_2
    L5_2 = L5_2 .. L6_2
    L4_2 = L4_2[L5_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      L3_2 = _tap
      L4_2 = 1506
      L5_2 = 968
      L6_2 = 1
      L7_2 = 10
      L3_2(L4_2, L5_2, L6_2, L7_2)
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      repeat
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["师门"]
        L4_2 = L4_2[A1_2]
        L4_2 = L4_2["关闭界面"]
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["师门"]
          L4_2 = L4_2[A1_2]
          L4_2 = L4_2["关闭购买对话"]
          L3_2 = L3_2(L4_2)
          if not L3_2 then
            goto lbl_142
          end
        end
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
        ::lbl_142::
        L3_2 = mSleep
        L4_2 = 300
        L3_2(L4_2)
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["主界面"]
        L3_2 = L3_2(L4_2)
      until L3_2
      L3_2 = _print
      L4_2 = "购买成功！"
      L3_2(L4_2)
      L3_2 = false
      return L3_2
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
end

_ENV["购买40级装备"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["_前往"]
  L2_2 = L2_2["长寿村"]
  L2_2()
  while true do
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["固定坐标"]
    L3_2 = "长寿村"
    L4_2 = 1068
    L5_2 = 572
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["进入光圈"]
    L3_2 = 872
    L4_2 = 524
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = 1500
    L2_2(L3_2)
    L2_2 = _ENV["起号"]
    L2_2 = L2_2["坐标"]
    L2_2, L3_2 = L2_2()
    y = L3_2
    x = L2_2
    L2_2 = y
    if L2_2 < 30 then
      break
    end
  end
  repeat
    repeat
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
      if not (25 <= L2_2) then
        L2_2 = y
        if not (L2_2 <= 11) then
          goto lbl_55
        end
      end
      L2_2 = tap
      L3_2 = 820
      L4_2 = 732
      L2_2(L3_2, L4_2)
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["叉叉"]
      L2_2()
      L2_2 = mSleep
      L3_2 = 3000
      L2_2(L3_2)
      ::lbl_55::
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
    until L2_2 <= 25
    L2_2 = y
  until L2_2 <= 9
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取目标屏幕坐标"]
  L3_2 = "傲来国服饰店"
  L4_2 = x
  L5_2 = y
  L6_2 = 16
  L7_2 = 11
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  _ENV["老板坐标y"] = L3_2
  _ENV["老板坐标x"] = L2_2
  L2_2 = _ENV["_功能"]
  L2_2 = L2_2["屏蔽"]
  L3_2 = 4
  L2_2(L3_2)
  L2_2 = nil
  while true do
    if L2_2 ~= nil then
      L3_2 = os
      L3_2 = L3_2.time
      L3_2 = L3_2()
      L3_2 = L3_2 - L2_2
      if not (3 < L3_2) then
        goto lbl_103
      end
    end
    L3_2 = _tap
    L4_2 = _ENV["老板坐标x"]
    L5_2 = _ENV["老板坐标y"]
    L6_2 = 1
    L7_2 = 1
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = _Sleep
    L4_2 = 500
    L5_2 = 1000
    L3_2(L4_2, L5_2)
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L2_2 = L3_2
    ::lbl_103::
    L3_2 = _find_tb
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2["购买按钮"]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _cmp_cx
      L4_2 = Color
      L4_2 = L4_2["师门"]
      L4_2 = L4_2[A1_2]
      L4_2 = L4_2["物品界面"]
      L5_2 = {}
      L6_2 = 30
      L7_2 = 100
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        break
      end
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
  while true do
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2[A1_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
    end
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L5_2 = "选中"
    L6_2 = A1_2
    L5_2 = L5_2 .. L6_2
    L4_2 = L4_2[L5_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      L3_2 = _tap
      L4_2 = 1506
      L5_2 = 968
      L6_2 = 1
      L7_2 = 10
      L3_2(L4_2, L5_2, L6_2, L7_2)
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      repeat
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["师门"]
        L4_2 = L4_2[A1_2]
        L4_2 = L4_2["关闭界面"]
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["师门"]
          L4_2 = L4_2[A1_2]
          L4_2 = L4_2["关闭购买对话"]
          L3_2 = L3_2(L4_2)
          if not L3_2 then
            goto lbl_184
          end
        end
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
        ::lbl_184::
        L3_2 = mSleep
        L4_2 = 300
        L3_2(L4_2)
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["主界面"]
        L3_2 = L3_2(L4_2)
      until L3_2
      L3_2 = _print
      L4_2 = "购买成功！"
      L3_2(L4_2)
      L3_2 = false
      return L3_2
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
end

_ENV["购买40级武器"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["_前往"]
  L2_2 = L2_2["长寿村酒店"]
  L2_2()
  L2_2 = _ENV["_功能"]
  L2_2 = L2_2["屏蔽"]
  L3_2 = 4
  L2_2(L3_2)
  L2_2 = nil
  while true do
    if L2_2 ~= nil then
      L3_2 = os
      L3_2 = L3_2.time
      L3_2 = L3_2()
      L3_2 = L3_2 - L2_2
      if not (3 < L3_2) then
        goto lbl_38
      end
    end
    L3_2 = _tap
    L4_2 = 648
    L5_2 = 562
    L6_2 = 1
    L7_2 = 1
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = _Sleep
    L4_2 = 500
    L5_2 = 1000
    L3_2(L4_2, L5_2)
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L2_2 = L3_2
    ::lbl_38::
    L3_2 = _find_tb
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2["购买按钮"]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _cmp_cx
      L4_2 = Color
      L4_2 = L4_2["师门"]
      L4_2 = L4_2[A1_2]
      L4_2 = L4_2["物品界面"]
      L5_2 = {}
      L6_2 = 30
      L7_2 = 100
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        break
      end
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
  while true do
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2[A1_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
    end
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L5_2 = "选中"
    L6_2 = A1_2
    L5_2 = L5_2 .. L6_2
    L4_2 = L4_2[L5_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      L3_2 = _tap
      L4_2 = 1506
      L5_2 = 968
      L6_2 = 1
      L7_2 = 10
      L3_2(L4_2, L5_2, L6_2, L7_2)
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      repeat
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["师门"]
        L4_2 = L4_2[A1_2]
        L4_2 = L4_2["关闭界面"]
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["师门"]
          L4_2 = L4_2[A1_2]
          L4_2 = L4_2["关闭购买对话"]
          L3_2 = L3_2(L4_2)
          if not L3_2 then
            goto lbl_119
          end
        end
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
        ::lbl_119::
        L3_2 = mSleep
        L4_2 = 300
        L3_2(L4_2)
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["主界面"]
        L3_2 = L3_2(L4_2)
      until L3_2
      L3_2 = _print
      L4_2 = "购买成功！"
      L3_2(L4_2)
      L3_2 = false
      return L3_2
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
end

_ENV["购买虎骨酒"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["_使用"]
  L2_2 = L2_2["旗子"]
  L3_2 = "傲来国"
  L4_2 = "傲来_东海湾d"
  L2_2 = L2_2(L3_2, L4_2)
  if L2_2 == "关闭" then
    L2_2 = _ENV["_使用"]
    L2_2 = L2_2["飞行符"]
    L3_2 = "傲来国"
    L2_2(L3_2)
    L2_2 = _ENV["_返回"]
    L2_2 = L2_2["地图"]
    L3_2 = "傲来国"
    L2_2(L3_2)
  end
  while true do
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["固定坐标"]
    L3_2 = "傲来国"
    L4_2 = 1289
    L5_2 = 763
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["进入光圈"]
    L3_2 = 781
    L4_2 = 338
    L2_2(L3_2, L4_2)
    L2_2 = _cmp_tb_cx
    L3_2 = Color
    L3_2 = L3_2["地图"]
    L3_2 = L3_2["到达傲来国酒店"]
    L4_2 = {}
    L5_2 = 50
    L6_2 = 100
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    L2_2 = L2_2(L3_2, L4_2)
    if L2_2 then
      break
    end
    L2_2 = mSleep
    L3_2 = 500
    L2_2(L3_2)
  end
  repeat
    repeat
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
      if not (30 <= L2_2) then
        L2_2 = y
        if not (14 <= L2_2) then
          goto lbl_70
        end
      end
      L2_2 = tap
      L3_2 = 530
      L4_2 = 535
      L2_2(L3_2, L4_2)
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["叉叉"]
      L2_2()
      L2_2 = mSleep
      L3_2 = 3000
      L2_2(L3_2)
      ::lbl_70::
      L2_2 = _ENV["起号"]
      L2_2 = L2_2["坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
    until L2_2 <= 30
    L2_2 = y
  until L2_2 <= 14
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取目标屏幕坐标"]
  L3_2 = "傲来国酒店"
  L4_2 = x
  L5_2 = y
  L6_2 = 22
  L7_2 = 16
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  _ENV["老板坐标y"] = L3_2
  _ENV["老板坐标x"] = L2_2
  L2_2 = _ENV["_功能"]
  L2_2 = L2_2["屏蔽"]
  L3_2 = 4
  L2_2(L3_2)
  L2_2 = nil
  while true do
    if L2_2 ~= nil then
      L3_2 = os
      L3_2 = L3_2.time
      L3_2 = L3_2()
      L3_2 = L3_2 - L2_2
      if not (3 < L3_2) then
        goto lbl_118
      end
    end
    L3_2 = _tap
    L4_2 = _ENV["老板坐标x"]
    L5_2 = _ENV["老板坐标y"]
    L6_2 = 1
    L7_2 = 1
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = _Sleep
    L4_2 = 500
    L5_2 = 1000
    L3_2(L4_2, L5_2)
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L2_2 = L3_2
    ::lbl_118::
    L3_2 = _find_tb
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2["购买按钮"]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _cmp_cx
      L4_2 = Color
      L4_2 = L4_2["师门"]
      L4_2 = L4_2[A1_2]
      L4_2 = L4_2["物品界面"]
      L5_2 = {}
      L6_2 = 30
      L7_2 = 100
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        break
      end
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
  while true do
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2[A1_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
    end
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L5_2 = "选中"
    L6_2 = A1_2
    L5_2 = L5_2 .. L6_2
    L4_2 = L4_2[L5_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      L3_2 = _tap
      L4_2 = 1506
      L5_2 = 968
      L6_2 = 1
      L7_2 = 10
      L3_2(L4_2, L5_2, L6_2, L7_2)
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      repeat
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["师门"]
        L4_2 = L4_2[A1_2]
        L4_2 = L4_2["关闭界面"]
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["师门"]
          L4_2 = L4_2[A1_2]
          L4_2 = L4_2["关闭购买对话"]
          L3_2 = L3_2(L4_2)
          if not L3_2 then
            goto lbl_199
          end
        end
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
        ::lbl_199::
        L3_2 = mSleep
        L4_2 = 300
        L3_2(L4_2)
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["主界面"]
        L3_2 = L3_2(L4_2)
      until L3_2
      L3_2 = _print
      L4_2 = "购买成功！"
      L3_2(L4_2)
      L3_2 = false
      return L3_2
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
end

_ENV["购买女儿红"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = isItemExist
  L3_2 = A1_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = _ENV["_使用"]
  L2_2 = L2_2["旗子"]
  L3_2 = "长安城"
  L4_2 = "长安_酒店d"
  L2_2(L3_2, L4_2)
  while true do
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["固定坐标"]
    L3_2 = "长安城"
    L4_2 = 1494
    L5_2 = 489
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = _ENV["_前往"]
    L2_2 = L2_2["进入光圈"]
    L3_2 = 1093
    L4_2 = 410
    L2_2(L3_2, L4_2)
    L2_2 = _cmp_tb_cx
    L3_2 = Color
    L3_2 = L3_2["地图"]
    L3_2 = L3_2["到达长安城药店"]
    L4_2 = {}
    L5_2 = 50
    L6_2 = 100
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    L2_2 = L2_2(L3_2, L4_2)
    if L2_2 then
      break
    end
    L2_2 = mSleep
    L3_2 = 500
    L2_2(L3_2)
  end
  L2_2 = _ENV["_功能"]
  L2_2 = L2_2["屏蔽"]
  L3_2 = 4
  L2_2(L3_2)
  L2_2 = nil
  while true do
    if L2_2 ~= nil then
      L3_2 = os
      L3_2 = L3_2.time
      L3_2 = L3_2()
      L3_2 = L3_2 - L2_2
      if not (3 < L3_2) then
        goto lbl_66
      end
    end
    L3_2 = _tap
    L4_2 = 666
    L5_2 = 323
    L6_2 = 1
    L7_2 = 3
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = _Sleep
    L4_2 = 500
    L5_2 = 1000
    L3_2(L4_2, L5_2)
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L2_2 = L3_2
    ::lbl_66::
    L3_2 = _find_tb
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2["购买按钮"]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _cmp_cx
      L4_2 = Color
      L4_2 = L4_2["师门"]
      L4_2 = L4_2[A1_2]
      L4_2 = L4_2["物品界面"]
      L5_2 = {}
      L6_2 = 30
      L7_2 = 100
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        break
      end
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
  while true do
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L4_2 = L4_2[A1_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
    end
    L3_2 = _cmp
    L4_2 = Color
    L4_2 = L4_2["师门"]
    L4_2 = L4_2[A1_2]
    L5_2 = "选中"
    L6_2 = A1_2
    L5_2 = L5_2 .. L6_2
    L4_2 = L4_2[L5_2]
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      L3_2 = _tap
      L4_2 = 1506
      L5_2 = 968
      L6_2 = 1
      L7_2 = 10
      L3_2(L4_2, L5_2, L6_2, L7_2)
      L3_2 = _Sleep
      L4_2 = 400
      L5_2 = 800
      L3_2(L4_2, L5_2)
      repeat
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["师门"]
        L4_2 = L4_2[A1_2]
        L4_2 = L4_2["关闭界面"]
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["师门"]
          L4_2 = L4_2[A1_2]
          L4_2 = L4_2["关闭购买对话"]
          L3_2 = L3_2(L4_2)
          if not L3_2 then
            goto lbl_147
          end
        end
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
        ::lbl_147::
        L3_2 = mSleep
        L4_2 = 300
        L3_2(L4_2)
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["主界面"]
        L3_2 = L3_2(L4_2)
      until L3_2
      L3_2 = _print
      L4_2 = "购买成功！"
      L3_2(L4_2)
      L3_2 = false
      return L3_2
    end
    L3_2 = mSleep
    L4_2 = 100
    L3_2(L4_2)
  end
end

_ENV["长安城药店购买"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L2_2 = true
  L3_2 = 0
  L4_2 = isItemExist
  L5_2 = A1_2
  L4_2 = L4_2(L5_2)
  if L4_2 then
    L4_2 = false
    return L4_2
  end
  L4_2 = _ENV["门派传送"]
  if L4_2 == "是" then
    L4_2 = _ENV["长安传送NPC"]
    L5_2 = A0_2
    L4_2(L5_2)
  else
    L4_2 = useCAFlag
    L5_2 = 1044
    L6_2 = 829
    L7_2 = 1125
    L8_2 = 913
    L9_2 = 1182
    L10_2 = 509
    L11_2 = 910
    L12_2 = 766
    L13_2 = 1002
    L14_2 = 841
    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    L4_2 = mSleep
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 500
    L7_2 = 800
    L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L5_2(L6_2, L7_2)
    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
  end
  L4_2 = _find
  L5_2 = Color
  L5_2 = L5_2["红尘"]
  L5_2 = L5_2["没点了"]
  L4_2 = L4_2(L5_2)
  if L4_2 then
    L4_2 = _ENV["没点了下号"]
    return L4_2()
  end
  while true do
    L4_2 = _ENV["弹窗查看详情"]
    L4_2()
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["红尘"]
    L5_2 = L5_2["没点了"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _ENV["没点了下号"]
      L4_2()
      L4_2 = _ENV["_随机延时"]
      L5_2 = 811
      L4_2(L5_2)
    end
    L4_2 = getWordStock
    L5_2 = WordStock
    L6_2 = "长安城"
    L7_2 = 154
    L8_2 = 37
    L9_2 = 308
    L10_2 = 78
    L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    if not L4_2 then
      L4_2 = getNewYearView
      L5_2 = "长安城"
      L4_2 = L4_2(L5_2)
      if not L4_2 then
        goto lbl_325
      end
    end
    L4_2 = os
    L4_2 = L4_2.time
    L4_2 = L4_2()
    _ENV["卡点时间"] = L4_2
    while true do
      L4_2 = _ENV["弹窗查看详情"]
      L4_2()
      L4_2 = getColour
      L5_2 = colorList
      L6_2 = "长安小地图"
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = printLog
        L5_2 = "前往商会：329,18"
        L4_2(L5_2)
        L4_2 = 1098.37
        L5_2 = 846.46
        L6_2 = randomTap
        L7_2 = L4_2
        L8_2 = L5_2
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 80
        L9_2 = 150
        L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
        L6_2 = tap
        L7_2 = L4_2
        L8_2 = L5_2
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 800
        L9_2 = 1500
        L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
        L2_2 = false
        L6_2 = randomClick
        L7_2 = 2
        L8_2 = 400
        L9_2 = 1677
        L10_2 = 79
        L6_2(L7_2, L8_2, L9_2, L10_2)
        L6_2 = os
        L6_2 = L6_2.time
        L6_2 = L6_2()
        L3_2 = L6_2
      else
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L5_2 = _ENV["卡点时间"]
        L4_2 = L4_2 - L5_2
        if 300 < L4_2 then
          L4_2 = 66
          return L4_2
        else
          L4_2 = getColour
          L5_2 = colorList
          L6_2 = "宠物属性页面"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 then
            L4_2 = randomClick
            L5_2 = 2
            L6_2 = 300
            L7_2 = 1705
            L8_2 = 76
            L9_2 = 20
            L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
          else
            L4_2 = monitorAddress
            L5_2 = "329"
            L6_2 = "19"
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = wordStock
              L4_2 = L4_2["隐藏摊位"]
              L4_2()
              L4_2 = true
              while true do
                L5_2 = _ENV["弹窗查看详情"]
                L5_2()
                L5_2 = getWordStock
                L6_2 = WordStock
                L7_2 = "买东西"
                L8_2 = 1523
                L9_2 = 163
                L10_2 = 1670
                L11_2 = 223
                L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                if L5_2 then
                  L5_2 = randomClick
                  L6_2 = 0
                  L7_2 = 1300
                  L8_2 = 1483
                  L9_2 = 170
                  L10_2 = 1694
                  L11_2 = 226
                  L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                else
                  if not L4_2 then
                    L5_2 = getColour
                    L6_2 = colorList
                    L7_2 = "商会列表"
                    L5_2 = L5_2(L6_2, L7_2)
                    if L5_2 then
                      L5_2 = randomClick
                      L6_2 = 2
                      L7_2 = 300
                      L8_2 = 98
                      L9_2 = 960
                      L5_2(L6_2, L7_2, L8_2, L9_2)
                      L5_2 = _ENV["物品店"]
                      if L5_2 ~= nil then
                        L5_2 = _ENV["物品店"]
                        if L5_2 ~= "" then
                          goto lbl_206
                        end
                      end
                      L5_2 = _ENV["百级龟速识别"]
                      L6_2 = A1_2
                      do return L5_2(L6_2) end
                      goto lbl_296
                      ::lbl_206::
                      L5_2 = _ENV["百级查找对于物品"]
                      L6_2 = _ENV["物品店"]
                      L7_2 = A1_2
                      L8_2 = "物品"
                      return L5_2(L6_2, L7_2, L8_2)
                  end
                  else
                    L5_2 = os
                    L5_2 = L5_2.time
                    L5_2 = L5_2()
                    L6_2 = _ENV["卡点时间"]
                    L5_2 = L5_2 - L6_2
                    if 300 < L5_2 then
                      L5_2 = 66
                      return L5_2
                    else
                      if not L4_2 then
                        L5_2 = getColour
                        L6_2 = colorList
                        L7_2 = "关闭对话框"
                        L5_2 = L5_2(L6_2, L7_2)
                        if L5_2 then
                          L5_2 = getWordStock
                          L6_2 = WordStock
                          L7_2 = "买东西"
                          L8_2 = 1523
                          L9_2 = 163
                          L10_2 = 1670
                          L11_2 = 223
                          L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                          if not L5_2 then
                            L4_2 = true
                            L5_2 = randomClick
                            L6_2 = 2
                            L7_2 = 500
                            L8_2 = 1848
                            L9_2 = 71
                            L5_2(L6_2, L7_2, L8_2, L9_2)
                        end
                      end
                      elseif L4_2 then
                        L4_2 = false
                        L5_2 = randomClick
                        L6_2 = 2
                        L7_2 = 400
                        L8_2 = 844
                        L9_2 = 538
                        L5_2(L6_2, L7_2, L8_2, L9_2)
                      else
                        if not L4_2 then
                          L5_2 = monitorAddress
                          L6_2 = "329"
                          L7_2 = "19"
                          L5_2 = L5_2(L6_2, L7_2)
                          if not L5_2 then
                            L5_2 = getWordStock
                            L6_2 = WordStock
                            L7_2 = "长安城"
                            L8_2 = 138
                            L9_2 = 18
                            L10_2 = 329
                            L11_2 = 86
                            L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                            if L5_2 then
                              goto lbl_290
                            end
                          end
                        end
                        if not L4_2 then
                          L5_2 = monitorAddress
                          L6_2 = "329"
                          L7_2 = "19"
                          L5_2 = L5_2(L6_2, L7_2)
                          if not L5_2 then
                            L5_2 = getNewYearView
                            L6_2 = "长安城"
                            L5_2 = L5_2(L6_2)
                            ::lbl_290::
                            if L5_2 then
                              L2_2 = true
                              L5_2 = os
                              L5_2 = L5_2.time
                              L5_2 = L5_2()
                              L3_2 = L5_2
                              goto lbl_324
                            end
                          end
                        end
                      end
                    end
                  end
                end
                ::lbl_296::
              end
            else
              L4_2 = os
              L4_2 = L4_2.time
              L4_2 = L4_2()
              L4_2 = L4_2 - L3_2
              if 50 < L4_2 and L3_2 ~= 0 then
                L2_2 = true
                L3_2 = 0
              elseif L2_2 then
                L4_2 = printLog
                L5_2 = "购买师门物品:"
                L6_2 = A1_2
                L5_2 = L5_2 .. L6_2
                L4_2(L5_2)
                L4_2 = randomClick
                L5_2 = 0
                L6_2 = 300
                L7_2 = 180
                L8_2 = 44
                L9_2 = 283
                L10_2 = 68
                L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
              end
            end
          end
        end
      end
      ::lbl_324::
    end
    ::lbl_325::
  end
end

_ENV["百级购买师门物品原"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L3_2 = true
  L4_2 = 0
  L5_2 = _ENV["门派传送"]
  if L5_2 == "是" then
    L5_2 = _ENV["长安传送NPC"]
    L6_2 = A0_2
    L5_2(L6_2)
  else
    L5_2 = useCAFlag
    L6_2 = 1044
    L7_2 = 829
    L8_2 = 1125
    L9_2 = 913
    L10_2 = 1182
    L11_2 = 509
    L12_2 = 910
    L13_2 = 766
    L14_2 = 1002
    L15_2 = 841
    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L5_2 = mSleep
    L6_2 = math
    L6_2 = L6_2.random
    L7_2 = 500
    L8_2 = 800
    L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L6_2(L7_2, L8_2)
    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
  end
  L5_2 = _find
  L6_2 = Color
  L6_2 = L6_2["红尘"]
  L6_2 = L6_2["没点了"]
  L5_2 = L5_2(L6_2)
  if L5_2 then
    L5_2 = 99
    return L5_2
  end
  while true do
    L5_2 = _ENV["弹窗查看详情"]
    L5_2()
    L5_2 = _find
    L6_2 = Color
    L6_2 = L6_2["红尘"]
    L6_2 = L6_2["没点了"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L5_2 = _ENV["没点了下号"]
      L5_2()
      L5_2 = _ENV["_随机延时"]
      L6_2 = 811
      L5_2(L6_2)
    end
    L5_2 = getWordStock
    L6_2 = WordStock
    L7_2 = "长安城"
    L8_2 = 154
    L9_2 = 37
    L10_2 = 308
    L11_2 = 78
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
    if not L5_2 then
      L5_2 = getNewYearView
      L6_2 = "长安城"
      L5_2 = L5_2(L6_2)
      if not L5_2 then
        goto lbl_319
      end
    end
    L5_2 = os
    L5_2 = L5_2.time
    L5_2 = L5_2()
    _ENV["卡点时间"] = L5_2
    while true do
      L5_2 = _ENV["弹窗查看详情"]
      L5_2()
      L5_2 = getColour
      L6_2 = colorList
      L7_2 = "长安小地图"
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = printLog
        L6_2 = "前往商会：329,18"
        L5_2(L6_2)
        L5_2 = 1098.37
        L6_2 = 846.46
        L7_2 = randomTap
        L8_2 = L5_2
        L9_2 = L6_2
        L7_2(L8_2, L9_2)
        L7_2 = mSleep
        L8_2 = math
        L8_2 = L8_2.random
        L9_2 = 80
        L10_2 = 150
        L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L8_2(L9_2, L10_2)
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
        L7_2 = tap
        L8_2 = L5_2
        L9_2 = L6_2
        L7_2(L8_2, L9_2)
        L7_2 = mSleep
        L8_2 = math
        L8_2 = L8_2.random
        L9_2 = 800
        L10_2 = 1500
        L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L8_2(L9_2, L10_2)
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
        L3_2 = false
        L7_2 = randomClick
        L8_2 = 2
        L9_2 = 400
        L10_2 = 1677
        L11_2 = 79
        L7_2(L8_2, L9_2, L10_2, L11_2)
        L7_2 = os
        L7_2 = L7_2.time
        L7_2 = L7_2()
        L4_2 = L7_2
      else
        L5_2 = os
        L5_2 = L5_2.time
        L5_2 = L5_2()
        L6_2 = _ENV["卡点时间"]
        L5_2 = L5_2 - L6_2
        if 300 < L5_2 then
          L5_2 = 66
          return L5_2
        else
          L5_2 = getColour
          L6_2 = colorList
          L7_2 = "宠物属性页面"
          L5_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            L5_2 = randomClick
            L6_2 = 2
            L7_2 = 300
            L8_2 = 1705
            L9_2 = 76
            L10_2 = 20
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
          else
            L5_2 = monitorAddress
            L6_2 = "329"
            L7_2 = "19"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = wordStock
              L5_2 = L5_2["隐藏摊位"]
              L5_2()
              L5_2 = true
              while true do
                L6_2 = _ENV["弹窗查看详情"]
                L6_2()
                L6_2 = getWordStock
                L7_2 = WordStock
                L8_2 = "买东西"
                L9_2 = 1523
                L10_2 = 163
                L11_2 = 1670
                L12_2 = 223
                L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                if L6_2 then
                  L6_2 = randomClick
                  L7_2 = 0
                  L8_2 = 1300
                  L9_2 = 1483
                  L10_2 = 170
                  L11_2 = 1694
                  L12_2 = 226
                  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                else
                  if not L5_2 then
                    L6_2 = getColour
                    L7_2 = colorList
                    L8_2 = "商会列表"
                    L6_2 = L6_2(L7_2, L8_2)
                    if L6_2 then
                      L6_2 = randomClick
                      L7_2 = 2
                      L8_2 = 300
                      L9_2 = 98
                      L10_2 = 960
                      L6_2(L7_2, L8_2, L9_2, L10_2)
                      L6_2 = _ENV["宠物店"]
                      if L6_2 ~= nil then
                        L6_2 = _ENV["宠物店"]
                        if L6_2 ~= "" then
                          goto lbl_199
                        end
                      end
                      L6_2 = _ENV["百级龟速识别"]
                      L7_2 = A1_2
                      L8_2 = A2_2
                      do return L6_2(L7_2, L8_2) end
                      goto lbl_290
                      ::lbl_199::
                      L6_2 = _ENV["百级查找对于物品"]
                      L7_2 = _ENV["宠物店"]
                      L8_2 = A1_2
                      L9_2 = "宠物"
                      L10_2 = A2_2
                      return L6_2(L7_2, L8_2, L9_2, L10_2)
                  end
                  else
                    L6_2 = os
                    L6_2 = L6_2.time
                    L6_2 = L6_2()
                    L7_2 = _ENV["卡点时间"]
                    L6_2 = L6_2 - L7_2
                    if 300 < L6_2 then
                      L6_2 = 66
                      return L6_2
                    else
                      if not L5_2 then
                        L6_2 = getColour
                        L7_2 = colorList
                        L8_2 = "关闭对话框"
                        L6_2 = L6_2(L7_2, L8_2)
                        if L6_2 then
                          L6_2 = getWordStock
                          L7_2 = WordStock
                          L8_2 = "买东西"
                          L9_2 = 1523
                          L10_2 = 163
                          L11_2 = 1670
                          L12_2 = 223
                          L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                          if not L6_2 then
                            L5_2 = true
                            L6_2 = randomClick
                            L7_2 = 2
                            L8_2 = 500
                            L9_2 = 1848
                            L10_2 = 71
                            L6_2(L7_2, L8_2, L9_2, L10_2)
                        end
                      end
                      elseif L5_2 then
                        L5_2 = false
                        L6_2 = randomClick
                        L7_2 = 2
                        L8_2 = 400
                        L9_2 = 844
                        L10_2 = 538
                        L6_2(L7_2, L8_2, L9_2, L10_2)
                      else
                        if not L5_2 then
                          L6_2 = monitorAddress
                          L7_2 = "329"
                          L8_2 = "19"
                          L6_2 = L6_2(L7_2, L8_2)
                          if not L6_2 then
                            L6_2 = getWordStock
                            L7_2 = WordStock
                            L8_2 = "长安城"
                            L9_2 = 138
                            L10_2 = 18
                            L11_2 = 329
                            L12_2 = 86
                            L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                            if L6_2 then
                              goto lbl_284
                            end
                          end
                        end
                        if not L5_2 then
                          L6_2 = monitorAddress
                          L7_2 = "329"
                          L8_2 = "19"
                          L6_2 = L6_2(L7_2, L8_2)
                          if not L6_2 then
                            L6_2 = getNewYearView
                            L7_2 = "长安城"
                            L6_2 = L6_2(L7_2)
                            ::lbl_284::
                            if L6_2 then
                              L3_2 = true
                              L6_2 = os
                              L6_2 = L6_2.time
                              L6_2 = L6_2()
                              L4_2 = L6_2
                              goto lbl_318
                            end
                          end
                        end
                      end
                    end
                  end
                end
                ::lbl_290::
              end
            else
              L5_2 = os
              L5_2 = L5_2.time
              L5_2 = L5_2()
              L5_2 = L5_2 - L4_2
              if 50 < L5_2 and L4_2 ~= 0 then
                L3_2 = true
                L4_2 = 0
              elseif L3_2 then
                L5_2 = printLog
                L6_2 = "帮师傅抓到宠物:"
                L7_2 = A1_2
                L6_2 = L6_2 .. L7_2
                L5_2(L6_2)
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 300
                L8_2 = 180
                L9_2 = 44
                L10_2 = 283
                L11_2 = 68
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              end
            end
          end
        end
      end
      ::lbl_318::
    end
    ::lbl_319::
  end
end

_ENV["百级帮师傅抓到宠物原"] = L0_1
