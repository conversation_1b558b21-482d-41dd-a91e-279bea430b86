local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 199 - L0_2
    L3_2 = 160 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 199
        L9_2 = 160
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["我要做其他事情"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["开启副本"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["快速开启副本界面"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _cmp
              L9_2 = Color
              L9_2 = L9_2["屏蔽"]
              L9_2 = L9_2["取消"]
              L8_2(L9_2)
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["新手副本"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["初出副本"]
            L10_2 = {}
            L11_2 = 750
            L12_2 = 1
            L10_2[1] = L11_2
            L10_2[2] = L12_2
            L8_2 = L8_2(L9_2, L10_2)
            if L8_2 then
              L8_2 = _cmp
              L9_2 = Color
              L9_2 = L9_2["屏蔽"]
              L9_2 = L9_2["取消"]
              L8_2(L9_2)
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["新旧朋友满长安"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = true
              return L8_2
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["进入副本"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "长安城"
        L6_2 = 773
        L7_2 = 482
        L4_2(L5_2, L6_2, L7_2)
      end
    end
    L4_2 = mSleep
    L5_2 = 100
    L4_2(L5_2)
  end
end

_ENV["进入副本"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["判断小副本任务"]
  L0_2()
end

_ENV["初出茅庐任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 222 - L0_2
    L3_2 = 171 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 221
        L9_2 = 171
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["来吧得罪了"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["购买"]
            L9_2 = L9_2["发现战斗界面"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = true
                return L8_2
              end
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 411
                L8_2(L9_2)
              end
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _ENV["_前往"]
      L4_2 = L4_2["固定坐标"]
      L5_2 = "长安城"
      L6_2 = 830
      L7_2 = 458
      L4_2(L5_2, L6_2, L7_2)
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["购买"]
    L5_2 = L5_2["发现战斗界面"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["绘卷"]
      L4_2 = L4_2(L5_2)
      if L4_2 == false then
        L4_2 = true
        return L4_2
      end
    end
    L4_2 = mSleep
    L5_2 = 100
    L4_2(L5_2)
  end
end

_ENV["战斗百晓星君"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 5
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 521 - L1_2
    L4_2 = 154 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 3 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 3 then
        if L0_2 == 1 then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 1306
          L7_2 = 320
          L5_2(L6_2, L7_2)
        else
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "长安城"
      L7_2 = 1585
      L8_2 = 507
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = _cmp
    L6_2 = Color
    L6_2 = L6_2["购买"]
    L6_2 = L6_2["发现战斗界面"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L5_2 = _find_tb
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["绘卷"]
      L5_2 = L5_2(L6_2)
      if L5_2 == false then
        L5_2 = _ENV["初出茅庐战斗"]
        L5_2()
      end
    end
    L5_2 = _find_tb
    L6_2 = Color
    L6_2 = L6_2["起号"]
    L6_2 = L6_2["绘卷"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L5_2 = supplement
      L6_2 = 2
      L5_2(L6_2)
    else
      L5_2 = mSleep
      L6_2 = 100
      L5_2(L6_2)
    end
    L5_2 = _cmp
    L6_2 = Color
    L6_2 = L6_2["初出茅庐"]
    L6_2 = L6_2["到达长风镖局"]
    L5_2 = L5_2(L6_2)
    if L5_2 then
      return
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
end

_ENV["前往长风镖局"] = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = 237
L3_1 = 474
L4_1 = 584
L5_1 = 532
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L2_1 = {}
L3_1 = 379
L4_1 = 427
L5_1 = 765
L6_1 = 477
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L3_1 = {}
L4_1 = 527
L5_1 = 366
L6_1 = 912
L7_1 = 422
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L4_1 = {}
L5_1 = 672
L6_1 = 322
L7_1 = 1030
L8_1 = 366
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L5_1 = {}
L6_1 = 830
L7_1 = 260
L8_1 = 1222
L9_1 = 317
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L6_1 = {}
L7_1 = 313
L8_1 = 568
L9_1 = 760
L10_1 = 620
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L7_1 = {}
L8_1 = 494
L9_1 = 516
L10_1 = 873
L11_1 = 570
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L8_1 = {}
L9_1 = 653
L10_1 = 463
L11_1 = 1026
L12_1 = 514
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L9_1 = {}
L10_1 = 777
L11_1 = 411
L12_1 = 1207
L13_1 = 462
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L10_1 = {}
L11_1 = 938
L12_1 = 354
L13_1 = 1406
L14_1 = 415
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L0_1[1] = L1_1
L0_1[2] = L2_1
L0_1[3] = L3_1
L0_1[4] = L4_1
L0_1[5] = L5_1
L0_1[6] = L6_1
L0_1[7] = L7_1
L0_1[8] = L8_1
L0_1[9] = L9_1
L0_1[10] = L10_1
_ENV["识别范围"] = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = 418
L3_1 = 417
L1_1[1] = L2_1
L1_1[2] = L3_1
L2_1 = {}
L3_1 = 568
L4_1 = 375
L2_1[1] = L3_1
L2_1[2] = L4_1
L3_1 = {}
L4_1 = 720
L5_1 = 317
L3_1[1] = L4_1
L3_1[2] = L5_1
L4_1 = {}
L5_1 = 871
L6_1 = 261
L4_1[1] = L5_1
L4_1[2] = L6_1
L5_1 = {}
L6_1 = 1021
L7_1 = 210
L5_1[1] = L6_1
L5_1[2] = L7_1
L6_1 = {}
L7_1 = 537
L8_1 = 512
L6_1[1] = L7_1
L6_1[2] = L8_1
L7_1 = {}
L8_1 = 683
L9_1 = 440
L7_1[1] = L8_1
L7_1[2] = L9_1
L8_1 = {}
L9_1 = 851
L10_1 = 400
L8_1[1] = L9_1
L8_1[2] = L10_1
L9_1 = {}
L10_1 = 999
L11_1 = 342
L9_1[1] = L10_1
L9_1[2] = L11_1
L10_1 = {}
L11_1 = 1131
L12_1 = 300
L10_1[1] = L11_1
L10_1[2] = L12_1
L0_1[1] = L1_1
L0_1[2] = L2_1
L0_1[3] = L3_1
L0_1[4] = L4_1
L0_1[5] = L5_1
L0_1[6] = L6_1
L0_1[7] = L7_1
L0_1[8] = L8_1
L0_1[9] = L9_1
L0_1[10] = L10_1
_ENV["点击位置"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L2_2 = A0_2[1]
  L3_2 = A0_2[2]
  L4_2 = A0_2[3]
  L5_2 = A0_2[4]
  L6_2 = findMultiColorInRegionFuzzy
  L7_2 = 5693293
  L8_2 = "1|0|0x56df6d"
  L9_2 = 85
  L10_2 = L2_2
  L11_2 = L3_2
  L12_2 = L4_2
  L13_2 = L5_2
  L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  y = L7_2
  x = L6_2
  L6_2 = findMultiColorInRegionFuzzy
  L7_2 = 16711937
  L8_2 = "1|0|0xff0101"
  L9_2 = 85
  L10_2 = L2_2
  L11_2 = L3_2
  L12_2 = L4_2
  L13_2 = L5_2
  L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  yy = L7_2
  xx = L6_2
  L6_2 = x
  if not (0 < L6_2) then
    L6_2 = xx
    if not (0 < L6_2) then
      goto lbl_36
    end
  end
  L6_2 = true
  do return L6_2 end
  goto lbl_38
  ::lbl_36::
  L6_2 = false
  do return L6_2 end
  ::lbl_38::
end

_ENV["有绿色字或者红色字"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = 1
  L1_2 = 10
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _ENV["有绿色字或者红色字"]
    L5_2 = _ENV["识别范围"]
    L5_2 = L5_2[L3_2]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return L3_2
    end
    if L3_2 == 10 then
      L4_2 = 1
      return L4_2
    end
  end
end

_ENV["识别怪物"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  repeat
    L0_2 = keepScreen
    L1_2 = true
    L0_2(L1_2)
    L0_2 = UI_zhandou_BB_model
    if L0_2 == "宝宝攻击" then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["战斗"]
      L1_2 = L1_2["宝宝普攻"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _tap
        L1_2 = 1843
        L2_2 = 600
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
        L0_2 = _Sleep
        L1_2 = 200
        L2_2 = 300
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["识别怪物"]
        L0_2 = L0_2()
        index = L0_2
        L0_2 = _tap
        L1_2 = _ENV["点击位置"]
        L2_2 = index
        L1_2 = L1_2[L2_2]
        L1_2 = L1_2[1]
        L2_2 = _ENV["点击位置"]
        L3_2 = index
        L2_2 = L2_2[L3_2]
        L2_2 = L2_2[2]
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
        L0_2 = math
        L0_2 = L0_2.random
        L1_2 = 200
        L2_2 = 300
        L0_2(L1_2, L2_2)
      end
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["战斗"]
      L1_2 = L1_2["宝宝技能"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = keepScreen
        L1_2 = false
        L0_2(L1_2)
        L0_2 = _Sleep
        L1_2 = 500
        L2_2 = 1000
        L0_2(L1_2, L2_2)
        while true do
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["战斗"]
          L1_2 = L1_2["宝宝技能列表"]
          L2_2 = UI_zhandou_BB_selet
          L1_2 = L1_2[L2_2]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _Sleep
            L1_2 = 500
            L2_2 = 1000
            L0_2(L1_2, L2_2)
            break
          end
          L0_2 = mSleep
          L1_2 = 400
          L0_2(L1_2)
        end
        L0_2 = _ENV["识别怪物"]
        L0_2 = L0_2()
        index = L0_2
        L0_2 = _tap
        L1_2 = _ENV["点击位置"]
        L2_2 = index
        L1_2 = L1_2[L2_2]
        L1_2 = L1_2[1]
        L2_2 = _ENV["点击位置"]
        L3_2 = index
        L2_2 = L2_2[L3_2]
        L2_2 = L2_2[2]
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
        L0_2 = math
        L0_2 = L0_2.random
        L1_2 = 400
        L2_2 = 600
        L0_2(L1_2, L2_2)
      end
    end
    L0_2 = _ENV["UI_zhandou_师门"]
    if L0_2 == "百级普攻" then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["战斗"]
      L1_2 = L1_2["角色普攻"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _tap
        L1_2 = 1843
        L2_2 = 222
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
        _ENV["确认进入战斗"] = true
        L0_2 = _Sleep
        L1_2 = 200
        L2_2 = 300
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["识别怪物"]
        L0_2 = L0_2()
        index = L0_2
        L0_2 = _tap
        L1_2 = _ENV["点击位置"]
        L2_2 = index
        L1_2 = L1_2[L2_2]
        L1_2 = L1_2[1]
        L2_2 = _ENV["点击位置"]
        L3_2 = index
        L2_2 = L2_2[L3_2]
        L2_2 = L2_2[2]
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
        L0_2 = _Sleep
        L1_2 = 200
        L2_2 = 300
        L0_2(L1_2, L2_2)
      end
    else
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["战斗"]
      L1_2 = L1_2["角色普攻"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        _ENV["确认进入战斗"] = true
        L0_2 = 1
        L1_2 = 30
        L2_2 = 1
        for L3_2 = L0_2, L1_2, L2_2 do
          L4_2 = _cmp
          L5_2 = Color
          L5_2 = L5_2["战斗"]
          L5_2 = L5_2["绿色+标准位置"]
          L6_2 = 85
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 then
            L4_2 = _tap
            L5_2 = 1697
            L6_2 = 601
            L4_2(L5_2, L6_2)
            break
          else
            L4_2 = _cmp
            L5_2 = Color
            L5_2 = L5_2["战斗"]
            L5_2 = L5_2["绿色+存在葫芦"]
            L6_2 = 85
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = _tap
              L5_2 = 1691
              L6_2 = 511
              L4_2(L5_2, L6_2)
              break
            else
              L4_2 = mSleep
              L5_2 = 5
              L4_2(L5_2)
            end
          end
          if L3_2 == 30 then
            L4_2 = _tap
            L5_2 = 1845
            L6_2 = 256
            L4_2(L5_2, L6_2)
          end
        end
        L0_2 = _Sleep
        L1_2 = 200
        L2_2 = 300
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["识别怪物"]
        L0_2 = L0_2()
        index = L0_2
        L0_2 = _tap
        L1_2 = _ENV["点击位置"]
        L2_2 = index
        L1_2 = L1_2[L2_2]
        L1_2 = L1_2[1]
        L2_2 = _ENV["点击位置"]
        L3_2 = index
        L2_2 = L2_2[L3_2]
        L2_2 = L2_2[2]
        L3_2 = 1
        L4_2 = 10
        L0_2(L1_2, L2_2, L3_2, L4_2)
      end
    end
    L0_2 = keepScreen
    L1_2 = false
    L0_2(L1_2)
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["百级师门"]
    L1_2 = L1_2["随机目标"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 311
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2.npc
    L1_2 = L1_2["通用关闭对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 511
      L0_2(L1_2)
    end
    L0_2 = mSleep
    L1_2 = 250
    L0_2(L1_2)
    L0_2 = _cmp_tb
    L1_2 = Color
    L1_2 = L1_2["主界面"]
    L0_2 = L0_2(L1_2)
  until L0_2
end

_ENV["初出茅庐战斗"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["初出茅庐"]
  L1_2 = L1_2["动作栏显示"]
  L0_2 = L0_2(L1_2)
  if L0_2 == false then
    L0_2 = tap
    L1_2 = 1011
    L2_2 = 991
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
  end
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["初出茅庐"]
  L1_2 = L1_2["动作栏显示"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = tap
    L1_2 = 1649
    L2_2 = 415
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_随机延时"]
    L1_2 = 1411
    L0_2(L1_2)
  end
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["初出茅庐"]
    L5_2 = L5_2["动作界面"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = tap
      L5_2 = 1869
      L6_2 = 178
      L4_2(L5_2, L6_2)
      L4_2 = _ENV["_随机延时"]
      L5_2 = 411
      L4_2(L5_2)
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["初出茅庐"]
    L5_2 = L5_2["动作界面"]
    L4_2 = L4_2(L5_2)
    if L4_2 == false then
      break
    end
  end
end

_ENV["跳个舞"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_前往"]
      L0_2 = L0_2["固定坐标"]
      L1_2 = "傲来国"
      L2_2 = 1049
      L3_2 = 385
      L0_2(L1_2, L2_2, L3_2)
    end
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 131 - L0_2
    L3_2 = 109 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 131
        L9_2 = 109
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 411
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["找渔夫"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 then
                return
              end
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        end
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "傲来国"
        L6_2 = 1049
        L7_2 = 385
        L4_2(L5_2, L6_2, L7_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["初出茅庐"]
    L5_2 = L5_2["找渔夫"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["绘卷"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        return
      end
    end
  end
end

_ENV["找琵琶"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  
  function L0_2()
    local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3
    L0_3 = nil
    L1_3 = nil
    L2_3 = math
    L2_3 = L2_3.random
    L3_3 = 478
    L4_3 = 841
    L2_3 = L2_3(L3_3, L4_3)
    L0_3 = L2_3
    L2_3 = math
    L2_3 = L2_3.random
    L3_3 = 595
    L4_3 = 832
    L2_3 = L2_3(L3_3, L4_3)
    L1_3 = L2_3
    L2_3 = _cmp
    L3_3 = Color
    L3_3 = L3_3["初出茅庐"]
    L3_3 = L3_3["傲来国小地图"]
    L2_3 = L2_3(L3_3)
    if L2_3 == false then
      L2_3 = tap
      L3_3 = 181
      L4_3 = 72
      L2_3(L3_3, L4_3)
      L2_3 = _ENV["_随机延时"]
      L3_3 = 411
      L2_3(L3_3)
    end
    L2_3 = _cmp
    L3_3 = Color
    L3_3 = L3_3["初出茅庐"]
    L3_3 = L3_3["傲来国小地图"]
    L2_3 = L2_3(L3_3)
    if L2_3 then
      L2_3 = tap
      L3_3 = L0_3
      L4_3 = L1_3
      L2_3(L3_3, L4_3)
      L2_3 = _ENV["_随机延时"]
      L3_3 = 411
      L2_3(L3_3)
      L2_3 = _ENV["通用功能"]
      L2_3 = L2_3["关闭"]
      L2_3()
      L2_3 = moveJudge
      L2_3()
    end
  end
  
  _ENV["转转"] = L0_2
  
  function L0_2()
    local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3
    L0_3 = _ENV["_功能"]
    L0_3 = L0_3["背包"]
    L1_3 = "open"
    L0_3(L1_3)
    L0_3 = _find_cx
    L1_3 = Color
    L1_3 = L1_3["初出茅庐"]
    L1_3 = L1_3["使用铲子"]
    L2_3 = {}
    L3_3 = 20
    L4_3 = 50
    L2_3[1] = L3_3
    L2_3[2] = L4_3
    L0_3 = L0_3(L1_3, L2_3)
    if L0_3 then
      L0_3 = mSleep
      L1_3 = 400
      L0_3(L1_3)
    end
    L0_3 = _ENV["通用功能"]
    L0_3 = L0_3["关闭"]
    L0_3()
  end
  
  _ENV["使用铲子"] = L0_2
  
  function L0_2()
    local L0_3, L1_3, L2_3
    repeat
      repeat
        L0_3 = _find
        L1_3 = Color
        L1_3 = L1_3.npc
        L1_3 = L1_3["通用关闭对话框"]
        L0_3 = L0_3(L1_3)
        if L0_3 then
          L0_3 = _ENV["_随机延时"]
          L1_3 = 811
          L0_3(L1_3)
        end
        L0_3 = _find
        L1_3 = Color
        L1_3 = L1_3["探访奇闻"]
        L1_3 = L1_3["出剪刀"]
        L0_3 = L0_3(L1_3)
        if not L0_3 then
          L0_3 = _find
          L1_3 = Color
          L1_3 = L1_3["探访奇闻"]
          L1_3 = L1_3["出石头"]
          L0_3 = L0_3(L1_3)
          if not L0_3 then
            L0_3 = _find
            L1_3 = Color
            L1_3 = L1_3["探访奇闻"]
            L1_3 = L1_3["出布"]
            L0_3 = L0_3(L1_3)
            if not L0_3 then
              goto lbl_35
            end
          end
        end
        L0_3 = _ENV["_随机延时"]
        L1_3 = 811
        L0_3(L1_3)
        ::lbl_35::
        L0_3 = _ENV["_随机延时"]
        L1_3 = 811
        L0_3(L1_3)
        L0_3 = _cmp_tb
        L1_3 = Color
        L1_3 = L1_3["主界面"]
        L0_3 = L0_3(L1_3)
      until L0_3
      L0_3 = _find_tb
      L1_3 = Color
      L1_3 = L1_3["起号"]
      L1_3 = L1_3["绘卷"]
      L0_3 = L0_3(L1_3)
    until L0_3
  end
  
  _ENV["猜拳"] = L0_2
  while true do
    L0_2 = _ENV["转转"]
    L0_2()
    L0_2 = _ENV["使用铲子"]
    L0_2()
    L0_2 = _ENV["猜拳"]
    L0_2()
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        do return end
        break
      end
    end
  end
end

_ENV["生蚝任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["傲来客栈"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1550
        L2_2 = 909
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达傲来国"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["出客栈"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["出客栈"]
  L0_2()
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达傲来国"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_前往"]
        L0_2 = L0_2["固定坐标"]
        L1_2 = "傲来国"
        L2_2 = 1385
        L3_2 = 382
        L0_2(L1_2, L2_2, L3_2)
      end
    end
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 199 - L0_2
    L3_2 = 107 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 199
        L9_2 = 107
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 411
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["找猴子"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 then
                return
              end
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        end
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "傲来国"
        L6_2 = 1385
        L7_2 = 382
        L4_2(L5_2, L6_2, L7_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["初出茅庐"]
    L5_2 = L5_2["找猴子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["绘卷"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        return
      end
    end
  end
end

_ENV["找小孩"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找猴子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_前往"]
      L0_2 = L0_2["固定坐标"]
      L1_2 = "傲来国"
      L2_2 = 690
      L3_2 = 406
      L0_2(L1_2, L2_2, L3_2)
    end
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 51 - L0_2
    L3_2 = 105 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 51
        L9_2 = 105
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["打猴子"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 500
              L10_2 = 1000
              L8_2(L9_2, L10_2)
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["购买"]
            L9_2 = L9_2["发现战斗界面"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = _ENV["初出茅庐战斗"]
                L8_2()
              end
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 411
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["初出茅庐"]
            L9_2 = L9_2["找二黑子"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 then
                return
              end
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        end
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "傲来国"
        L6_2 = 690
        L7_2 = 406
        L4_2(L5_2, L6_2, L7_2)
      end
    end
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["绘卷"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = supplement
      L5_2 = 2
      L4_2(L5_2)
    else
      L4_2 = mSleep
      L5_2 = 100
      L4_2(L5_2)
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["初出茅庐"]
    L5_2 = L5_2["找二黑子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["绘卷"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        return
      end
    end
  end
end

_ENV["找猴子"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["百晓星君"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["战斗百晓星君"]
      L0_2()
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找兰虎"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = mSleep
    L1_2 = 200
    L0_2(L1_2)
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = moveJudge
    L0_2()
    L0_2 = _find_cx
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 110
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 40
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["不客气了"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["可选对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = tap
            L5_2 = 963
            L6_2 = 857
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_随机延时"]
            L5_2 = 411
            L4_2(L5_2)
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["购买"]
        L5_2 = L5_2["发现战斗界面"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = _ENV["初出茅庐战斗"]
            L4_2()
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["听兰虎"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["去长风镖局"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["进长风镖局"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = mSleep
        L5_2 = 60
        L4_2(L5_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["去长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["进长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["去长风镖局"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["进长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["前往长风镖局"]
      L0_2()
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
      L0_2 = moveJudge
      L0_2()
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点击给予"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["去长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = tap
        L1_2 = 19
        L2_2 = 323
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达赵姨娘家"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "460"
    L2_2 = "168"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1230
        L3_2 = 418
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "17"
    L2_2 = "10"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 530
        L3_2 = 797
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["替天行道"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达江南野外"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["起号"]
        L0_2 = L0_2["坐标"]
        L0_2, L1_2 = L0_2()
        L2_2 = 12 - L0_2
        L3_2 = 14 - L1_2
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 3 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 3 then
            break
          end
        end
        L4_2 = tap
        L5_2 = 58
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_随机延时"]
        L5_2 = 2111
        L4_2(L5_2)
        L4_2 = moveJudge
        L4_2()
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["跳个舞"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_1154
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_1154::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_1677
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_1677::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = moveJudge
    L0_2()
    L0_2 = _find_cx
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 110
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 40
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["不客气了"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["可选对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = tap
            L5_2 = 963
            L6_2 = 857
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_随机延时"]
            L5_2 = 411
            L4_2(L5_2)
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["购买"]
        L5_2 = L5_2["发现战斗界面"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = _ENV["初出茅庐战斗"]
            L4_2()
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["听兰虎"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["去长风镖局"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["进长风镖局"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          break
        end
        L4_2 = mSleep
        L5_2 = 60
        L4_2(L5_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["去长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["进长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["去长风镖局"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["进长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["前往长风镖局"]
      L0_2()
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
      L0_2 = moveJudge
      L0_2()
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点击给予"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["去长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = tap
        L1_2 = 19
        L2_2 = 323
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达赵姨娘家"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "460"
    L2_2 = "168"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1230
        L3_2 = 418
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "17"
    L2_2 = "10"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 530
        L3_2 = 797
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["替天行道"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达江南野外"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["起号"]
        L0_2 = L0_2["坐标"]
        L0_2, L1_2 = L0_2()
        L2_2 = 12 - L0_2
        L3_2 = 14 - L1_2
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 3 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 3 then
            break
          end
        end
        L4_2 = tap
        L5_2 = 58
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_随机延时"]
        L5_2 = 2111
        L4_2(L5_2)
        L4_2 = moveJudge
        L4_2()
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["跳个舞"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_1083
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_1083::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_1606
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_1606::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["去长风镖局"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["进长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["前往长风镖局"]
      L0_2()
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
      L0_2 = moveJudge
      L0_2()
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点击给予"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["去长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = tap
        L1_2 = 19
        L2_2 = 323
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达赵姨娘家"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "460"
    L2_2 = "168"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1230
        L3_2 = 418
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "17"
    L2_2 = "10"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 530
        L3_2 = 797
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["替天行道"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达江南野外"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["起号"]
        L0_2 = L0_2["坐标"]
        L0_2, L1_2 = L0_2()
        L2_2 = 12 - L0_2
        L3_2 = 14 - L1_2
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 3 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 3 then
            break
          end
        end
        L4_2 = tap
        L5_2 = 58
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_随机延时"]
        L5_2 = 2111
        L4_2(L5_2)
        L4_2 = moveJudge
        L4_2()
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["跳个舞"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_913
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_913::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_1436
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_1436::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长风镖局"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = tap
        L1_2 = 19
        L2_2 = 323
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达赵姨娘家"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "460"
    L2_2 = "168"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1230
        L3_2 = 418
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "17"
    L2_2 = "10"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 530
        L3_2 = 797
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安酒店"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["替天行道"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达江南野外"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 2111
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["起号"]
        L0_2 = L0_2["坐标"]
        L0_2, L1_2 = L0_2()
        L2_2 = 12 - L0_2
        L3_2 = 14 - L1_2
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 3 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 3 then
            break
          end
        end
        L4_2 = tap
        L5_2 = 58
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_随机延时"]
        L5_2 = 2111
        L4_2(L5_2)
        L4_2 = moveJudge
        L4_2()
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["跳个舞"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_735
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_735::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_1258
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_1258::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["起号"]
        L0_2 = L0_2["坐标"]
        L0_2, L1_2 = L0_2()
        L2_2 = 12 - L0_2
        L3_2 = 14 - L1_2
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 3 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 3 then
            break
          end
        end
        L4_2 = tap
        L5_2 = 58
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_随机延时"]
        L5_2 = 2111
        L4_2(L5_2)
        L4_2 = moveJudge
        L4_2()
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["调查柜子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["跳个舞"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_417
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_417::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_940
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_940::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["检查完毕找百晓"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达地府"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捣蛋鬼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找御林军"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找飞贼"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["幕后黑手"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["御林军报告"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "31"
    L2_2 = "13"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 1467
        L3_2 = 764
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "378"
    L2_2 = "7"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 865
        L3_2 = 606
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["人鬼殊途"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["借神剑"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["看招"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["看招1"]
          L0_2 = L0_2(L1_2)
          if not L0_2 then
            goto lbl_317
          end
        end
      end
    end
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    ::lbl_317::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打探仙人踪迹"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["到达傲来国"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          break
        end
      end
    end
  end
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_840
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_840::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程6"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = _ENV["找琵琶"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_486
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_486::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程7"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找渔夫"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["小铲子任务"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生蚝任务"]
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["铲子任务完成"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["给予界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 3
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["初出茅庐"]
        L5_2 = L5_2["点生蚝"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["_随机延时"]
          L5_2 = 411
          L4_2(L5_2)
        end
        L4_2 = mSleep
        L5_2 = 70
        L4_2(L5_2)
      end
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["点击给予"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找捕鱼翁"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 1411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找小孩"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        break
      end
    end
  end
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_484
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_484::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程8"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = _ENV["找小孩"]
  L0_2()
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_246
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_246::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程9"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = _ENV["找猴子"]
  L0_2()
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_244
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_244::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程10"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["问二黑子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找王福来2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["打慕容先生"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["点魏征"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "183"
    L2_2 = "29"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["贼王"]
      L2_2 = L2_2["传送门"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 1
        L3_2 = 5
        L1_2 = L1_2(L2_2, L3_2)
        L0_2 = L1_2
      end
      if L0_2 == 1 then
        L1_2 = _ENV["起号"]
        L1_2 = L1_2["进入光圈"]
        L2_2 = 765
        L3_2 = 414
        L1_2(L2_2, L3_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 1511
        L1_2(L2_2)
        L1_2 = moveJudge
        L1_2()
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["贼王"]
        L2_2 = L2_2["点传送门"]
        L1_2(L2_2)
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["寻路"]
        L1_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = tap
        L1_2 = 963
        L2_2 = 857
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["得罪了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["初出茅庐战斗"]
        L0_2()
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_242
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_242::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["主流程11"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["初出茅庐"]
  L1_2 = L1_2["百晓星君"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = toast
    L1_2 = "继续任务"
    L2_2 = 1
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["主流程1"]
    L0_2()
  else
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["找兰虎"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = toast
      L1_2 = "继续任务"
      L2_2 = 1
      L0_2(L1_2, L2_2)
      L0_2 = _ENV["主流程2"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["去长风镖局"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["进长风镖局"]
        L0_2 = L0_2(L1_2)
        if not L0_2 then
          goto lbl_50
        end
      end
      L0_2 = toast
      L1_2 = "继续任务"
      L2_2 = 1
      L0_2(L1_2, L2_2)
      L0_2 = _ENV["主流程3"]
      L0_2()
      goto lbl_222
      ::lbl_50::
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["去长安酒店"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = toast
        L1_2 = "继续任务"
        L2_2 = 1
        L0_2(L1_2, L2_2)
        L0_2 = _ENV["主流程4"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["初出茅庐"]
        L1_2 = L1_2["调查柜子"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _find_tb
          L1_2 = Color
          L1_2 = L1_2["起号"]
          L1_2 = L1_2["绘卷"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = toast
            L1_2 = "继续任务"
            L2_2 = 1
            L0_2(L1_2, L2_2)
            L0_2 = _ENV["主流程5"]
            L0_2()
        end
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["初出茅庐"]
          L1_2 = L1_2["检查完毕找百晓"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _find_tb
            L1_2 = Color
            L1_2 = L1_2["起号"]
            L1_2 = L1_2["绘卷"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = toast
              L1_2 = "继续任务"
              L2_2 = 1
              L0_2(L1_2, L2_2)
              L0_2 = _ENV["主流程6"]
              L0_2()
          end
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["初出茅庐"]
            L1_2 = L1_2["打探仙人踪迹"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["初出茅庐"]
              L1_2 = L1_2["到达傲来国"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _find_tb
                L1_2 = Color
                L1_2 = L1_2["起号"]
                L1_2 = L1_2["绘卷"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  L0_2 = toast
                  L1_2 = "继续任务"
                  L2_2 = 1
                  L0_2(L1_2, L2_2)
                  L0_2 = _ENV["主流程7"]
                  L0_2()
              end
            end
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["初出茅庐"]
              L1_2 = L1_2["找渔夫"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _find_tb
                L1_2 = Color
                L1_2 = L1_2["起号"]
                L1_2 = L1_2["绘卷"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  L0_2 = toast
                  L1_2 = "继续任务"
                  L2_2 = 1
                  L0_2(L1_2, L2_2)
                  L0_2 = _ENV["主流程8"]
                  L0_2()
              end
              else
                L0_2 = _cmp
                L1_2 = Color
                L1_2 = L1_2["初出茅庐"]
                L1_2 = L1_2["找小孩"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  L0_2 = _find_tb
                  L1_2 = Color
                  L1_2 = L1_2["起号"]
                  L1_2 = L1_2["绘卷"]
                  L0_2 = L0_2(L1_2)
                  if L0_2 then
                    L0_2 = toast
                    L1_2 = "继续任务"
                    L2_2 = 1
                    L0_2(L1_2, L2_2)
                    L0_2 = _ENV["主流程9"]
                    L0_2()
                end
                else
                  L0_2 = _cmp
                  L1_2 = Color
                  L1_2 = L1_2["初出茅庐"]
                  L1_2 = L1_2["找猴子"]
                  L0_2 = L0_2(L1_2)
                  if L0_2 then
                    L0_2 = _find_tb
                    L1_2 = Color
                    L1_2 = L1_2["起号"]
                    L1_2 = L1_2["绘卷"]
                    L0_2 = L0_2(L1_2)
                    if L0_2 then
                      L0_2 = toast
                      L1_2 = "继续任务"
                      L2_2 = 1
                      L0_2(L1_2, L2_2)
                      L0_2 = _ENV["主流程10"]
                      L0_2()
                  end
                  else
                    L0_2 = _cmp
                    L1_2 = Color
                    L1_2 = L1_2["初出茅庐"]
                    L1_2 = L1_2["找二黑子"]
                    L0_2 = L0_2(L1_2)
                    if L0_2 then
                      L0_2 = _find_tb
                      L1_2 = Color
                      L1_2 = L1_2["起号"]
                      L1_2 = L1_2["绘卷"]
                      L0_2 = L0_2(L1_2)
                      if L0_2 then
                        L0_2 = toast
                        L1_2 = "继续任务"
                        L2_2 = 1
                        L0_2(L1_2, L2_2)
                        L0_2 = _ENV["主流程11"]
                        L0_2()
                    end
                    else
                      L0_2 = _ENV["进入副本"]
                      L0_2()
                      L0_2 = _ENV["主流程1"]
                      L0_2()
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  ::lbl_222::
end

_ENV["判断小副本任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  _ENV["对话框次数"] = 0
  while true do
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["红尘"]
    L1_2 = L1_2["其他事情"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 800
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["同意副本"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 800
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["对话框次数"]
      L0_2 = L0_2 + 1
      _ENV["对话框次数"] = L0_2
    end
    L0_2 = _ENV["对话框次数"]
    if 10 < L0_2 then
      L0_2 = _ENV["通用功能"]
      L0_2 = L0_2["关闭"]
      L0_2()
      _ENV["对话框次数"] = 0
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = mSleep
        L1_2 = 2000
        L0_2(L1_2)
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["出现对话框"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _find
          L1_2 = Color
          L1_2 = L1_2["起号"]
          L1_2 = L1_2["可选对话框"]
          L0_2 = L0_2(L1_2)
          if L0_2 == false then
            L0_2 = tap
            L1_2 = 963
            L2_2 = 857
            L0_2(L1_2, L2_2)
            L0_2 = _ENV["_随机延时"]
            L1_2 = 811
            L0_2(L1_2)
          end
        end
      end
    end
    L0_2 = _cmp_cx
    L1_2 = Color
    L1_2 = L1_2["购买"]
    L1_2 = L1_2["发现战斗界面"]
    L2_2 = {}
    L3_2 = 5
    L4_2 = 5
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = _ENV["初出茅庐战斗"]
      L0_2()
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = mSleep
        L1_2 = 300
        L0_2(L1_2)
        L0_2 = supplement
        L1_2 = 2
        L0_2(L1_2)
      else
        L0_2 = mSleep
        L1_2 = 500
        L0_2(L1_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["验证"]
    L1_2 = L1_2["地图不存在"]
    L0_2 = L0_2(L1_2)
    if L0_2 == false then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["验证"]
      L1_2 = L1_2["战斗倒计时"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["抓鬼"]
        L1_2 = L1_2["出现随机"]
        L0_2 = L0_2(L1_2)
        if L0_2 == false then
          L0_2 = _ENV["_验证"]
          L0_2 = L0_2["朝向"]
          L0_2()
          L0_2 = _ENV["检测四小人"]
          L0_2()
        end
      end
    end
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = mSleep
      L1_2 = 300
      L0_2(L1_2)
      L0_2 = supplement
      L1_2 = 2
      L0_2(L1_2)
    else
      L0_2 = mSleep
      L1_2 = 500
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["回复"]
    L1_2 = L1_2["同时补满"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
      L0_2 = _cmp_cx
      L1_2 = Color
      L1_2 = L1_2["回复"]
      L1_2 = L1_2["关闭对话"]
      L2_2 = {}
      L3_2 = 50
      L4_2 = 60
      L2_2[1] = L3_2
      L2_2[2] = L4_2
      L0_2 = L0_2(L1_2, L2_2)
      if L0_2 then
        L0_2 = _Sleep
        L1_2 = 500
        L2_2 = 1000
        L0_2(L1_2, L2_2)
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["可选对话框"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _Sleep
        L1_2 = 400
        L2_2 = 800
        L0_2(L1_2, L2_2)
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["抓鬼"]
        L1_2 = L1_2["购买"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _Sleep
          L1_2 = 400
          L2_2 = 800
          L0_2(L1_2, L2_2)
          L0_2 = _cmp_cx
          L1_2 = Color
          L1_2 = L1_2["购买"]
          L1_2 = L1_2["包子"]
          L1_2 = L1_2["物品界面"]
          L2_2 = {}
          L3_2 = 30
          L4_2 = 100
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L0_2 = L0_2(L1_2, L2_2)
          if L0_2 then
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["购买"]
            L1_2 = L1_2["包子"]
            L1_2 = L1_2["包子"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _Sleep
              L1_2 = 400
              L2_2 = 800
              L0_2(L1_2, L2_2)
              L0_2 = _ENV["跟对买红"]
              L0_2()
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["购买"]
              L1_2 = L1_2["佛手"]
              L1_2 = L1_2["佛手"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _Sleep
                L1_2 = 400
                L2_2 = 800
                L0_2(L1_2, L2_2)
                L0_2 = _ENV["跟对买蓝"]
                L0_2()
              end
            end
          end
        end
      end
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["初出茅庐"]
    L1_2 = L1_2["开始转盘"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["初出茅庐"]
      L1_2 = L1_2["停止转盘"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_266
      end
    end
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
    ::lbl_266::
    L0_2 = MyGetRunningAccess
    L0_2()
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
  end
end

_ENV["跟队模式"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["购买"]
    L5_2 = L5_2["佛手"]
    L5_2 = L5_2["佛手"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 400
      L6_2 = 800
      L4_2(L5_2, L6_2)
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["购买"]
    L5_2 = L5_2["佛手"]
    L5_2 = L5_2["选中佛手"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _tap
      L5_2 = 1741
      L6_2 = 776
      L4_2(L5_2, L6_2)
      L4_2 = _Sleep
      L5_2 = 400
      L6_2 = 800
      L4_2(L5_2, L6_2)
      L4_2 = _cmp_cx
      L5_2 = Color
      L5_2 = L5_2["购买"]
      L5_2 = L5_2["佛手"]
      L5_2 = L5_2["键盘已显示"]
      L6_2 = {}
      L7_2 = 30
      L8_2 = 20
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = _tap
        L5_2 = 1793
        L6_2 = 336
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1639
        L6_2 = 638
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1639
        L6_2 = 638
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1795
        L6_2 = 634
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1506
        L6_2 = 968
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 700
        L6_2 = 1300
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_卡区延迟"]
        L4_2()
        repeat
          L4_2 = _cmp
          L5_2 = Color
          L5_2 = L5_2["购买"]
          L5_2 = L5_2["佛手"]
          L5_2 = L5_2["关闭界面"]
          L4_2 = L4_2(L5_2)
          if not L4_2 then
            L4_2 = _cmp
            L5_2 = Color
            L5_2 = L5_2["购买"]
            L5_2 = L5_2["佛手"]
            L5_2 = L5_2["关闭购买对话"]
            L4_2 = L4_2(L5_2)
            if not L4_2 then
              goto lbl_117
            end
          end
          L4_2 = _Sleep
          L5_2 = 500
          L6_2 = 1000
          L4_2(L5_2, L6_2)
          ::lbl_117::
          L4_2 = mSleep
          L5_2 = 300
          L4_2(L5_2)
          L4_2 = _cmp_tb
          L5_2 = Color
          L5_2 = L5_2["主界面"]
          L4_2 = L4_2(L5_2)
        until L4_2
        L4_2 = _print
        L5_2 = "佛手购买成功！"
        L4_2(L5_2)
        break
      end
    end
    L4_2 = mSleep
    L5_2 = 100
    L4_2(L5_2)
  end
end

_ENV["跟对买蓝"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["购买"]
    L5_2 = L5_2["包子"]
    L5_2 = L5_2["包子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 400
      L6_2 = 800
      L4_2(L5_2, L6_2)
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["购买"]
    L5_2 = L5_2["包子"]
    L5_2 = L5_2["选中包子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _tap
      L5_2 = 1741
      L6_2 = 776
      L4_2(L5_2, L6_2)
      L4_2 = _Sleep
      L5_2 = 400
      L6_2 = 800
      L4_2(L5_2, L6_2)
      L4_2 = _cmp_cx
      L5_2 = Color
      L5_2 = L5_2["购买"]
      L5_2 = L5_2["包子"]
      L5_2 = L5_2["键盘已显示"]
      L6_2 = {}
      L7_2 = 30
      L8_2 = 1000
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = _tap
        L5_2 = 1793
        L6_2 = 336
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1639
        L6_2 = 638
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1792
        L6_2 = 485
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1795
        L6_2 = 634
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 400
        L6_2 = 800
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1506
        L6_2 = 968
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 700
        L6_2 = 1300
        L4_2(L5_2, L6_2)
        L4_2 = _ENV["_卡区延迟"]
        L4_2()
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
        L4_2 = _print
        L5_2 = "包子购买成功！"
        L4_2(L5_2)
        return
      end
    end
    L4_2 = mSleep
    L5_2 = 100
    L4_2(L5_2)
  end
end

_ENV["跟对买红"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 117 - L0_2
    L3_2 = 71 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 117
        L9_2 = 71
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["领取探案任务"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["领取探案任务1"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案kj"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 814
        L7_2 = 548
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["建邺探案kj"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["领取建邺探案"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = _ENV["_返回"]
  L0_2 = L0_2["坐标"]
  L0_2, L1_2 = L0_2()
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取角色屏幕坐标"]
  L3_2 = "建邺衙门"
  L4_2 = L0_2
  L5_2 = L1_2
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2)
  L4_2 = _ENV["_计算"]
  L4_2 = L4_2["取目标屏幕坐标"]
  L5_2 = "建邺衙门"
  L6_2 = L0_2
  L7_2 = L1_2
  L8_2 = 33
  L9_2 = 20
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L6_2 = tap
  L7_2 = L4_2
  L8_2 = L5_2
  L6_2(L7_2, L8_2)
end

_ENV["点击县令"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = _ENV["_返回"]
  L0_2 = L0_2["坐标"]
  L0_2, L1_2 = L0_2()
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取角色屏幕坐标"]
  L3_2 = "建邺衙门"
  L4_2 = L0_2
  L5_2 = L1_2
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2)
  L4_2 = _ENV["_计算"]
  L4_2 = L4_2["取目标屏幕坐标"]
  L5_2 = "建邺衙门"
  L6_2 = L0_2
  L7_2 = L1_2
  L8_2 = 28
  L9_2 = 21
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L6_2 = tap
  L7_2 = L4_2
  L8_2 = L5_2
  L6_2(L7_2, L8_2)
end

_ENV["点击师爷"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = _ENV["_返回"]
  L0_2 = L0_2["坐标"]
  L0_2, L1_2 = L0_2()
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取角色屏幕坐标"]
  L3_2 = "建邺衙门"
  L4_2 = L0_2
  L5_2 = L1_2
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2)
  L4_2 = _ENV["_计算"]
  L4_2 = L4_2["取目标屏幕坐标"]
  L5_2 = "建邺衙门"
  L6_2 = L0_2
  L7_2 = L1_2
  L8_2 = 30
  L9_2 = 13
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L6_2 = tap
  L7_2 = L4_2
  L8_2 = L5_2
  L6_2(L7_2, L8_2)
end

_ENV["点击衙役"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = _ENV["_返回"]
  L0_2 = L0_2["坐标"]
  L0_2, L1_2 = L0_2()
  L2_2 = _ENV["_计算"]
  L2_2 = L2_2["取角色屏幕坐标"]
  L3_2 = "李善人家"
  L4_2 = L0_2
  L5_2 = L1_2
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2)
  L4_2 = _ENV["_计算"]
  L4_2 = L4_2["取目标屏幕坐标"]
  L5_2 = "李善人家"
  L6_2 = L0_2
  L7_2 = L1_2
  L8_2 = 18
  L9_2 = 9
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L6_2 = tap
  L7_2 = L4_2
  L8_2 = L5_2
  L6_2(L7_2, L8_2)
end

_ENV["点击李善人"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["建邺探案"]
  L1_2 = L1_2["找县令"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    while true do
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = randomTap
        L1_2 = 1735
        L2_2 = 403
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
        L0_2 = _ENV["_随机延时"]
        L1_2 = 411
        L0_2(L1_2)
        L0_2 = moveJudge
        L0_2()
      end
      L0_2 = _ENV["monitorAddress改"]
      L1_2 = "33"
      L2_2 = "20"
      L0_2 = L0_2(L1_2, L2_2)
      if L0_2 then
        L0_2 = _ENV["点击县令"]
        L0_2()
        L0_2 = _find_cx
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["出现对话框"]
        L2_2 = {}
        L3_2 = 30
        L4_2 = 110
        L2_2[1] = L3_2
        L2_2[2] = L4_2
        L0_2 = L0_2(L1_2, L2_2)
        if L0_2 then
          L0_2 = 1
          L1_2 = 40
          L2_2 = 1
          for L3_2 = L0_2, L1_2, L2_2 do
            L4_2 = _find_tb
            L5_2 = Color
            L5_2 = L5_2["建邺探案"]
            L5_2 = L5_2["建邺探案an"]
            L4_2 = L4_2(L5_2)
            if L4_2 then
              L4_2 = _Sleep
              L5_2 = 300
              L6_2 = 500
              L4_2(L5_2, L6_2)
            end
            L4_2 = _find
            L5_2 = Color
            L5_2 = L5_2["起号"]
            L5_2 = L5_2["出现对话框"]
            L4_2 = L4_2(L5_2)
            if L4_2 then
              L4_2 = _find
              L5_2 = Color
              L5_2 = L5_2["起号"]
              L5_2 = L5_2["可选对话框"]
              L4_2 = L4_2(L5_2)
              if L4_2 == false then
                L4_2 = tap
                L5_2 = 963
                L6_2 = 857
                L4_2(L5_2, L6_2)
                L4_2 = _ENV["_随机延时"]
                L5_2 = 111
                L4_2(L5_2)
              end
            end
            L4_2 = _cmp
            L5_2 = Color
            L5_2 = L5_2["建邺探案"]
            L5_2 = L5_2["找师爷"]
            L4_2 = L4_2(L5_2)
            if L4_2 then
              return
            end
            if L3_2 == 40 then
              L4_2 = _ENV["通用功能"]
              L4_2 = L4_2["关闭"]
              L4_2()
            end
            L4_2 = mSleep
            L5_2 = 50
            L4_2(L5_2)
          end
        end
      end
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["找师爷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["建邺1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["绘卷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = randomTap
      L1_2 = 1735
      L2_2 = 403
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
      L0_2 = moveJudge
      L0_2()
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
      L0_2 = moveJudge
      L0_2()
    end
    L0_2 = _ENV["monitorAddress改"]
    L1_2 = "28"
    L2_2 = "21"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = _ENV["点击师爷"]
      L0_2()
      L0_2 = _find_cx
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["出现对话框"]
      L2_2 = {}
      L3_2 = 30
      L4_2 = 110
      L2_2[1] = L3_2
      L2_2[2] = L4_2
      L0_2 = L0_2(L1_2, L2_2)
      if L0_2 then
        L0_2 = 1
        L1_2 = 40
        L2_2 = 1
        for L3_2 = L0_2, L1_2, L2_2 do
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["建邺探案"]
          L5_2 = L5_2["建邺探案an"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = _Sleep
            L5_2 = 300
            L6_2 = 500
            L4_2(L5_2, L6_2)
          end
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["出现对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = _find
            L5_2 = Color
            L5_2 = L5_2["起号"]
            L5_2 = L5_2["可选对话框"]
            L4_2 = L4_2(L5_2)
            if L4_2 == false then
              L4_2 = tap
              L5_2 = 963
              L6_2 = 857
              L4_2(L5_2, L6_2)
              L4_2 = _ENV["_随机延时"]
              L5_2 = 111
              L4_2(L5_2)
            end
          end
          L4_2 = _cmp
          L5_2 = Color
          L5_2 = L5_2["建邺探案"]
          L5_2 = L5_2["找衙役"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            return
          end
          if L3_2 == 40 then
            L4_2 = _ENV["通用功能"]
            L4_2 = L4_2["关闭"]
            L4_2()
          end
          L4_2 = mSleep
          L5_2 = 50
          L4_2(L5_2)
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["找衙役"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      return
    end
  end
end

_ENV["建邺2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["建邺探案"]
  L1_2 = L1_2["找衙役"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _ENV["点击衙役"]
    L0_2()
    L0_2 = _find_cx
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 110
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 40
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["建邺探案an"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _Sleep
          L5_2 = 300
          L6_2 = 500
          L4_2(L5_2, L6_2)
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["可选对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = tap
            L5_2 = 963
            L6_2 = 857
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_随机延时"]
            L5_2 = 111
            L4_2(L5_2)
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["找衙役2"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          return
        end
        if L3_2 == 40 then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
        end
        L4_2 = mSleep
        L5_2 = 50
        L4_2(L5_2)
      end
    end
  end
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["建邺探案"]
  L1_2 = L1_2["找衙役2"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    return
  end
end

_ENV["建邺3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _ENV["点击师爷"]
  L0_2()
  L0_2 = _find_cx
  L1_2 = Color
  L1_2 = L1_2["起号"]
  L1_2 = L1_2["出现对话框"]
  L2_2 = {}
  L3_2 = 30
  L4_2 = 110
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 then
    L0_2 = 1
    L1_2 = 40
    L2_2 = 1
    for L3_2 = L0_2, L1_2, L2_2 do
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["建邺探案"]
      L5_2 = L5_2["建邺探案an"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _Sleep
        L5_2 = 300
        L6_2 = 500
        L4_2(L5_2, L6_2)
      end
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现对话框"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["可选对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 == false then
          L4_2 = tap
          L5_2 = 963
          L6_2 = 857
          L4_2(L5_2, L6_2)
          L4_2 = _ENV["_随机延时"]
          L5_2 = 111
          L4_2(L5_2)
        end
      end
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["建邺探案"]
      L5_2 = L5_2["衙役丁"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _Sleep
        L5_2 = 300
        L6_2 = 500
        L4_2(L5_2, L6_2)
      end
      L4_2 = _cmp
      L5_2 = Color
      L5_2 = L5_2["建邺探案"]
      L5_2 = L5_2["找赵捕头"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        return
      end
      if L3_2 == 40 then
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
      end
      L4_2 = mSleep
      L5_2 = 50
      L4_2(L5_2)
    end
  end
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["建邺探案"]
  L1_2 = L1_2["找赵捕头"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    return
  end
end

_ENV["建邺4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _find_tb
  L1_2 = Color
  L1_2 = L1_2["起号"]
  L1_2 = L1_2["绘卷"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = randomTap
    L1_2 = 1735
    L2_2 = 403
    L3_2 = 10
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    L0_2 = moveJudge
    L0_2()
    L0_2 = _ENV["_随机延时"]
    L1_2 = 411
    L0_2(L1_2)
    L0_2 = moveJudge
    L0_2()
  end
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 117 - L0_2
    L3_2 = 71 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 117
        L9_2 = 71
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["失窃疑云"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 814
        L7_2 = 548
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["失窃疑云"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["建邺5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 278
  L3_2 = 481
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 6 - L1_2
    L4_2 = 83 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 133
          L7_2 = 340
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城李善人家一楼"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 278
          L8_2 = 481
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 278
      L8_2 = 481
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  repeat
    L0_2 = _tap
    L1_2 = 142
    L2_2 = 364
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _tap
    L1_2 = 142
    L2_2 = 364
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _tap
    L1_2 = 142
    L2_2 = 364
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城李善人家一楼"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
  until not L0_2
  L0_2 = _print
  L1_2 = "到达建邺城李善人家二楼"
  L0_2(L1_2)
end

_ENV["前往李善人旁"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = 1
  L1_2 = 50
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["证据栏"]
    L4_2, L5_2, L6_2 = L4_2(L5_2)
    if L4_2 then
      if not (670 <= L5_2 and 1036 <= L6_2 and L5_2 <= 690 and L6_2 <= 1056) then
        goto lbl_21
      end
      do break end
      ::lbl_21::
      L7_2 = touchDown
      L8_2 = 0
      L9_2 = L5_2
      L10_2 = L6_2
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 350
      L7_2(L8_2)
      L7_2 = touchMove
      L8_2 = 0
      L9_2 = 680
      L10_2 = 1046
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 250
      L7_2(L8_2)
      L7_2 = touchUp
      L8_2 = 0
      L9_2 = 680
      L10_2 = 1046
      L7_2(L8_2, L9_2, L10_2)
      break
    end
    L7_2 = mSleep
    L8_2 = 30
    L7_2(L8_2)
  end
end

_ENV["移动证据栏"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 117 - L0_2
    L3_2 = 71 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 117
        L9_2 = 71
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["李善人家"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 814
        L7_2 = 548
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["李善人家"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["失窃1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 32 - L0_2
    L3_2 = 86 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 32
        L9_2 = 86
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["案发现场"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 60
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 411
        L7_2 = 470
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _ENV["移动证据栏"]
    L4_2()
    L4_2 = mSleep
    L5_2 = 500
    L4_2(L5_2)
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["案发现场"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["失窃2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _ENV["前往李善人旁"]
  L0_2()
  while true do
    L0_2 = _ENV["点击李善人"]
    L0_2()
    L0_2 = _find_cx
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 110
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 40
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["建邺探案an"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _Sleep
          L5_2 = 300
          L6_2 = 500
          L4_2(L5_2, L6_2)
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["可选对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = tap
            L5_2 = 963
            L6_2 = 857
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_随机延时"]
            L5_2 = 111
            L4_2(L5_2)
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["福全"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          return
        end
        if L3_2 == 40 then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
        end
        L4_2 = mSleep
        L5_2 = 50
        L4_2(L5_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["福全"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      return
    end
  end
end

_ENV["失窃3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["建邺城"]
  L0_2()
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 19 - L0_2
    L3_2 = 91 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 19
        L9_2 = 91
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["戏班老板"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 344
        L7_2 = 441
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["戏班老板"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["失窃4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 86 - L0_2
    L3_2 = 114 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 86
        L9_2 = 114
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["吹牛王"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 681
        L7_2 = 335
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["吹牛王"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["失窃5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 90 - L0_2
    L3_2 = 110 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 90
        L9_2 = 110
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["马全有"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 681
        L7_2 = 335
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["马全有"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["失窃6"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 51 - L0_2
    L3_2 = 51 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 51
        L9_2 = 51
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["教书先生"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 509
        L7_2 = 633
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["教书先生"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["生父2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 77 - L0_2
    L3_2 = 89 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 77
        L9_2 = 89
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["雷黑子"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 642
        L7_2 = 448
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["雷黑子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["生父3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 73 - L0_2
    L3_2 = 10 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 73
        L9_2 = 10
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["小花"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 598
        L7_2 = 839
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["小花"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["生父4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 207 - L0_2
    L3_2 = 36 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 207
        L9_2 = 36
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["布店"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1268
        L7_2 = 712
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["布店"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["生父5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 1086
  L3_2 = 727
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 169 - L1_2
    L4_2 = 33 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 1132
          L7_2 = 371
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城布店"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 1086
          L8_2 = 727
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 1086
      L8_2 = 727
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_105
        end
      end
      L2_2 = _tap
      L3_2 = 941
      L4_2 = 464
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_105::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["药店"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["药店"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["生父6"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  while true do
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城布店"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = _tap
      L1_2 = 620
      L2_2 = 901
      L0_2(L1_2, L2_2)
      L0_2 = mSleep
      L1_2 = 800
      L0_2(L1_2)
      L0_2 = _ENV["_功能"]
      L0_2 = L0_2["寻路"]
      L0_2()
    end
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      break
    end
  end
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 1325
  L3_2 = 860
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 221 - L1_2
    L4_2 = 6 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 767
          L7_2 = 693
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城药店"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 1327
          L8_2 = 860
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 1327
      L8_2 = 860
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_138
        end
      end
      L2_2 = _tap
      L3_2 = 983
      L4_2 = 469
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_138::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["马全有2"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["马全有2"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["生父7"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["建邺城"]
  L0_2()
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 51 - L0_2
    L3_2 = 51 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 51
        L9_2 = 51
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["老孙头"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 509
        L7_2 = 633
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["老孙头"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["生父8"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 223 - L0_2
    L3_2 = 132 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 223
        L9_2 = 132
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["牛大胆"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1336
        L7_2 = 245
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["牛大胆"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 230 - L0_2
    L3_2 = 108 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 230
        L9_2 = 108
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["飞儿"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1379
        L7_2 = 364
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["飞儿"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 103 - L0_2
    L3_2 = 106 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 103
        L9_2 = 106
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["王大嫂"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 764
        L7_2 = 370
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["王大嫂"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 139 - L0_2
    L3_2 = 130 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 139
        L9_2 = 130
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["杂货店"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 937
        L7_2 = 248
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["杂货店"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 836
  L3_2 = 252
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 119 - L1_2
    L4_2 = 130 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 841
          L7_2 = 326
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城杂货店"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 836
          L8_2 = 252
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 836
      L8_2 = 252
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_105
        end
      end
      L2_2 = _tap
      L3_2 = 1392
      L4_2 = 493
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_105::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["当铺"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["当铺"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["望夫5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = _ENV["_使用"]
  L0_2 = L0_2["飞行符"]
  L1_2 = "建邺城"
  L0_2(L1_2)
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 881
  L3_2 = 694
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 127 - L1_2
    L4_2 = 43 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 1142
          L7_2 = 406
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城当铺"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 881
          L8_2 = 694
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 881
      L8_2 = 694
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_109
        end
      end
      L2_2 = _tap
      L3_2 = 946
      L4_2 = 656
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_109::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["钱庄"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["钱庄"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["望夫6"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  while true do
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城当铺"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = _tap
      L1_2 = 620
      L2_2 = 901
      L0_2(L1_2, L2_2)
      L0_2 = mSleep
      L1_2 = 800
      L0_2(L1_2)
      L0_2 = _ENV["_功能"]
      L0_2 = L0_2["寻路"]
      L0_2()
    end
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      break
    end
  end
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 1420
  L3_2 = 498
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 234 - L1_2
    L4_2 = 79 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 1115
          L7_2 = 464
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城钱庄"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 1420
          L8_2 = 498
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 1420
      L8_2 = 498
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_138
        end
      end
      L2_2 = _tap
      L3_2 = 1259
      L4_2 = 490
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_138::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["老胡"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["老胡"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["望夫7"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["建邺城"]
  L0_2()
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 51 - L0_2
    L3_2 = 31 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 51
        L9_2 = 31
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["张来福"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 501
        L7_2 = 739
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["张来福"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫8"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 87 - L0_2
    L3_2 = 73 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 87
        L9_2 = 73
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["罗招弟"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 689
        L7_2 = 533
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["罗招弟"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫9"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 220 - L0_2
    L3_2 = 113 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 220
        L9_2 = 113
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["赵元宝"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1340
        L7_2 = 338
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["赵元宝"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["望夫10"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 228 - L0_2
    L3_2 = 64 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 228
        L9_2 = 64
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["宠物仙子"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1380
        L7_2 = 573
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 1000
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["宠物仙子"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["漏洞1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 63 - L0_2
    L3_2 = 115 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 63
        L9_2 = 115
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["吹牛王2"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 555
        L7_2 = 330
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 1000
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["吹牛王2"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["漏洞2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 90 - L0_2
    L3_2 = 110 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 90
        L9_2 = 110
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["周猎户"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 681
        L7_2 = 335
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["周猎户"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["漏洞3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 1449
  L3_2 = 792
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 244 - L1_2
    L4_2 = 20 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 838
          L7_2 = 432
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城李善人家一楼"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 1449
          L8_2 = 792
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 1449
      L8_2 = 792
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_105
        end
      end
      L2_2 = _tap
      L3_2 = 907
      L4_2 = 452
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_105::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["再案发现场"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["再案发现场"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["漏洞4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["建邺城"]
  L0_2()
  L0_2 = _ENV["前往李善人旁"]
  L0_2()
  while true do
    L0_2 = _ENV["点击李善人"]
    L0_2()
    L0_2 = _find_cx
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["出现对话框"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 110
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = 1
      L1_2 = 40
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["建邺探案an"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _Sleep
          L5_2 = 300
          L6_2 = 500
          L4_2(L5_2, L6_2)
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["可选对话框"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = tap
            L5_2 = 963
            L6_2 = 857
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_随机延时"]
            L5_2 = 111
            L4_2(L5_2)
          end
        end
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["建邺探案"]
        L5_2 = L5_2["陈长寿"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          return
        end
        if L3_2 == 40 then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
        end
        L4_2 = mSleep
        L5_2 = 50
        L4_2(L5_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["陈长寿"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      return
    end
  end
end

_ENV["漏洞5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["建邺城"]
  L0_2()
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 219 - L0_2
    L3_2 = 122 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 219
        L9_2 = 122
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["超级巫医"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 1330
        L7_2 = 289
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 1000
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["超级巫医"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["水落1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 104 - L0_2
    L3_2 = 55 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 104
        L9_2 = 55
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["兵器"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 767
        L7_2 = 628
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 1000
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["兵器"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["水落2"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["固定坐标"]
  L1_2 = "建邺城"
  L2_2 = 626
  L3_2 = 832
  L0_2(L1_2, L2_2, L3_2)
  while true do
    L0_2 = 1
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["贼王"]
    L2_2 = L2_2["传送门"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 1
      L3_2 = 2
      L1_2 = L1_2(L2_2, L3_2)
      L0_2 = L1_2
    end
    L1_2 = _ENV["起号"]
    L1_2 = L1_2["坐标"]
    L1_2, L2_2 = L1_2()
    L3_2 = 74 - L1_2
    L4_2 = 11 - L2_2
    L5_2 = math
    L5_2 = L5_2.abs
    L6_2 = L3_2
    L5_2 = L5_2(L6_2)
    if L5_2 <= 5 then
      L5_2 = math
      L5_2 = L5_2.abs
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      if L5_2 <= 5 then
        if L0_2 == 2 then
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["点传送门"]
          L5_2(L6_2)
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["寻路"]
          L5_2()
        else
          L5_2 = _ENV["_功能"]
          L5_2 = L5_2["屏蔽"]
          L6_2 = 4
          L5_2(L6_2)
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["进入光圈"]
          L6_2 = 1092
          L7_2 = 479
          L5_2(L6_2, L7_2)
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "建邺城铁匠铺"
        L5_2 = L5_2(L6_2)
        if L5_2 == false then
          L5_2 = _ENV["_前往"]
          L5_2 = L5_2["固定坐标"]
          L6_2 = "建邺城"
          L7_2 = 626
          L8_2 = 832
          L5_2(L6_2, L7_2, L8_2)
        else
          break
        end
    end
    else
      L5_2 = _ENV["_前往"]
      L5_2 = L5_2["固定坐标"]
      L6_2 = "建邺城"
      L7_2 = 626
      L8_2 = 832
      L5_2(L6_2, L7_2, L8_2)
    end
    L5_2 = mSleep
    L6_2 = 100
    L5_2(L6_2)
  end
  while true do
    L0_2 = nil
    L1_2 = os
    L1_2 = L1_2.time
    L1_2 = L1_2()
    while true do
      if L0_2 ~= nil then
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if not (3 < L2_2) then
          goto lbl_109
        end
      end
      L2_2 = _tap
      L3_2 = 746
      L4_2 = 623
      L5_2 = 1
      L6_2 = 1
      L2_2(L3_2, L4_2, L5_2, L6_2)
      L2_2 = _Sleep
      L3_2 = 500
      L4_2 = 1000
      L2_2(L3_2, L4_2)
      L2_2 = os
      L2_2 = L2_2.time
      L2_2 = L2_2()
      L0_2 = L2_2
      ::lbl_109::
      L2_2 = _find_tb
      L3_2 = Color
      L3_2 = L3_2["出现重叠"]
      L2_2, L3_2, L4_2 = L2_2(L3_2)
      if L2_2 then
        L5_2 = L3_2 - 120
        L6_2 = L4_2 + 31
        L7_2 = L3_2 - 53
        L8_2 = L4_2 + 540
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["师门"]
        L10_2 = L10_2["缎带"]
        L10_2 = L10_2["npc重叠"]
        L11_2 = {}
        L12_2 = 0
        L13_2 = 0
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L12_2 = {}
        L13_2 = L5_2
        L14_2 = L6_2
        L15_2 = L7_2
        L16_2 = L8_2
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L12_2[3] = L15_2
        L12_2[4] = L16_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        if L9_2 then
          L9_2 = _Sleep
          L10_2 = 500
          L11_2 = 1000
          L9_2(L10_2, L11_2)
        end
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["起号"]
      L6_2 = L6_2["出现对话框"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 110
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = 1
        L6_2 = 40
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["建邺探案an"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 300
            L11_2 = 500
            L9_2(L10_2, L11_2)
          end
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2["起号"]
          L10_2 = L10_2["出现对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["起号"]
            L10_2 = L10_2["可选对话框"]
            L9_2 = L9_2(L10_2)
            if L9_2 == false then
              L9_2 = tap
              L10_2 = 963
              L11_2 = 857
              L9_2(L10_2, L11_2)
              L9_2 = _ENV["_随机延时"]
              L10_2 = 111
              L9_2(L10_2)
            end
          end
          L9_2 = _cmp
          L10_2 = Color
          L10_2 = L10_2["建邺探案"]
          L10_2 = L10_2["抓人"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            return
          end
          if L8_2 == 40 then
            L9_2 = _ENV["通用功能"]
            L9_2 = L9_2["关闭"]
            L9_2()
          end
          L9_2 = mSleep
          L10_2 = 41
          L9_2(L10_2)
        end
      end
      L5_2 = _cmp
      L6_2 = Color
      L6_2 = L6_2["建邺探案"]
      L6_2 = L6_2["抓人"]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        return
      end
      L5_2 = os
      L5_2 = L5_2.time
      L5_2 = L5_2()
      L5_2 = L5_2 - L1_2
      if 90 < L5_2 then
        L5_2 = _ENV["_使用"]
        L5_2 = L5_2["飞行符"]
        L6_2 = "建邺城"
        L5_2(L6_2)
      end
    end
  end
end

_ENV["水落3"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城铁匠铺"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = _tap
      L1_2 = 620
      L2_2 = 901
      L0_2(L1_2, L2_2)
      L0_2 = mSleep
      L1_2 = 800
      L0_2(L1_2)
      L0_2 = _ENV["_功能"]
      L0_2 = L0_2["寻路"]
      L0_2()
    end
    L0_2 = _cmp_tb_cx
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达建邺城"]
    L2_2 = {}
    L3_2 = 30
    L4_2 = 100
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      break
    end
  end
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 32 - L0_2
    L3_2 = 86 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 32
        L9_2 = 86
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["汇报"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = mSleep
            L9_2 = 60
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 411
        L7_2 = 470
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = mSleep
    L5_2 = 500
    L4_2(L5_2)
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["汇报"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      return
    end
  end
end

_ENV["水落4"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 117 - L0_2
    L3_2 = 71 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "建邺城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 117
        L9_2 = 71
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 200
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 110
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = 1
          L5_2 = 40
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find_tb
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺探案an"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _Sleep
              L9_2 = 300
              L10_2 = 500
              L8_2(L9_2, L10_2)
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["起号"]
            L9_2 = L9_2["出现对话框"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["可选对话框"]
              L8_2 = L8_2(L9_2)
              if L8_2 == false then
                L8_2 = tap
                L9_2 = 963
                L10_2 = 857
                L8_2(L9_2, L10_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 111
                L8_2(L9_2)
              end
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["建邺神探"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
              return
            end
            if L7_2 == 40 then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["关闭"]
              L8_2()
            end
            L8_2 = _cmp
            L9_2 = Color
            L9_2 = L9_2["建邺探案"]
            L9_2 = L9_2["水落石出"]
            L8_2 = L8_2(L9_2)
            if L8_2 == false then
              L8_2 = _find_tb
              L9_2 = Color
              L9_2 = L9_2["起号"]
              L9_2 = L9_2["绘卷"]
              L8_2 = L8_2(L9_2)
              if L8_2 then
                return
              end
            end
            L8_2 = mSleep
            L9_2 = 41
            L8_2(L9_2)
          end
        end
    end
    else
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["出现手势"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp_tb
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现手势1"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["绘卷"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
            L4_2 = touchDown
            L5_2 = 963
            L6_2 = 369
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 54
            L4_2(L5_2)
            L4_2 = touchMove
            L5_2 = 1004
            L6_2 = 368
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 41
            L4_2(L5_2)
            L4_2 = touchUp
            L5_2 = 1300
            L6_2 = 357
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = 500
            L4_2(L5_2)
        end
      end
      else
        L4_2 = _ENV["_前往"]
        L4_2 = L4_2["固定坐标"]
        L5_2 = "建邺城"
        L6_2 = 814
        L7_2 = 548
        L4_2(L5_2, L6_2, L7_2)
        L4_2 = mSleep
        L5_2 = 100
        L4_2(L5_2)
      end
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["建邺神探"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _ENV["通用功能"]
      L4_2 = L4_2["关闭"]
      L4_2()
      return
    end
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["建邺探案"]
    L5_2 = L5_2["水落石出"]
    L4_2 = L4_2(L5_2)
    if L4_2 == false then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["起号"]
      L5_2 = L5_2["绘卷"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        return
      end
    end
  end
end

_ENV["水落5"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["陈长寿"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["水落1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["超级巫医"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["水落2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["兵器"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["水落3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["抓人"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["水落4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["汇报"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["水落5"]
              L0_2()
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["建邺神探"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["通用功能"]
      L0_2 = L0_2["关闭"]
      L0_2()
      return
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["水落石出"]
    L0_2 = L0_2(L1_2)
    if L0_2 == false then
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["起号"]
      L1_2 = L1_2["绘卷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        return
      end
    end
  end
end

_ENV["建邺水落石出"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["赵元宝"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["漏洞1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["宠物仙子"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["漏洞2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["吹牛王2"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["漏洞3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["周猎户"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["漏洞4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["再案发现场"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["漏洞5"]
              L0_2()
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["水落石出"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺水落石出"]
      return L0_2()
    end
  end
end

_ENV["建邺漏洞百出"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["老孙头"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["望夫1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["牛大胆"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["望夫2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["飞儿"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["望夫3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["王大嫂"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["望夫4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["杂货店"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["望夫5"]
              L0_2()
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["建邺探案"]
              L1_2 = L1_2["当铺"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _ENV["望夫6"]
                L0_2()
              else
                L0_2 = _cmp
                L1_2 = Color
                L1_2 = L1_2["建邺探案"]
                L1_2 = L1_2["钱庄"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  L0_2 = _ENV["望夫7"]
                  L0_2()
                else
                  L0_2 = _cmp
                  L1_2 = Color
                  L1_2 = L1_2["建邺探案"]
                  L1_2 = L1_2["老胡"]
                  L0_2 = L0_2(L1_2)
                  if L0_2 then
                    L0_2 = _ENV["望夫8"]
                    L0_2()
                  else
                    L0_2 = _cmp
                    L1_2 = Color
                    L1_2 = L1_2["建邺探案"]
                    L1_2 = L1_2["张来福"]
                    L0_2 = L0_2(L1_2)
                    if L0_2 then
                      L0_2 = _ENV["望夫9"]
                      L0_2()
                    else
                      L0_2 = _cmp
                      L1_2 = Color
                      L1_2 = L1_2["建邺探案"]
                      L1_2 = L1_2["罗招弟"]
                      L0_2 = L0_2(L1_2)
                      if L0_2 then
                        L0_2 = _ENV["望夫10"]
                        L0_2()
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["漏洞百出"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺漏洞百出"]
      return L0_2()
    end
  end
end

_ENV["建邺望夫归"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["吹牛王"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["生父1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["马全有"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["生父2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["教书先生"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["生父3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["雷黑子"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["生父4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["小花"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["生父5"]
              L0_2()
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["建邺探案"]
              L1_2 = L1_2["布店"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _ENV["生父6"]
                L0_2()
              else
                L0_2 = _cmp
                L1_2 = Color
                L1_2 = L1_2["建邺探案"]
                L1_2 = L1_2["药店"]
                L0_2 = L0_2(L1_2)
                if L0_2 then
                  L0_2 = _ENV["生父7"]
                  L0_2()
                else
                  L0_2 = _cmp
                  L1_2 = Color
                  L1_2 = L1_2["建邺探案"]
                  L1_2 = L1_2["马全有2"]
                  L0_2 = L0_2(L1_2)
                  if L0_2 then
                    L0_2 = _ENV["生父8"]
                    L0_2()
                  end
                end
              end
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["望夫归"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺望夫归"]
      return L0_2()
    end
  end
end

_ENV["建邺生父迷"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["找赵捕头2"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["失窃1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["李善人家"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["失窃2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["案发现场"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["失窃3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["福全"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["失窃4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["戏班老板"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["失窃5"]
              L0_2()
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["建邺探案"]
              L1_2 = L1_2["吹牛王"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _ENV["失窃6"]
                L0_2()
              end
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["生父迷"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺生父迷"]
      return L0_2()
    end
  end
end

_ENV["建邺失窃疑云"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["建邺探案kj"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺1"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["找师爷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["建邺2"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["找衙役"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["建邺3"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["找衙役2"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["建邺4"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["找赵捕头"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["建邺5"]
              L0_2()
            end
          end
        end
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["失窃疑云"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺失窃疑云"]
      return L0_2()
    end
  end
end

_ENV["建邺疑难初探"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["建邺探案"]
  L1_2 = L1_2["疑难初探"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _ENV["建邺疑难初探"]
    L0_2()
  else
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["建邺探案"]
    L1_2 = L1_2["失窃疑云"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["建邺失窃疑云"]
      L0_2()
    else
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["建邺探案"]
      L1_2 = L1_2["生父迷"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["建邺生父迷"]
        L0_2()
      else
        L0_2 = _cmp
        L1_2 = Color
        L1_2 = L1_2["建邺探案"]
        L1_2 = L1_2["望夫归"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _ENV["建邺望夫归"]
          L0_2()
        else
          L0_2 = _cmp
          L1_2 = Color
          L1_2 = L1_2["建邺探案"]
          L1_2 = L1_2["漏洞百出"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = _ENV["建邺漏洞百出"]
            L0_2()
          else
            L0_2 = _cmp
            L1_2 = Color
            L1_2 = L1_2["建邺探案"]
            L1_2 = L1_2["水落石出"]
            L0_2 = L0_2(L1_2)
            if L0_2 then
              L0_2 = _ENV["建邺水落石出"]
              L0_2()
            else
              L0_2 = _cmp
              L1_2 = Color
              L1_2 = L1_2["建邺探案"]
              L1_2 = L1_2["建邺城探案任务"]
              L0_2 = L0_2(L1_2)
              if L0_2 then
                L0_2 = _ENV["建邺疑难初探"]
                L0_2()
              else
                L0_2 = _ENV["通用功能"]
                L0_2 = L0_2["任务栏清理"]
                L0_2()
                L0_2 = _ENV["_前往"]
                L0_2 = L0_2["建邺城"]
                L0_2()
                L0_2 = _ENV["领取建邺探案"]
                L0_2()
                L0_2 = _ENV["建邺疑难初探"]
                L0_2()
              end
            end
          end
        end
      end
    end
  end
end

_ENV["建邺探案主流程"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["建邺探案主流程"]
  L0_2()
end

_ENV["建邺探案任务"] = L0_1
