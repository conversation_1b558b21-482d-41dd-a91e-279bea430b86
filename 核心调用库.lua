local L0_1, L1_1, L2_1
L0_1 = {}
propsVacancy = L0_1
L0_1 = {}
lugVacancy = L0_1
travelMod = "fly"
needDodge = false
dodgeTime = -1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["_功能"]
  L0_2 = L0_2["屏蔽"]
  L1_2 = 3
  L0_2(L1_2)
end

_ENV["隐藏窗口按钮"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = _ENV["判断收缩充值"]
  L0_2()
  L0_2 = tsOcrText
  L1_2 = mapNumb
  L2_2 = 145
  L3_2 = 91
  L4_2 = 312
  L5_2 = 127
  L6_2 = "C6D0D5 , 3A2F2B#C6D0D5 , 3A2F2B#C6D0D5 , 3A2F2B#F7D8C7 , 082739"
  L7_2 = 90
  L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  if L0_2 == "1" or L0_2 == "" then
    L1_2 = {}
    L2_2 = -1
    L3_2 = -1
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    return L1_2
  else
    L1_2 = strSplit
    L2_2 = L0_2
    L3_2 = ","
    L1_2 = L1_2(L2_2, L3_2)
    L2_2 = L1_2[1]
    L3_2 = L1_2[2]
    L4_2 = tonumber
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    L2_2 = L4_2
    L4_2 = tonumber
    L5_2 = L3_2
    L4_2 = L4_2(L5_2)
    L3_2 = L4_2
    L4_2 = type
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 == "number" then
      L4_2 = type
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 == "number" then
        L4_2 = {}
        L5_2 = L2_2
        L6_2 = L3_2
        L4_2[1] = L5_2
        L4_2[2] = L6_2
        return L4_2
    end
    else
      L4_2 = {}
      L5_2 = -1
      L6_2 = -1
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      return L4_2
    end
  end
end

getCurPos = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = tsOcrText
  L1_2 = mapName
  L2_2 = 152
  L3_2 = 39
  L4_2 = 315
  L5_2 = 75
  L6_2 = "212227 , 202126 "
  L7_2 = 85
  L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  return L0_2
end

getCurMap = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = dodgeSystem
    L0_2()
    L0_2 = getCurPos
    L0_2 = L0_2()
    L1_2 = mSleep
    L2_2 = 400
    L1_2(L2_2)
    L1_2 = getCurPos
    L1_2 = L1_2()
    L2_2 = L1_2[1]
    L3_2 = L0_2[1]
    if L2_2 == L3_2 then
      L2_2 = L1_2[2]
      L3_2 = L0_2[2]
      if L2_2 == L3_2 then
        return L1_2
      end
    end
  end
end

posMonitor = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = color
  L1_2 = L1_2.map
  L1_2 = L1_2.filterBtn
  L2_2 = constMapSize
  L2_2 = L2_2[A0_2]
  L2_2 = L2_2[4]
  L2_2 = L2_2[1]
  L2_2 = L2_2 - 120
  L1_2[4] = L2_2
  L1_2 = color
  L1_2 = L1_2.map
  L1_2 = L1_2.filterBtn
  L2_2 = constMapSize
  L2_2 = L2_2[A0_2]
  L2_2 = L2_2[4]
  L2_2 = L2_2[2]
  L2_2 = L2_2 - 40
  L1_2[5] = L2_2
  L1_2 = color
  L1_2 = L1_2.map
  L1_2 = L1_2.filterBtn
  L2_2 = constMapSize
  L2_2 = L2_2[A0_2]
  L2_2 = L2_2[4]
  L2_2 = L2_2[1]
  L2_2 = L2_2 + 120
  L1_2[6] = L2_2
  L1_2 = color
  L1_2 = L1_2.map
  L1_2 = L1_2.filterBtn
  L2_2 = constMapSize
  L2_2 = L2_2[A0_2]
  L2_2 = L2_2[4]
  L2_2 = L2_2[2]
  L2_2 = L2_2 + 40
  L1_2[7] = L2_2
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = findFeat
    L6_2 = color
    L6_2 = L6_2.map
    L6_2 = L6_2.filterBtn
    L5_2 = L5_2(L6_2)
    if 0 < L5_2 then
      L5_2 = true
      return L5_2
    else
      L5_2 = cleanupINF
      L5_2()
      L5_2 = fightSystem
      L5_2()
      L5_2 = singleTap
      L6_2 = constPos
      L6_2 = L6_2.openMap
      L7_2 = 30
      L5_2(L6_2, L7_2)
      L5_2 = 1
      L6_2 = 10
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = mSleep
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 30
        L12_2 = 60
        L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2)
        L9_2 = findFeat
        L10_2 = color
        L10_2 = L10_2.map
        L10_2 = L10_2.filterBtn
        L9_2 = L9_2(L10_2)
        if 0 < L9_2 then
          L9_2 = true
          return L9_2
        end
      end
    end
  end
  L1_2 = false
  return L1_2
end

openMap = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  L1_2 = _find_tb
  L2_2 = Color
  L2_2 = L2_2["地图"]
  L2_2 = L2_2["小地图传送口"]
  L1_2 = L1_2(L2_2)
  if L1_2 then
    L1_2 = 1
    L2_2 = 5
    L3_2 = 1
    for L4_2 = L1_2, L2_2, L3_2 do
      L5_2 = _find_tb
      L6_2 = Color
      L6_2 = L6_2["地图"]
      L6_2 = L6_2["小地图全部"]
      L5_2, L6_2, L7_2 = L5_2(L6_2)
      if L5_2 then
        L8_2 = L6_2 - 90
        L9_2 = L7_2 + 621
        L10_2 = L6_2 - 31
        L11_2 = L7_2 + 675
        L12_2 = _find_tb
        L13_2 = Color
        L13_2 = L13_2["地图"]
        L13_2 = L13_2["小地图筛选对勾"]
        L14_2 = {}
        L15_2 = 0
        L16_2 = 0
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = {}
        L16_2 = L8_2
        L17_2 = L9_2
        L18_2 = L10_2
        L19_2 = L11_2
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L15_2[3] = L18_2
        L15_2[4] = L19_2
        L12_2 = L12_2(L13_2, L14_2, L15_2)
        if L12_2 == false then
          L12_2 = _find_tb
          L13_2 = Color
          L13_2 = L13_2["地图"]
          L13_2 = L13_2["小地图传送口"]
          L12_2 = L12_2(L13_2)
          if L12_2 == false then
            L12_2 = _tap
            L13_2 = 229
            L14_2 = 14
            L12_2(L13_2, L14_2)
            L12_2 = _Sleep
            L13_2 = 160
            L14_2 = 210
            L12_2(L13_2, L14_2)
            L12_2 = true
            return L12_2
        end
        else
          L12_2 = _tap
          L13_2 = L6_2 - 63
          L14_2 = L7_2
          L12_2(L13_2, L14_2)
          L12_2 = _Sleep
          L13_2 = 300
          L14_2 = 310
          L12_2(L13_2, L14_2)
        end
      else
        L8_2 = _find_tb
        L9_2 = Color
        L9_2 = L9_2["地图"]
        L9_2 = L9_2["小地图筛选"]
        L8_2 = L8_2(L9_2)
        if L8_2 then
          L8_2 = _Sleep
          L9_2 = 300
          L10_2 = 310
          L8_2(L9_2, L10_2)
        end
      end
      L8_2 = _Sleep
      L9_2 = 100
      L10_2 = 130
      L8_2(L9_2, L10_2)
    end
  end
  L1_2 = true
  return L1_2
end

removeMapBarrier = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = math
  L2_2 = L2_2.floor
  L3_2 = A0_2[1]
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = -1
  L6_2 = 1
  L4_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2 + L4_2
  L4_2 = constMapSize
  L4_2 = L4_2[A1_2]
  L4_2 = L4_2[1]
  L4_2 = L4_2[1]
  L3_2 = L3_2 / L4_2
  L4_2 = constMapSize
  L4_2 = L4_2[A1_2]
  L4_2 = L4_2[2]
  L4_2 = L4_2[1]
  L3_2 = L3_2 * L4_2
  L2_2 = L2_2(L3_2)
  L3_2 = math
  L3_2 = L3_2.floor
  L4_2 = A0_2[2]
  L5_2 = math
  L5_2 = L5_2.random
  L6_2 = -1
  L7_2 = 1
  L5_2 = L5_2(L6_2, L7_2)
  L4_2 = L4_2 + L5_2
  L5_2 = constMapSize
  L5_2 = L5_2[A1_2]
  L5_2 = L5_2[1]
  L5_2 = L5_2[2]
  L4_2 = L4_2 / L5_2
  L5_2 = constMapSize
  L5_2 = L5_2[A1_2]
  L5_2 = L5_2[2]
  L5_2 = L5_2[2]
  L4_2 = L4_2 * L5_2
  L3_2 = L3_2(L4_2)
  L4_2 = {}
  L5_2 = L2_2
  L6_2 = L3_2
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  return L4_2
end

posConvert = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L1_2 = A0_2[1]
  L1_2 = L1_2 - 50
  L2_2 = A0_2[1]
  L2_2 = L2_2 + 50
  L3_2 = A0_2[2]
  L3_2 = L3_2 - 65
  L4_2 = A0_2[2]
  L4_2 = L4_2 + 65
  L5_2 = color
  L5_2 = L5_2.map
  L5_2 = L5_2.me
  L6_2 = {}
  L7_2 = L5_2[1]
  L8_2 = L5_2[2]
  L9_2 = L5_2[3]
  L10_2 = L1_2
  L11_2 = L3_2
  L12_2 = L2_2
  L13_2 = L4_2
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L6_2[4] = L10_2
  L6_2[5] = L11_2
  L6_2[6] = L12_2
  L6_2[7] = L13_2
  L7_2 = findFeat
  L8_2 = L6_2
  L7_2, L8_2 = L7_2(L8_2)
  if 0 < L7_2 then
    L9_2 = findFeat
    L10_2 = L6_2
    L9_2, L10_2 = L9_2(L10_2)
    L8_2 = L10_2
    L7_2 = L9_2
    L9_2 = _Sleep
    L10_2 = 250
    L11_2 = 350
    L9_2(L10_2, L11_2)
    L9_2 = findFeat
    L10_2 = L6_2
    L9_2, L10_2 = L9_2(L10_2)
    L3_2 = L10_2
    L1_2 = L9_2
    if L7_2 == L1_2 and L8_2 == L3_2 then
      L9_2 = true
      return L9_2
    end
    L9_2 = false
    return L9_2
  else
    L9_2 = false
    return L9_2
  end
end

isArrived = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L2_2 = A0_2[1]
  L2_2 = L2_2 - 65
  L3_2 = A0_2[1]
  L3_2 = L3_2 + 50
  L4_2 = A0_2[2]
  L4_2 = L4_2 - 125
  L5_2 = A0_2[2]
  L5_2 = L5_2 + 30
  L6_2 = color
  L6_2 = L6_2.map
  L6_2 = L6_2.mark
  L7_2 = {}
  L8_2 = L6_2[1]
  L9_2 = L6_2[2]
  L10_2 = L6_2[3]
  L11_2 = L2_2
  L12_2 = L4_2
  L13_2 = L3_2
  L14_2 = L5_2
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L7_2[5] = L12_2
  L7_2[6] = L13_2
  L7_2[7] = L14_2
  L8_2 = findFeat
  L9_2 = L7_2
  L8_2, L9_2 = L8_2(L9_2)
  L10_2 = math
  L10_2 = L10_2.random
  L11_2 = L2_2
  L12_2 = L3_2
  L10_2 = L10_2(L11_2, L12_2)
  L11_2 = math
  L11_2 = L11_2.random
  L12_2 = L4_2
  L13_2 = L5_2
  L11_2 = L11_2(L12_2, L13_2)
  if 960 < L10_2 then
    L12_2 = L10_2 - 100
    x6 = L12_2
  else
    L12_2 = L10_2 + 100
    x6 = L12_2
  end
  if 540 < L11_2 then
    L12_2 = L11_2 - 100
    y6 = L12_2
  else
    L12_2 = L11_2 + 150
    y6 = L12_2
  end
  if 0 < L8_2 then
    L12_2 = 0
    while true do
      L13_2 = _ENV["UI_卡小地图"]
      if L13_2 then
        L13_2 = _ENV["moveJudge押镖"]
        L14_2 = 150
        L13_2 = L13_2(L14_2)
        if L13_2 == "stop" then
          L12_2 = L12_2 + 1
        end
      else
        L13_2 = _ENV["moveJudge快速"]
        L14_2 = 150
        L13_2 = L13_2(L14_2)
        if L13_2 then
          L12_2 = L12_2 + 1
        end
      end
      if 10 < L12_2 then
        L12_2 = 0
        L13_2 = isArrived
        L14_2 = A0_2
        L13_2 = L13_2(L14_2)
        if L13_2 == false then
          L13_2 = _find
          L14_2 = Color
          L14_2 = L14_2["百级师门"]
          L14_2 = L14_2["地图小葫芦"]
          L13_2 = L13_2(L14_2)
          if L13_2 then
            L13_2 = randomTap
            L14_2 = x6
            L15_2 = y6
            L16_2 = 5
            L13_2(L14_2, L15_2, L16_2)
            L13_2 = false
            return L13_2
        end
        else
          L13_2 = false
          return L13_2
        end
      end
      L13_2 = isArrived
      L14_2 = A0_2
      L13_2 = L13_2(L14_2)
      if L13_2 then
        L13_2 = true
        return L13_2
      end
    end
  else
    L12_2 = false
    return L12_2
  end
end

isMarked = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L2_2 = posConvert
  L3_2 = A0_2
  L4_2 = A1_2
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = {}
  L4_2 = L2_2[1]
  L5_2 = constMapSize
  L5_2 = L5_2[A1_2]
  L5_2 = L5_2[3]
  L5_2 = L5_2[1]
  L4_2 = L4_2 + L5_2
  L5_2 = constMapSize
  L5_2 = L5_2[A1_2]
  L5_2 = L5_2[3]
  L5_2 = L5_2[2]
  L6_2 = L2_2[2]
  L5_2 = L5_2 - L6_2
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L4_2 = removeMapBarrier
  L5_2 = A1_2
  L4_2 = L4_2(L5_2)
  if L4_2 then
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 1
    L6_2 = 3
    L4_2 = L4_2(L5_2, L6_2)
    if L4_2 == 3 then
      L5_2 = singleTap
      L6_2 = L3_2
      L7_2 = 15
      L5_2(L6_2, L7_2)
    end
    L5_2 = mSleep
    L6_2 = math
    L6_2 = L6_2.random
    L7_2 = 300
    L8_2 = 400
    L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2, L8_2)
    L5_2(L6_2, L7_2, L8_2, L9_2)
    L5_2 = singleTap
    L6_2 = L3_2
    L7_2 = 5
    return L5_2(L6_2, L7_2)
  else
    L4_2 = false
    return L4_2
  end
end

clickMap = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = nil
  L3_2 = nil
  L4_2 = getCurPos
  L4_2 = L4_2()
  L5_2 = L4_2[1]
  if L5_2 <= 24 then
    L5_2 = L4_2[1]
    L5_2 = 24 - L5_2
    L5_2 = L5_2 * 40
    L2_2 = 950 - L5_2
  else
    L5_2 = constMapSize
    L5_2 = L5_2[A1_2]
    L5_2 = L5_2[1]
    L5_2 = L5_2[1]
    L6_2 = L4_2[1]
    L5_2 = L5_2 - L6_2
    if L5_2 <= 24 then
      L5_2 = constMapSize
      L5_2 = L5_2[A1_2]
      L5_2 = L5_2[1]
      L5_2 = L5_2[1]
      L5_2 = 24 - L5_2
      L6_2 = L4_2[1]
      L5_2 = L5_2 + L6_2
      L5_2 = L5_2 * 40
      L2_2 = 950 + L5_2
    else
      L2_2 = 950
    end
  end
  L5_2 = L4_2[2]
  if L5_2 <= 13 then
    L5_2 = L4_2[2]
    L5_2 = 13 - L5_2
    L5_2 = L5_2 * 30
    L3_2 = 545 + L5_2
  else
    L5_2 = constMapSize
    L5_2 = L5_2[A1_2]
    L5_2 = L5_2[1]
    L5_2 = L5_2[2]
    L6_2 = L4_2[2]
    L5_2 = L5_2 - L6_2
    if L5_2 <= 13 then
      L5_2 = constMapSize
      L5_2 = L5_2[A1_2]
      L5_2 = L5_2[1]
      L5_2 = L5_2[2]
      L5_2 = 13 - L5_2
      L6_2 = L4_2[2]
      L5_2 = L5_2 + L6_2
      L5_2 = L5_2 * 40
      L3_2 = 545 - L5_2
    else
      L3_2 = 545
    end
  end
  L5_2 = {}
  L6_2 = A0_2[1]
  L6_2 = L6_2 * 40
  L6_2 = L2_2 + L6_2
  L7_2 = A0_2[2]
  L7_2 = L7_2 * 40
  L7_2 = L3_2 - L7_2
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L6_2 = singleTap
  L7_2 = L5_2
  L6_2(L7_2)
end

move = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  while true do
    L2_2 = getCurMap
    L2_2 = L2_2()
    if L2_2 == A1_2 then
      L3_2 = posMonitor
      L3_2 = L3_2()
      L4_2 = L3_2[1]
      if 0 < L4_2 then
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = L3_2[1]
        L6_2 = A0_2[1]
        L5_2 = L5_2 - L6_2
        L4_2 = L4_2(L5_2)
        if L4_2 <= 15 then
          L4_2 = math
          L4_2 = L4_2.abs
          L5_2 = L3_2[2]
          L6_2 = A0_2[2]
          L5_2 = L5_2 - L6_2
          L4_2 = L4_2(L5_2)
          if L4_2 <= 8 then
        end
        else
          L4_2 = dodgeSystem
          L4_2()
          L4_2 = openMap
          L5_2 = A1_2
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 350
          L7_2 = 450
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          L4_2 = clickMap
          L5_2 = A0_2
          L6_2 = A1_2
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 350
          L7_2 = 450
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          L4_2 = closeAllWnd
          L4_2()
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 350
          L7_2 = 450
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        end
        L4_2 = 1
        L5_2 = 3
        L6_2 = 1
        for L7_2 = L4_2, L5_2, L6_2 do
          L8_2 = posMonitor
          L8_2 = L8_2()
          L3_2 = L8_2
          L8_2 = math
          L8_2 = L8_2.abs
          L9_2 = L3_2[1]
          L10_2 = A0_2[1]
          L9_2 = L9_2 - L10_2
          L8_2 = L8_2(L9_2)
          if L8_2 <= 4 then
            L8_2 = math
            L8_2 = L8_2.abs
            L9_2 = L3_2[2]
            L10_2 = A0_2[2]
            L9_2 = L9_2 - L10_2
            L8_2 = L8_2(L9_2)
            if L8_2 <= 3 then
              L8_2 = math
              L8_2 = L8_2.random
              L9_2 = 1
              L10_2 = 10
              L8_2 = L8_2(L9_2, L10_2)
              if L2_2 == "CAC" and 5 < L8_2 then
                L9_2 = _ENV["跑商屏蔽"]
                L9_2()
              end
              L9_2 = fightSystem
              L9_2()
              L9_2 = true
              return L9_2
          end
          else
            L8_2 = math
            L8_2 = L8_2.abs
            L9_2 = L3_2[1]
            L10_2 = A0_2[1]
            L9_2 = L9_2 - L10_2
            L8_2 = L8_2(L9_2)
            if L8_2 <= 15 then
              L8_2 = math
              L8_2 = L8_2.abs
              L9_2 = L3_2[2]
              L10_2 = A0_2[2]
              L9_2 = L9_2 - L10_2
              L8_2 = L8_2(L9_2)
              if L8_2 <= 8 then
                L8_2 = math
                L8_2 = L8_2.random
                L9_2 = 1
                L10_2 = 10
                L8_2 = L8_2(L9_2, L10_2)
                if L2_2 == "CAC" and 5 < L8_2 then
                  L9_2 = _ENV["跑商屏蔽"]
                  L9_2()
                end
                L9_2 = dodgeSystem
                L9_2()
                L9_2 = fightSystem
                L9_2()
                L9_2 = math
                L9_2 = L9_2.random
                L10_2 = -2
                L11_2 = 2
                L9_2 = L9_2(L10_2, L11_2)
                L10_2 = A0_2[1]
                L11_2 = L3_2[1]
                L10_2 = L10_2 - L11_2
                L10_2 = L10_2 + L9_2
                L11_2 = A0_2[2]
                L12_2 = L3_2[2]
                L11_2 = L11_2 - L12_2
                L11_2 = L11_2 + L9_2
                L12_2 = {}
                L13_2 = L10_2
                L14_2 = L11_2
                L12_2[1] = L13_2
                L12_2[2] = L14_2
                L13_2 = move
                L14_2 = L12_2
                L15_2 = A1_2
                L13_2(L14_2, L15_2)
                L13_2 = mSleep
                L14_2 = math
                L14_2 = L14_2.random
                L15_2 = 350
                L16_2 = 450
                L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2, L16_2)
                L13_2(L14_2, L15_2, L16_2, L17_2)
              end
            end
          end
        end
      else
        L4_2 = posConvert
        L5_2 = A0_2
        L6_2 = A1_2
        L4_2 = L4_2(L5_2, L6_2)
        L5_2 = 0
        while true do
          L6_2 = dodgeSystem
          L6_2()
          if L5_2 == 0 or 10 < L5_2 then
            L5_2 = 1
            L6_2 = openMap
            L7_2 = A1_2
            L6_2 = L6_2(L7_2)
            if L6_2 then
              L6_2 = clickMap
              L7_2 = A0_2
              L8_2 = A1_2
              L6_2 = L6_2(L7_2, L8_2)
              L4_2 = L6_2
              L6_2 = _print
              L7_2 = "way to"
              L6_2(L7_2)
              if L4_2 == false then
                L6_2 = false
                return L6_2
              end
            else
              L6_2 = _print
              L7_2 = "open map s"
              L6_2(L7_2)
              L6_2 = false
              return L6_2
            end
          end
          L6_2 = _ENV["moveJudge快速"]
          L7_2 = 150
          L6_2 = L6_2(L7_2)
          if L6_2 then
            L5_2 = L5_2 + 1
          end
          L6_2 = isArrived
          L7_2 = L4_2
          L6_2 = L6_2(L7_2)
          if L6_2 then
            L6_2 = closeAllWnd
            L6_2()
            L6_2 = _print
            L7_2 = "get to"
            L6_2(L7_2)
            L6_2 = math
            L6_2 = L6_2.random
            L7_2 = 1
            L8_2 = 15
            L6_2 = L6_2(L7_2, L8_2)
            if 14 < L6_2 then
              L7_2 = _ENV["押镖屏蔽"]
              L7_2()
            end
            L7_2 = true
            return L7_2
          end
        end
      end
    else
      L3_2 = _print
      L4_2 = "goto map"
      L3_2(L4_2)
      L3_2 = goToMap
      L4_2 = A1_2
      L5_2 = travelMod
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 == false then
        L3_2 = false
        return L3_2
      end
    end
  end
end

goTo = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  if A0_2 == "ALG" then
    L2_2 = goToALG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "LHS" then
    L2_2 = goToLHS
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CSZ" then
    L2_2 = goToCSZ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "NBM" then
    L2_2 = goToNBM
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DHYD" then
    L2_2 = goToDHYD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "BJLZ" then
    L2_2 = goToBJLZ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CAC" then
    L2_2 = goToCAC
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CFBJ" then
    L2_2 = goToCJBJ
    return L2_2()
  elseif A0_2 == "CJG" then
    L2_2 = goToCJG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CSC" then
    L2_2 = goToCSC
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CSJW" then
    L2_2 = goToCSJW
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CYD" then
    L2_2 = goToCYD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "CYJF" then
    L2_2 = goToCYJF
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DF" then
    L2_2 = goToDF
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DHW" then
    L2_2 = goToDHW
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DTGF" then
    L2_2 = goToDTGF
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DTGJ" then
    L2_2 = goToDTGJ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DTJW" then
    L2_2 = goToDTJW
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DXD" then
    L2_2 = goToDXD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "DZWF" then
    L2_2 = goToDZWF
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "FCS" then
    L2_2 = goToFCS
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "HGS" then
    L2_2 = goToHGS
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "HSS" then
    L2_2 = goToHSS
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "LDD" then
    L2_2 = goToLDD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "JNYW" then
    L2_2 = goToJNYW
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "JYC" then
    L2_2 = goToJYC
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "LG" then
    L2_2 = goToLG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "LTG" then
    L2_2 = goToLTG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "LXBD" then
    L2_2 = goToLXBD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "MJC" then
    L2_2 = goToMJC
    return L2_2()
  elseif A0_2 == "MWJ" then
    L2_2 = goToMWJ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "MWZ" then
    L2_2 = goToMWZ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "NEC" then
    L2_2 = goToNEC
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "NECCZJ" then
    L2_2 = goToNECCJZ
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "PSD" then
    L2_2 = goToPSD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "PSL" then
    L2_2 = goToPSL
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "PTS" then
    L2_2 = goToPTS
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "QKD" then
    L2_2 = goToQKD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "QLS" then
    L2_2 = goToQLS
    return L2_2()
  elseif A0_2 == "QQF" then
    L2_2 = goToQQF
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "SJG" then
    L2_2 = goToSJG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "SLD" then
    L2_2 = goToSLD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "STL" then
    L2_2 = goToSTL
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "SWD" then
    L2_2 = goToSWD
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "TG" then
    L2_2 = goToTG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "WZG" then
    L2_2 = goToWZG
    L3_2 = A1_2
    return L2_2(L3_2)
  elseif A0_2 == "ZZG" then
    L2_2 = goToZZG
    return L2_2()
  end
end

goToMap = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往傲来国"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "ALG" then
        L2_2 = closeScrnLog
        L3_2 = "前往傲来国"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "DHW" or L1_2 == "JYC" or L1_2 == "JNYW" or L1_2 == "CAC" or L1_2 == "DF" or L1_2 == "DTGJ" or L1_2 == "DHYD" or L1_2 == "NBM" or L1_2 == "CSZ" then
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.boatMan
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = tapBoatMan
          L5_2 = "ALG"
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DHWtoALG
          L6_2 = "DHW"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      elseif L1_2 == "HGS" or L1_2 == "BJLZ" or L1_2 == "CSJW" or L1_2 == "CSC" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.HGStoALG
        L4_2 = "HGS"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = math
          L2_2 = L2_2.random
          L3_2 = 2
          L4_2 = 6
          L2_2 = L2_2(L3_2, L4_2)
          L3_2 = 1
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["贼王"]
          L5_2 = L5_2["传送门"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = math
            L4_2 = L4_2.random
            L5_2 = 1
            L6_2 = 3
            L4_2 = L4_2(L5_2, L6_2)
            L3_2 = L4_2
          end
          if 1 < L3_2 then
            L4_2 = _find
            L5_2 = Color
            L5_2 = L5_2["贼王"]
            L5_2 = L5_2["点传送门"]
            L4_2(L5_2)
            L4_2 = _Sleep
            L5_2 = 200
            L6_2 = 300
            L4_2(L5_2, L6_2)
          else
            L4_2 = _cmp
            L5_2 = Color
            L5_2 = L5_2["屏蔽"]
            L5_2 = L5_2["取消"]
            L4_2 = L4_2(L5_2)
            if L4_2 then
              L4_2 = mSleep
              L5_2 = 200
              L4_2(L5_2)
            end
            if 4 < L2_2 then
              L4_2 = {}
              L5_2 = 57
              L6_2 = 467
              L4_2[1] = L5_2
              L4_2[2] = L6_2
              L5_2 = singleTap
              L6_2 = L4_2
              L7_2 = 1
              L5_2(L6_2, L7_2)
            else
              L4_2 = {}
              L5_2 = 18
              L6_2 = 933
              L4_2[1] = L5_2
              L4_2[2] = L6_2
              L5_2 = singleTap
              L6_2 = L4_2
              L7_2 = 1
              L5_2(L6_2, L7_2)
            end
          end
          L4_2 = 1
          L5_2 = 60
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = mSleep
            L9_2 = math
            L9_2 = L9_2.random
            L10_2 = 90
            L11_2 = 120
            L9_2, L10_2, L11_2, L12_2 = L9_2(L10_2, L11_2)
            L8_2(L9_2, L10_2, L11_2, L12_2)
            L8_2 = getCurMap
            L8_2 = L8_2()
            if L8_2 == "ALG" then
              L8_2 = closeScrnLog
              L9_2 = "前往傲来国"
              L8_2(L9_2)
              L8_2 = true
              return L8_2
            end
          end
        else
          L2_2 = false
          return L2_2
        end
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = useFlyRuneTo
    L2_2 = constPos
    L2_2 = L2_2.runeALGpoint
    L1_2(L2_2)
  end
end

goToALG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往北俱芦洲"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "BJLZ" then
        L2_2 = closeScrnLog
        L3_2 = "前往北俱芦洲"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "HGS" or L1_2 == "ALG" or L1_2 == "DHW" or L1_2 == "JYC" or L1_2 == "JNYW" or L1_2 == "CAC" or L1_2 == "DTGJ" or L1_2 == "DF" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.HGStoBJLZ
        L4_2 = "HGS"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = tapPostUncle
          L3_2 = "BJLZ"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "CSJW" or L1_2 == "CSC" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CSJWtoBJLZ
        L4_2 = "CSJW"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = mSleep
          L3_2 = 200
          L2_2(L3_2)
          L2_2 = tapPostMan
          L3_2 = "BJLZ"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "DHYD" or L1_2 == "NBM" or L1_2 == "CSZ" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.HGStoBJLZ
        L4_2 = "HGS"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = tapPostUncle
          L3_2 = "BJLZ"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      end
    end
  elseif A0_2 == "fly" then
    repeat
      L1_2 = goTo
      L2_2 = constPos
      L2_2 = L2_2.CSJWtoBJLZ
      L3_2 = "CSJW"
      L1_2(L2_2, L3_2)
      L1_2 = tapPostMan
      L2_2 = "BJLZ"
      L1_2(L2_2)
      L1_2 = mSleep
      L2_2 = 2000
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
    until L1_2 == "BJLZ"
  end
end

goToBJLZ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" or A0_2 == "fly" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往帮派"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "BP" then
        L2_2 = closeScrnLog
        L3_2 = "前往帮派"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "WMC" then
        L2_2 = closeScrnLog
        L3_2 = "前往帮派"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "DF" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.DFtoLHS
        L4_2 = "DF"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = {}
          L3_2 = 933
          L4_2 = 309
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L3_2 = singleTap
          L4_2 = L2_2
          L5_2 = 15
          L3_2(L4_2, L5_2)
          L3_2 = 1
          L4_2 = 40
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 100
            L10_2 = 150
            L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2)
            L7_2 = getCurMap
            L7_2 = L7_2()
            if L7_2 == "LHS" then
              L7_2 = closeScrnLog
              L8_2 = "前往轮回司"
              L7_2(L8_2)
              L7_2 = true
              return L7_2
            end
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "LHS" then
        L2_2 = tapPostGirl
        L3_2 = "CAC"
        L2_2 = L2_2(L3_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
      else
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoBP
        L4_2 = "CAC"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = tapGangster
          L3_2 = "BP"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      end
    end
  end
end

goToBP = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往长安城"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CAC" then
        L2_2 = closeScrnLog
        L3_2 = "前往长安城"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = getCurMap
        L2_2 = L2_2()
        if L2_2 == "CFBJ" then
          L2_2 = {}
          L3_2 = 910
          L4_2 = 816
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L3_2 = singleTap
          L4_2 = L2_2
          L5_2 = 30
          L3_2(L4_2, L5_2)
          L3_2 = mSleep
          L4_2 = math
          L4_2 = L4_2.random
          L5_2 = 1500
          L6_2 = 2000
          L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2 = L4_2(L5_2, L6_2)
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
        elseif L1_2 == "DTGJ" or L1_2 == "DF" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DTGJtoCAC
          L4_2 = "DTGJ"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = findFeat
            L3_2 = color
            L3_2 = L3_2.npcPos
            L3_2 = L3_2.postMan
            L2_2, L3_2 = L2_2(L3_2)
            if 0 < L2_2 then
              L4_2 = tapPostMan
              L5_2 = "CAC"
              L4_2 = L4_2(L5_2)
              if L4_2 == false then
                L4_2 = false
                return L4_2
              end
            end
          else
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "DFFFF" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DFtoLHS
          L4_2 = "DF"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = {}
            L3_2 = 933
            L4_2 = 309
            L2_2[1] = L3_2
            L2_2[2] = L4_2
            L3_2 = singleTap
            L4_2 = L2_2
            L5_2 = 15
            L3_2(L4_2, L5_2)
            L3_2 = 1
            L4_2 = 40
            L5_2 = 1
            for L6_2 = L3_2, L4_2, L5_2 do
              L7_2 = mSleep
              L8_2 = math
              L8_2 = L8_2.random
              L9_2 = 100
              L10_2 = 150
              L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
              L7_2(L8_2, L9_2, L10_2, L11_2)
              L7_2 = getCurMap
              L7_2 = L7_2()
              if L7_2 == "LHS" then
                L7_2 = tapPostGirl
                L8_2 = "CAC"
                L7_2 = L7_2(L8_2)
                if L7_2 == false then
                  L7_2 = false
                  return L7_2
                end
                break
              end
            end
            L3_2 = false
            return L3_2
          else
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "LHS" then
          L2_2 = tapPostGirl
          L3_2 = "CAC"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "DTJW" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DTJWtoCAC
          L4_2 = "DTJW"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = mSleep
            L3_2 = 200
            L2_2(L3_2)
            L2_2 = tapPostMan
            L3_2 = "CAC"
            L2_2 = L2_2(L3_2)
            if L2_2 == false then
              L2_2 = false
              return L2_2
            end
          else
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "BJLZ" or L1_2 == "HGS" or L1_2 == "CSJW" or L1_2 == "CSC" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.BJLZtoCAC
          L4_2 = "BJLZ"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = tapPostMan
            L3_2 = "CAC"
            L2_2 = L2_2(L3_2)
            if L2_2 == false then
              L2_2 = false
              return L2_2
            end
          else
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "ALG" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.ALGtoCAC
          L4_2 = "ALG"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = findFeat
            L3_2 = color
            L3_2 = L3_2.npcPos
            L3_2 = L3_2.postMan
            L2_2, L3_2 = L2_2(L3_2)
            if 0 < L2_2 then
              L4_2 = tapPostMan
              L5_2 = "CAC"
              L4_2 = L4_2(L5_2)
              if L4_2 == false then
                L4_2 = false
                return L4_2
              end
            end
          else
            L2_2 = false
            return L2_2
          end
        elseif L1_2 == "DTGF" or L1_2 == "CJG" or L1_2 == "CYJF" or L1_2 == "HSS" or L1_2 == "QQF" then
          L2_2 = false
          return L2_2
        elseif L1_2 == "JNYW" or L1_2 == "JYC" or L1_2 == "DHW" or L1_2 == "DHYD" or L1_2 == "NBM" or L1_2 == "CSZ" then
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.ALGtoCAC
          L4_2 = "ALG"
          L2_2(L3_2, L4_2)
        elseif L1_2 == "SXZ" then
          L2_2 = tap
          L3_2 = 181
          L4_2 = 925
          L2_2(L3_2, L4_2)
          L2_2 = mSleep
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 1200
          L5_2 = 1400
          L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2 = L3_2(L4_2, L5_2)
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
        end
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = useFlyRuneTo
    L2_2 = constPos
    L2_2 = L2_2.runeCACpoint
    L1_2(L2_2)
  end
end

goToCAC = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "fly" then
    L1_2 = {}
    L2_2 = math
    L2_2 = L2_2.random
    L3_2 = 10
    L4_2 = 12
    L2_2 = L2_2(L3_2, L4_2)
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 6
    L5_2 = 8
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L3_2(L4_2, L5_2)
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    L1_2[3] = L4_2
    L1_2[4] = L5_2
    L1_2[5] = L6_2
    L1_2[6] = L7_2
    L1_2[7] = L8_2
    L1_2[8] = L9_2
    L1_2[9] = L10_2
    L1_2[10] = L11_2
    L1_2[11] = L12_2
    L1_2[12] = L13_2
    L1_2[13] = L14_2
    L1_2[14] = L15_2
    while true do
      L2_2 = scrnLog
      L3_2 = "前往长风镖局"
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
      if L2_2 == "CFBJ" then
        L3_2 = closeScrnLog
        L4_2 = "前往长风镖局"
        L3_2(L4_2)
        L3_2 = true
        return L3_2
      elseif L2_2 == "" then
        L3_2 = fightSystem
        L3_2()
      else
        L3_2 = findFeat
        L4_2 = color
        L4_2 = L4_2.doorPos
        L4_2 = L4_2.CFBJ
        L3_2, L4_2 = L3_2(L4_2)
        if 0 < L3_2 then
          L5_2 = {}
          L6_2 = 1744
          L7_2 = 532
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L6_2 = singleTap
          L7_2 = L5_2
          L8_2 = 15
          L6_2(L7_2, L8_2)
          L6_2 = 1
          L7_2 = 7
          L8_2 = 1
          for L9_2 = L6_2, L7_2, L8_2 do
            L10_2 = mSleep
            L11_2 = math
            L11_2 = L11_2.random
            L12_2 = 400
            L13_2 = 550
            L11_2, L12_2, L13_2, L14_2, L15_2 = L11_2(L12_2, L13_2)
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
            L10_2 = getCurMap
            L10_2 = L10_2()
            if L10_2 == "CFBJ" then
              L10_2 = closeScrnLog
              L11_2 = "前往长风镖局"
              L10_2(L11_2)
              L10_2 = true
              return L10_2
            end
          end
        elseif L2_2 == "CAC" then
          L5_2 = _ENV["_返回"]
          L5_2 = L5_2["坐标"]
          L5_2, L6_2 = L5_2()
          L7_2 = 532 - L5_2
          L8_2 = 152 - L6_2
          L9_2 = math
          L9_2 = L9_2.abs
          L10_2 = L7_2
          L9_2 = L9_2(L10_2)
          if L9_2 <= 5 then
            L9_2 = math
            L9_2 = L9_2.abs
            L10_2 = L8_2
            L9_2 = L9_2(L10_2)
            if L9_2 <= 5 then
              L9_2 = _ENV["_功能"]
              L9_2 = L9_2["屏蔽"]
              L10_2 = 4
              L9_2(L10_2)
              L9_2 = _ENV["_计算"]
              L9_2 = L9_2["取目标屏幕坐标"]
              L10_2 = "长安城"
              L11_2 = L5_2
              L12_2 = L6_2
              L13_2 = 532
              L14_2 = 152
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              L11_2 = tap
              L12_2 = L9_2
              L13_2 = L10_2
              L11_2(L12_2, L13_2)
              L11_2 = _ENV["_随机延时"]
              L12_2 = 1111
              L11_2(L12_2)
              if L2_2 == "CFBJ" then
                L11_2 = closeScrnLog
                L12_2 = "前往长风镖局"
                L11_2(L12_2)
                L11_2 = true
                return L11_2
              end
          end
          else
            L9_2 = _ENV["_前往"]
            L9_2 = L9_2["固定坐标"]
            L10_2 = "长安城"
            L11_2 = 1607
            L12_2 = 503
            L9_2(L10_2, L11_2, L12_2)
          end
        else
          L5_2 = _ENV["_使用"]
          L5_2 = L5_2["旗子"]
          L6_2 = "长风镖局"
          L7_2 = "长安_镖局d"
          L5_2(L6_2, L7_2)
        end
      end
    end
  end
end

goToCFBJ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往轮回司"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "LHS" then
        L2_2 = closeScrnLog
        L3_2 = "前往轮回司"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.LHS
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = 1
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["贼王"]
          L6_2 = L6_2["传送门"]
          L5_2 = L5_2(L6_2)
          if L5_2 then
            L5_2 = math
            L5_2 = L5_2.random
            L6_2 = 1
            L7_2 = 3
            L5_2 = L5_2(L6_2, L7_2)
            L4_2 = L5_2
          end
          if 1 < L4_2 then
            L5_2 = _find
            L6_2 = Color
            L6_2 = L6_2["贼王"]
            L6_2 = L6_2["点传送门"]
            L5_2(L6_2)
            L5_2 = _Sleep
            L6_2 = 200
            L7_2 = 300
            L5_2(L6_2, L7_2)
          else
            L5_2 = {}
            L6_2 = 933
            L7_2 = 309
            L5_2[1] = L6_2
            L5_2[2] = L7_2
            L6_2 = singleTap
            L7_2 = L5_2
            L8_2 = 15
            L6_2(L7_2, L8_2)
          end
          L5_2 = 1
          L6_2 = 40
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 100
            L12_2 = 150
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "LHS" then
              L9_2 = closeScrnLog
              L10_2 = "前往轮回司"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DFtoLHS
          L6_2 = "DF"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToLHS = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = dialog
    L1_2 = "daozhele2"
    L2_2 = time
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["固定坐标"]
    L1_2 = "长安城"
    L2_2 = 1607
    L3_2 = 503
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["进入光圈"]
    L1_2 = 1514
    L2_2 = 254
    L0_2(L1_2, L2_2)
    L0_2 = curMap
    if L0_2 == "CFBJ" then
      L0_2 = closeScrnLog
      L1_2 = "前往长风镖局"
      L0_2(L1_2)
      break
    end
  end
end

_ENV["走进长风镖局"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往藏经阁"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CJG" then
        L2_2 = closeScrnLog
        L3_2 = "前往藏经阁"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.CJG
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.CJG1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = {}
          L7_2 = 1310
          L8_2 = 151
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L7_2 = singleTap
          L8_2 = L6_2
          L9_2 = 15
          L7_2(L8_2, L9_2)
          L7_2 = 1
          L8_2 = 5
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 400
            L14_2 = 550
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "CJG" then
              L11_2 = closeScrnLog
              L12_2 = "前往藏经阁"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.HSStoCJG
          L8_2 = "HSS"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      end
    end
  end
end

goToCJG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L1_2 = combo2
  if L1_2 == "蓝色合成旗" then
    L1_2 = color
    L1_2 = L1_2.flag
    L1_2 = L1_2.blue
    _ENV["旗子颜色"] = L1_2
  else
    L1_2 = combo2
    if L1_2 == "红色合成旗" then
      L1_2 = color
      L1_2 = L1_2.flag
      L1_2 = L1_2.red
      _ENV["旗子颜色"] = L1_2
    else
      L1_2 = combo2
      if L1_2 == "黄色合成旗" then
        L1_2 = color
        L1_2 = L1_2.flag
        L1_2 = L1_2.yellow
        _ENV["旗子颜色"] = L1_2
      else
        L1_2 = combo2
        if L1_2 == "绿色合成旗" then
          L1_2 = color
          L1_2 = L1_2.flag
          L1_2 = L1_2.green
          _ENV["旗子颜色"] = L1_2
        else
          L1_2 = combo2
          if L1_2 == "白色合成旗" then
            L1_2 = color
            L1_2 = L1_2.flag
            L1_2 = L1_2.white
            _ENV["旗子颜色"] = L1_2
          end
        end
      end
    end
  end
  if A0_2 == "fly" then
    L1_2 = {}
    L2_2 = math
    L2_2 = L2_2.random
    L3_2 = 10
    L4_2 = 12
    L2_2 = L2_2(L3_2, L4_2)
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 6
    L5_2 = 8
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2 = L3_2(L4_2, L5_2)
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    L1_2[3] = L4_2
    L1_2[4] = L5_2
    L1_2[5] = L6_2
    L1_2[6] = L7_2
    L1_2[7] = L8_2
    L1_2[8] = L9_2
    L1_2[9] = L10_2
    L1_2[10] = L11_2
    L1_2[11] = L12_2
    L1_2[12] = L13_2
    L1_2[13] = L14_2
    while true do
      L2_2 = scrnLog
      L3_2 = "前往长风镖局"
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
      if L2_2 == "CFBJ" then
        L3_2 = closeScrnLog
        L4_2 = "前往长风镖局"
        L3_2(L4_2)
        L3_2 = true
        return L3_2
      elseif L2_2 == "" then
        L3_2 = fightSystem
        L3_2()
      else
        L3_2 = findFeat
        L4_2 = color
        L4_2 = L4_2.doorPos
        L4_2 = L4_2.CFBJ
        L3_2, L4_2 = L3_2(L4_2)
        if 0 < L3_2 then
          L5_2 = {}
          L6_2 = 1744
          L7_2 = 532
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L6_2 = singleTap
          L7_2 = L5_2
          L8_2 = 15
          L6_2(L7_2, L8_2)
          L6_2 = 1
          L7_2 = 7
          L8_2 = 1
          for L9_2 = L6_2, L7_2, L8_2 do
            L10_2 = mSleep
            L11_2 = math
            L11_2 = L11_2.random
            L12_2 = 400
            L13_2 = 550
            L11_2, L12_2, L13_2, L14_2 = L11_2(L12_2, L13_2)
            L10_2(L11_2, L12_2, L13_2, L14_2)
            L10_2 = getCurMap
            L10_2 = L10_2()
            if L10_2 == "CFBJ" then
              L10_2 = closeScrnLog
              L11_2 = "前往长风镖局"
              L10_2(L11_2)
              L10_2 = true
              return L10_2
            end
          end
        elseif L2_2 == "CAC" then
          L5_2 = getCurPos
          L5_2 = L5_2()
          L6_2 = {}
          L7_2 = 525
          L8_2 = 150
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L7_2 = math
          L7_2 = L7_2.abs
          L8_2 = L5_2[1]
          L9_2 = L6_2[1]
          L8_2 = L8_2 - L9_2
          L7_2 = L7_2(L8_2)
          if L7_2 <= 21 then
            L7_2 = math
            L7_2 = L7_2.abs
            L8_2 = L5_2[2]
            L9_2 = L6_2[2]
            L8_2 = L8_2 - L9_2
            L7_2 = L7_2(L8_2)
            if L7_2 <= 10 then
              L7_2 = L6_2[1]
              L8_2 = L5_2[1]
              L7_2 = L7_2 - L8_2
              L8_2 = L6_2[2]
              L9_2 = L5_2[2]
              L8_2 = L8_2 - L9_2
              L9_2 = {}
              L10_2 = L7_2
              L11_2 = L8_2
              L9_2[1] = L10_2
              L9_2[2] = L11_2
              L10_2 = move
              L11_2 = L9_2
              L12_2 = "CAC"
              L10_2(L11_2, L12_2)
              L10_2 = mSleep
              L11_2 = math
              L11_2 = L11_2.random
              L12_2 = 350
              L13_2 = 450
              L11_2, L12_2, L13_2, L14_2 = L11_2(L12_2, L13_2)
              L10_2(L11_2, L12_2, L13_2, L14_2)
          end
          else
            L7_2 = _ENV["走进长风镖局"]
            return L7_2()
          end
        else
          L5_2 = useFlagTo
          L6_2 = _ENV["旗子颜色"]
          L7_2 = color
          L7_2 = L7_2.flagPoint
          L7_2 = L7_2.escort
          L5_2(L6_2, L7_2)
        end
      end
    end
  end
end

_ENV["goToCFBJ原版"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往长寿村"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CSC" then
        L2_2 = closeScrnLog
        L3_2 = "前往长寿村"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.CSC
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.CSC1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = math
          L6_2 = L6_2.random
          L7_2 = 2
          L8_2 = 6
          L6_2 = L6_2(L7_2, L8_2)
          if 4 < L6_2 then
            L7_2 = {}
            L8_2 = 866
            L9_2 = 43
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 15
            L8_2(L9_2, L10_2)
          else
            L7_2 = {}
            L8_2 = 1218
            L9_2 = 72
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 15
            L8_2(L9_2, L10_2)
          end
          L7_2 = 1
          L8_2 = 45
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "CSC" then
              L11_2 = closeScrnLog
              L12_2 = "前往长寿村"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 50
            L14_2 = 100
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.CSJWtoCSC
          L8_2 = "CSJW"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      end
    end
  end
end

goToCSC = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往长寿郊外"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CSJW" then
        L2_2 = closeScrnLog
        L3_2 = "前往长寿郊外"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "DTJW" or L1_2 == "DTGJ" or L1_2 == "CAC" then
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postUncle
        L2_2 = L2_2(L3_2)
        if not (0 < L2_2) then
          L2_2 = findFeat
          L3_2 = color
          L3_2 = L3_2.npcPos
          L3_2 = L3_2["境外to郊外"]
          L2_2 = L2_2(L3_2)
          if not (0 < L2_2) then
            goto lbl_49
          end
        end
        L2_2 = tapPostUncle
        L3_2 = "CSJW"
        L2_2 = L2_2(L3_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
        goto lbl_124
        ::lbl_49::
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.DTJWtoCSJW
        L4_2 = "DTJW"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 == false then
        end
        L2_2 = false
        return L2_2
      elseif L1_2 == "BJLZ" or L1_2 == "HGS" or L1_2 == "ALG" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.BJLZtoCSJW
        L4_2 = "BJLZ"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = tapPostRabbit
          L3_2 = "CSJW"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "CSC" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CSCtoCSJW
        L4_2 = "CSC"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = {}
          L3_2 = 1710
          L4_2 = 870
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L3_2 = singleTap
          L4_2 = L2_2
          L5_2 = 15
          L3_2(L4_2, L5_2)
          L3_2 = 1
          L4_2 = 43
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = getCurMap
            L7_2 = L7_2()
            if L7_2 == "CSJW" then
              L7_2 = closeScrnLog
              L8_2 = "前往长寿郊外"
              L7_2(L8_2)
              L7_2 = true
              return L7_2
            end
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 50
            L10_2 = 100
            L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2)
          end
        else
          L2_2 = false
          return L2_2
        end
      end
      ::lbl_124::
    end
  elseif A0_2 == "fly" then
    L1_2 = {}
    L2_2 = 1715
    L3_2 = 886
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    repeat
      L2_2 = useFlagTo
      L3_2 = image
      L3_2 = L3_2.greenFlag
      L4_2 = image
      L4_2 = L4_2.flagCSJWpoint
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 300
      L2_2(L3_2)
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 2000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "CSJW"
  end
end

goToCSJW = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往潮音洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CYD" then
        L2_2 = closeScrnLog
        L3_2 = "前往潮音洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.CYD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 164
          L6_2 = 198
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "CYD" then
              L9_2 = closeScrnLog
              L10_2 = "前往潮音洞"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.PTStoCYD
          L6_2 = "PTS"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToCYD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往程咬金府"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CYJF" then
        L2_2 = closeScrnLog
        L3_2 = "前往程咬金府"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.CYJF
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 550
          L6_2 = 276
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "CYJF" then
              L9_2 = closeScrnLog
              L10_2 = "前往程咬金府"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTGFtoCYJF
          L6_2 = "DTGF"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToCYJF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往地府"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DF" then
        L2_2 = closeScrnLog
        L3_2 = "前往地府"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "LHS" then
        L2_2 = tap
        L3_2 = 977
        L4_2 = 890
        L2_2(L3_2, L4_2)
        L2_2 = mSleep
        L3_2 = 3000
        L2_2(L3_2)
      elseif L1_2 == "CAC" or L1_2 == "BJLZ" then
        L2_2 = scrnLog
        L3_2 = "前往大唐国境"
        L2_2(L3_2)
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoYZ
        L4_2 = "CAC"
        L2_2(L3_2, L4_2)
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postMan
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = tapPostMan
          L5_2 = "DTGJ"
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
        L4_2 = closeScrnLog
        L5_2 = "前往大唐国境"
        L4_2(L5_2)
      elseif L1_2 == "DTGJ" then
        L2_2 = _find_tb
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["地府口"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = {}
          L3_2 = 1082
          L4_2 = 110
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L3_2 = singleTap
          L4_2 = L2_2
          L5_2 = 20
          L3_2(L4_2, L5_2)
          L3_2 = 1
          L4_2 = 30
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 80
            L10_2 = 100
            L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2)
            L7_2 = getCurMap
            L7_2 = L7_2()
            if L7_2 == "DF" then
              L7_2 = closeScrnLog
              L8_2 = "前往地府"
              L7_2(L8_2)
              L7_2 = true
              return L7_2
            end
          end
        else
          L2_2 = closeAllDialog
          L2_2()
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DTGJtoDF
          L4_2 = "DTGJ"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      else
        L2_2 = false
        return L2_2
      end
    end
  end
end

goToDF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往东海湾"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DHW" then
        L2_2 = closeScrnLog
        L3_2 = "前往东海湾"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "DF" or L1_2 == "DTGJ" or L1_2 == "CSZ" or L1_2 == "NBM" or L1_2 == "DHYD" then
        L2_2 = _find_tb
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["东海岩洞口"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = 1
          L3_2 = _find
          L4_2 = Color
          L4_2 = L4_2["贼王"]
          L4_2 = L4_2["传送门"]
          L3_2 = L3_2(L4_2)
          if L3_2 then
            L3_2 = math
            L3_2 = L3_2.random
            L4_2 = 1
            L5_2 = 5
            L3_2 = L3_2(L4_2, L5_2)
            L2_2 = L3_2
          end
          if 1 < L2_2 then
            L3_2 = _find
            L4_2 = Color
            L4_2 = L4_2["贼王"]
            L4_2 = L4_2["点传送门"]
            L3_2(L4_2)
            L3_2 = _Sleep
            L4_2 = 200
            L5_2 = 300
            L3_2(L4_2, L5_2)
          else
            L3_2 = {}
            L4_2 = 954
            L5_2 = 905
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 2
            L4_2(L5_2, L6_2)
          end
          L3_2 = _cmp_tb_cx
          L4_2 = Color
          L4_2 = L4_2["地图"]
          L4_2 = L4_2["到达东海湾"]
          L5_2 = {}
          L6_2 = 100
          L7_2 = 40
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L3_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L3_2 = true
            return L3_2
          end
        else
          L2_2 = closeAllDialog
          L2_2()
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DHYDtoDHW
          L4_2 = "DHYD"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.DHW
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.DHW2
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = math
          L6_2 = L6_2.random
          L7_2 = 1
          L8_2 = 2
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == 2 then
            L7_2 = {}
            L8_2 = 1634
            L9_2 = 714
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 15
            L8_2(L9_2, L10_2)
          else
            L7_2 = {}
            L8_2 = 1505
            L9_2 = 555
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 5
            L8_2(L9_2, L10_2)
          end
          L7_2 = 1
          L8_2 = 50
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 50
            L14_2 = 100
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "DHW" then
              L11_2 = closeScrnLog
              L12_2 = "前往东海湾"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.JYCtoDHW
          L8_2 = "JYC"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      end
    end
  elseif A0_2 == "fly" then
    repeat
      L1_2 = useFlagTo
      L2_2 = image
      L2_2 = L2_2.yellowFlag
      L3_2 = image
      L3_2 = L3_2.flagDHWpoint
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = 300
      L1_2(L2_2)
      L1_2 = tapPostMan
      L2_2 = "DHW"
      L1_2(L2_2)
      L1_2 = mSleep
      L2_2 = 2000
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
    until L1_2 == "DHW"
  end
end

goToDHW = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往东海岩洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DHYD" then
        L2_2 = closeScrnLog
        L3_2 = "前往东海岩洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = _find_tb
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["女魃墓口"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = 1
          L3_2 = _find
          L4_2 = Color
          L4_2 = L4_2["贼王"]
          L4_2 = L4_2["传送门"]
          L3_2 = L3_2(L4_2)
          if L3_2 then
            L3_2 = math
            L3_2 = L3_2.random
            L4_2 = 1
            L5_2 = 2
            L3_2 = L3_2(L4_2, L5_2)
            L2_2 = L3_2
          end
          if L2_2 == 2 then
            L3_2 = _find
            L4_2 = Color
            L4_2 = L4_2["贼王"]
            L4_2 = L4_2["点传送门"]
            L3_2(L4_2)
            L3_2 = _Sleep
            L4_2 = 200
            L5_2 = 300
            L3_2(L4_2, L5_2)
          else
            L3_2 = _cmp
            L4_2 = Color
            L4_2 = L4_2["屏蔽"]
            L4_2 = L4_2["取消"]
            L3_2 = L3_2(L4_2)
            if L3_2 then
              L3_2 = mSleep
              L4_2 = 200
              L3_2(L4_2)
            end
            L3_2 = {}
            L4_2 = 196
            L5_2 = 941
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 5
            L4_2(L5_2, L6_2)
          end
          L3_2 = _cmp_tb_cx
          L4_2 = Color
          L4_2 = L4_2["地图"]
          L4_2 = L4_2["到达东海岩洞"]
          L5_2 = {}
          L6_2 = 100
          L7_2 = 40
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L3_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L3_2 = true
            return L3_2
          end
        else
          L2_2 = closeAllDialog
          L2_2()
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.NBMtoDHYD
          L4_2 = "NBM"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      end
    end
  end
end

goToDHYD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往女魃墓"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "NBM" then
        L2_2 = closeScrnLog
        L3_2 = "前往女魃墓"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = _find_tb
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["赤水洲口"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = 1
          L3_2 = _find
          L4_2 = Color
          L4_2 = L4_2["贼王"]
          L4_2 = L4_2["传送门"]
          L3_2 = L3_2(L4_2)
          if L3_2 then
            L3_2 = math
            L3_2 = L3_2.random
            L4_2 = 1
            L5_2 = 2
            L3_2 = L3_2(L4_2, L5_2)
            L2_2 = L3_2
          end
          if L2_2 == 2 then
            L3_2 = _find
            L4_2 = Color
            L4_2 = L4_2["贼王"]
            L4_2 = L4_2["点传送门"]
            L3_2(L4_2)
            L3_2 = _Sleep
            L4_2 = 200
            L5_2 = 300
            L3_2(L4_2, L5_2)
          else
            L3_2 = {}
            L4_2 = 1072
            L5_2 = 171
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 20
            L4_2(L5_2, L6_2)
          end
          L3_2 = _cmp_tb_cx
          L4_2 = Color
          L4_2 = L4_2["地图"]
          L4_2 = L4_2["到达女魃墓"]
          L5_2 = {}
          L6_2 = 100
          L7_2 = 40
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L3_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L3_2 = true
            return L3_2
          end
        else
          L2_2 = closeAllDialog
          L2_2()
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.CSZtoNBM
          L4_2 = "CSZ"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      end
    end
  end
end

goToNBM = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往赤水洲"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "CSZ" then
        L2_2 = closeScrnLog
        L3_2 = "前往赤水洲"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = _find_tb
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["赤水洲接引"]
        L4_2 = {}
        L5_2 = 5
        L6_2 = -50
        L4_2[1] = L5_2
        L4_2[2] = L6_2
        L2_2 = L2_2(L3_2, L4_2)
        if not L2_2 then
          L2_2 = _find_tb
          L3_2 = Color
          L3_2 = L3_2["跑商"]
          L3_2 = L3_2["赤水洲接引2"]
          L4_2 = {}
          L5_2 = 25
          L6_2 = 330
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L2_2 = L2_2(L3_2, L4_2)
          if not L2_2 then
            goto lbl_103
          end
        end
        L2_2 = 1
        L3_2 = 50
        L4_2 = 1
        for L5_2 = L2_2, L3_2, L4_2 do
          L6_2 = _find_tb
          L7_2 = Color
          L7_2 = L7_2["出现重叠"]
          L6_2, L7_2, L8_2 = L6_2(L7_2)
          if L6_2 then
            L9_2 = L7_2 - 100
            L10_2 = L8_2 + 20
            L11_2 = L7_2 - 40
            L12_2 = L8_2 + 540
            L13_2 = _find_tb
            L14_2 = Color
            L14_2 = L14_2["跑商"]
            L14_2 = L14_2["赤水洲接引重叠"]
            L15_2 = {}
            L16_2 = 0
            L17_2 = 0
            L15_2[1] = L16_2
            L15_2[2] = L17_2
            L16_2 = {}
            L17_2 = L9_2
            L18_2 = L10_2
            L19_2 = L11_2
            L20_2 = L12_2
            L16_2[1] = L17_2
            L16_2[2] = L18_2
            L16_2[3] = L19_2
            L16_2[4] = L20_2
            L13_2 = L13_2(L14_2, L15_2, L16_2)
            if L13_2 then
              L13_2 = _Sleep
              L14_2 = 100
              L15_2 = 200
              L13_2(L14_2, L15_2)
            end
          end
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["跑商"]
          L10_2 = L10_2["送我到赤水洲"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _Sleep
            L10_2 = 200
            L11_2 = 300
            L9_2(L10_2, L11_2)
          end
          L9_2 = _cmp_tb
          L10_2 = Color
          L10_2 = L10_2["地图"]
          L10_2 = L10_2["到达赤水洲"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = true
            return L9_2
          end
          L9_2 = mSleep
          L10_2 = 100
          L9_2(L10_2)
        end
        goto lbl_114
        ::lbl_103::
        L2_2 = closeAllDialog
        L2_2()
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.DTGJtoCSZ
        L4_2 = "DTGJ"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
      end
      ::lbl_114::
    end
  end
end

goToCSZ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往大唐官府"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DTGF" then
        L2_2 = closeScrnLog
        L3_2 = "前往大唐官府"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = closeAllDialog
        L2_2()
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoDTGF
        L4_2 = "CAC"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
        L2_2 = {}
        L3_2 = -6
        L4_2 = 7
        L2_2[1] = L3_2
        L2_2[2] = L4_2
        L3_2 = move
        L4_2 = L2_2
        L5_2 = "CAC"
        L3_2(L4_2, L5_2)
        L3_2 = 1
        L4_2 = 7
        L5_2 = 1
        for L6_2 = L3_2, L4_2, L5_2 do
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 400
          L10_2 = 550
          L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2)
          L7_2 = getCurMap
          L7_2 = L7_2()
          if L7_2 == "DTGF" then
            L7_2 = closeScrnLog
            L8_2 = "前往大唐官府"
            L7_2(L8_2)
            L7_2 = true
            return L7_2
          end
        end
      end
    end
  end
end

goToDTGF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往大唐国境"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DTGJ" then
        L2_2 = closeScrnLog
        L3_2 = "前往大唐国境"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "CAC" then
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.DTGJ
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.DTGJ1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = {}
          L7_2 = 50
          L8_2 = 900
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L7_2 = singleTap
          L8_2 = L6_2
          L9_2 = 15
          L7_2(L8_2, L9_2)
          L7_2 = 1
          L8_2 = 25
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 100
            L14_2 = 150
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "DTGJ" then
              L11_2 = closeScrnLog
              L12_2 = "前往大唐国境"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.CACtoDTGJ
          L8_2 = "CAC"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      elseif L1_2 == "DF" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.DFtoDTGJ
        L4_2 = "DF"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = 1
          L3_2 = _find
          L4_2 = Color
          L4_2 = L4_2["贼王"]
          L4_2 = L4_2["传送门"]
          L3_2 = L3_2(L4_2)
          if L3_2 then
            L3_2 = math
            L3_2 = L3_2.random
            L4_2 = 1
            L5_2 = 3
            L3_2 = L3_2(L4_2, L5_2)
            L2_2 = L3_2
          end
          if 1 < L2_2 then
            L3_2 = _find
            L4_2 = Color
            L4_2 = L4_2["贼王"]
            L4_2 = L4_2["点传送门"]
            L3_2(L4_2)
            L3_2 = _Sleep
            L4_2 = 200
            L5_2 = 300
            L3_2(L4_2, L5_2)
          else
            L3_2 = {}
            L4_2 = 1710
            L5_2 = 870
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 15
            L4_2(L5_2, L6_2)
          end
          L3_2 = 1
          L4_2 = 50
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 100
            L10_2 = 120
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
            L7_2 = getCurMap
            L7_2 = L7_2()
            if L7_2 == "DTGJ" then
              L7_2 = closeScrnLog
              L8_2 = "前往大唐国境"
              L7_2(L8_2)
              L7_2 = true
              return L7_2
            end
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "DHYD" or L1_2 == "NBM" or L1_2 == "CSZ" then
        L2_2 = false
        return L2_2
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = {}
    L2_2 = 60
    L3_2 = 960
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    repeat
      L2_2 = useFlagTo
      L3_2 = image
      L3_2 = L3_2.redFlag
      L4_2 = image
      L4_2 = L4_2.flagDTGJpoint
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 300
      L2_2(L3_2)
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 2000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "DTGJ"
  end
end

goToDTGJ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DTJW" then
        L2_2 = closeScrnLog
        L3_2 = "前往大唐境外"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "CAC" then
        L2_2 = scrnLog
        L3_2 = "前往大唐国境"
        L2_2(L3_2)
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoYZ
        L4_2 = "CAC"
        L2_2(L3_2, L4_2)
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postMan
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = findFeat
          L5_2 = color
          L5_2 = L5_2.npcPos
          L5_2 = L5_2.postMan
          L4_2 = L4_2(L5_2)
          if L4_2 == L2_2 then
            L4_2 = tapPostMan
            L5_2 = "DTGJ"
            L4_2 = L4_2(L5_2)
            if L4_2 == false then
              L4_2 = false
              return L4_2
            end
          end
        end
        L4_2 = closeScrnLog
        L5_2 = "前往大唐国境"
        L4_2(L5_2)
      elseif L1_2 == "DTGJ" then
        L2_2 = scrnLog
        L3_2 = "前往大唐境外"
        L2_2(L3_2)
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.DTJW
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.DTJW1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = 1
          L7_2 = _find
          L8_2 = Color
          L8_2 = L8_2["贼王"]
          L8_2 = L8_2["传送门"]
          L7_2 = L7_2(L8_2)
          if L7_2 then
            L7_2 = math
            L7_2 = L7_2.random
            L8_2 = 1
            L9_2 = 3
            L7_2 = L7_2(L8_2, L9_2)
            L6_2 = L7_2
          end
          if 1 < L6_2 then
            L7_2 = _find
            L8_2 = Color
            L8_2 = L8_2["贼王"]
            L8_2 = L8_2["点传送门"]
            L7_2(L8_2)
            L7_2 = _Sleep
            L8_2 = 200
            L9_2 = 300
            L7_2(L8_2, L9_2)
          else
            L7_2 = {}
            L8_2 = 150
            L9_2 = 323
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 15
            L8_2(L9_2, L10_2)
          end
          L7_2 = 1
          L8_2 = 50
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 50
            L14_2 = 100
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "DTJW" then
              L11_2 = closeScrnLog
              L12_2 = "前往大唐境外"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.DTGJtoDTJW
          L8_2 = "DTGJ"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      elseif L1_2 == "CSJW" or L1_2 == "CSC" then
        L2_2 = scrnLog
        L3_2 = "前往大唐境外"
        L2_2(L3_2)
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CSJWtoDTJW
        L4_2 = "CSJW"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = tapPostUncle
          L3_2 = "DTJW"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = false
          return L2_2
        end
      else
        L2_2 = false
        return L2_2
      end
    end
  elseif A0_2 == "fly" then
  end
end

goToDTJW = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往大象洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DXD" then
        L2_2 = closeScrnLog
        L3_2 = "前往大象洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.DXD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1216
          L6_2 = 144
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "DXD" then
              L9_2 = closeScrnLog
              L10_2 = "前往大象洞"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.STLtoDXD
          L6_2 = "STL"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToDXD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往地藏王府"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "DZWF" then
        L2_2 = closeScrnLog
        L3_2 = "前往地藏王府"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = fightSystem
        L2_2()
        if L1_2 == "SLD" then
          L2_2 = singleTap
          L3_2 = {}
          L4_2 = 629
          L5_2 = 111
          L3_2[1] = L4_2
          L3_2[2] = L5_2
          L4_2 = 15
          L2_2(L3_2, L4_2)
          L2_2 = 1
          L3_2 = 6
          L4_2 = 1
          for L5_2 = L2_2, L3_2, L4_2 do
            L6_2 = mSleep
            L7_2 = math
            L7_2 = L7_2.random
            L8_2 = 400
            L9_2 = 550
            L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
            L6_2(L7_2, L8_2, L9_2, L10_2)
            L6_2 = getCurMap
            L6_2 = L6_2()
            if L6_2 == "DZWF" then
              L6_2 = closeScrnLog
              L7_2 = "前往地藏王府"
              L6_2(L7_2)
              L6_2 = true
              return L6_2
            end
          end
        else
          L2_2 = goToSLD
          L3_2 = A0_2
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      end
    end
  end
end

goToDZWF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往方寸山"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "FCS" then
        L2_2 = closeScrnLog
        L3_2 = "前往方寸山"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.FCS
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1090
          L6_2 = 68
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 20
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 6
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "FCS" then
              L9_2 = closeScrnLog
              L10_2 = "前往方寸山"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.CSCtoFCS
          L6_2 = "CSC"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToFCS = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往花果山"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "HGS" then
        L2_2 = closeScrnLog
        L3_2 = "前往花果山"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "ALG" or L1_2 == "DHW" or L1_2 == "JYC" or L1_2 == "JNYW" or L1_2 == "CAC" or L1_2 == "DF" or L1_2 == "DTGJ" or L1_2 == "DHYD" or L1_2 == "NBM" or L1_2 == "CSZ" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.ALGtoHGS
        L4_2 = "ALG"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = math
          L2_2 = L2_2.random
          L3_2 = 1
          L4_2 = 2
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == 2 then
            L3_2 = {}
            L4_2 = 1624
            L5_2 = 114
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 1
            L4_2(L5_2, L6_2)
          else
            L3_2 = {}
            L4_2 = 1837
            L5_2 = 205
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 2
            L4_2(L5_2, L6_2)
          end
          L3_2 = 1
          L4_2 = 40
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 50
            L10_2 = 100
            L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2)
            L7_2 = getCurMap
            L7_2 = L7_2()
            if L7_2 == "HGS" then
              L7_2 = closeScrnLog
              L8_2 = "前往花果山"
              L7_2(L8_2)
              L7_2 = true
              return L7_2
            end
          end
        else
          L2_2 = false
          return L2_2
        end
      elseif L1_2 == "BJLZ" or L1_2 == "CSJW" or L1_2 == "CSC" then
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.BJLZtoHGS
        L4_2 = "BJLZ"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 then
          L2_2 = findFeat
          L3_2 = color
          L3_2 = L3_2.npcPos
          L3_2 = L3_2.postUncle
          L2_2 = L2_2(L3_2)
          if not (0 < L2_2) then
            L2_2 = findFeat
            L3_2 = color
            L3_2 = L3_2.npcPos
            L3_2 = L3_2["北俱g花果山"]
            L2_2 = L2_2(L3_2)
            if not (0 < L2_2) then
              L2_2 = findFeat
              L3_2 = color
              L3_2 = L3_2.npcPos
              L3_2 = L3_2["北俱g花果山2"]
              L2_2 = L2_2(L3_2)
            end
          end
          if 0 < L2_2 then
            L2_2 = mSleep
            L3_2 = 200
            L2_2(L3_2)
            L2_2 = tapPostUncle
            L3_2 = "HGS"
            L2_2 = L2_2(L3_2)
            if L2_2 == false then
              L2_2 = false
              return L2_2
            end
          end
        else
          L2_2 = false
          return L2_2
        end
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = {}
    L2_2 = 1725
    L3_2 = 180
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    repeat
      L2_2 = mSleep
      L3_2 = 300
      L2_2(L3_2)
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 2000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "HGS"
  end
end

goToHGS = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往化生寺"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "HSS" then
        L2_2 = closeScrnLog
        L3_2 = "前往化生寺"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = closeAllDialog
        L2_2()
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoHSS
        L4_2 = "CAC"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
        L2_2 = {}
        L3_2 = 8
        L4_2 = 12
        L2_2[1] = L3_2
        L2_2[2] = L4_2
        L3_2 = move
        L4_2 = L2_2
        L5_2 = "CAC"
        L3_2(L4_2, L5_2)
        L3_2 = 1
        L4_2 = 6
        L5_2 = 1
        for L6_2 = L3_2, L4_2, L5_2 do
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 400
          L10_2 = 550
          L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2)
          L7_2 = getCurMap
          L7_2 = L7_2()
          if L7_2 == "HSS" then
            L7_2 = closeScrnLog
            L8_2 = "前往化生寺"
            L7_2(L8_2)
            L7_2 = true
            return L7_2
          end
        end
      end
    end
  end
end

goToHSS = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往老雕洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "LDD" then
        L2_2 = closeScrnLog
        L3_2 = "前往老雕洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.LDD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 724
          L6_2 = 74
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "LDD" then
              L9_2 = closeScrnLog
              L10_2 = "前往老雕洞"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.STLtoLDD
          L6_2 = "STL"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToLDD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往江南野外"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "JNYW" then
        L2_2 = closeScrnLog
        L3_2 = "前往江南野外"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "JYC" then
        L2_2 = false
        return L2_2
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.JNYW
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.JNYW1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 or 0 < L4_2 then
          L6_2 = {}
          L7_2 = 1774
          L8_2 = 1060
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L7_2 = singleTap
          L8_2 = L6_2
          L9_2 = 3
          L7_2(L8_2, L9_2)
          L7_2 = 1
          L8_2 = 80
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 80
            L14_2 = 100
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "JNYW" then
              L11_2 = closeScrnLog
              L12_2 = "前往江南野外"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.CACtoJNYW
          L8_2 = "CAC"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = {}
    L2_2 = 1715
    L3_2 = 886
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    repeat
      L2_2 = useFlagTo
      L3_2 = image
      L3_2 = L3_2.redFlag
      L4_2 = image
      L4_2 = L4_2.flagJNYWpoint
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 300
      L2_2(L3_2)
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 2000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "JNYW"
  end
end

goToJNYW = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往建邺城"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "JYC" then
        L2_2 = closeScrnLog
        L3_2 = "前往建邺城"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      elseif L1_2 == "DHW" then
        L2_2 = false
        return L2_2
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.JYC
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.doorPos
        L5_2 = L5_2.JYC1
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 then
          L6_2 = math
          L6_2 = L6_2.random
          L7_2 = 1
          L8_2 = 2
          L6_2 = L6_2(L7_2, L8_2)
          if 1 < L6_2 then
            L7_2 = {}
            L8_2 = 1895
            L9_2 = 542
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 5
            L8_2(L9_2, L10_2)
          else
            L7_2 = {}
            L8_2 = 1893
            L9_2 = 715
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 5
            L8_2(L9_2, L10_2)
          end
          L7_2 = 1
          L8_2 = 25
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 100
            L14_2 = 150
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "JYC" then
              L11_2 = closeScrnLog
              L12_2 = "前往建邺城"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
        else
          if not (0 < L4_2) then
            L6_2 = _find_tb
            L7_2 = Color
            L7_2 = L7_2["跑商"]
            L7_2 = L7_2["建邺城口"]
            L6_2 = L6_2(L7_2)
            if not L6_2 then
              goto lbl_139
            end
          end
          L6_2 = math
          L6_2 = L6_2.random
          L7_2 = 1
          L8_2 = 2
          L6_2 = L6_2(L7_2, L8_2)
          if 1 < L6_2 then
            L7_2 = {}
            L8_2 = 1895
            L9_2 = 542
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 5
            L8_2(L9_2, L10_2)
          else
            L7_2 = {}
            L8_2 = 1893
            L9_2 = 715
            L7_2[1] = L8_2
            L7_2[2] = L9_2
            L8_2 = singleTap
            L9_2 = L7_2
            L10_2 = 5
            L8_2(L9_2, L10_2)
          end
          L7_2 = 1
          L8_2 = 40
          L9_2 = 1
          for L10_2 = L7_2, L8_2, L9_2 do
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 50
            L14_2 = 100
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2)
            L11_2 = getCurMap
            L11_2 = L11_2()
            if L11_2 == "JYC" then
              L11_2 = closeScrnLog
              L12_2 = "前往建邺城"
              L11_2(L12_2)
              L11_2 = true
              return L11_2
            end
          end
          goto lbl_150
          ::lbl_139::
          L6_2 = closeAllDialog
          L6_2()
          L6_2 = goTo
          L7_2 = constPos
          L7_2 = L7_2.JNYWtoJYC
          L8_2 = "JNYW"
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 == false then
            L6_2 = false
            return L6_2
          end
        end
      end
      ::lbl_150::
    end
  elseif A0_2 == "fly" then
    L1_2 = useFlyRuneTo
    L2_2 = constPos
    L2_2 = L2_2.runeJYCpoint
    L1_2(L2_2)
  end
end

goToJYC = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往龙宫"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "LG" then
        L2_2 = closeScrnLog
        L3_2 = "前往龙宫"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postShrimp
        L2_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L2_2 = mSleep
          L3_2 = 200
          L2_2(L3_2)
          L2_2 = tapPostShrimp
          L3_2 = "LG"
          L2_2 = L2_2(L3_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        else
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.DHWtoLG
          L4_2 = "DHW"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
          L2_2 = mSleep
          L3_2 = 300
          L2_2(L3_2)
        end
      end
    end
  end
end

goToLG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往灵台宫"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "LTG" then
        L2_2 = closeScrnLog
        L3_2 = "前往灵台宫"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.LTG
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1400
          L6_2 = 212
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "LTG" then
              L9_2 = closeScrnLog
              L10_2 = "前往灵台宫"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.FCStoLTG
          L6_2 = "FCS"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToLTG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往凌霄宝殿"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "LXBD" then
        L2_2 = closeScrnLog
        L3_2 = "前往凌霄宝殿"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.LXBD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 620
          L6_2 = 315
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 20
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "LXBD" then
              L9_2 = closeScrnLog
              L10_2 = "前往凌霄宝殿"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.TGtoLXBD
          L6_2 = "TG"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToLXBD = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = {}
  L1_2 = 60
  L2_2 = 960
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  repeat
    L1_2 = useFlagTo
    L2_2 = image
    L2_2 = L2_2.whiteFlag
    L3_2 = image
    L3_2 = L3_2.flagDTJWpoint
    L1_2(L2_2, L3_2)
    L1_2 = mSleep
    L2_2 = 300
    L1_2(L2_2)
    L1_2 = singleTap
    L2_2 = L0_2
    L3_2 = 30
    L1_2(L2_2, L3_2)
    L1_2 = mSleep
    L2_2 = 2000
    L1_2(L2_2)
    L1_2 = getCurMap
    L1_2 = L1_2()
  until L1_2 == "DTJW"
  repeat
    L1_2 = goTo
    L2_2 = constPos
    L2_2 = L2_2.DTJWtoMJC
    L3_2 = "DTJW"
    L1_2(L2_2, L3_2)
    L1_2 = tapPostUncle
    L1_2()
    L1_2 = mSleep
    L2_2 = 2000
    L1_2(L2_2)
    L1_2 = getCurMap
    L1_2 = L1_2()
  until L1_2 == "MJC"
end

goToMJC = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往魔王居"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "MWJ" then
        L2_2 = closeScrnLog
        L3_2 = "前往魔王居"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.MWJ
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1416
          L6_2 = 284
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "MWJ" then
              L9_2 = closeScrnLog
              L10_2 = "前往魔王居"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.MWZtoMWJ
          L6_2 = "MWZ"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToMWJ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往魔王寨"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "MWZ" then
        L2_2 = closeScrnLog
        L3_2 = "前往魔王寨"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.MWZ
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 924
          L6_2 = 87
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "MWZ" then
              L9_2 = closeScrnLog
              L10_2 = "前往魔王寨"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTJWtoMWZ
          L6_2 = "DTJW"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToMWZ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往女儿村"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "NEC" then
        L2_2 = closeScrnLog
        L3_2 = "前往女儿村"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.NEC
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 142
          L6_2 = 255
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "NEC" then
              L9_2 = closeScrnLog
              L10_2 = "前往女儿村"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.ALGtoNEC
          L6_2 = "ALG"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  elseif A0_2 == "fly" then
    L1_2 = {}
    L2_2 = 177
    L3_2 = 367
    L1_2[1] = L2_2
    L1_2[2] = L3_2
    repeat
      L2_2 = useFlagTo
      L3_2 = image
      L3_2 = L3_2.yellowFlag
      L4_2 = image
      L4_2 = L4_2.flagNECpoint
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 300
      L2_2(L3_2)
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 1000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "NEC"
  end
end

goToNEC = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往女儿村村长家"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 ~= "NECCZJ" then
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.masterPos
        L3_2 = L3_2.SPP
        L2_2 = L2_2(L3_2)
        if not (0 < L2_2) then
          goto lbl_23
        end
      end
      L2_2 = closeScrnLog
      L3_2 = "前往女儿村村长家"
      L2_2(L3_2)
      L2_2 = true
      do return L2_2 end
      goto lbl_83
      ::lbl_23::
      if L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.NECCZJ
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 426
          L6_2 = 255
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 ~= "NECCZJ" then
              L9_2 = findFeat
              L10_2 = color
              L10_2 = L10_2.masterPos
              L10_2 = L10_2.SPP
              L9_2 = L9_2(L10_2)
              if not (0 < L9_2) then
                goto lbl_70
              end
            end
            L9_2 = closeScrnLog
            L10_2 = "前往女儿村村长家"
            L9_2(L10_2)
            L9_2 = true
            do return L9_2 end
            ::lbl_70::
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.NECtoNECCZJ
          L6_2 = "NEC"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
      ::lbl_83::
    end
  end
end

goToNECCZJ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往盘丝洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "PSD" then
        L2_2 = closeScrnLog
        L3_2 = "前往盘丝洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.PSD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1632
          L6_2 = 171
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "PSD" then
              L9_2 = closeScrnLog
              L10_2 = "前往盘丝洞"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.PSLtoPSD
          L6_2 = "PSL"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToPSD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往盘丝岭"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "PSL" then
        L2_2 = closeScrnLog
        L3_2 = "前往盘丝岭"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.PSL
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 884
          L6_2 = 56
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "PSL" then
              L9_2 = closeScrnLog
              L10_2 = "前往盘丝岭"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTJWtoPSL
          L6_2 = "DTJW"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToPSL = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往普陀山"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "PTS" then
        L2_2 = closeScrnLog
        L3_2 = "前往普陀山"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postGirl
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = tapPostGirl
          L5_2 = "PTS"
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        else
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTGJtoPTS
          L6_2 = "DTGJ"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToPTS = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往乾坤殿"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "QKD" then
        L2_2 = closeScrnLog
        L3_2 = "前往乾坤殿"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.QKD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1376
          L6_2 = 242
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 15
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "QKD" then
              L9_2 = closeScrnLog
              L10_2 = "前往乾坤殿"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.WZGtoQKD
          L6_2 = "WZG"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToQKD = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = {}
  L1_2 = 75
  L2_2 = 160
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  repeat
    L1_2 = useFlagTo
    L2_2 = image
    L2_2 = L2_2.whiteFlag
    L3_2 = image
    L3_2 = L3_2.flagQLSpoint
    L1_2(L2_2, L3_2)
    L1_2 = mSleep
    L2_2 = 300
    L1_2(L2_2)
    L1_2 = singleTap
    L2_2 = L0_2
    L3_2 = 15
    L1_2(L2_2, L3_2)
    L1_2 = mSleep
    L2_2 = 2000
    L1_2(L2_2)
    L1_2 = getCurMap
    L1_2 = L1_2()
  until L1_2 == "QLS"
end

goToQLS = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往秦琼府"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "QQF" then
        L2_2 = closeScrnLog
        L3_2 = "前往秦琼府"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = closeAllDialog
        L2_2()
        L2_2 = goTo
        L3_2 = constPos
        L3_2 = L3_2.CACtoQQF
        L4_2 = "CAC"
        L2_2 = L2_2(L3_2, L4_2)
        if L2_2 == false then
          L2_2 = false
          return L2_2
        end
        L2_2 = {}
        L3_2 = -12
        L4_2 = 6
        L2_2[1] = L3_2
        L2_2[2] = L4_2
        L3_2 = move
        L4_2 = L2_2
        L5_2 = "CAC"
        L3_2(L4_2, L5_2)
        L3_2 = 1
        L4_2 = 7
        L5_2 = 1
        for L6_2 = L3_2, L4_2, L5_2 do
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 400
          L10_2 = 550
          L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2)
          L7_2 = getCurMap
          L7_2 = L7_2()
          if L7_2 == "QQF" then
            L7_2 = closeScrnLog
            L8_2 = "前往秦琼府"
            L7_2(L8_2)
            L7_2 = true
            return L7_2
          end
        end
      end
    end
  end
end

goToQQF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往水晶宫"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "SJG" then
        L2_2 = closeScrnLog
        L3_2 = "前往水晶宫"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.SJG
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1239
          L6_2 = 296
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 8
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "SJG" then
              L9_2 = closeScrnLog
              L10_2 = "前往水晶宫"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.LGtoSJG
          L6_2 = "LG"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToSJG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往森罗殿"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "SLD" then
        L2_2 = closeScrnLog
        L3_2 = "前往森罗殿"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.SLD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 700
          L6_2 = 320
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 20
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 5
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "SLD" then
              L9_2 = closeScrnLog
              L10_2 = "前往森罗殿"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DFtoSLD
          L6_2 = "DF"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToSLD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往狮驼岭"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "STL" then
        L2_2 = closeScrnLog
        L3_2 = "前往狮驼岭"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.STL
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 194
          L6_2 = 861
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 8
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "STL" then
              L9_2 = closeScrnLog
              L10_2 = "前往狮驼岭"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTJWtoSTL
          L6_2 = "DTJW"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  elseif A0_2 == "fly" then
    repeat
      L1_2 = useFlagTo
      L2_2 = image
      L2_2 = L2_2.whiteFlag
      L3_2 = image
      L3_2 = L3_2.flagDTJWpoint
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = 300
      L1_2(L2_2)
      L1_2 = {}
      L2_2 = 60
      L3_2 = 960
      L1_2[1] = L2_2
      L1_2[2] = L3_2
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 2000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "DTJW"
    repeat
      L1_2 = goTo
      L2_2 = constPos
      L2_2 = L2_2.DTJWtoSTL
      L3_2 = "DTJW"
      L1_2(L2_2, L3_2)
      L1_2 = {}
      L2_2 = 60
      L3_2 = 960
      L1_2[1] = L2_2
      L1_2[2] = L3_2
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 4000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "STL"
  end
end

goToSTL = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往狮王洞"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "SWD" then
        L2_2 = closeScrnLog
        L3_2 = "前往狮王洞"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.SWD
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1374
          L6_2 = 168
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 20
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "SWD" then
              L9_2 = closeScrnLog
              L10_2 = "前往狮王洞"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.STLtoSWD
          L6_2 = "STL"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  end
end

goToSWD = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往天宫"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "TG" then
        L2_2 = closeScrnLog
        L3_2 = "前往天宫"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = fightSystem
        L2_2()
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.npcPos
        L3_2 = L3_2.postSoldier
        L2_2 = L2_2(L3_2)
        if not (0 < L2_2) then
          L2_2 = findFeat
          L3_2 = color
          L3_2 = L3_2.npcPos
          L3_2 = L3_2.postSoldier1
          L2_2 = L2_2(L3_2)
          if not (0 < L2_2) then
            goto lbl_48
          end
        end
        L2_2 = mSleep
        L3_2 = 200
        L2_2(L3_2)
        L2_2 = tapPostSoldier
        L3_2 = "TG"
        L2_2 = L2_2(L3_2)
        if L2_2 == false then
          L2_2 = false
          do return L2_2 end
          goto lbl_57
          ::lbl_48::
          L2_2 = goTo
          L3_2 = constPos
          L3_2 = L3_2.CSJWtoTG
          L4_2 = "CSJW"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 == false then
            L2_2 = false
            return L2_2
          end
        end
      end
      ::lbl_57::
    end
  end
end

goToTG = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 == "walk" then
    while true do
      L1_2 = scrnLog
      L2_2 = "前往五庄观"
      L1_2(L2_2)
      L1_2 = getCurMap
      L1_2 = L1_2()
      if L1_2 == "WZG" then
        L2_2 = closeScrnLog
        L3_2 = "前往五庄观"
        L2_2(L3_2)
        L2_2 = true
        return L2_2
      elseif L1_2 == "" then
        L2_2 = fightSystem
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.doorPos
        L3_2 = L3_2.WZG
        L2_2, L3_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L4_2 = {}
          L5_2 = 1900
          L6_2 = 462
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L5_2 = singleTap
          L6_2 = L4_2
          L7_2 = 10
          L5_2(L6_2, L7_2)
          L5_2 = 1
          L6_2 = 7
          L7_2 = 1
          for L8_2 = L5_2, L6_2, L7_2 do
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 400
            L12_2 = 550
            L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = getCurMap
            L9_2 = L9_2()
            if L9_2 == "WZG" then
              L9_2 = closeScrnLog
              L10_2 = "前往五庄观"
              L9_2(L10_2)
              L9_2 = true
              return L9_2
            end
          end
        else
          L4_2 = closeAllDialog
          L4_2()
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.DTJWtoWZG
          L6_2 = "DTJW"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == false then
            L4_2 = false
            return L4_2
          end
        end
      end
    end
  elseif A0_2 == "fly" then
    repeat
      L1_2 = goTo
      L2_2 = constPos
      L2_2 = L2_2.DTJWtoWZG
      L3_2 = "DTJW"
      L1_2(L2_2, L3_2)
      L1_2 = {}
      L2_2 = 1863
      L3_2 = 570
      L1_2[1] = L2_2
      L1_2[2] = L3_2
      L2_2 = singleTap
      L3_2 = L1_2
      L4_2 = 30
      L2_2(L3_2, L4_2)
      L2_2 = mSleep
      L3_2 = 3000
      L2_2(L3_2)
      L2_2 = getCurMap
      L2_2 = L2_2()
    until L2_2 == "WZG"
  end
end

goToWZG = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = useFlyRuneTo
  L1_2 = constPos
  L1_2 = L1_2.runeZZGpoint
  L0_2(L1_2)
end

goToZZG = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.propsBtn
    L0_2, L1_2 = L0_2(L1_2)
    L2_2 = findFeat
    L3_2 = color
    L3_2 = L3_2.talisman
    L2_2, L3_2 = L2_2(L3_2)
    if 0 < L0_2 and 0 < L2_2 then
      L4_2 = nLog
      L5_2 = "Done"
      L4_2(L5_2)
      L4_2 = true
      return L4_2
    elseif L0_2 < 0 and 0 < L2_2 then
      L4_2 = nLog
      L5_2 = "tap props"
      L4_2(L5_2)
      L4_2 = {}
      L5_2 = 980
      L6_2 = 200
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L5_2 = singleTap
      L6_2 = L4_2
      L7_2 = 50
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = 200
      L5_2(L6_2)
    elseif L2_2 < 0 then
      while true do
        L4_2 = fightSystem
        L4_2()
        L4_2 = nLog
        L5_2 = "tap openprops"
        L4_2(L5_2)
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["背包"]
        L5_2 = L5_2["道具"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 300
          L7_2 = 450
          L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2)
          break
        else
          L4_2 = _find_tb
          L5_2 = Color
          L5_2 = L5_2["背包"]
          L5_2 = L5_2["道具"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = cleanupINF
            L4_2()
            L4_2 = mSleep
            L5_2 = math
            L5_2 = L5_2.random
            L6_2 = 300
            L7_2 = 450
            L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
            L4_2(L5_2, L6_2, L7_2, L8_2)
            L4_2 = findAndTap
            L5_2 = color
            L5_2 = L5_2.fighting
            L5_2 = L5_2.shrinkAuto
            L6_2 = "singleTap"
            L7_2 = 5
            L4_2(L5_2, L6_2, L7_2)
            L4_2 = mSleep
            L5_2 = math
            L5_2 = L5_2.random
            L6_2 = 300
            L7_2 = 450
            L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
            L4_2(L5_2, L6_2, L7_2, L8_2)
          end
        end
        L4_2 = mSleep
        L5_2 = math
        L5_2 = L5_2.random
        L6_2 = 200
        L7_2 = 350
        L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
        L4_2(L5_2, L6_2, L7_2, L8_2)
      end
    end
  end
end

openProps = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.lugBtn
    L0_2, L1_2 = L0_2(L1_2)
    L2_2 = findFeat
    L3_2 = color
    L3_2 = L3_2.charFeat
    L2_2, L3_2 = L2_2(L3_2)
    L4_2 = findFeat
    L5_2 = color
    L5_2 = L5_2.talisman
    L4_2, L5_2 = L4_2(L5_2)
    if 0 < L0_2 and 0 < L2_2 then
      L6_2 = true
      return L6_2
    elseif L0_2 < 0 and 0 < L2_2 then
      L6_2 = {}
      L7_2 = 1235
      L8_2 = 200
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L7_2 = singleTap
      L8_2 = L6_2
      L9_2 = 50
      L7_2(L8_2, L9_2)
      L7_2 = mSleep
      L8_2 = 200
      L7_2(L8_2)
    elseif L4_2 < 0 then
      while true do
        L6_2 = fightSystem
        L6_2()
        L6_2 = _find_tb
        L7_2 = Color
        L7_2 = L7_2["背包"]
        L7_2 = L7_2["道具"]
        L6_2 = L6_2(L7_2)
        if L6_2 then
          L6_2 = mSleep
          L7_2 = 200
          L6_2(L7_2)
          break
        else
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.expandBtn
          L8_2 = "singleTap"
          L9_2 = 30
          L6_2 = L6_2(L7_2, L8_2, L9_2)
          if L6_2 == false then
            L6_2 = cleanupINF
            L6_2()
          end
        end
      end
    end
  end
end

openLug = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.giveINF
    L0_2, L1_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L2_2 = nLog
      L3_2 = "Done"
      L2_2(L3_2)
      L2_2 = true
      return L2_2
    else
      while true do
        L2_2 = fightSystem
        L2_2()
        L2_2 = nLog
        L3_2 = "tap givebtn"
        L2_2(L3_2)
        L2_2 = findAndTap
        L3_2 = color
        L3_2 = L3_2.btn
        L3_2 = L3_2.giveBtn
        L4_2 = "singleTap"
        L5_2 = 20
        L2_2 = L2_2(L3_2, L4_2, L5_2)
        if L2_2 then
          L2_2 = mSleep
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 300
          L5_2 = 450
          L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2)
          L2_2(L3_2, L4_2, L5_2, L6_2)
          break
        else
          L2_2 = findAndTap
          L3_2 = color
          L3_2 = L3_2.expandBtn
          L4_2 = "singleTap"
          L5_2 = 30
          L2_2 = L2_2(L3_2, L4_2, L5_2)
          if L2_2 == false then
            L2_2 = cleanupINF
            L2_2()
            L2_2 = mSleep
            L3_2 = math
            L3_2 = L3_2.random
            L4_2 = 350
            L5_2 = 400
            L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2)
            L2_2(L3_2, L4_2, L5_2, L6_2)
          end
        end
        L2_2 = mSleep
        L3_2 = math
        L3_2 = L3_2.random
        L4_2 = 350
        L5_2 = 400
        L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2)
        L2_2(L3_2, L4_2, L5_2, L6_2)
      end
    end
  end
end

openGiveINF = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = openProps
  L1_2()
  L1_2 = findAndTap
  L2_2 = color
  L2_2 = L2_2.props
  L2_2 = L2_2.flyRune
  L3_2 = "doubleTap"
  L4_2 = 30
  L1_2(L2_2, L3_2, L4_2)
  L1_2 = mSleep
  L2_2 = math
  L2_2 = L2_2.random
  L3_2 = 450
  L4_2 = 550
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2)
  L1_2(L2_2, L3_2, L4_2, L5_2)
  L1_2 = singleTap
  L2_2 = A0_2
  L3_2 = 30
  L1_2(L2_2, L3_2)
  L1_2 = mSleep
  L2_2 = math
  L2_2 = L2_2.random
  L3_2 = 450
  L4_2 = 550
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2)
  L1_2(L2_2, L3_2, L4_2, L5_2)
  L1_2 = closeAllWnd
  L1_2()
end

useFlyRuneTo = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = openProps
  L2_2()
  L2_2 = _ENV["_卡区延迟"]
  L2_2()
  L2_2 = findAndTap
  L3_2 = A0_2
  L4_2 = "doubleTap"
  L5_2 = 20
  L2_2(L3_2, L4_2, L5_2)
  L2_2 = mSleep
  L3_2 = math
  L3_2 = L3_2.random
  L4_2 = 450
  L5_2 = 550
  L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L3_2(L4_2, L5_2)
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L2_2 = 1
  L3_2 = 10
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = findFeat
    L7_2 = color
    L7_2 = L7_2.btn
    L7_2 = L7_2.propsBtn
    L6_2 = L6_2(L7_2)
    if L6_2 < 0 then
      break
    end
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = 450
    L9_2 = 550
    L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2)
  end
  L2_2 = findFeat
  L3_2 = A1_2
  L2_2, L3_2 = L2_2(L3_2)
  if 0 < L2_2 then
    L4_2 = {}
    L5_2 = L2_2
    L6_2 = L3_2
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    p = L4_2
    L4_2 = singleTap
    L5_2 = p
    L6_2 = 5
    L4_2(L5_2, L6_2)
    L4_2 = _ENV["_卡区延迟"]
    L4_2()
    L4_2 = mSleep
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 750
    L7_2 = 950
    L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L5_2(L6_2, L7_2)
    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    L4_2 = findFeat
    L5_2 = color
    L5_2 = L5_2.btn
    L5_2 = L5_2.propsBtn
    L4_2 = L4_2(L5_2)
    if L4_2 < 0 then
      L4_2 = singleTap
      L5_2 = p
      L6_2 = 5
      L4_2(L5_2, L6_2)
      L4_2 = mSleep
      L5_2 = math
      L5_2 = L5_2.random
      L6_2 = 450
      L7_2 = 550
      L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L5_2(L6_2, L7_2)
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      L4_2 = _ENV["_卡区延迟"]
      L4_2()
    end
  else
    L4_2 = color
    L4_2 = L4_2.flag
    L4_2 = L4_2.blue
    if A0_2 == L4_2 then
      qizi_color = "蓝旗"
    else
      L4_2 = color
      L4_2 = L4_2.flag
      L4_2 = L4_2.red
      if A0_2 == L4_2 then
        qizi_color = "红旗"
      else
        L4_2 = color
        L4_2 = L4_2.flag
        L4_2 = L4_2.yellow
        if A0_2 == L4_2 then
          qizi_color = "黄旗"
        else
          L4_2 = color
          L4_2 = L4_2.flag
          L4_2 = L4_2.green
          if A0_2 == L4_2 then
            qizi_color = "绿旗"
          else
            L4_2 = color
            L4_2 = L4_2.flag
            L4_2 = L4_2.white
            if A0_2 == L4_2 then
              qizi_color = "白旗"
            end
          end
        end
      end
    end
    L4_2 = _find_tb_cx
    L5_2 = Color
    L5_2 = L5_2["飞行"]
    L6_2 = qizi_color
    L5_2 = L5_2[L6_2]
    L5_2 = L5_2["查找"]
    L6_2 = {}
    L7_2 = 20
    L8_2 = 50
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L4_2 = L4_2(L5_2, L6_2)
    if not L4_2 then
      L4_2 = _ENV["_功能"]
      L4_2 = L4_2["行囊取回"]
      L5_2 = qizi_color
      L6_2 = Color
      L6_2 = L6_2["飞行"]
      L7_2 = qizi_color
      L6_2 = L6_2[L7_2]
      L6_2 = L6_2["双击"]
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
      end
    end
  end
  L4_2 = closeAllWnd
  L4_2()
end

useFlagTo = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postMan
      L6_2, L7_2 = L6_2(L7_2)
      L8_2 = findFeat
      L9_2 = color
      L9_2 = L9_2.npcPos
      L9_2 = L9_2["郊外g北俱"]
      L8_2, L9_2 = L8_2(L9_2)
      if 0 < L6_2 then
        L10_2 = {}
        L11_2 = L6_2 + 70
        L12_2 = L7_2 - 70
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = _ENV["卡顿掉帧"]
        L12_2 = L12_2 / 2
        L11_2(L12_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 100
        L14_2 = 150
        L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L11_2 = _find_tb
        L12_2 = Color
        L12_2 = L12_2["出现重叠"]
        L11_2, L12_2, L13_2 = L11_2(L12_2)
        if L11_2 then
          L14_2 = L12_2 - 100
          L15_2 = L13_2 + 31
          L16_2 = L12_2 + 50
          L17_2 = L13_2 + 540
          L18_2 = _find_tb
          L19_2 = Color
          L19_2 = L19_2.npc
          L19_2 = L19_2["驿站老板重叠"]
          L20_2 = {}
          L21_2 = 0
          L22_2 = 0
          L20_2[1] = L21_2
          L20_2[2] = L22_2
          L21_2 = {}
          L22_2 = L14_2
          L23_2 = L15_2
          L24_2 = L16_2
          L25_2 = L17_2
          L21_2[1] = L22_2
          L21_2[2] = L23_2
          L21_2[3] = L24_2
          L21_2[4] = L25_2
          L18_2 = L18_2(L19_2, L20_2, L21_2)
          if L18_2 then
            L18_2 = _Sleep
            L19_2 = 100
            L20_2 = 200
            L18_2(L19_2, L20_2)
          end
        end
        L14_2 = _find_cx
        L15_2 = Color
        L15_2 = L15_2.npc
        L15_2 = L15_2["是的我要去"]
        L16_2 = {}
        L17_2 = 40
        L18_2 = 50
        L16_2[1] = L17_2
        L16_2[2] = L18_2
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = 1
          L15_2 = 10
          L16_2 = 1
          for L17_2 = L14_2, L15_2, L16_2 do
            L18_2 = getCurMap
            L18_2 = L18_2()
            if L18_2 == A0_2 then
              L18_2 = closeScrnLog
              L19_2 = "正在找传送人"
              L18_2(L19_2)
              L18_2 = true
              return L18_2
            end
            L18_2 = mSleep
            L19_2 = math
            L19_2 = L19_2.random
            L20_2 = 100
            L21_2 = 110
            L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L19_2(L20_2, L21_2)
            L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
          end
        end
      elseif 0 < L8_2 then
        L10_2 = {}
        L11_2 = L8_2 - 248
        L12_2 = L9_2 + 171
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = _ENV["卡顿掉帧"]
        L12_2 = L12_2 / 2
        L11_2(L12_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 100
        L14_2 = 150
        L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L11_2 = _find_tb
        L12_2 = Color
        L12_2 = L12_2["出现重叠"]
        L11_2, L12_2, L13_2 = L11_2(L12_2)
        if L11_2 then
          L14_2 = L12_2 - 100
          L15_2 = L13_2 + 31
          L16_2 = L12_2 + 50
          L17_2 = L13_2 + 540
          L18_2 = _find_tb
          L19_2 = Color
          L19_2 = L19_2.npc
          L19_2 = L19_2["驿站老板重叠"]
          L20_2 = {}
          L21_2 = 0
          L22_2 = 0
          L20_2[1] = L21_2
          L20_2[2] = L22_2
          L21_2 = {}
          L22_2 = L14_2
          L23_2 = L15_2
          L24_2 = L16_2
          L25_2 = L17_2
          L21_2[1] = L22_2
          L21_2[2] = L23_2
          L21_2[3] = L24_2
          L21_2[4] = L25_2
          L18_2 = L18_2(L19_2, L20_2, L21_2)
          if L18_2 then
            L18_2 = _Sleep
            L19_2 = 100
            L20_2 = 200
            L18_2(L19_2, L20_2)
          end
        end
        L14_2 = _find_cx
        L15_2 = Color
        L15_2 = L15_2.npc
        L15_2 = L15_2["是的我要去"]
        L16_2 = {}
        L17_2 = 40
        L18_2 = 50
        L16_2[1] = L17_2
        L16_2[2] = L18_2
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = 1
          L15_2 = 10
          L16_2 = 1
          for L17_2 = L14_2, L15_2, L16_2 do
            L18_2 = getCurMap
            L18_2 = L18_2()
            if L18_2 == A0_2 then
              L18_2 = closeScrnLog
              L19_2 = "正在找传送人"
              L18_2(L19_2)
              L18_2 = true
              return L18_2
            end
            L18_2 = mSleep
            L19_2 = math
            L19_2 = L19_2.random
            L20_2 = 100
            L21_2 = 110
            L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L19_2(L20_2, L21_2)
            L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
          end
        end
      else
        L10_2 = {}
        L11_2 = 950
        L12_2 = 545
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 5
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 500
        L14_2 = 850
        L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L11_2 = _ENV["_卡区延迟"]
        L11_2()
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostMan = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postUncle
      L6_2, L7_2 = L6_2(L7_2)
      L8_2 = findFeat
      L9_2 = color
      L9_2 = L9_2.npcPos
      L9_2 = L9_2["花果山g北俱"]
      L8_2, L9_2 = L8_2(L9_2)
      L10_2 = findFeat
      L11_2 = color
      L11_2 = L11_2.npcPos
      L11_2 = L11_2["北俱g花果山"]
      L10_2, L11_2 = L10_2(L11_2)
      L12_2 = findFeat
      L13_2 = color
      L13_2 = L13_2.npcPos
      L13_2 = L13_2["境外to郊外"]
      L12_2, L13_2 = L12_2(L13_2)
      if 0 < L6_2 then
        L14_2 = {}
        L15_2 = L6_2 - 35
        L16_2 = L7_2 - 70
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = singleTap
        L16_2 = L14_2
        L17_2 = 6
        L15_2(L16_2, L17_2)
        L15_2 = mSleep
        L16_2 = _ENV["卡顿掉帧"]
        L16_2 = L16_2 / 2
        L15_2(L16_2)
        L15_2 = mSleep
        L16_2 = math
        L16_2 = L16_2.random
        L17_2 = 100
        L18_2 = 250
        L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L16_2(L17_2, L18_2)
        L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
        L15_2 = _find_tb
        L16_2 = Color
        L16_2 = L16_2["出现重叠"]
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        if L15_2 then
          L18_2 = L16_2 + 1
          L19_2 = L17_2 + 31
          L20_2 = L16_2 + 130
          L21_2 = L17_2 + 540
          L22_2 = _find_tb
          L23_2 = Color
          L23_2 = L23_2.npc
          L23_2 = L23_2["土地重叠"]
          L24_2 = {}
          L25_2 = 0
          L26_2 = 0
          L24_2[1] = L25_2
          L24_2[2] = L26_2
          L25_2 = {}
          L26_2 = L18_2
          L27_2 = L19_2
          L28_2 = L20_2
          L29_2 = L21_2
          L25_2[1] = L26_2
          L25_2[2] = L27_2
          L25_2[3] = L28_2
          L25_2[4] = L29_2
          L22_2 = L22_2(L23_2, L24_2, L25_2)
          if L22_2 then
            L22_2 = _Sleep
            L23_2 = 100
            L24_2 = 200
            L22_2(L23_2, L24_2)
          end
        end
        L18_2 = _find_cx
        L19_2 = Color
        L19_2 = L19_2.npc
        L19_2 = L19_2["是的我要去"]
        L20_2 = {}
        L21_2 = 40
        L22_2 = 50
        L20_2[1] = L21_2
        L20_2[2] = L22_2
        L18_2 = L18_2(L19_2, L20_2)
        if L18_2 then
          L18_2 = 1
          L19_2 = 30
          L20_2 = 1
          for L21_2 = L18_2, L19_2, L20_2 do
            L22_2 = mSleep
            L23_2 = math
            L23_2 = L23_2.random
            L24_2 = 50
            L25_2 = 100
            L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L23_2(L24_2, L25_2)
            L22_2(L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
            L22_2 = getCurMap
            L22_2 = L22_2()
            if L22_2 == A0_2 then
              L22_2 = closeScrnLog
              L23_2 = "正在找传送人"
              L22_2(L23_2)
              L22_2 = true
              return L22_2
            end
          end
        end
      elseif 0 < L8_2 then
        L14_2 = scrnLog
        L15_2 = "寻找1"
        L14_2(L15_2)
        L14_2 = {}
        L15_2 = L8_2 - 775
        L16_2 = L9_2 - 259
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = singleTap
        L16_2 = L14_2
        L17_2 = 6
        L15_2(L16_2, L17_2)
        L15_2 = mSleep
        L16_2 = _ENV["卡顿掉帧"]
        L16_2 = L16_2 / 2
        L15_2(L16_2)
        L15_2 = mSleep
        L16_2 = math
        L16_2 = L16_2.random
        L17_2 = 100
        L18_2 = 250
        L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L16_2(L17_2, L18_2)
        L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
        L15_2 = _find_tb
        L16_2 = Color
        L16_2 = L16_2["出现重叠"]
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        if L15_2 then
          L18_2 = L16_2 + 1
          L19_2 = L17_2 + 31
          L20_2 = L16_2 + 130
          L21_2 = L17_2 + 540
          L22_2 = _find_tb
          L23_2 = Color
          L23_2 = L23_2.npc
          L23_2 = L23_2["土地重叠"]
          L24_2 = {}
          L25_2 = 0
          L26_2 = 0
          L24_2[1] = L25_2
          L24_2[2] = L26_2
          L25_2 = {}
          L26_2 = L18_2
          L27_2 = L19_2
          L28_2 = L20_2
          L29_2 = L21_2
          L25_2[1] = L26_2
          L25_2[2] = L27_2
          L25_2[3] = L28_2
          L25_2[4] = L29_2
          L22_2 = L22_2(L23_2, L24_2, L25_2)
          if L22_2 then
            L22_2 = _Sleep
            L23_2 = 100
            L24_2 = 200
            L22_2(L23_2, L24_2)
          end
        end
        L18_2 = _find_cx
        L19_2 = Color
        L19_2 = L19_2.npc
        L19_2 = L19_2["是的我要去"]
        L20_2 = {}
        L21_2 = 40
        L22_2 = 50
        L20_2[1] = L21_2
        L20_2[2] = L22_2
        L18_2 = L18_2(L19_2, L20_2)
        if L18_2 then
          L18_2 = 1
          L19_2 = 30
          L20_2 = 1
          for L21_2 = L18_2, L19_2, L20_2 do
            L22_2 = mSleep
            L23_2 = math
            L23_2 = L23_2.random
            L24_2 = 50
            L25_2 = 100
            L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L23_2(L24_2, L25_2)
            L22_2(L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
            L22_2 = getCurMap
            L22_2 = L22_2()
            if L22_2 == A0_2 then
              L22_2 = closeScrnLog
              L23_2 = "正在找传送人"
              L22_2(L23_2)
              L22_2 = true
              return L22_2
            end
          end
        end
      elseif 0 < L10_2 then
        L14_2 = scrnLog
        L15_2 = "寻找2222222"
        L14_2(L15_2)
        L14_2 = {}
        L15_2 = L10_2 - 275
        L16_2 = L11_2 - 319
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = singleTap
        L16_2 = L14_2
        L17_2 = 6
        L15_2(L16_2, L17_2)
        L15_2 = mSleep
        L16_2 = _ENV["卡顿掉帧"]
        L16_2 = L16_2 / 2
        L15_2(L16_2)
        L15_2 = mSleep
        L16_2 = math
        L16_2 = L16_2.random
        L17_2 = 100
        L18_2 = 250
        L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L16_2(L17_2, L18_2)
        L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
        L15_2 = _find_tb
        L16_2 = Color
        L16_2 = L16_2["出现重叠"]
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        if L15_2 then
          L18_2 = L16_2 + 1
          L19_2 = L17_2 + 31
          L20_2 = L16_2 + 130
          L21_2 = L17_2 + 540
          L22_2 = _find_tb
          L23_2 = Color
          L23_2 = L23_2.npc
          L23_2 = L23_2["土地重叠"]
          L24_2 = {}
          L25_2 = 0
          L26_2 = 0
          L24_2[1] = L25_2
          L24_2[2] = L26_2
          L25_2 = {}
          L26_2 = L18_2
          L27_2 = L19_2
          L28_2 = L20_2
          L29_2 = L21_2
          L25_2[1] = L26_2
          L25_2[2] = L27_2
          L25_2[3] = L28_2
          L25_2[4] = L29_2
          L22_2 = L22_2(L23_2, L24_2, L25_2)
          if L22_2 then
            L22_2 = _Sleep
            L23_2 = 100
            L24_2 = 200
            L22_2(L23_2, L24_2)
          end
        end
        L18_2 = _find_cx
        L19_2 = Color
        L19_2 = L19_2.npc
        L19_2 = L19_2["是的我要去"]
        L20_2 = {}
        L21_2 = 40
        L22_2 = 50
        L20_2[1] = L21_2
        L20_2[2] = L22_2
        L18_2 = L18_2(L19_2, L20_2)
        if L18_2 then
          L18_2 = 1
          L19_2 = 30
          L20_2 = 1
          for L21_2 = L18_2, L19_2, L20_2 do
            L22_2 = mSleep
            L23_2 = math
            L23_2 = L23_2.random
            L24_2 = 50
            L25_2 = 100
            L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L23_2(L24_2, L25_2)
            L22_2(L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
            L22_2 = getCurMap
            L22_2 = L22_2()
            if L22_2 == A0_2 then
              L22_2 = closeScrnLog
              L23_2 = "正在找传送人"
              L22_2(L23_2)
              L22_2 = true
              return L22_2
            end
          end
        end
      elseif 0 < L12_2 then
        L14_2 = scrnLog
        L15_2 = "寻找4"
        L14_2(L15_2)
        L14_2 = {}
        L15_2 = L12_2 + 253
        L16_2 = L13_2 + 430
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = singleTap
        L16_2 = L14_2
        L17_2 = 3
        L15_2(L16_2, L17_2)
        L15_2 = mSleep
        L16_2 = _ENV["卡顿掉帧"]
        L16_2 = L16_2 / 2
        L15_2(L16_2)
        L15_2 = mSleep
        L16_2 = math
        L16_2 = L16_2.random
        L17_2 = 100
        L18_2 = 250
        L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L16_2(L17_2, L18_2)
        L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
        L15_2 = _find_tb
        L16_2 = Color
        L16_2 = L16_2["出现重叠"]
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        if L15_2 then
          L18_2 = L16_2 + 1
          L19_2 = L17_2 + 31
          L20_2 = L16_2 + 130
          L21_2 = L17_2 + 540
          L22_2 = _find_tb
          L23_2 = Color
          L23_2 = L23_2.npc
          L23_2 = L23_2["土地重叠"]
          L24_2 = {}
          L25_2 = 0
          L26_2 = 0
          L24_2[1] = L25_2
          L24_2[2] = L26_2
          L25_2 = {}
          L26_2 = L18_2
          L27_2 = L19_2
          L28_2 = L20_2
          L29_2 = L21_2
          L25_2[1] = L26_2
          L25_2[2] = L27_2
          L25_2[3] = L28_2
          L25_2[4] = L29_2
          L22_2 = L22_2(L23_2, L24_2, L25_2)
          if L22_2 then
            L22_2 = _Sleep
            L23_2 = 100
            L24_2 = 200
            L22_2(L23_2, L24_2)
          end
        end
        L18_2 = _find_cx
        L19_2 = Color
        L19_2 = L19_2.npc
        L19_2 = L19_2["是的我要去"]
        L20_2 = {}
        L21_2 = 40
        L22_2 = 50
        L20_2[1] = L21_2
        L20_2[2] = L22_2
        L18_2 = L18_2(L19_2, L20_2)
        if L18_2 then
          L18_2 = 1
          L19_2 = 30
          L20_2 = 1
          for L21_2 = L18_2, L19_2, L20_2 do
            L22_2 = mSleep
            L23_2 = math
            L23_2 = L23_2.random
            L24_2 = 50
            L25_2 = 100
            L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2 = L23_2(L24_2, L25_2)
            L22_2(L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
            L22_2 = getCurMap
            L22_2 = L22_2()
            if L22_2 == A0_2 then
              L22_2 = closeScrnLog
              L23_2 = "正在找传送人"
              L22_2(L23_2)
              L22_2 = true
              return L22_2
            end
          end
        end
      else
        L14_2 = scrnLog
        L15_2 = "没找到"
        L14_2(L15_2)
        L14_2 = {}
        L15_2 = 950
        L16_2 = 545
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L15_2 = singleTap
        L16_2 = L14_2
        L17_2 = 5
        L15_2(L16_2, L17_2)
        L15_2 = _ENV["_卡区延迟"]
        L15_2()
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostUncle = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postGirl
      L6_2, L7_2 = L6_2(L7_2)
      L8_2 = findFeat
      L9_2 = color
      L9_2 = L9_2.npcPos
      L9_2 = L9_2["白无常"]
      L8_2, L9_2 = L8_2(L9_2)
      L10_2 = findFeat
      L11_2 = color
      L11_2 = L11_2.npcPos
      L11_2 = L11_2["白无常2"]
      L10_2, L11_2 = L10_2(L11_2)
      if 0 < L6_2 then
        L12_2 = {}
        L13_2 = L6_2 - 35
        L14_2 = L7_2 - 70
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 20
        L13_2(L14_2, L15_2)
        L13_2 = mSleep
        L14_2 = math
        L14_2 = L14_2.random
        L15_2 = 100
        L16_2 = 150
        L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
        L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["是的我要去"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 11
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 100
          L20_2 = 150
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
        end
      elseif 0 < L8_2 then
        L12_2 = {}
        L13_2 = L8_2 + 46
        L14_2 = L9_2 - 141
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 5
        L13_2(L14_2, L15_2)
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["好啊"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 23
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 100
          L20_2 = 150
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
        end
      elseif 0 < L10_2 then
        L12_2 = {}
        L13_2 = L10_2 - 149
        L14_2 = L11_2 + 49
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 3
        L13_2(L14_2, L15_2)
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["好啊"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 43
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 100
          L20_2 = 150
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostGirl = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postShrimp
      L6_2, L7_2 = L6_2(L7_2)
      if 0 < L6_2 then
        L8_2 = {}
        L9_2 = L6_2 + 35
        L10_2 = L7_2 - 70
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L9_2 = singleTap
        L10_2 = L8_2
        L11_2 = 20
        L9_2(L10_2, L11_2)
        L9_2 = mSleep
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 100
        L12_2 = 150
        L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L10_2(L11_2, L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L9_2 = _ENV["_卡区延迟"]
        L9_2()
        L9_2 = _find_cx
        L10_2 = Color
        L10_2 = L10_2.npc
        L10_2 = L10_2["是的我要去"]
        L11_2 = {}
        L12_2 = 40
        L13_2 = 50
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L9_2 = L9_2(L10_2, L11_2)
        if L9_2 then
        end
        L9_2 = _ENV["_卡区延迟"]
        L9_2()
        L9_2 = 1
        L10_2 = 23
        L11_2 = 1
        for L12_2 = L9_2, L10_2, L11_2 do
          L13_2 = mSleep
          L14_2 = math
          L14_2 = L14_2.random
          L15_2 = 100
          L16_2 = 150
          L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2, L16_2)
          L13_2(L14_2, L15_2, L16_2, L17_2)
          L13_2 = getCurMap
          L13_2 = L13_2()
          if L13_2 == A0_2 then
            L13_2 = closeScrnLog
            L14_2 = "正在找传送人"
            L13_2(L14_2)
            L13_2 = true
            return L13_2
          end
          L13_2 = _ENV["_卡区延迟"]
          L13_2()
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostShrimp = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.boatMan
      L6_2, L7_2 = L6_2(L7_2)
      if 0 < L6_2 then
        L8_2 = {}
        L9_2 = L6_2 + 35
        L10_2 = L7_2 - 70
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L9_2 = singleTap
        L10_2 = L8_2
        L11_2 = 20
        L9_2(L10_2, L11_2)
        L9_2 = mSleep
        L10_2 = _ENV["卡顿掉帧"]
        L10_2 = L10_2 / 2
        L9_2(L10_2)
        L9_2 = mSleep
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 100
        L12_2 = 150
        L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2 = L10_2(L11_2, L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
        L9_2 = _find_tb
        L10_2 = Color
        L10_2 = L10_2["出现重叠"]
        L9_2, L10_2, L11_2 = L9_2(L10_2)
        if L9_2 then
          L12_2 = L10_2 - 100
          L13_2 = L11_2 + 31
          L14_2 = L10_2 + 50
          L15_2 = L11_2 + 540
          L16_2 = _find_tb
          L17_2 = Color
          L17_2 = L17_2.npc
          L17_2 = L17_2["船夫重叠"]
          L18_2 = {}
          L19_2 = 0
          L20_2 = 0
          L18_2[1] = L19_2
          L18_2[2] = L20_2
          L19_2 = {}
          L20_2 = L12_2
          L21_2 = L13_2
          L22_2 = L14_2
          L23_2 = L15_2
          L19_2[1] = L20_2
          L19_2[2] = L21_2
          L19_2[3] = L22_2
          L19_2[4] = L23_2
          L16_2 = L16_2(L17_2, L18_2, L19_2)
          if L16_2 then
            L16_2 = _Sleep
            L17_2 = 100
            L18_2 = 200
            L16_2(L17_2, L18_2)
          end
        end
        L12_2 = _find_cx
        L13_2 = Color
        L13_2 = L13_2.npc
        L13_2 = L13_2["是的我要去"]
        L14_2 = {}
        L15_2 = 40
        L16_2 = 50
        L14_2[1] = L15_2
        L14_2[2] = L16_2
        L12_2 = L12_2(L13_2, L14_2)
        if L12_2 then
          L12_2 = 1
          L13_2 = 10
          L14_2 = 1
          for L15_2 = L12_2, L13_2, L14_2 do
            L16_2 = getCurMap
            L16_2 = L16_2()
            if L16_2 == A0_2 then
              L16_2 = closeScrnLog
              L17_2 = "正在找传送人"
              L16_2(L17_2)
              L16_2 = true
              return L16_2
            end
            L16_2 = mSleep
            L17_2 = math
            L17_2 = L17_2.random
            L18_2 = 100
            L19_2 = 110
            L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2 = L17_2(L18_2, L19_2)
            L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
          end
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapBoatMan = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postSoldier
      L6_2, L7_2 = L6_2(L7_2)
      L8_2 = findFeat
      L9_2 = color
      L9_2 = L9_2.npcPos
      L9_2 = L9_2.postSoldier1
      L8_2, L9_2 = L8_2(L9_2)
      L10_2 = findFeat
      L11_2 = color
      L11_2 = L11_2.npcPos
      L11_2 = L11_2["天宫传送天将"]
      L10_2, L11_2 = L10_2(L11_2)
      if 0 < L6_2 then
        L12_2 = {}
        L13_2 = L6_2 + 35
        L14_2 = L7_2 - 70
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 20
        L13_2(L14_2, L15_2)
        L13_2 = mSleep
        L14_2 = math
        L14_2 = L14_2.random
        L15_2 = 1800
        L16_2 = 1850
        L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
        L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = _find_tb
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["天将重叠"]
        L13_2 = L13_2(L14_2)
        if L13_2 then
          L13_2 = _Sleep
          L14_2 = 500
          L15_2 = 1000
          L13_2(L14_2, L15_2)
        end
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["是的我要去"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 15
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 100
          L20_2 = 150
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
        end
      elseif 0 < L8_2 then
        L12_2 = {}
        L13_2 = L6_2 + 3
        L14_2 = L7_2 - 70
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 20
        L13_2(L14_2, L15_2)
        L13_2 = mSleep
        L14_2 = math
        L14_2 = L14_2.random
        L15_2 = 800
        L16_2 = 850
        L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
        L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = _find_tb
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["天将重叠"]
        L13_2 = L13_2(L14_2)
        if L13_2 then
          L13_2 = _Sleep
          L14_2 = 500
          L15_2 = 1000
          L13_2(L14_2, L15_2)
        end
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["是的我要去"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 5
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 400
          L20_2 = 550
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
        end
      elseif 0 < L10_2 then
        L12_2 = {}
        L13_2 = L10_2 - 590
        L14_2 = L9_2 - 45
        L12_2[1] = L13_2
        L12_2[2] = L14_2
        L13_2 = singleTap
        L14_2 = L12_2
        L15_2 = 20
        L13_2(L14_2, L15_2)
        L13_2 = mSleep
        L14_2 = math
        L14_2 = L14_2.random
        L15_2 = 1800
        L16_2 = 1850
        L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
        L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = _find_tb
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["天将重叠"]
        L13_2 = L13_2(L14_2)
        if L13_2 then
          L13_2 = _Sleep
          L14_2 = 500
          L15_2 = 1000
          L13_2(L14_2, L15_2)
        end
        L13_2 = _find_cx
        L14_2 = Color
        L14_2 = L14_2.npc
        L14_2 = L14_2["是的我要去"]
        L15_2 = {}
        L16_2 = 40
        L17_2 = 50
        L15_2[1] = L16_2
        L15_2[2] = L17_2
        L13_2 = L13_2(L14_2, L15_2)
        if L13_2 then
        end
        L13_2 = _ENV["_卡区延迟"]
        L13_2()
        L13_2 = 1
        L14_2 = 5
        L15_2 = 1
        for L16_2 = L13_2, L14_2, L15_2 do
          L17_2 = mSleep
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 400
          L20_2 = 550
          L18_2, L19_2, L20_2, L21_2 = L18_2(L19_2, L20_2)
          L17_2(L18_2, L19_2, L20_2, L21_2)
          L17_2 = getCurMap
          L17_2 = L17_2()
          if L17_2 == A0_2 then
            L17_2 = closeScrnLog
            L18_2 = "正在找传送人"
            L17_2(L18_2)
            L17_2 = true
            return L17_2
          end
          L17_2 = _ENV["_卡区延迟"]
          L17_2()
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostSoldier = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2
  L1_2 = scrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 15
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找传送人"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.postRabbit
      L6_2, L7_2 = L6_2(L7_2)
      L8_2 = findFeat
      L9_2 = color
      L9_2 = L9_2.npcPos
      L9_2 = L9_2.postRabbittuzi
      L8_2, L9_2 = L8_2(L9_2)
      if 0 < L6_2 then
        L10_2 = {}
        L11_2 = L6_2 + 35
        L12_2 = L7_2 - 70
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = _ENV["卡顿掉帧"]
        L12_2 = L12_2 / 2
        L11_2(L12_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 100
        L14_2 = 150
        L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L11_2 = _find_tb
        L12_2 = Color
        L12_2 = L12_2["出现重叠"]
        L11_2, L12_2, L13_2 = L11_2(L12_2)
        if L11_2 then
          L14_2 = L12_2 - 50
          L15_2 = L13_2 + 31
          L16_2 = L12_2 + 50
          L17_2 = L13_2 + 540
          L18_2 = _find_tb
          L19_2 = Color
          L19_2 = L19_2.npc
          L19_2 = L19_2["遁地鬼重叠"]
          L20_2 = {}
          L21_2 = 0
          L22_2 = 0
          L20_2[1] = L21_2
          L20_2[2] = L22_2
          L21_2 = {}
          L22_2 = L14_2
          L23_2 = L15_2
          L24_2 = L16_2
          L25_2 = L17_2
          L21_2[1] = L22_2
          L21_2[2] = L23_2
          L21_2[3] = L24_2
          L21_2[4] = L25_2
          L18_2 = L18_2(L19_2, L20_2, L21_2)
          if L18_2 then
            L18_2 = _Sleep
            L19_2 = 100
            L20_2 = 200
            L18_2(L19_2, L20_2)
          end
        end
        L14_2 = _find_cx
        L15_2 = Color
        L15_2 = L15_2.npc
        L15_2 = L15_2["是的我要去"]
        L16_2 = {}
        L17_2 = 40
        L18_2 = 50
        L16_2[1] = L17_2
        L16_2[2] = L18_2
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = 1
          L15_2 = 50
          L16_2 = 1
          for L17_2 = L14_2, L15_2, L16_2 do
            L18_2 = mSleep
            L19_2 = math
            L19_2 = L19_2.random
            L20_2 = 40
            L21_2 = 50
            L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L19_2(L20_2, L21_2)
            L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
            L18_2 = getCurMap
            L18_2 = L18_2()
            if L18_2 == A0_2 then
              L18_2 = closeScrnLog
              L19_2 = "正在找传送人"
              L18_2(L19_2)
              L18_2 = true
              return L18_2
            end
          end
        end
      elseif 0 < L8_2 then
        L10_2 = {}
        L11_2 = L8_2 - 284
        L12_2 = L9_2 - 5
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = _ENV["卡顿掉帧"]
        L12_2 = L12_2 / 2
        L11_2(L12_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 100
        L14_2 = 150
        L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L11_2 = _find_tb
        L12_2 = Color
        L12_2 = L12_2["出现重叠"]
        L11_2, L12_2, L13_2 = L11_2(L12_2)
        if L11_2 then
          L14_2 = L12_2 - 50
          L15_2 = L13_2 + 31
          L16_2 = L12_2 + 50
          L17_2 = L13_2 + 540
          L18_2 = _find_tb
          L19_2 = Color
          L19_2 = L19_2.npc
          L19_2 = L19_2["遁地鬼重叠"]
          L20_2 = {}
          L21_2 = 0
          L22_2 = 0
          L20_2[1] = L21_2
          L20_2[2] = L22_2
          L21_2 = {}
          L22_2 = L14_2
          L23_2 = L15_2
          L24_2 = L16_2
          L25_2 = L17_2
          L21_2[1] = L22_2
          L21_2[2] = L23_2
          L21_2[3] = L24_2
          L21_2[4] = L25_2
          L18_2 = L18_2(L19_2, L20_2, L21_2)
          if L18_2 then
            L18_2 = _Sleep
            L19_2 = 100
            L20_2 = 200
            L18_2(L19_2, L20_2)
          end
        end
        L14_2 = _find_cx
        L15_2 = Color
        L15_2 = L15_2.npc
        L15_2 = L15_2["是的我要去"]
        L16_2 = {}
        L17_2 = 40
        L18_2 = 50
        L16_2[1] = L17_2
        L16_2[2] = L18_2
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = 1
          L15_2 = 50
          L16_2 = 1
          for L17_2 = L14_2, L15_2, L16_2 do
            L18_2 = mSleep
            L19_2 = math
            L19_2 = L19_2.random
            L20_2 = 40
            L21_2 = 50
            L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L19_2(L20_2, L21_2)
            L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
            L18_2 = getCurMap
            L18_2 = L18_2()
            if L18_2 == A0_2 then
              L18_2 = closeScrnLog
              L19_2 = "正在找传送人"
              L18_2(L19_2)
              L18_2 = true
              return L18_2
            end
          end
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找传送人"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapPostRabbit = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L1_2 = scrnLog
  L2_2 = "正在找帮派主管"
  L1_2(L2_2)
  L1_2 = 1
  L2_2 = 5
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = getCurMap
    L5_2 = L5_2()
    if L5_2 == A0_2 then
      L6_2 = closeScrnLog
      L7_2 = "正在找帮派主管"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L5_2 == "" then
      L6_2 = fightSystem
      L6_2()
    else
      L6_2 = _ENV["跑商屏蔽"]
      L6_2()
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.npcPos
      L7_2 = L7_2.gangster
      L6_2, L7_2 = L6_2(L7_2)
      if 0 < L6_2 then
        L8_2 = {}
        L9_2 = L6_2 + 70
        L10_2 = L7_2 - 70
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L9_2 = singleTap
        L10_2 = L8_2
        L11_2 = 20
        L9_2(L10_2, L11_2)
        L9_2 = _find_cx
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["进入帮派"]
        L11_2 = {}
        L12_2 = 40
        L13_2 = 50
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L9_2 = L9_2(L10_2, L11_2)
        if L9_2 then
          L9_2 = _tap_tb
          L10_2 = 1409
          L11_2 = 166
          L12_2 = 1811
          L13_2 = 226
          L9_2(L10_2, L11_2, L12_2, L13_2)
        end
        L9_2 = _ENV["_返回"]
        L9_2 = L9_2["地图"]
        L10_2 = "帮派"
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L9_2 = mSleep
          L10_2 = 422
          L9_2(L10_2)
          L9_2 = true
          return L9_2
        end
      else
        L8_2 = _ENV["起号"]
        L8_2 = L8_2["坐标"]
        L8_2, L9_2 = L8_2()
        L10_2 = 384 - L8_2
        L11_2 = 270 - L9_2
        L12_2 = math
        L12_2 = L12_2.abs
        L13_2 = L10_2
        L12_2 = L12_2(L13_2)
        if L12_2 <= 8 then
          L12_2 = math
          L12_2 = L12_2.abs
          L13_2 = L11_2
          L12_2 = L12_2(L13_2)
          if L12_2 <= 8 then
            L12_2 = _ENV["_计算"]
            L12_2 = L12_2["取目标屏幕坐标"]
            L13_2 = "长安城"
            L14_2 = L8_2
            L15_2 = L9_2
            L16_2 = 384
            L17_2 = 270
            L12_2, L13_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
            L9_2 = L13_2
            L8_2 = L12_2
            L12_2 = _tap
            L13_2 = L8_2
            L14_2 = L9_2
            L12_2(L13_2, L14_2)
            L12_2 = _find_cx
            L13_2 = Color
            L13_2 = L13_2["跑商"]
            L13_2 = L13_2["进入帮派"]
            L14_2 = {}
            L15_2 = 50
            L16_2 = 50
            L14_2[1] = L15_2
            L14_2[2] = L16_2
            L12_2 = L12_2(L13_2, L14_2)
            if L12_2 then
              L12_2 = _tap_tb
              L13_2 = 1409
              L14_2 = 166
              L15_2 = 1811
              L16_2 = 226
              L12_2(L13_2, L14_2, L15_2, L16_2)
              L12_2 = _ENV["_返回"]
              L12_2 = L12_2["地图"]
              L13_2 = "帮派"
              L12_2 = L12_2(L13_2)
              if L12_2 then
                L12_2 = mSleep
                L13_2 = 222
                L12_2(L13_2)
                L12_2 = true
                return L12_2
              end
            end
        end
        else
          if L5_2 == "CAC" then
            L12_2 = {}
            L13_2 = 889
            L14_2 = 360
            L12_2[1] = L13_2
            L12_2[2] = L14_2
            L13_2 = singleTap
            L14_2 = L12_2
            L15_2 = 5
            L13_2(L14_2, L15_2)
          end
          L12_2 = _find_cx
          L13_2 = Color
          L13_2 = L13_2["跑商"]
          L13_2 = L13_2["进入帮派"]
          L14_2 = {}
          L15_2 = 50
          L16_2 = 50
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L12_2 = L12_2(L13_2, L14_2)
          if L12_2 then
            L12_2 = _tap_tb
            L13_2 = 1409
            L14_2 = 166
            L15_2 = 1811
            L16_2 = 226
            L12_2(L13_2, L14_2, L15_2, L16_2)
            L12_2 = _ENV["_返回"]
            L12_2 = L12_2["地图"]
            L13_2 = "帮派"
            L12_2 = L12_2(L13_2)
            if L12_2 then
              L12_2 = mSleep
              L13_2 = 222
              L12_2(L13_2)
              L12_2 = true
              return L12_2
            end
          end
        end
      end
    end
  end
  L1_2 = closeScrnLog
  L2_2 = "正在找帮派主管"
  L1_2(L2_2)
  L1_2 = false
  return L1_2
end

tapGangster = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  if A0_2 == "props" then
    L1_2 = 1
    L2_2 = 20
    L3_2 = 1
    for L4_2 = L1_2, L2_2, L3_2 do
      L5_2 = isVacancy
      L6_2 = constPropsPos
      L6_2 = L6_2[L4_2]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = propsVacancy
        L5_2[L4_2] = true
      else
        L5_2 = propsVacancy
        L5_2[L4_2] = false
      end
    end
  elseif A0_2 == "lug" then
    L1_2 = 1
    L2_2 = 20
    L3_2 = 1
    for L4_2 = L1_2, L2_2, L3_2 do
      L5_2 = isVacancy
      L6_2 = constPropsPos
      L6_2 = L6_2[L4_2]
      L5_2 = L5_2(L6_2)
      if L5_2 then
        L5_2 = lugVacancy
        L5_2[L4_2] = true
      else
        L5_2 = lugVacancy
        L5_2[L4_2] = false
      end
    end
  end
end

checkVacancy = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["进入游戏"]
    L1_2 = L1_2["新版签到"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = findAndTap
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.closeActivityBar
    L2_2 = "singleTap"
    L3_2 = 20
    L0_2 = L0_2(L1_2, L2_2, L3_2)
    if L0_2 == false then
      L0_2 = true
      return L0_2
    end
    L0_2 = mSleep
    L1_2 = math
    L1_2 = L1_2.random
    L2_2 = 450
    L3_2 = 500
    L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2)
  end
end

closeActivityBar = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = findAndTap
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.closeChatWnd
    L2_2 = "singleTap"
    L3_2 = 5
    L0_2 = L0_2(L1_2, L2_2, L3_2)
    if L0_2 == false then
      L0_2 = true
      return L0_2
    end
    L0_2 = mSleep
    L1_2 = math
    L1_2 = L1_2.random
    L2_2 = 450
    L3_2 = 500
    L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2)
  end
end

closeChatWnd = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.dialogClose
    L0_2, L1_2 = L0_2(L1_2)
    if not (0 < L0_2) then
      break
    end
    L2_2 = {}
    L3_2 = L0_2 - 1100
    L2_2[1] = L3_2
    L3_2 = L1_2 - 180
    L2_2[2] = L3_2
    L3_2 = singleTap
    L4_2 = L2_2
    L5_2 = 50
    L3_2(L4_2, L5_2)
    L3_2 = _find_tb_cx
    L4_2 = Color
    L4_2 = L4_2["起号"]
    L4_2 = L4_2["绘卷"]
    L5_2 = {}
    L6_2 = 20
    L7_2 = 20
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    L3_2 = L3_2(L4_2, L5_2)
    if not L3_2 then
    else
      break
    end
  end
end

closeAllDialog = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = true
  repeat
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["跑商"]
    L2_2 = L2_2["叉叉被按压"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = mSleep
      L2_2 = 300
      L1_2(L2_2)
    end
    L1_2 = _find_tb
    L2_2 = Color
    L2_2 = L2_2["通用"]
    L2_2 = L2_2["关闭窗口"]
    L3_2 = {}
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -5
    L6_2 = 5
    L4_2 = L4_2(L5_2, L6_2)
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -5
    L7_2 = 5
    L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2, L7_2)
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    L3_2[3] = L6_2
    L3_2[4] = L7_2
    L3_2[5] = L8_2
    L1_2 = L1_2(L2_2, L3_2)
    if L1_2 == false then
      L0_2 = false
      L1_2 = _find_tb_cx
      L2_2 = Color
      L2_2 = L2_2["起号"]
      L2_2 = L2_2["绘卷"]
      L3_2 = {}
      L4_2 = 20
      L5_2 = 20
      L3_2[1] = L4_2
      L3_2[2] = L5_2
      L1_2 = L1_2(L2_2, L3_2)
      if L1_2 then
        break
      end
    end
    L1_2 = mSleep
    L2_2 = math
    L2_2 = L2_2.random
    L3_2 = 250
    L4_2 = 350
    L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L1_2 = mSleep
    L2_2 = _ENV["卡顿掉帧"]
    L2_2 = L2_2 / 2
    L1_2(L2_2)
  until L0_2 == false
end

closeAllWnd = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  L0_2 = isColor
  L1_2 = 18
  L2_2 = 80
  L3_2 = 8664616
  L4_2 = 85
  L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2)
  if L0_2 then
    L0_2 = isColor
    L1_2 = 1359
    L2_2 = 992
    L3_2 = 15761753
    L4_2 = 85
    L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2)
    if L0_2 then
      L0_2 = isColor
      L1_2 = 1829
      L2_2 = 987
      L3_2 = 10536660
      L4_2 = 85
      L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2)
      if L0_2 then
        L0_2 = isColor
        L1_2 = 1820
        L2_2 = 311
        L3_2 = 4099493
        L4_2 = 85
        L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2)
        if L0_2 then
      end
    end
  end
  else
    L0_2 = closeAllDialog
    L0_2()
    L0_2 = closeAllWnd
    L0_2()
    L0_2 = closeActivityBar
    L0_2()
  end
end

cleanupINF = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.logginBtn
    L0_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L0_2 = lua_exit
      L0_2()
    else
      L0_2 = findAndTap
      L1_2 = color
      L1_2 = L1_2.btn
      L1_2 = L1_2.systemBtn
      L2_2 = "singleTap"
      L3_2 = 30
      L0_2 = L0_2(L1_2, L2_2, L3_2)
      if L0_2 then
        L0_2 = mSleep
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 250
        L3_2 = 300
        L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
        L0_2(L1_2, L2_2, L3_2, L4_2)
        L0_2 = findAndTap
        L1_2 = color
        L1_2 = L1_2.btn
        L1_2 = L1_2.logoutBtn
        L2_2 = "singleTap"
        L3_2 = 30
        L0_2 = L0_2(L1_2, L2_2, L3_2)
        if L0_2 then
          L0_2 = mSleep
          L1_2 = math
          L1_2 = L1_2.random
          L2_2 = 250
          L3_2 = 300
          L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
          L0_2(L1_2, L2_2, L3_2, L4_2)
          L0_2 = findAndTap
          L1_2 = color
          L1_2 = L1_2.btn
          L1_2 = L1_2.sureLogout
          L2_2 = "singleTap"
          L3_2 = 30
          L0_2(L1_2, L2_2, L3_2)
          L0_2 = mSleep
          L1_2 = math
          L1_2 = L1_2.random
          L2_2 = 4500
          L3_2 = 5000
          L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
          L0_2(L1_2, L2_2, L3_2, L4_2)
        end
      else
        L0_2 = cleanupINF
        L0_2()
        L0_2 = findAndTap
        L1_2 = color
        L1_2 = L1_2.expandBtn
        L2_2 = "singleTap"
        L3_2 = 30
        L0_2(L1_2, L2_2, L3_2)
      end
    end
  end
end

logOut = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L0_2 = false
  L1_2 = 0
  L2_2 = 0
  while true do
    L3_2 = -1
    L4_2 = -1
    L5_2 = ""
    L6_2 = findFeat
    L7_2 = color
    L7_2 = L7_2.VERINF
    L7_2 = L7_2.VER4Man_3
    L6_2 = L6_2(L7_2)
    if 0 < L6_2 then
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.VERINF
      L7_2 = L7_2.VER4Man_3
      L6_2, L7_2 = L6_2(L7_2)
      L4_2 = L7_2
      L3_2 = L6_2
      L5_2 = "3"
    else
      L6_2 = findFeat
      L7_2 = color
      L7_2 = L7_2.VERINF
      L7_2 = L7_2.VER4Man_1
      L6_2 = L6_2(L7_2)
      if 0 < L6_2 then
        L6_2 = findFeat
        L7_2 = color
        L7_2 = L7_2.VERINF
        L7_2 = L7_2.VER4Man_1
        L6_2, L7_2 = L6_2(L7_2)
        L4_2 = L7_2
        L3_2 = L6_2
        L5_2 = "1"
      else
        L6_2 = findFeat
        L7_2 = color
        L7_2 = L7_2.VERINF
        L7_2 = L7_2.VER4Man_2
        L6_2 = L6_2(L7_2)
        if 0 < L6_2 then
          L6_2 = findFeat
          L7_2 = color
          L7_2 = L7_2.VERINF
          L7_2 = L7_2.VER4Man_2
          L6_2, L7_2 = L6_2(L7_2)
          L4_2 = L7_2
          L3_2 = L6_2
          L5_2 = "2"
        else
          if L0_2 then
            L6_2 = closeScrnLog
            L7_2 = "弹出验证"
            L6_2(L7_2)
          end
          L6_2 = true
          return L6_2
        end
      end
    end
    L6_2 = _find
    L7_2 = Color
    L7_2 = L7_2["验证"]
    L7_2 = L7_2["战斗中A"]
    L6_2 = L6_2(L7_2)
    if not L6_2 then
      L6_2 = _find
      L7_2 = Color
      L7_2 = L7_2["验证"]
      L7_2 = L7_2["战斗中B"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
      else
        L6_2 = true
        return L6_2
      end
    end
    L6_2 = scrnLog
    L7_2 = "弹出验证"
    L6_2(L7_2)
    L0_2 = true
    L6_2 = mSleep
    L7_2 = 300
    L6_2(L7_2)
    L6_2 = {}
    L7_2 = L3_2 - 130
    L8_2 = L4_2 + 100
    L9_2 = L3_2 + 690
    L10_2 = L4_2 + 330
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L6_2[3] = L9_2
    L6_2[4] = L10_2
    VERArea = L6_2
    L6_2 = snapshot
    L7_2 = "VER.png"
    L8_2 = VERArea
    L8_2 = L8_2[1]
    L9_2 = VERArea
    L9_2 = L9_2[2]
    L10_2 = VERArea
    L10_2 = L10_2[3]
    L11_2 = VERArea
    L11_2 = L11_2[4]
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
    L6_2 = postData
    L7_2 = CARD_NUM
    L8_2 = "x4002"
    L9_2 = image_dir
    L10_2 = "VER.png"
    L9_2 = L9_2 .. L10_2
    L6_2 = L6_2(L7_2, L8_2, L9_2)
    L7_2 = L6_2.err_code
    if 0 < L7_2 then
      L7_2 = nLog
      L8_2 = "Call failed: "
      L9_2 = L6_2.err_msg
      L8_2 = L8_2 .. L9_2
      L7_2(L8_2)
    else
      L7_2 = L6_2.ret
      L8_2 = L7_2[1]
      if L8_2 then
        L8_2 = nLog
        L9_2 = "Succeed: "
        L10_2 = [[

No: ]]
        L11_2 = L7_2[2]
        L12_2 = [[

Pos: ]]
        L13_2 = L7_2[3]
        L13_2 = L13_2[1]
        L14_2 = ", "
        L15_2 = L7_2[3]
        L15_2 = L15_2[2]
        L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
        L8_2(L9_2)
        L8_2 = nLog
        L9_2 = "Done,click the correct one"
        L8_2(L9_2)
        L8_2 = scrnLog
        L9_2 = "点击第"
        L10_2 = tostring
        L11_2 = tonumber
        L12_2 = L7_2[2]
        L11_2 = L11_2(L12_2)
        L11_2 = L11_2 + 1
        L10_2 = L10_2(L11_2)
        L11_2 = "个人物"
        L9_2 = L9_2 .. L10_2 .. L11_2
        L8_2(L9_2)
        L8_2 = {}
        L9_2 = L3_2 - 130
        L10_2 = L7_2[3]
        L10_2 = L10_2[1]
        L9_2 = L9_2 + L10_2
        L10_2 = L4_2 + 100
        L11_2 = L7_2[3]
        L11_2 = L11_2[2]
        L10_2 = L10_2 + L11_2
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L9_2 = singleTap
        L10_2 = L8_2
        L11_2 = 15
        L9_2(L10_2, L11_2)
        L9_2 = mSleep
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 350
        L12_2 = 400
        L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L10_2(L11_2, L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
        L9_2 = closeScrnLog
        L10_2 = "点击第"
        L11_2 = tostring
        L12_2 = tonumber
        L13_2 = L7_2[2]
        L12_2 = L12_2(L13_2)
        L12_2 = L12_2 + 1
        L11_2 = L11_2(L12_2)
        L12_2 = "个人物"
        L10_2 = L10_2 .. L11_2 .. L12_2
        L9_2(L10_2)
      else
        L8_2 = nLog
        L9_2 = "Failed:"
        L10_2 = L6_2.tid
        L9_2 = L9_2 .. L10_2
        L8_2(L9_2)
        L8_2 = nLog
        L9_2 = "Bill："
        L10_2 = L6_2.tid
        L9_2 = L9_2 .. L10_2
        L8_2(L9_2)
      end
    end
    L2_2 = L2_2 + 1
    if 5 < L2_2 then
      L7_2 = _ENV["_验证"]
      L7_2 = L7_2["震动提示"]
      L8_2 = "朝向"
      L9_2 = "请手动完成 > 验证"
      L7_2(L8_2, L9_2)
      L7_2 = mSleep
      L8_2 = 5000
      L7_2(L8_2)
    end
  end
end

VER4Man = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2
  L0_2 = 0
  L1_2 = false
  while true do
    L2_2 = _find_tb
    L3_2 = Color
    L3_2 = L3_2["验证"]
    L3_2 = L3_2["漂浮"]
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    L5_2 = findFeat
    L6_2 = color
    L6_2 = L6_2.VERINF
    L6_2 = L6_2.VERmoveWord
    L5_2, L6_2 = L5_2(L6_2)
    if 0 < L5_2 and L2_2 then
      L7_2 = findFeat
      L8_2 = color
      L8_2 = L8_2.VERINF
      L8_2 = L8_2.VERidiom
      L7_2, L8_2 = L7_2(L8_2)
      if 0 < L7_2 then
        L9_2 = true
        return L9_2
      end
      L9_2 = scrnLog
      L10_2 = "弹出验证"
      L9_2(L10_2)
      L1_2 = true
      L9_2 = {}
      L10_2 = L5_2 - 221
      L11_2 = L6_2 - 36
      L12_2 = L5_2 + 507
      L13_2 = L6_2 + 300
      L9_2[1] = L10_2
      L9_2[2] = L11_2
      L9_2[3] = L12_2
      L9_2[4] = L13_2
      VERArea = L9_2
      L9_2 = snapshot
      L10_2 = "VER.png"
      L11_2 = VERArea
      L11_2 = L11_2[1]
      L12_2 = VERArea
      L12_2 = L12_2[2]
      L13_2 = VERArea
      L13_2 = L13_2[3]
      L14_2 = VERArea
      L14_2 = L14_2[4]
      L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
      L9_2 = {}
      L9_2.ref_char_roi = "405,0,135,91"
      L9_2.min_dis = "20"
      L10_2 = postData
      L11_2 = CARD_NUM
      L12_2 = "3001"
      L13_2 = image_dir
      L14_2 = "VER.png"
      L13_2 = L13_2 .. L14_2
      L14_2 = L9_2
      L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2)
      L11_2 = L10_2.err_code
      if 0 < L11_2 then
        L11_2 = nLog
        L12_2 = "调用失败: "
        L13_2 = L10_2.err_msg
        L12_2 = L12_2 .. L13_2
        L13_2 = time
        L11_2(L12_2, L13_2)
      else
        L11_2 = L10_2.ret
        L12_2 = L11_2[1]
        if L12_2 then
          L12_2 = nLog
          L13_2 = "识别成功: "
          L14_2 = "\n坐标x,y: "
          L15_2 = L11_2[2]
          L15_2 = L15_2[1]
          L16_2 = ", "
          L17_2 = L11_2[2]
          L17_2 = L17_2[2]
          L18_2 = "\n颜色r,g,b: "
          L19_2 = L11_2[4]
          L19_2 = L19_2[1]
          L20_2 = ", "
          L21_2 = L11_2[4]
          L21_2 = L21_2[2]
          L22_2 = ", "
          L23_2 = L11_2[4]
          L23_2 = L23_2[3]
          L13_2 = L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2 .. L20_2 .. L21_2 .. L22_2 .. L23_2
          L14_2 = time
          L12_2(L13_2, L14_2)
          L12_2 = {}
          L13_2 = L5_2 - 221
          L14_2 = L11_2[2]
          L14_2 = L14_2[1]
          L13_2 = L13_2 + L14_2
          L14_2 = L6_2 - 36
          L15_2 = L11_2[2]
          L15_2 = L15_2[2]
          L14_2 = L14_2 + L15_2
          L12_2[1] = L13_2
          L12_2[2] = L14_2
          L13_2 = singleTap
          L14_2 = L12_2
          L13_2(L14_2)
          L13_2 = mSleep
          L14_2 = math
          L14_2 = L14_2.random
          L15_2 = 150
          L16_2 = 300
          L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2 = L14_2(L15_2, L16_2)
          L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2)
          L13_2 = 1
          L14_2 = 10
          L15_2 = 1
          for L16_2 = L13_2, L14_2, L15_2 do
            L17_2 = _find
            L18_2 = Color
            L18_2 = L18_2["验证"]
            L18_2 = L18_2["回答正确"]
            L17_2 = L17_2(L18_2)
            if L17_2 then
              L17_2 = true
              return L17_2
            else
              L17_2 = _find
              L18_2 = Color
              L18_2 = L18_2["验证"]
              L18_2 = L18_2["回答错误"]
              L17_2 = L17_2(L18_2)
              if L17_2 then
                L17_2 = _print
                L18_2 = "无名漂浮验证：回答错误"
                L17_2(L18_2)
                L17_2 = mSleep
                L18_2 = 2000
                L17_2(L18_2)
              end
            end
            L17_2 = mSleep
            L18_2 = 300
            L17_2(L18_2)
          end
          L0_2 = L0_2 + 1
          if 10 < L0_2 then
            L13_2 = _print
            L14_2 = "无名错误3次,请手动处理！"
            L13_2(L14_2)
            L13_2 = _ENV["_验证"]
            L13_2 = L13_2["震动提示"]
            L14_2 = "漂浮"
            L15_2 = "请手动完成 > 漂浮验证"
            L13_2(L14_2, L15_2)
            L13_2 = true
            return L13_2
          end
          L13_2 = _cmp_tb
          L14_2 = Color
          L14_2 = L14_2["验证结束"]
          L14_2 = L14_2["主界面"]
          L13_2 = L13_2(L14_2)
          if L13_2 then
            L13_2 = closeScrnLog
            L14_2 = "弹出验证"
            L13_2(L14_2)
            L13_2 = true
            return L13_2
          end
        else
          L12_2 = nLog
          L13_2 = "识别失败，流水号："
          L14_2 = L10_2.tid
          L13_2 = L13_2 .. L14_2
          L14_2 = time
          L12_2(L13_2, L14_2)
        end
      end
    else
      if L1_2 then
        L7_2 = closeScrnLog
        L8_2 = "弹出验证"
        L7_2(L8_2)
      end
      L7_2 = true
      return L7_2
    end
  end
end

VERmoveWord = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2
  L0_2 = false
  L1_2 = 0
  while true do
    L2_2 = findFeat
    L3_2 = color
    L3_2 = L3_2.VERINF
    L3_2 = L3_2.VERidiom
    L2_2, L3_2 = L2_2(L3_2)
    if 0 < L2_2 then
      L4_2 = scrnLog
      L5_2 = "弹出成语验证"
      L4_2(L5_2)
      L0_2 = true
      L4_2 = {}
      L5_2 = L2_2
      L6_2 = L3_2 + 250
      L7_2 = L2_2 + 430
      L8_2 = L3_2 + 380
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L4_2[3] = L7_2
      L4_2[4] = L8_2
      VERArea = L4_2
      L4_2 = 1
      L5_2 = 4
      L6_2 = 1
      for L7_2 = L4_2, L5_2, L6_2 do
        L8_2 = keepScreen
        L9_2 = false
        L8_2(L9_2)
        L8_2 = scrnLog
        L9_2 = "第"
        L10_2 = L7_2
        L11_2 = "字"
        L9_2 = L9_2 .. L10_2 .. L11_2
        L8_2(L9_2)
        L8_2 = snapshot
        L9_2 = "VER.png"
        L10_2 = VERArea
        L10_2 = L10_2[1]
        L11_2 = VERArea
        L11_2 = L11_2[2]
        L12_2 = VERArea
        L12_2 = L12_2[3]
        L13_2 = VERArea
        L13_2 = L13_2[4]
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2)
        L8_2 = postData
        L9_2 = CARD_NUM
        L10_2 = "x8054"
        L11_2 = image_dir
        L12_2 = "VER.png"
        L11_2 = L11_2 .. L12_2
        L8_2 = L8_2(L9_2, L10_2, L11_2)
        L9_2 = L8_2.err_code
        if 0 < L9_2 then
          L9_2 = nLog
          L10_2 = "调用失败: "
          L11_2 = L8_2.err_msg
          L10_2 = L10_2 .. L11_2
          L11_2 = time
          L9_2(L10_2, L11_2)
          L9_2 = scrnLog
          L10_2 = "服务器调用失败"
          L9_2(L10_2)
          L9_2 = mSleep
          L10_2 = 1000
          L9_2(L10_2)
          L9_2 = _tap
          L10_2 = 664
          L11_2 = 405
          L12_2 = 1
          L13_2 = 0
          L9_2(L10_2, L11_2, L12_2, L13_2)
          L9_2 = _Sleep
          L10_2 = 400
          L11_2 = 500
          L9_2(L10_2, L11_2)
          L9_2 = _tap
          L10_2 = 754
          L11_2 = 404
          L12_2 = 1
          L13_2 = 0
          L9_2(L10_2, L11_2, L12_2, L13_2)
          L9_2 = mSleep
          L10_2 = 1000
          L9_2(L10_2)
          L9_2 = _tap
          L10_2 = 837
          L11_2 = 699
          L9_2(L10_2, L11_2)
          L9_2 = _Sleep
          L10_2 = 400
          L11_2 = 500
          L9_2(L10_2, L11_2)
          L9_2 = _ENV["_验证"]
          L9_2 = L9_2["成语"]
          L9_2 = L9_2["验证"]
          return L9_2()
        else
          L9_2 = L8_2.ret
          L10_2 = L9_2[1]
          if L10_2 then
            L10_2 = nLog
            L11_2 = "识别成功: "
            L12_2 = "\n成语: "
            L13_2 = L9_2[2]
            L14_2 = "\n排序: "
            L15_2 = L9_2[3]
            L15_2 = L15_2[1]
            L16_2 = L9_2[3]
            L16_2 = L16_2[2]
            L17_2 = L9_2[3]
            L17_2 = L17_2[3]
            L18_2 = L9_2[3]
            L18_2 = L18_2[4]
            L19_2 = "\n第1个字坐标: "
            L20_2 = L9_2[4]
            L20_2 = L20_2[1]
            L20_2 = L20_2[1]
            L21_2 = ", "
            L22_2 = L9_2[4]
            L22_2 = L22_2[1]
            L22_2 = L22_2[2]
            L23_2 = "\n第2个字坐标: "
            L24_2 = L9_2[4]
            L24_2 = L24_2[2]
            L24_2 = L24_2[1]
            L25_2 = ", "
            L26_2 = L9_2[4]
            L26_2 = L26_2[2]
            L26_2 = L26_2[2]
            L27_2 = "\n第3个字坐标: "
            L28_2 = L9_2[4]
            L28_2 = L28_2[3]
            L28_2 = L28_2[1]
            L29_2 = ", "
            L30_2 = L9_2[4]
            L30_2 = L30_2[3]
            L30_2 = L30_2[2]
            L31_2 = "\n第4个字坐标: "
            L32_2 = L9_2[4]
            L32_2 = L32_2[4]
            L32_2 = L32_2[1]
            L33_2 = ", "
            L34_2 = L9_2[4]
            L34_2 = L34_2[4]
            L34_2 = L34_2[2]
            L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2 .. L20_2 .. L21_2 .. L22_2 .. L23_2 .. L24_2 .. L25_2 .. L26_2 .. L27_2 .. L28_2 .. L29_2 .. L30_2 .. L31_2 .. L32_2 .. L33_2 .. L34_2
            L12_2 = time
            L10_2(L11_2, L12_2)
            L10_2 = {}
            L11_2 = L9_2[4]
            L11_2 = L11_2[L7_2]
            L11_2 = L11_2[1]
            L11_2 = L2_2 + L11_2
            L12_2 = L3_2 + 250
            L13_2 = L9_2[4]
            L13_2 = L13_2[L7_2]
            L13_2 = L13_2[2]
            L12_2 = L12_2 + L13_2
            L10_2[1] = L11_2
            L10_2[2] = L12_2
            L11_2 = singleTap
            L12_2 = L10_2
            L11_2(L12_2)
            L11_2 = scrnLog
            L12_2 = "点击第"
            L13_2 = L7_2
            L14_2 = "字"
            L12_2 = L12_2 .. L13_2 .. L14_2
            L11_2(L12_2)
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 300
            L14_2 = 350
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
            if L7_2 == 2 then
              L11_2 = findImage
              L12_2 = "VER.png"
              L13_2 = VERArea
              L13_2 = L13_2[1]
              L14_2 = VERArea
              L14_2 = L14_2[2]
              L15_2 = VERArea
              L15_2 = L15_2[3]
              L16_2 = VERArea
              L16_2 = L16_2[4]
              L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
              if 0 < L11_2 then
                L11_2 = {}
                L12_2 = L9_2[4]
                L12_2 = L12_2[1]
                L12_2 = L12_2[1]
                L12_2 = L2_2 + L12_2
                L13_2 = L3_2 + 250
                L14_2 = L9_2[4]
                L14_2 = L14_2[1]
                L14_2 = L14_2[2]
                L13_2 = L13_2 + L14_2
                L11_2[1] = L12_2
                L11_2[2] = L13_2
                L10_2 = L11_2
                L11_2 = singleTap
                L12_2 = L10_2
                L11_2(L12_2)
                L11_2 = mSleep
                L12_2 = math
                L12_2 = L12_2.random
                L13_2 = 300
                L14_2 = 350
                L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L12_2(L13_2, L14_2)
                L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
              end
            elseif L7_2 == 4 then
              L11_2 = findImage
              L12_2 = "VER.png"
              L13_2 = VERArea
              L13_2 = L13_2[1]
              L14_2 = VERArea
              L14_2 = L14_2[2]
              L15_2 = VERArea
              L15_2 = L15_2[3]
              L16_2 = VERArea
              L16_2 = L16_2[4]
              L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
              if 0 < L11_2 then
                L11_2 = {}
                L12_2 = L9_2[4]
                L12_2 = L12_2[3]
                L12_2 = L12_2[1]
                L12_2 = L2_2 + L12_2
                L13_2 = L3_2 + 250
                L14_2 = L9_2[4]
                L14_2 = L14_2[3]
                L14_2 = L14_2[2]
                L13_2 = L13_2 + L14_2
                L11_2[1] = L12_2
                L11_2[2] = L13_2
                L10_2 = L11_2
                L11_2 = singleTap
                L12_2 = L10_2
                L11_2(L12_2)
                L11_2 = mSleep
                L12_2 = math
                L12_2 = L12_2.random
                L13_2 = 300
                L14_2 = 350
                L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L12_2(L13_2, L14_2)
                L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
              end
            end
          else
            L10_2 = scrnLog
            L11_2 = "识别失败重置"
            L10_2(L11_2)
            L10_2 = nLog
            L11_2 = "识别失败，流水号："
            L12_2 = L8_2.tid
            L11_2 = L11_2 .. L12_2
            L12_2 = time
            L10_2(L11_2, L12_2)
            L10_2 = {}
            L11_2 = L2_2 + 86
            L12_2 = L3_2 + 323
            L10_2[1] = L11_2
            L10_2[2] = L12_2
            L11_2 = singleTap
            L12_2 = L10_2
            L13_2 = 20
            L11_2(L12_2, L13_2)
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 300
            L14_2 = 350
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
            L11_2 = {}
            L12_2 = L2_2 + 279
            L13_2 = L3_2 + 600
            L11_2[1] = L12_2
            L11_2[2] = L13_2
            L12_2 = singleTap
            L13_2 = L11_2
            L14_2 = 20
            L12_2(L13_2, L14_2)
            L12_2 = mSleep
            L13_2 = math
            L13_2 = L13_2.random
            L14_2 = 300
            L15_2 = 350
            L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L13_2(L14_2, L15_2)
            L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
            L12_2 = VERidiom
            return L12_2()
          end
        end
        if L7_2 == 4 then
          L9_2 = scrnLog
          L10_2 = "点击确定"
          L9_2(L10_2)
          L9_2 = {}
          L10_2 = L2_2 + 682
          L11_2 = L3_2 + 600
          L9_2[1] = L10_2
          L9_2[2] = L11_2
          L10_2 = singleTap
          L11_2 = L9_2
          L12_2 = 20
          L10_2(L11_2, L12_2)
          L10_2 = mSleep
          L11_2 = math
          L11_2 = L11_2.random
          L12_2 = 700
          L13_2 = 850
          L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2 = L11_2(L12_2, L13_2)
          L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2)
        end
      end
      L1_2 = L1_2 + 1
      if 5 < L1_2 then
        L4_2 = _print
        L5_2 = "无名错误3次,请手动处理！"
        L4_2(L5_2)
        L4_2 = _ENV["_验证"]
        L4_2 = L4_2["震动提示"]
        L5_2 = "漂浮"
        L6_2 = "请手动完成 > 漂浮验证"
        L4_2(L5_2, L6_2)
        L4_2 = true
        return L4_2
      end
    else
      if L0_2 then
        L4_2 = closeScrnLog
        L5_2 = "弹出验证"
        L4_2(L5_2)
      end
      L4_2 = true
      return L4_2
    end
  end
end

VERidiom = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = findFeat
  L1_2 = color
  L1_2 = L1_2.VERINF
  L1_2 = L1_2.VER4Man_3
  L0_2 = L0_2(L1_2)
  if 0 < L0_2 then
    L0_2 = VER4Man
    L0_2()
  else
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.VERINF
    L1_2 = L1_2.VER4Man_1
    L0_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L0_2 = VER4Man
      L0_2()
    else
      L0_2 = findFeat
      L1_2 = color
      L1_2 = L1_2.VERINF
      L1_2 = L1_2.VER4Man_2
      L0_2 = L0_2(L1_2)
      if 0 < L0_2 then
        L0_2 = VER4Man
        L0_2()
      else
      end
    end
  end
end

_ENV["检测四小人"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = findFeat
  L1_2 = color
  L1_2 = L1_2.VERINF
  L1_2 = L1_2.VER4Man_3
  L0_2 = L0_2(L1_2)
  if 0 < L0_2 then
    L0_2 = _ENV["_验证"]
    L0_2 = L0_2["朝向"]
    L0_2()
  else
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.VERINF
    L1_2 = L1_2.VER4Man_1
    L0_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L0_2 = _ENV["_验证"]
      L0_2 = L0_2["朝向"]
      L0_2()
    else
      L0_2 = findFeat
      L1_2 = color
      L1_2 = L1_2.VERINF
      L1_2 = L1_2.VER4Man_2
      L0_2 = L0_2(L1_2)
      if 0 < L0_2 then
        L0_2 = _ENV["_验证"]
        L0_2 = L0_2["朝向"]
        L0_2()
      else
        L0_2 = nLog
        L1_2 = "四小人没了"
        L0_2(L1_2)
      end
    end
  end
end

_ENV["服务器检测四小人"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  while true do
    L0_2 = findFeat
    L1_2 = fightingINF
    L1_2 = L1_2.fightFeat
    L0_2, L1_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L2_2 = scrnLog
      L3_2 = "检测到正在战斗"
      L2_2(L3_2)
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.fighting
      L3_2 = L3_2.shrinkAuto
      L2_2, L3_2 = L2_2(L3_2)
      L4_2 = findFeat
      L5_2 = color
      L5_2 = L5_2.fighting
      L5_2 = L5_2.reset
      L4_2, L5_2 = L4_2(L5_2)
      if 0 < L2_2 and 0 < L4_2 then
        L6_2 = {}
        L7_2 = L2_2
        L8_2 = L3_2
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L7_2 = singleTap
        L8_2 = L6_2
        L9_2 = 15
        L7_2(L8_2, L9_2)
        L7_2 = mSleep
        L8_2 = math
        L8_2 = L8_2.random
        L9_2 = 450
        L10_2 = 500
        L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L8_2(L9_2, L10_2)
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L7_2 = {}
        L8_2 = 1243
        L9_2 = 986
        L7_2[1] = L8_2
        L7_2[2] = L9_2
        L8_2 = {}
        L9_2 = L6_2
        L10_2 = L7_2
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L9_2 = slide
        L10_2 = L8_2
        L9_2(L10_2)
        L9_2 = mSleep
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 350
        L12_2 = 400
        L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
        L9_2(L10_2, L11_2, L12_2, L13_2)
      elseif L2_2 < 0 and L4_2 < 0 then
        L6_2 = findAndTap
        L7_2 = color
        L7_2 = L7_2.fighting
        L7_2 = L7_2.auto
        L8_2 = "singleTap"
        L9_2 = 20
        L6_2 = L6_2(L7_2, L8_2, L9_2)
        if L6_2 == false then
          L6_2 = cleanupINF
          L6_2()
        end
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 350
        L9_2 = 400
        L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      end
    else
      L2_2 = closeScrnLog
      L3_2 = "检测到正在战斗"
      L2_2(L3_2)
      L2_2 = supplementYB
      L2_2()
      L2_2 = cleanupINF
      L2_2()
      L2_2 = mSleep
      L3_2 = math
      L3_2 = L3_2.random
      L4_2 = 350
      L5_2 = 400
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L3_2(L4_2, L5_2)
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      L2_2 = true
      return L2_2
    end
  end
end

_ENV["检测战斗1"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _ENV["UI_角色回复"]
  L1_2 = _ENV["UI_宠物回复"]
  L2_2 = multiColor
  L3_2 = _ENV["颜色"]
  L3_2 = L3_2["加血"]
  L3_2 = L3_2["角色"]
  L3_2 = L3_2[L0_2]
  L2_2 = L2_2(L3_2)
  if L2_2 == true then
    L2_2 = {}
    L3_2 = 1867
    L4_2 = 40
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L3_2 = singleTap
    L4_2 = L2_2
    L5_2 = 20
    L3_2(L4_2, L5_2)
    L3_2 = mSleep
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 450
    L6_2 = 550
    L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = findAndTap
    L4_2 = color
    L4_2 = L4_2.btn
    L4_2 = L4_2.addCharBlood
    L5_2 = "singleTap"
    L6_2 = 20
    L3_2(L4_2, L5_2, L6_2)
    L3_2 = mSleep
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 450
    L6_2 = 550
    L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
    L3_2(L4_2, L5_2, L6_2, L7_2)
  end
  L2_2 = multiColor
  L3_2 = _ENV["颜色"]
  L3_2 = L3_2["加血"]
  L3_2 = L3_2["宠物"]
  L3_2 = L3_2[L1_2]
  L2_2 = L2_2(L3_2)
  if L2_2 == true then
    L2_2 = {}
    L3_2 = 1622
    L4_2 = 30
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    L3_2 = singleTap
    L4_2 = L2_2
    L5_2 = 20
    L3_2(L4_2, L5_2)
    L3_2 = mSleep
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 450
    L6_2 = 550
    L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
    L3_2(L4_2, L5_2, L6_2, L7_2)
    L3_2 = findAndTap
    L4_2 = color
    L4_2 = L4_2.btn
    L4_2 = L4_2.addPetBlood
    L5_2 = "singleTap"
    L6_2 = 20
    L3_2(L4_2, L5_2, L6_2)
    L3_2 = mSleep
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 450
    L6_2 = 550
    L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
    L3_2(L4_2, L5_2, L6_2, L7_2)
  end
end

supplementYB = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L0_2 = _find_tb
  L1_2 = Color
  L1_2 = L1_2["起号"]
  L1_2 = L1_2["绘卷"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
  else
    L0_2 = _ENV["服务器检测四小人"]
    L0_2()
    L0_2 = _ENV["检测四小人"]
    L0_2()
    while true do
      L0_2 = findFeat
      L1_2 = fightingINF
      L1_2 = L1_2.fightFeat
      L0_2, L1_2 = L0_2(L1_2)
      if 0 < L0_2 then
        L2_2 = scrnLog
        L3_2 = "检测到正在战斗"
        L2_2(L3_2)
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.fighting
        L3_2 = L3_2.shrinkAuto
        L2_2, L3_2 = L2_2(L3_2)
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.fighting
        L5_2 = L5_2.reset
        L4_2, L5_2 = L4_2(L5_2)
        if 0 < L2_2 and 0 < L4_2 then
          L6_2 = {}
          L7_2 = L2_2
          L8_2 = L3_2
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L7_2 = singleTap
          L8_2 = L6_2
          L9_2 = 15
          L7_2(L8_2, L9_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 450
          L10_2 = 500
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
          L7_2 = {}
          L8_2 = 379
          L9_2 = 63
          L7_2[1] = L8_2
          L7_2[2] = L9_2
          L8_2 = {}
          L9_2 = L6_2
          L10_2 = L7_2
          L8_2[1] = L9_2
          L8_2[2] = L10_2
          L9_2 = slide
          L10_2 = L8_2
          L9_2(L10_2)
          L9_2 = mSleep
          L10_2 = math
          L10_2 = L10_2.random
          L11_2 = 350
          L12_2 = 400
          L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
          L9_2(L10_2, L11_2, L12_2, L13_2)
        elseif L2_2 < 0 and L4_2 < 0 then
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.fighting
          L7_2 = L7_2.auto
          L8_2 = "singleTap"
          L9_2 = 20
          L6_2 = L6_2(L7_2, L8_2, L9_2)
          if L6_2 == false then
            L6_2 = cleanupINF
            L6_2()
          end
          L6_2 = mSleep
          L7_2 = math
          L7_2 = L7_2.random
          L8_2 = 350
          L9_2 = 400
          L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L7_2(L8_2, L9_2)
          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        end
        dodgeTime = -1
        L6_2 = 1
        L7_2 = 5
        L8_2 = 1
        for L9_2 = L6_2, L7_2, L8_2 do
          L10_2 = _find
          L11_2 = Color
          L11_2 = L11_2["战斗"]
          L11_2 = L11_2["取消自动战斗"]
          L10_2 = L10_2(L11_2)
          if L10_2 then
            L10_2 = _Sleep
            L11_2 = 200
            L12_2 = 300
            L10_2(L11_2, L12_2)
          end
          L10_2 = mSleep
          L11_2 = 50
          L10_2(L11_2)
        end
      else
        L2_2 = _ENV["判断收缩充值"]
        L2_2()
        L2_2 = isColor
        L3_2 = 1391
        L4_2 = 16
        L5_2 = 8682797
        L6_2 = 95
        L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2)
        if L2_2 then
          L2_2 = tap
          L3_2 = 969
          L4_2 = 36
          L2_2(L3_2, L4_2)
          L2_2 = mSleep
          L3_2 = 200
          L2_2(L3_2)
        end
        L2_2 = closeScrnLog
        L3_2 = "检测到正在战斗"
        L2_2(L3_2)
        L2_2 = cleanupINF
        L2_2()
        L2_2 = dodgeSystem
        L2_2()
        L2_2 = true
        return L2_2
      end
    end
  end
end

fightSystem = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["发现错误操作"]
  L0_2 = L0_2()
  if L0_2 == "错误操作" then
    L0_2 = _ENV["_游戏"]
    L0_2 = L0_2["验证错误"]
    L0_2()
  else
    L0_2 = _ENV["发现错误操作"]
    L0_2 = L0_2()
    if L0_2 == "成语错误" then
      L0_2 = _ENV["_游戏"]
      L0_2 = L0_2["验证错误"]
      L0_2()
    else
      L0_2 = _ENV["发现错误操作"]
      L0_2 = L0_2()
      if L0_2 == "漂浮错误" then
        L0_2 = _ENV["_游戏"]
        L0_2 = L0_2["验证错误"]
        L0_2()
      else
        L0_2 = mSleep
        L1_2 = 10
        L0_2(L1_2)
      end
    end
  end
end

_ENV["验证错了"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = {}
  L1_2 = "0180041c0071e0030fff107ff000010000100000001101c3107f31047b1043b1343b3b43bfe43bfe43bfe43b3f43b1b43b107fb103c310001100000000000000000000000000040600c0f0fffff0e3e0041c000300007100071071f307b3307f1314f33b47f3f4403f4407f4403f44f3f4673f4f1336f1317f1300131000f10$错误操作$371$20$51"
  L2_2 = "003800038060380703c0783c03c3ff1c3ff000000000000000000000000000030000303fc307fc30ffe30f0e30e0e30e0e30e0e30e0e38e0e3fe0e3fe0e3fe0e38e0e30e0e30e0e30f0e30ffe307fc303f8300000000000000000000000000000000000003801038030380303c03ffffffffff07e1f0380c0380c00008000f8001f8003fc3c38cfe30cf630cc230cc238cc23fcc21f8c20f8c2001c2003c2000c23f8c23fcc23dcc238cfe30cfe30cfe30c0038c003fc000f80000000000$漂浮错误$520$20$76"
  L3_2 = "00380003806038070380783c0383ff1c3ff000000000000000000000000000020000303f8307fc30ffe30f0e30e0e30e0e30e0e30e0e38e0e3fe0e3fe0e3fe0e38e0e30e0e30e0e30f0e30ffe307fc303f8300002000000000000000000000000000000003801038030380303c03ffffffffff3ffff0380e0380c00008000f8001f8003fc3839cfe30cfe30cc230cc238cc23fcc23f8c21f8c2001c2003c2001c23f8c23f8c23dcc238cf630cfe30cfe30c0038c003fc001f80006000000$成语错误$530$20$76"
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L1_2 = addTSOcrDictEx
  L2_2 = L0_2
  L1_2 = L1_2(L2_2)
  L2_2 = tsOcrText
  L3_2 = L1_2
  L4_2 = 518
  L5_2 = 116
  L6_2 = 852
  L7_2 = 514
  L8_2 = "FFFF5D , 2A2A5D # FFFF5E , 3E3E5E # FFFF5E , 3E3E5E # FFFF5E , 3E3E5E"
  L9_2 = 85
  L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  return L2_2
end

_ENV["发现错误操作"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = needDodge
  if L0_2 then
    L0_2 = dodgeTime
    if L0_2 ~= -1 then
      L0_2 = os
      L0_2 = L0_2.time
      L0_2 = L0_2()
      L1_2 = dodgeTime
      L0_2 = L0_2 - L1_2
      L0_2 = L0_2 / 60
      if not (25 < L0_2) then
        goto lbl_136
      end
    end
    L0_2 = scrnLog
    L1_2 = "使用摄妖香"
    L0_2(L1_2)
    L0_2 = cleanupINF
    L0_2()
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["背包"]
    L1_2 = "open"
    L0_2(L1_2)
    L0_2 = 0
    repeat
      L1_2 = _find_tb_cx
      L2_2 = Color
      L2_2 = L2_2["背包"]
      L2_2 = L2_2["摄魂香"]
      L2_2 = L2_2["双击"]
      L3_2 = {}
      L4_2 = 20
      L5_2 = 50
      L3_2[1] = L4_2
      L3_2[2] = L5_2
      L4_2 = {}
      L5_2 = math
      L5_2 = L5_2.random
      L6_2 = -9
      L7_2 = 9
      L5_2 = L5_2(L6_2, L7_2)
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = -9
      L8_2 = 9
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2 = L6_2(L7_2, L8_2)
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L4_2[3] = L7_2
      L4_2[4] = L8_2
      L4_2[5] = L9_2
      L4_2[6] = L10_2
      L4_2[7] = L11_2
      L1_2 = L1_2(L2_2, L3_2, L4_2)
      if not L1_2 then
        L1_2 = _ENV["_功能"]
        L1_2 = L1_2["行囊取回"]
        L2_2 = "摄魂香"
        L3_2 = Color
        L3_2 = L3_2["背包"]
        L3_2 = L3_2["摄魂香"]
        L3_2 = L3_2["单击"]
        L1_2 = L1_2(L2_2, L3_2)
        if L1_2 then
          L1_2 = _ENV["_功能"]
          L1_2 = L1_2["背包"]
          L2_2 = "open"
          L1_2(L2_2)
        end
        L1_2 = findAndTap
        L2_2 = color
        L2_2 = L2_2.props
        L2_2 = L2_2.dodgeCoil
        L3_2 = "doubleTap"
        L4_2 = 15
        L1_2(L2_2, L3_2, L4_2)
      end
      L1_2 = _find_cx
      L2_2 = Color
      L2_2 = L2_2["提示框"]
      L3_2 = {}
      L4_2 = 20
      L5_2 = 40
      L3_2[1] = L4_2
      L3_2[2] = L5_2
      L1_2(L2_2, L3_2)
      L1_2 = moveTips
      L1_2()
      L1_2 = 1
      L2_2 = 50
      L3_2 = 1
      for L4_2 = L1_2, L2_2, L3_2 do
        L5_2 = _find_ps_tb
        L6_2 = Color
        L6_2 = L6_2["青龙"]
        L6_2 = L6_2["使用香成功"]
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = os
          L5_2 = L5_2.time
          L5_2 = L5_2()
          dodgeTime = L5_2
          break
        else
          L5_2 = _find_ps_tb
          L6_2 = Color
          L6_2 = L6_2["青龙"]
          L6_2 = L6_2["香继续使用"]
          L5_2 = L5_2(L6_2)
          if L5_2 then
            L5_2 = 1
            L6_2 = 10
            L7_2 = 1
            for L8_2 = L5_2, L6_2, L7_2 do
              L9_2 = _find
              L10_2 = Color
              L10_2 = L10_2.npc
              L10_2 = L10_2["通用关闭对话框"]
              L9_2 = L9_2(L10_2)
              if L9_2 then
                L9_2 = _ENV["_随机延时"]
                L10_2 = 811
                L9_2(L10_2)
              end
              L9_2 = _ENV["_随机延时"]
              L10_2 = 61
              L9_2(L10_2)
            end
            L5_2 = os
            L5_2 = L5_2.time
            L5_2 = L5_2()
            L5_2 = L5_2 + 16000
            dodgeTime = L5_2
            break
          end
        end
      end
    until L4_2 ~= 50
    L1_2 = closeAllWnd
    L1_2()
    L1_2 = closeScrnLog
    L2_2 = "使用摄妖香"
    L1_2(L2_2)
  end
  ::lbl_136::
end

dodgeSystem = L0_1
