local L0_1, L1_1, L2_1
L0_1 = require
L1_1 = "ts"
L0_1 = L0_1(L1_1)
ts = L0_1
L0_1 = require
L1_1 = "luajson"
L0_1 = L0_1(L1_1)
json = L0_1
L0_1 = init
L1_1 = 1
L0_1(L1_1)
logname = "hblog"
L0_1 = math
L0_1 = L0_1.randomseed
L1_1 = getRndNum
L1_1, L2_1 = L1_1()
L0_1(L1_1, L2_1)
L0_1 = os
L0_1 = L0_1.execute
L1_1 = "mkdir "
L2_1 = "/sdcard/360ex"
L1_1 = L1_1 .. L2_1
L0_1(L1_1)
L0_1 = os
L0_1 = L0_1.execute
L1_1 = "mkdir "
L2_1 = "/sdcard/hbbug"
L1_1 = L1_1 .. L2_1
L0_1(L1_1)

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = mSleep
  L3_2 = math
  L3_2 = L3_2.random
  L4_2 = A0_2
  L5_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
  L2_2(L3_2, L4_2, L5_2)
end

_Sleep = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = __isnlog__
  if L1_2 and A0_2 ~= "" then
    L1_2 = nLog
    L2_2 = A0_2
    L1_2(L2_2)
    L1_2 = log
    L2_2 = A0_2
    L3_2 = logname
    L1_2(L2_2, L3_2)
  end
  L1_2 = printLog
  L2_2 = A0_2
  L3_2 = Type
  L1_2(L2_2, L3_2)
end

_print = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = A2_2 or nil
  if not A2_2 then
    L3_2 = 5
  end
  L4_2 = nil
  L5_2 = nil
  L6_2 = nil
  repeat
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = A0_2 - L3_2
    L9_2 = A0_2 + L3_2
    L7_2 = L7_2(L8_2, L9_2)
    L4_2 = L7_2
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = A1_2 - L3_2
    L9_2 = A1_2 + L3_2
    L7_2 = L7_2(L8_2, L9_2)
    L5_2 = L7_2
    L7_2 = math
    L7_2 = L7_2.sqrt
    L8_2 = L4_2 - A0_2
    L9_2 = L4_2 - A0_2
    L8_2 = L8_2 * L9_2
    L9_2 = L5_2 - A1_2
    L10_2 = L5_2 - A1_2
    L9_2 = L9_2 * L10_2
    L8_2 = L8_2 + L9_2
    L7_2 = L7_2(L8_2)
    L6_2 = L7_2
  until L3_2 >= L6_2
  L7_2 = tap
  L8_2 = L4_2
  L9_2 = L5_2
  L7_2(L8_2, L9_2)
end

_tap_r = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2
  if not A3_2 then
    A3_2 = 7
  end
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = -A3_2
  L6_2 = A3_2
  L4_2 = L4_2(L5_2, L6_2)
  A0_2 = L4_2 + A0_2
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = -A3_2
  L6_2 = A3_2
  L4_2 = L4_2(L5_2, L6_2)
  A1_2 = L4_2 + A1_2
  if A2_2 == 2 then
    L4_2 = touchDown
    L5_2 = 1
    L6_2 = A0_2
    L7_2 = A1_2
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = mSleep
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 20
    L7_2 = 80
    L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2)
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = touchUp
    L5_2 = 1
    L6_2 = A0_2
    L7_2 = A1_2
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = mSleep
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 20
    L7_2 = 80
    L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2)
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A3_2
    L6_2 = A3_2
    L4_2 = L4_2(L5_2, L6_2)
    A0_2 = L4_2 + A0_2
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A3_2
    L6_2 = A3_2
    L4_2 = L4_2(L5_2, L6_2)
    A1_2 = L4_2 + A1_2
    L4_2 = touchDown
    L5_2 = 1
    L6_2 = A0_2
    L7_2 = A1_2
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = mSleep
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 20
    L7_2 = 80
    L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2)
    L4_2(L5_2, L6_2, L7_2)
    L4_2 = touchUp
    L5_2 = 1
    L6_2 = A0_2
    L7_2 = A1_2
    L4_2(L5_2, L6_2, L7_2)
  else
    L4_2 = tap
    L5_2 = A0_2
    L6_2 = A1_2
    L4_2(L5_2, L6_2)
  end
end

_ENV["原_tap"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L4_2 = A3_2 or nil
  if not A3_2 then
    L4_2 = 8
  end
  if A2_2 == 2 then
    L5_2 = _tap_r
    L6_2 = A0_2
    L7_2 = A1_2
    L8_2 = L4_2
    L5_2(L6_2, L7_2, L8_2)
    L5_2 = mSleep
    L6_2 = math
    L6_2 = L6_2.random
    L7_2 = 50
    L8_2 = 90
    L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2, L8_2)
    L5_2(L6_2, L7_2, L8_2, L9_2)
    L5_2 = _tap_r
    L6_2 = A0_2
    L7_2 = A1_2
    L8_2 = L4_2
    L5_2(L6_2, L7_2, L8_2)
  else
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = 1
    L7_2 = 6
    L5_2 = L5_2(L6_2, L7_2)
    if 4 <= L5_2 then
      L6_2 = _tap_r
      L7_2 = A0_2
      L8_2 = A1_2
      L9_2 = L4_2
      L6_2(L7_2, L8_2, L9_2)
    elseif L5_2 == 3 then
      L6_2 = _tap_r
      L7_2 = A0_2
      L8_2 = A1_2
      L9_2 = L4_2 * 0.8
      L6_2(L7_2, L8_2, L9_2)
    elseif L5_2 == 2 then
      L6_2 = _tap_r
      L7_2 = A0_2
      L8_2 = A1_2
      L9_2 = L4_2 * 1.2
      L6_2(L7_2, L8_2, L9_2)
    else
      L6_2 = _tap_r
      L7_2 = A0_2
      L8_2 = A1_2
      L9_2 = L4_2 * 1.4
      L6_2(L7_2, L8_2, L9_2)
    end
  end
end

_tap = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2
  L6_2 = A0_2 - A2_2
  L7_2 = A1_2 - A3_2
  L8_2 = L6_2 * L6_2
  L9_2 = A4_2 * A4_2
  L8_2 = L8_2 / L9_2
  L9_2 = L7_2 * L7_2
  L10_2 = A5_2 * A5_2
  L9_2 = L9_2 / L10_2
  L8_2 = L8_2 + L9_2
  L8_2 = L8_2 <= 1
  return L8_2
end

_ty_fw = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L5_2 = A4_2 or nil
  if not A4_2 then
    L5_2 = 50
  end
  L6_2 = math
  L6_2 = L6_2.random
  L7_2 = L5_2 / 2
  L8_2 = L5_2
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = A0_2 + A2_2
  L7_2 = L7_2 / 2
  L8_2 = A1_2 + A3_2
  L8_2 = L8_2 / 2
  L9_2 = A2_2 - A0_2
  L9_2 = L9_2 / 2
  L10_2 = A3_2 - A1_2
  L10_2 = L10_2 / 2
  if L9_2 > L10_2 then
    r = L10_2
  else
    r = L9_2
  end
  L11_2 = math
  L11_2 = L11_2.random
  L12_2 = -1
  L13_2 = 3
  L11_2 = L11_2(L12_2, L13_2)
  if 0 < L11_2 then
    repeat
      L12_2 = math
      L12_2 = L12_2.random
      L13_2 = A0_2
      L14_2 = A2_2
      L12_2 = L12_2(L13_2, L14_2)
      tx = L12_2
      L12_2 = math
      L12_2 = L12_2.random
      L13_2 = A1_2
      L14_2 = A3_2
      L12_2 = L12_2(L13_2, L14_2)
      ty = L12_2
      L12_2 = _ty_fw
      L13_2 = tx
      L14_2 = ty
      L15_2 = L7_2
      L16_2 = L8_2
      L17_2 = L9_2
      L18_2 = L10_2
      L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    until L12_2
    L12_2 = tap
    L13_2 = tx
    L14_2 = ty
    L12_2(L13_2, L14_2)
  else
    L12_2 = _tap_r
    L13_2 = L7_2
    L14_2 = L8_2
    L15_2 = r
    L12_2(L13_2, L14_2, L15_2)
  end
  L12_2 = mSleep
  L13_2 = L6_2
  L12_2(L13_2)
end

_tap_tb = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  if not A1_2 then
    A1_2 = 90
  end
  L2_2 = multiColor
  L3_2 = A0_2[2]
  L4_2 = A1_2
  L2_2 = L2_2(L3_2, L4_2)
  if L2_2 then
    L2_2 = A0_2[1]
    L2_2 = L2_2[2]
    if L2_2 then
      L2_2 = _tap
      L3_2 = A0_2[2]
      L3_2 = L3_2[1]
      L3_2 = L3_2[1]
      L4_2 = A0_2[2]
      L4_2 = L4_2[1]
      L4_2 = L4_2[2]
      L2_2(L3_2, L4_2)
    end
    L2_2 = _print
    L3_2 = A0_2[1]
    L3_2 = L3_2[1]
    L2_2(L3_2)
    L2_2 = true
    return L2_2
  end
  L2_2 = false
  return L2_2
end

_cmp = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L3_2 = "rb"
  L1_2 = L1_2(L2_2, L3_2)
  f = L1_2
  L1_2 = f
  L2_2 = null
  if L1_2 == L2_2 then
    L1_2 = null
    return L1_2
  end
  L1_2 = f
  L2_2 = L1_2
  L1_2 = L1_2.read
  L3_2 = "*all"
  L1_2 = L1_2(L2_2, L3_2)
  bytes = L1_2
  L1_2 = f
  L2_2 = L1_2
  L1_2 = L1_2.close
  L1_2(L2_2)
  L1_2 = bytes
  L2_2 = L1_2
  L1_2 = L1_2.base64_encode
  return L1_2(L2_2)
end

_ReadFileBase64 = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = userPath
  L0_2 = L0_2()
  L1_2 = "/res/测试.png"
  L0_2 = L0_2 .. L1_2
  L1_2 = snapshot
  L2_2 = L0_2
  L3_2 = 0
  L4_2 = 0
  L5_2 = 1919
  L6_2 = 1079
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  L1_2 = _ReadFileBase64
  L2_2 = L0_2
  L1_2 = L1_2(L2_2)
  b64 = L1_2
  L1_2 = {}
  L1_2.tstab = 1
  L1_2.timeOut = 10
  L1_2.urlEnCode = false
  L2_2 = json
  L2_2 = L2_2.encode
  L3_2 = {}
  L4_2 = b64
  L3_2.base64 = L4_2
  L2_2 = L2_2(L3_2)
  b64 = L2_2
  L2_2 = httpPost
  L3_2 = "http://49.7.206.12:888/jl"
  L4_2 = b64
  L5_2 = L1_2
  L2_2 = L2_2(L3_2, L4_2, L5_2)
  str = L2_2
  L2_2 = str
  if L2_2 then
    L2_2 = pcall
    L3_2 = json
    L3_2 = L3_2.decode
    L4_2 = str
    L2_2, L3_2 = L2_2(L3_2, L4_2)
    result = L3_2
    ok = L2_2
    L2_2 = ok
    if L2_2 then
      L2_2 = result
      L2_2 = L2_2.qs
      return L2_2
    else
      L2_2 = ""
      return L2_2
    end
  end
end

_ENV["_记录图片"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2
  if not A1_2 then
    A1_2 = 0
  end
  if not A2_2 then
    A2_2 = 0
  end
  L3_2 = multiColor
  L4_2 = A0_2[2]
  L5_2 = 90
  L3_2 = L3_2(L4_2, L5_2)
  if L3_2 then
    L3_2 = A0_2[1]
    L3_2 = L3_2[2]
    if L3_2 then
      L3_2 = _tap
      L4_2 = A0_2[2]
      L4_2 = L4_2[1]
      L4_2 = L4_2[1]
      L4_2 = L4_2 + A1_2
      L5_2 = A0_2[2]
      L5_2 = L5_2[1]
      L5_2 = L5_2[2]
      L5_2 = L5_2 + A2_2
      L3_2(L4_2, L5_2)
    end
    L3_2 = _print
    L4_2 = A0_2[1]
    L4_2 = L4_2[1]
    L3_2(L4_2)
    L3_2 = true
    return L3_2
  end
  L3_2 = false
  return L3_2
end

_cmp_p = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = 1
  L3_2 = A1_2[1]
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = multiColor
    L7_2 = A0_2[2]
    L8_2 = 90
    L6_2 = L6_2(L7_2, L8_2)
    if L6_2 then
      L6_2 = A0_2[1]
      L6_2 = L6_2[2]
      if L6_2 then
        L6_2 = _tap
        L7_2 = A0_2[2]
        L7_2 = L7_2[1]
        L7_2 = L7_2[1]
        L8_2 = A0_2[2]
        L8_2 = L8_2[1]
        L8_2 = L8_2[2]
        L6_2(L7_2, L8_2)
      end
      L6_2 = _print
      L7_2 = A0_2[1]
      L7_2 = L7_2[1]
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    end
    L6_2 = mSleep
    L7_2 = A1_2[2]
    L6_2(L7_2)
  end
  L2_2 = false
  return L2_2
end

_cmp_cx = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  if not A1_2 then
    A1_2 = 90
  end
  if not A2_2 then
    A2_2 = 0
  end
  if not A3_2 then
    A3_2 = 0
  end
  L4_2 = keepScreen
  L5_2 = true
  L4_2(L5_2)
  L4_2 = pairs
  L5_2 = A0_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = multiColor
    L10_2 = L8_2[2]
    L11_2 = A1_2
    L12_2 = false
    L9_2 = L9_2(L10_2, L11_2, L12_2)
    if L9_2 then
      L9_2 = L8_2[1]
      L9_2 = L9_2[2]
      if L9_2 then
        L9_2 = _tap
        L10_2 = L8_2[2]
        L10_2 = L10_2[1]
        L10_2 = L10_2[1]
        L11_2 = L8_2[2]
        L11_2 = L11_2[1]
        L11_2 = L11_2[2]
        L9_2(L10_2, L11_2)
      end
      L9_2 = _print
      L10_2 = L8_2[1]
      L10_2 = L10_2[1]
      L9_2(L10_2)
      L9_2 = keepScreen
      L10_2 = false
      L9_2(L10_2)
      L9_2 = true
      return L9_2
    end
  end
  L4_2 = keepScreen
  L5_2 = false
  L4_2(L5_2)
  L4_2 = false
  return L4_2
end

_cmp_tb = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  if not A2_2 then
    A2_2 = 90
  end
  if not A3_2 then
    A3_2 = 0
  end
  if not A4_2 then
    A4_2 = 0
  end
  L5_2 = 1
  L6_2 = A1_2[1]
  L7_2 = 1
  for L8_2 = L5_2, L6_2, L7_2 do
    L9_2 = keepScreen
    L10_2 = true
    L9_2(L10_2)
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = multiColor
      L15_2 = L13_2[2]
      L16_2 = A2_2
      L17_2 = false
      L14_2 = L14_2(L15_2, L16_2, L17_2)
      if L14_2 then
        L14_2 = L13_2[1]
        L14_2 = L14_2[2]
        if L14_2 then
          L14_2 = _tap
          L15_2 = L13_2[2]
          L15_2 = L15_2[1]
          L15_2 = L15_2[1]
          L16_2 = L13_2[2]
          L16_2 = L16_2[1]
          L16_2 = L16_2[2]
          L14_2(L15_2, L16_2)
        end
        L14_2 = _print
        L15_2 = L13_2[1]
        L15_2 = L15_2[1]
        L14_2(L15_2)
        L14_2 = keepScreen
        L15_2 = false
        L14_2(L15_2)
        L14_2 = true
        return L14_2
      end
    end
    L9_2 = keepScreen
    L10_2 = false
    L9_2(L10_2)
    L9_2 = mSleep
    L10_2 = A1_2[2]
    L9_2(L10_2)
  end
  L5_2 = false
  return L5_2
end

_cmp_tb_cx = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if not A1_2 then
    L3_2 = {}
    L4_2 = 0
    L5_2 = 0
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    A1_2 = L3_2
  end
  L3_2 = false
  L4_2 = -1
  L5_2 = -1
  if A2_2 ~= nil then
    L6_2 = findMultiColorInRegionFuzzy
    L7_2 = A0_2[3]
    L7_2 = L7_2[1]
    L8_2 = A0_2[3]
    L8_2 = L8_2[2]
    L9_2 = A0_2[2]
    L9_2 = L9_2[1]
    L10_2 = A2_2[1]
    L11_2 = A2_2[2]
    L12_2 = A2_2[3]
    L13_2 = A2_2[4]
    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L5_2 = L7_2
    L4_2 = L6_2
  else
    L6_2 = findMultiColorInRegionFuzzy
    L7_2 = A0_2[3]
    L7_2 = L7_2[1]
    L8_2 = A0_2[3]
    L8_2 = L8_2[2]
    L9_2 = A0_2[2]
    L9_2 = L9_2[1]
    L10_2 = A0_2[1]
    L10_2 = L10_2[2]
    L11_2 = A0_2[1]
    L11_2 = L11_2[3]
    L12_2 = A0_2[1]
    L12_2 = L12_2[4]
    L13_2 = A0_2[1]
    L13_2 = L13_2[5]
    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L5_2 = L7_2
    L4_2 = L6_2
  end
  if -1 < L4_2 then
    L6_2 = A0_2[2]
    L6_2 = L6_2[2]
    if L6_2 then
      L6_2 = A0_2[2]
      L6_2 = L6_2[3]
      if L6_2 == 1 then
        L6_2 = _tap
        L7_2 = A1_2[1]
        L7_2 = L4_2 + L7_2
        L8_2 = A1_2[2]
        L8_2 = L5_2 + L8_2
        L6_2(L7_2, L8_2)
      else
        L6_2 = A0_2[2]
        L6_2 = L6_2[3]
        if L6_2 == 3 then
          L6_2 = _tap
          L7_2 = A1_2[1]
          L7_2 = L4_2 + L7_2
          L8_2 = A1_2[2]
          L8_2 = L5_2 + L8_2
          L9_2 = 1
          L10_2 = A0_2[2]
          L10_2 = L10_2[4]
          L6_2(L7_2, L8_2, L9_2, L10_2)
        else
          L6_2 = A0_2[2]
          L6_2 = L6_2[3]
          if L6_2 == 4 then
            L6_2 = _tap
            L7_2 = A1_2[1]
            L7_2 = L4_2 + L7_2
            L8_2 = A1_2[2]
            L8_2 = L5_2 + L8_2
            L9_2 = 2
            L10_2 = A0_2[2]
            L10_2 = L10_2[4]
            L6_2(L7_2, L8_2, L9_2, L10_2)
          else
            L6_2 = _tap
            L7_2 = A1_2[1]
            L7_2 = L4_2 + L7_2
            L8_2 = A1_2[2]
            L8_2 = L5_2 + L8_2
            L9_2 = 2
            L6_2(L7_2, L8_2, L9_2)
          end
        end
      end
    end
    L6_2 = _print
    L7_2 = A0_2[1]
    L7_2 = L7_2[1]
    L6_2(L7_2)
    L3_2 = true
  end
  L6_2 = L3_2
  L7_2 = L4_2
  L8_2 = L5_2
  return L6_2, L7_2, L8_2
end

_find = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 0
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  L4_2 = false
  L5_2 = -1
  L6_2 = -1
  L6_2 = 1
  L7_2 = A1_2[1]
  L8_2 = 1
  for L9_2 = L6_2, L7_2, L8_2 do
    if A3_2 ~= nil then
      L10_2 = findMultiColorInRegionFuzzy
      L11_2 = A0_2[3]
      L11_2 = L11_2[1]
      L12_2 = A0_2[3]
      L12_2 = L12_2[2]
      L13_2 = A0_2[2]
      L13_2 = L13_2[1]
      L14_2 = A3_2[1]
      L15_2 = A3_2[2]
      L16_2 = A3_2[3]
      L17_2 = A3_2[4]
      L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L5_2 = L11_2
      L4_2 = L10_2
    else
      L10_2 = findMultiColorInRegionFuzzy
      L11_2 = A0_2[3]
      L11_2 = L11_2[1]
      L12_2 = A0_2[3]
      L12_2 = L12_2[2]
      L13_2 = A0_2[2]
      L13_2 = L13_2[1]
      L14_2 = A0_2[1]
      L14_2 = L14_2[2]
      L15_2 = A0_2[1]
      L15_2 = L15_2[3]
      L16_2 = A0_2[1]
      L16_2 = L16_2[4]
      L17_2 = A0_2[1]
      L17_2 = L17_2[5]
      L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L5_2 = L11_2
      L4_2 = L10_2
    end
    if -1 < L4_2 then
      L10_2 = A0_2[2]
      L10_2 = L10_2[2]
      if L10_2 then
        L10_2 = A0_2[2]
        L10_2 = L10_2[3]
        if L10_2 == 1 then
          L10_2 = _tap
          L11_2 = A2_2[1]
          L11_2 = L4_2 + L11_2
          L12_2 = A2_2[2]
          L12_2 = L5_2 + L12_2
          L10_2(L11_2, L12_2)
        else
          L10_2 = A0_2[2]
          L10_2 = L10_2[3]
          if L10_2 == 3 then
            L10_2 = _tap
            L11_2 = A2_2[1]
            L11_2 = L4_2 + L11_2
            L12_2 = A2_2[2]
            L12_2 = L5_2 + L12_2
            L13_2 = 1
            L14_2 = A0_2[2]
            L14_2 = L14_2[4]
            L10_2(L11_2, L12_2, L13_2, L14_2)
          else
            L10_2 = A0_2[2]
            L10_2 = L10_2[3]
            if L10_2 == 4 then
              L10_2 = _tap
              L11_2 = A2_2[1]
              L11_2 = L4_2 + L11_2
              L12_2 = A2_2[2]
              L12_2 = L5_2 + L12_2
              L13_2 = 2
              L14_2 = A0_2[2]
              L14_2 = L14_2[4]
              L10_2(L11_2, L12_2, L13_2, L14_2)
            else
              L10_2 = _tap
              L11_2 = A2_2[1]
              L11_2 = L4_2 + L11_2
              L12_2 = A2_2[2]
              L12_2 = L5_2 + L12_2
              L13_2 = 2
              L10_2(L11_2, L12_2, L13_2)
            end
          end
        end
      end
      L10_2 = _print
      L11_2 = A0_2[1]
      L11_2 = L11_2[1]
      L10_2(L11_2)
      L10_2 = true
      L11_2 = L4_2
      L12_2 = L5_2
      return L10_2, L11_2, L12_2
    end
    L10_2 = mSleep
    L11_2 = A1_2[2]
    L10_2(L11_2)
  end
  L6_2 = false
  L7_2 = L4_2
  L8_2 = L5_2
  return L6_2, L7_2, L8_2
end

_find_cx = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  if not A1_2 then
    L3_2 = {}
    L4_2 = 0
    L5_2 = 0
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    A1_2 = L3_2
  end
  L3_2 = false
  L4_2 = -1
  L5_2 = -1
  L6_2 = keepScreen
  L7_2 = true
  L6_2(L7_2)
  if A2_2 ~= nil then
    L6_2 = pairs
    L7_2 = A0_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L11_2 = findMultiColorInRegionFuzzy
      L12_2 = L10_2[3]
      L12_2 = L12_2[1]
      L13_2 = L10_2[3]
      L13_2 = L13_2[2]
      L14_2 = L10_2[2]
      L14_2 = L14_2[1]
      L15_2 = A2_2[1]
      L16_2 = A2_2[2]
      L17_2 = A2_2[3]
      L18_2 = A2_2[4]
      L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = L12_2
      L4_2 = L11_2
      if -1 < L4_2 then
        L11_2 = L10_2[2]
        L11_2 = L11_2[2]
        if L11_2 then
          L11_2 = L10_2[2]
          L11_2 = L11_2[3]
          if L11_2 == 1 then
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L11_2(L12_2, L13_2)
          else
            L11_2 = L10_2[2]
            L11_2 = L11_2[3]
            if L11_2 == 3 then
              L11_2 = _tap
              L12_2 = A1_2[1]
              L12_2 = L4_2 + L12_2
              L13_2 = A1_2[2]
              L13_2 = L5_2 + L13_2
              L14_2 = 1
              L15_2 = L10_2[2]
              L15_2 = L15_2[4]
              L11_2(L12_2, L13_2, L14_2, L15_2)
            else
              L11_2 = L10_2[2]
              L11_2 = L11_2[3]
              if L11_2 == 4 then
                L11_2 = _tap
                L12_2 = A1_2[1]
                L12_2 = L4_2 + L12_2
                L13_2 = A1_2[2]
                L13_2 = L5_2 + L13_2
                L14_2 = 2
                L15_2 = L10_2[2]
                L15_2 = L15_2[4]
                L11_2(L12_2, L13_2, L14_2, L15_2)
              else
                L11_2 = _tap
                L12_2 = A1_2[1]
                L12_2 = L4_2 + L12_2
                L13_2 = A1_2[2]
                L13_2 = L5_2 + L13_2
                L14_2 = 2
                L11_2(L12_2, L13_2, L14_2)
              end
            end
          end
        end
        L11_2 = _print
        L12_2 = L10_2[1]
        L12_2 = L12_2[1]
        L11_2(L12_2)
        L11_2 = L10_2[1]
        L11_2 = L11_2[1]
        _ENV["类型"] = L11_2
        L3_2 = true
        break
      end
    end
  else
    L6_2 = pairs
    L7_2 = A0_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L11_2 = findMultiColorInRegionFuzzy
      L12_2 = L10_2[3]
      L12_2 = L12_2[1]
      L13_2 = L10_2[3]
      L13_2 = L13_2[2]
      L14_2 = L10_2[2]
      L14_2 = L14_2[1]
      L15_2 = L10_2[1]
      L15_2 = L15_2[2]
      L16_2 = L10_2[1]
      L16_2 = L16_2[3]
      L17_2 = L10_2[1]
      L17_2 = L17_2[4]
      L18_2 = L10_2[1]
      L18_2 = L18_2[5]
      L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = L12_2
      L4_2 = L11_2
      if -1 < L4_2 then
        L11_2 = L10_2[2]
        L11_2 = L11_2[2]
        if L11_2 then
          L11_2 = L10_2[2]
          L11_2 = L11_2[3]
          if L11_2 == 1 then
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L11_2(L12_2, L13_2)
          else
            L11_2 = L10_2[2]
            L11_2 = L11_2[3]
            if L11_2 == 3 then
              L11_2 = _tap
              L12_2 = A1_2[1]
              L12_2 = L4_2 + L12_2
              L13_2 = A1_2[2]
              L13_2 = L5_2 + L13_2
              L14_2 = 1
              L15_2 = L10_2[2]
              L15_2 = L15_2[4]
              L11_2(L12_2, L13_2, L14_2, L15_2)
            else
              L11_2 = L10_2[2]
              L11_2 = L11_2[3]
              if L11_2 == 4 then
                L11_2 = _tap
                L12_2 = A1_2[1]
                L12_2 = L4_2 + L12_2
                L13_2 = A1_2[2]
                L13_2 = L5_2 + L13_2
                L14_2 = 2
                L15_2 = L10_2[2]
                L15_2 = L15_2[4]
                L11_2(L12_2, L13_2, L14_2, L15_2)
              else
                L11_2 = _tap
                L12_2 = A1_2[1]
                L12_2 = L4_2 + L12_2
                L13_2 = A1_2[2]
                L13_2 = L5_2 + L13_2
                L14_2 = 2
                L11_2(L12_2, L13_2, L14_2)
              end
            end
          end
        end
        L11_2 = _print
        L12_2 = L10_2[1]
        L12_2 = L12_2[1]
        L11_2(L12_2)
        L11_2 = L10_2[1]
        L11_2 = L11_2[1]
        _ENV["类型"] = L11_2
        L3_2 = true
        break
      end
    end
  end
  L6_2 = keepScreen
  L7_2 = false
  L6_2(L7_2)
  L6_2 = L3_2
  L7_2 = L4_2
  L8_2 = L5_2
  L9_2 = _ENV["类型"]
  return L6_2, L7_2, L8_2, L9_2
end

_find_tb = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 0
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  L4_2 = -1
  L5_2 = -1
  if A3_2 ~= nil then
    L6_2 = 1
    L7_2 = A1_2[1]
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = keepScreen
      L11_2 = true
      L10_2(L11_2)
      L10_2 = pairs
      L11_2 = A0_2
      L10_2, L11_2, L12_2 = L10_2(L11_2)
      for L13_2, L14_2 in L10_2, L11_2, L12_2 do
        L15_2 = findMultiColorInRegionFuzzy
        L16_2 = L14_2[3]
        L16_2 = L16_2[1]
        L17_2 = L14_2[3]
        L17_2 = L17_2[2]
        L18_2 = L14_2[2]
        L18_2 = L18_2[1]
        L19_2 = A3_2[1]
        L20_2 = A3_2[2]
        L21_2 = A3_2[3]
        L22_2 = A3_2[4]
        L15_2, L16_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
        L5_2 = L16_2
        L4_2 = L15_2
        if -1 < L4_2 then
          L15_2 = L14_2[2]
          L15_2 = L15_2[2]
          if L15_2 then
            L15_2 = L14_2[2]
            L15_2 = L15_2[3]
            if L15_2 == 1 then
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L15_2(L16_2, L17_2)
            else
              L15_2 = L14_2[2]
              L15_2 = L15_2[3]
              if L15_2 == 3 then
                L15_2 = _tap
                L16_2 = A2_2[1]
                L16_2 = L4_2 + L16_2
                L17_2 = A2_2[2]
                L17_2 = L5_2 + L17_2
                L18_2 = 1
                L19_2 = L14_2[2]
                L19_2 = L19_2[4]
                L15_2(L16_2, L17_2, L18_2, L19_2)
              else
                L15_2 = L14_2[2]
                L15_2 = L15_2[3]
                if L15_2 == 4 then
                  L15_2 = _tap
                  L16_2 = A2_2[1]
                  L16_2 = L4_2 + L16_2
                  L17_2 = A2_2[2]
                  L17_2 = L5_2 + L17_2
                  L18_2 = 2
                  L19_2 = L14_2[2]
                  L19_2 = L19_2[4]
                  L15_2(L16_2, L17_2, L18_2, L19_2)
                else
                  L15_2 = _tap
                  L16_2 = A2_2[1]
                  L16_2 = L4_2 + L16_2
                  L17_2 = A2_2[2]
                  L17_2 = L5_2 + L17_2
                  L18_2 = 2
                  L15_2(L16_2, L17_2, L18_2)
                end
              end
            end
          end
          L15_2 = _print
          L16_2 = L14_2[1]
          L16_2 = L16_2[1]
          L15_2(L16_2)
          L15_2 = keepScreen
          L16_2 = false
          L15_2(L16_2)
          L15_2 = true
          L16_2 = L4_2
          L17_2 = L5_2
          return L15_2, L16_2, L17_2
        end
      end
      L10_2 = keepScreen
      L11_2 = false
      L10_2(L11_2)
      L10_2 = mSleep
      L11_2 = A1_2[2]
      L10_2(L11_2)
    end
  else
    L6_2 = 1
    L7_2 = A1_2[1]
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = keepScreen
      L11_2 = true
      L10_2(L11_2)
      L10_2 = pairs
      L11_2 = A0_2
      L10_2, L11_2, L12_2 = L10_2(L11_2)
      for L13_2, L14_2 in L10_2, L11_2, L12_2 do
        L15_2 = findMultiColorInRegionFuzzy
        L16_2 = L14_2[3]
        L16_2 = L16_2[1]
        L17_2 = L14_2[3]
        L17_2 = L17_2[2]
        L18_2 = L14_2[2]
        L18_2 = L18_2[1]
        L19_2 = L14_2[1]
        L19_2 = L19_2[2]
        L20_2 = L14_2[1]
        L20_2 = L20_2[3]
        L21_2 = L14_2[1]
        L21_2 = L21_2[4]
        L22_2 = L14_2[1]
        L22_2 = L22_2[5]
        L15_2, L16_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
        L5_2 = L16_2
        L4_2 = L15_2
        if -1 < L4_2 then
          L15_2 = L14_2[2]
          L15_2 = L15_2[2]
          if L15_2 then
            L15_2 = L14_2[2]
            L15_2 = L15_2[3]
            if L15_2 == 1 then
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L15_2(L16_2, L17_2)
            else
              L15_2 = L14_2[2]
              L15_2 = L15_2[3]
              if L15_2 == 3 then
                L15_2 = _tap
                L16_2 = A2_2[1]
                L16_2 = L4_2 + L16_2
                L17_2 = A2_2[2]
                L17_2 = L5_2 + L17_2
                L18_2 = 1
                L19_2 = L14_2[2]
                L19_2 = L19_2[4]
                L15_2(L16_2, L17_2, L18_2, L19_2)
              else
                L15_2 = L14_2[2]
                L15_2 = L15_2[3]
                if L15_2 == 4 then
                  L15_2 = _tap
                  L16_2 = A2_2[1]
                  L16_2 = L4_2 + L16_2
                  L17_2 = A2_2[2]
                  L17_2 = L5_2 + L17_2
                  L18_2 = 2
                  L19_2 = L14_2[2]
                  L19_2 = L19_2[4]
                  L15_2(L16_2, L17_2, L18_2, L19_2)
                else
                  L15_2 = _tap
                  L16_2 = A2_2[1]
                  L16_2 = L4_2 + L16_2
                  L17_2 = A2_2[2]
                  L17_2 = L5_2 + L17_2
                  L18_2 = 2
                  L15_2(L16_2, L17_2, L18_2)
                end
              end
            end
          end
          L15_2 = _print
          L16_2 = L14_2[1]
          L16_2 = L16_2[1]
          L15_2(L16_2)
          L15_2 = keepScreen
          L16_2 = false
          L15_2(L16_2)
          L15_2 = true
          L16_2 = L4_2
          L17_2 = L5_2
          return L15_2, L16_2, L17_2
        end
      end
      L10_2 = keepScreen
      L11_2 = false
      L10_2(L11_2)
      L10_2 = mSleep
      L11_2 = A1_2[2]
      L10_2(L11_2)
    end
  end
  L6_2 = keepScreen
  L7_2 = false
  L6_2(L7_2)
  L6_2 = false
  L7_2 = -1
  L8_2 = -1
  return L6_2, L7_2, L8_2
end

_find_tb_cx = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 1
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  if not A3_2 then
    A3_2 = 1
  end
  L4_2 = {}
  L5_2 = 1
  L6_2 = A2_2[1]
  L7_2 = 1
  for L8_2 = L5_2, L6_2, L7_2 do
    L9_2 = keepScreen
    L10_2 = true
    L9_2(L10_2)
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = findMultiColorInRegionFuzzyExt
      L15_2 = L13_2[3]
      L15_2 = L15_2[1]
      L16_2 = L13_2[3]
      L16_2 = L16_2[2]
      L17_2 = L13_2[2]
      L17_2 = L17_2[1]
      L18_2 = A1_2[1]
      L19_2 = A1_2[2]
      L20_2 = A1_2[3]
      L21_2 = A1_2[4]
      L22_2 = {}
      L22_2.orient = A3_2
      L14_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
      L15_2 = #L14_2
      if L15_2 ~= 0 then
        L15_2 = _print
        L16_2 = L13_2[1]
        L16_2 = L16_2[1]
        L15_2(L16_2)
        L15_2 = pairs
        L16_2 = L14_2
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        for L18_2, L19_2 in L15_2, L16_2, L17_2 do
          L20_2 = #L4_2
          L20_2 = L20_2 + 1
          L4_2[L20_2] = L19_2
        end
      end
    end
    L9_2 = #L4_2
    if L9_2 ~= 0 then
      L9_2 = keepScreen
      L10_2 = false
      L9_2(L10_2)
      break
    end
    L9_2 = mSleep
    L10_2 = A2_2[2]
    L9_2(L10_2)
    L9_2 = keepScreen
    L10_2 = false
    L9_2(L10_2)
  end
  L5_2 = #L4_2
  if L5_2 ~= 0 then
    L5_2 = 1
    L6_2 = #L4_2
    L6_2 = L6_2 - 1
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = L8_2 + 1
      L10_2 = #L4_2
      L11_2 = 1
      for L12_2 = L9_2, L10_2, L11_2 do
        L13_2 = L4_2[L8_2]
        L13_2 = L13_2.y
        L14_2 = L4_2[L12_2]
        L14_2 = L14_2.y
        L13_2 = L13_2 - L14_2
        if L13_2 < 5 and -5 < L13_2 then
          L14_2 = L4_2[L12_2]
          L14_2.y = -1
        end
      end
    end
    L5_2 = #L4_2
    L6_2 = 1
    L7_2 = -1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = L4_2[L8_2]
      L9_2 = L9_2.y
      if L9_2 == -1 then
        L9_2 = table
        L9_2 = L9_2.remove
        L10_2 = L4_2
        L11_2 = L8_2
        L9_2(L10_2, L11_2)
      end
    end
  end
  return L4_2
end

_find_ex = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L1_2 = {}
  L2_2 = 1
  while true do
    L3_2 = #A0_2
    if L2_2 > L3_2 then
      break
    end
    L3_2 = string
    L3_2 = L3_2.byte
    L4_2 = A0_2
    L5_2 = L2_2
    L3_2 = L3_2(L4_2, L5_2)
    if not L3_2 then
      break
    end
    if L3_2 < 192 then
      if 48 <= L3_2 and L3_2 <= 57 or 65 <= L3_2 and L3_2 <= 90 or 97 <= L3_2 and L3_2 <= 122 then
        L4_2 = table
        L4_2 = L4_2.insert
        L5_2 = L1_2
        L6_2 = string
        L6_2 = L6_2.char
        L7_2 = L3_2
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L6_2(L7_2)
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      end
      L2_2 = L2_2 + 1
    elseif L3_2 < 224 then
      L2_2 = L2_2 + 2
    elseif L3_2 < 240 then
      if 228 <= L3_2 and L3_2 <= 233 then
        L4_2 = string
        L4_2 = L4_2.byte
        L5_2 = A0_2
        L6_2 = L2_2 + 1
        L4_2 = L4_2(L5_2, L6_2)
        L5_2 = string
        L5_2 = L5_2.byte
        L6_2 = A0_2
        L7_2 = L2_2 + 2
        L5_2 = L5_2(L6_2, L7_2)
        if L4_2 and L5_2 then
          L6_2 = 128
          L7_2 = 191
          L8_2 = 128
          L9_2 = 191
          if L3_2 == 228 then
            L6_2 = 184
          elseif L3_2 == 233 then
            L10_2 = 190
            if L4_2 ~= 190 then
              L11_2 = 191
              if L11_2 then
                goto lbl_78
                L9_2 = L11_2 or L9_2
              end
            end
            L9_2 = 165
            ::lbl_78::
            L7_2 = L10_2
          end
          if L4_2 >= L6_2 and L4_2 <= L7_2 and L5_2 >= L8_2 and L5_2 <= L9_2 then
            L10_2 = table
            L10_2 = L10_2.insert
            L11_2 = L1_2
            L12_2 = string
            L12_2 = L12_2.char
            L13_2 = L3_2
            L14_2 = L4_2
            L15_2 = L5_2
            L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2, L15_2)
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
          end
        end
      end
      L2_2 = L2_2 + 3
    elseif L3_2 < 248 then
      L2_2 = L2_2 + 4
    elseif L3_2 < 252 then
      L2_2 = L2_2 + 5
    elseif L3_2 < 254 then
      L2_2 = L2_2 + 6
    end
  end
  L3_2 = table
  L3_2 = L3_2.concat
  L4_2 = L1_2
  return L3_2(L4_2)
end

_ENV["_过滤特殊字符"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L7_2 = findColorInRegionFuzzy
  L8_2 = A0_2
  L9_2 = A1_2
  L10_2 = A2_2
  L11_2 = A3_2
  L12_2 = A4_2
  L13_2 = A5_2
  L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  if -1 < L7_2 then
    if A6_2 then
      L9_2 = _tap
      L10_2 = L7_2
      L11_2 = L8_2
      L9_2(L10_2, L11_2)
    end
    L9_2 = true
    return L9_2
  else
    L9_2 = false
    return L9_2
  end
end

_ENV["_单点找色"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  if not A1_2 then
    L5_2 = {}
    L6_2 = 0
    L7_2 = 0
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A1_2 = L5_2
  end
  if not A4_2 then
    L5_2 = {}
    L6_2 = 2105376
    L7_2 = 2105376
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A4_2 = L5_2
  end
  L5_2 = false
  L6_2 = -1
  L7_2 = -1
  if A2_2 ~= nil then
    L8_2 = findMultiColorInRegionFuzzy
    L9_2 = A0_2[3]
    L9_2 = L9_2[1]
    L10_2 = A0_2[3]
    L10_2 = L10_2[2]
    L11_2 = A0_2[2]
    L11_2 = L11_2[1]
    L12_2 = A2_2[1]
    L13_2 = A2_2[2]
    L14_2 = A2_2[3]
    L15_2 = A2_2[4]
    L16_2 = {}
    L17_2 = A4_2[1]
    L16_2.main = L17_2
    L17_2 = A4_2[2]
    L16_2.list = L17_2
    L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
    L7_2 = L9_2
    L6_2 = L8_2
  else
    L8_2 = findMultiColorInRegionFuzzy
    L9_2 = A0_2[3]
    L9_2 = L9_2[1]
    L10_2 = A0_2[3]
    L10_2 = L10_2[2]
    L11_2 = A0_2[2]
    L11_2 = L11_2[1]
    L12_2 = A0_2[1]
    L12_2 = L12_2[2]
    L13_2 = A0_2[1]
    L13_2 = L13_2[3]
    L14_2 = A0_2[1]
    L14_2 = L14_2[4]
    L15_2 = A0_2[1]
    L15_2 = L15_2[5]
    L16_2 = {}
    L17_2 = A4_2[1]
    L16_2.main = L17_2
    L17_2 = A4_2[2]
    L16_2.list = L17_2
    L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
    L7_2 = L9_2
    L6_2 = L8_2
  end
  if -1 < L6_2 then
    L8_2 = A0_2[2]
    L8_2 = L8_2[2]
    if L8_2 or A3_2 then
      L8_2 = A0_2[2]
      L8_2 = L8_2[3]
      if L8_2 == 1 then
        L8_2 = _tap
        L9_2 = A1_2[1]
        L9_2 = L6_2 + L9_2
        L10_2 = A1_2[2]
        L10_2 = L7_2 + L10_2
        L8_2(L9_2, L10_2)
      else
        L8_2 = A0_2[2]
        L8_2 = L8_2[3]
        if L8_2 == 3 then
          L8_2 = _tap
          L9_2 = A1_2[1]
          L9_2 = L6_2 + L9_2
          L10_2 = A1_2[2]
          L10_2 = L7_2 + L10_2
          L11_2 = 1
          L12_2 = A0_2[2]
          L12_2 = L12_2[4]
          L8_2(L9_2, L10_2, L11_2, L12_2)
        else
          L8_2 = A0_2[2]
          L8_2 = L8_2[3]
          if L8_2 == 4 then
            L8_2 = _tap
            L9_2 = A1_2[1]
            L9_2 = L6_2 + L9_2
            L10_2 = A1_2[2]
            L10_2 = L7_2 + L10_2
            L11_2 = 2
            L12_2 = A0_2[2]
            L12_2 = L12_2[4]
            L8_2(L9_2, L10_2, L11_2, L12_2)
          else
            L8_2 = _tap
            L9_2 = A1_2[1]
            L9_2 = L6_2 + L9_2
            L10_2 = A1_2[2]
            L10_2 = L7_2 + L10_2
            L11_2 = 2
            L8_2(L9_2, L10_2, L11_2)
          end
        end
      end
    end
    L8_2 = _print
    L9_2 = A0_2[1]
    L9_2 = L9_2[1]
    L8_2(L9_2)
    L5_2 = true
  end
  L8_2 = L5_2
  L9_2 = L6_2
  L10_2 = L7_2
  return L8_2, L9_2, L10_2
end

_find_ps = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  if not A2_2 then
    L6_2 = {}
    L7_2 = 0
    L8_2 = 0
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    A2_2 = L6_2
  end
  if not A5_2 then
    L6_2 = {}
    L7_2 = 2105376
    L8_2 = 2105376
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    A5_2 = L6_2
  end
  L6_2 = -1
  L7_2 = -1
  L8_2 = 1
  L9_2 = A1_2[1]
  L10_2 = 1
  for L11_2 = L8_2, L9_2, L10_2 do
    if A3_2 ~= nil then
      L12_2 = findMultiColorInRegionFuzzy
      L13_2 = A0_2[3]
      L13_2 = L13_2[1]
      L14_2 = A0_2[3]
      L14_2 = L14_2[2]
      L15_2 = A0_2[2]
      L15_2 = L15_2[1]
      L16_2 = A3_2[1]
      L17_2 = A3_2[2]
      L18_2 = A3_2[3]
      L19_2 = A3_2[4]
      L20_2 = {}
      L21_2 = A5_2[1]
      L20_2.main = L21_2
      L21_2 = A5_2[2]
      L20_2.list = L21_2
      L12_2, L13_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
      L7_2 = L13_2
      L6_2 = L12_2
    else
      L12_2 = findMultiColorInRegionFuzzy
      L13_2 = A0_2[3]
      L13_2 = L13_2[1]
      L14_2 = A0_2[3]
      L14_2 = L14_2[2]
      L15_2 = A0_2[2]
      L15_2 = L15_2[1]
      L16_2 = A0_2[1]
      L16_2 = L16_2[2]
      L17_2 = A0_2[1]
      L17_2 = L17_2[3]
      L18_2 = A0_2[1]
      L18_2 = L18_2[4]
      L19_2 = A0_2[1]
      L19_2 = L19_2[5]
      L20_2 = {}
      L21_2 = A5_2[1]
      L20_2.main = L21_2
      L21_2 = A5_2[2]
      L20_2.list = L21_2
      L12_2, L13_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
      L7_2 = L13_2
      L6_2 = L12_2
    end
    if -1 < L6_2 then
      L12_2 = A0_2[2]
      L12_2 = L12_2[2]
      if L12_2 or A4_2 then
        L12_2 = A0_2[2]
        L12_2 = L12_2[3]
        if L12_2 == 1 then
          L12_2 = _tap
          L13_2 = A2_2[1]
          L13_2 = L6_2 + L13_2
          L14_2 = A2_2[2]
          L14_2 = L7_2 + L14_2
          L12_2(L13_2, L14_2)
        else
          L12_2 = A0_2[2]
          L12_2 = L12_2[3]
          if L12_2 == 3 then
            L12_2 = _tap
            L13_2 = A2_2[1]
            L13_2 = L6_2 + L13_2
            L14_2 = A2_2[2]
            L14_2 = L7_2 + L14_2
            L15_2 = 1
            L16_2 = A0_2[2]
            L16_2 = L16_2[4]
            L12_2(L13_2, L14_2, L15_2, L16_2)
          else
            L12_2 = A0_2[2]
            L12_2 = L12_2[3]
            if L12_2 == 4 then
              L12_2 = _tap
              L13_2 = A2_2[1]
              L13_2 = L6_2 + L13_2
              L14_2 = A2_2[2]
              L14_2 = L7_2 + L14_2
              L15_2 = 2
              L16_2 = A0_2[2]
              L16_2 = L16_2[4]
              L12_2(L13_2, L14_2, L15_2, L16_2)
            else
              L12_2 = _tap
              L13_2 = A2_2[1]
              L13_2 = L6_2 + L13_2
              L14_2 = A2_2[2]
              L14_2 = L7_2 + L14_2
              L15_2 = 2
              L12_2(L13_2, L14_2, L15_2)
            end
          end
        end
      end
      L12_2 = _print
      L13_2 = A0_2[1]
      L13_2 = L13_2[1]
      L12_2(L13_2)
      L12_2 = true
      L13_2 = L6_2
      L14_2 = L7_2
      return L12_2, L13_2, L14_2
    end
    L12_2 = mSleep
    L13_2 = A1_2[2]
    L12_2(L13_2)
  end
  L8_2 = false
  L9_2 = L6_2
  L10_2 = L7_2
  return L8_2, L9_2, L10_2
end

_find_ps_cx = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  if not A1_2 then
    L5_2 = {}
    L6_2 = 0
    L7_2 = 0
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A1_2 = L5_2
  end
  if not A4_2 then
    L5_2 = {}
    L6_2 = 2105376
    L7_2 = 2105376
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A4_2 = L5_2
  end
  L5_2 = false
  L6_2 = -1
  L7_2 = -1
  L8_2 = ""
  L9_2 = keepScreen
  L10_2 = true
  L9_2(L10_2)
  if A2_2 ~= nil then
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = findMultiColorInRegionFuzzy
      L15_2 = L13_2[3]
      L15_2 = L15_2[1]
      L16_2 = L13_2[3]
      L16_2 = L16_2[2]
      L17_2 = L13_2[2]
      L17_2 = L17_2[1]
      L18_2 = A2_2[1]
      L19_2 = A2_2[2]
      L20_2 = A2_2[3]
      L21_2 = A2_2[4]
      L22_2 = {}
      L23_2 = A4_2[1]
      L22_2.main = L23_2
      L23_2 = A4_2[2]
      L22_2.list = L23_2
      L14_2, L15_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
      L7_2 = L15_2
      L6_2 = L14_2
      if -1 < L6_2 then
        L14_2 = L13_2[2]
        L14_2 = L14_2[2]
        if L14_2 or A3_2 then
          L14_2 = L13_2[2]
          L14_2 = L14_2[3]
          if L14_2 == 1 then
            L14_2 = _tap
            L15_2 = A1_2[1]
            L15_2 = L6_2 + L15_2
            L16_2 = A1_2[2]
            L16_2 = L7_2 + L16_2
            L14_2(L15_2, L16_2)
          else
            L14_2 = L13_2[2]
            L14_2 = L14_2[3]
            if L14_2 == 3 then
              L14_2 = _tap
              L15_2 = A1_2[1]
              L15_2 = L6_2 + L15_2
              L16_2 = A1_2[2]
              L16_2 = L7_2 + L16_2
              L17_2 = 1
              L18_2 = L13_2[2]
              L18_2 = L18_2[4]
              L14_2(L15_2, L16_2, L17_2, L18_2)
            else
              L14_2 = L13_2[2]
              L14_2 = L14_2[3]
              if L14_2 == 4 then
                L14_2 = _tap
                L15_2 = A1_2[1]
                L15_2 = L6_2 + L15_2
                L16_2 = A1_2[2]
                L16_2 = L7_2 + L16_2
                L17_2 = 2
                L18_2 = L13_2[2]
                L18_2 = L18_2[4]
                L14_2(L15_2, L16_2, L17_2, L18_2)
              else
                L14_2 = _tap
                L15_2 = A1_2[1]
                L15_2 = L6_2 + L15_2
                L16_2 = A1_2[2]
                L16_2 = L7_2 + L16_2
                L17_2 = 2
                L14_2(L15_2, L16_2, L17_2)
              end
            end
          end
        end
        L14_2 = _print
        L15_2 = L13_2[1]
        L15_2 = L15_2[1]
        L14_2(L15_2)
        L5_2 = true
        L14_2 = L13_2[1]
        L8_2 = L14_2[1]
        break
      end
    end
  else
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = findMultiColorInRegionFuzzy
      L15_2 = L13_2[3]
      L15_2 = L15_2[1]
      L16_2 = L13_2[3]
      L16_2 = L16_2[2]
      L17_2 = L13_2[2]
      L17_2 = L17_2[1]
      L18_2 = L13_2[1]
      L18_2 = L18_2[2]
      L19_2 = L13_2[1]
      L19_2 = L19_2[3]
      L20_2 = L13_2[1]
      L20_2 = L20_2[4]
      L21_2 = L13_2[1]
      L21_2 = L21_2[5]
      L22_2 = {}
      L23_2 = A4_2[1]
      L22_2.main = L23_2
      L23_2 = A4_2[2]
      L22_2.list = L23_2
      L14_2, L15_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
      L7_2 = L15_2
      L6_2 = L14_2
      if -1 < L6_2 then
        L14_2 = L13_2[2]
        L14_2 = L14_2[2]
        if L14_2 or A3_2 then
          L14_2 = L13_2[2]
          L14_2 = L14_2[3]
          if L14_2 == 1 then
            L14_2 = _tap
            L15_2 = A1_2[1]
            L15_2 = L6_2 + L15_2
            L16_2 = A1_2[2]
            L16_2 = L7_2 + L16_2
            L14_2(L15_2, L16_2)
          else
            L14_2 = L13_2[2]
            L14_2 = L14_2[3]
            if L14_2 == 3 then
              L14_2 = _tap
              L15_2 = A1_2[1]
              L15_2 = L6_2 + L15_2
              L16_2 = A1_2[2]
              L16_2 = L7_2 + L16_2
              L17_2 = 1
              L18_2 = L13_2[2]
              L18_2 = L18_2[4]
              L14_2(L15_2, L16_2, L17_2, L18_2)
            else
              L14_2 = L13_2[2]
              L14_2 = L14_2[3]
              if L14_2 == 4 then
                L14_2 = _tap
                L15_2 = A1_2[1]
                L15_2 = L6_2 + L15_2
                L16_2 = A1_2[2]
                L16_2 = L7_2 + L16_2
                L17_2 = 2
                L18_2 = L13_2[2]
                L18_2 = L18_2[4]
                L14_2(L15_2, L16_2, L17_2, L18_2)
              else
                L14_2 = _tap
                L15_2 = A1_2[1]
                L15_2 = L6_2 + L15_2
                L16_2 = A1_2[2]
                L16_2 = L7_2 + L16_2
                L17_2 = 2
                L14_2(L15_2, L16_2, L17_2)
              end
            end
          end
        end
        L14_2 = _print
        L15_2 = L13_2[1]
        L15_2 = L15_2[1]
        L14_2(L15_2)
        L5_2 = true
        L14_2 = L13_2[1]
        L8_2 = L14_2[1]
        break
      end
    end
  end
  L9_2 = keepScreen
  L10_2 = false
  L9_2(L10_2)
  L9_2 = L5_2
  L10_2 = L6_2
  L11_2 = L7_2
  L12_2 = L8_2
  return L9_2, L10_2, L11_2, L12_2
end

_find_ps_tb = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2
  if not A2_2 then
    L6_2 = {}
    L7_2 = 0
    L8_2 = 0
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    A2_2 = L6_2
  end
  if not A5_2 then
    L6_2 = {}
    L7_2 = 2105376
    L8_2 = 2105376
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    A5_2 = L6_2
  end
  L6_2 = -1
  L7_2 = -1
  L8_2 = ""
  if A3_2 ~= nil then
    L9_2 = 1
    L10_2 = A1_2[1]
    L11_2 = 1
    for L12_2 = L9_2, L10_2, L11_2 do
      L13_2 = keepScreen
      L14_2 = true
      L13_2(L14_2)
      L13_2 = pairs
      L14_2 = A0_2
      L13_2, L14_2, L15_2 = L13_2(L14_2)
      for L16_2, L17_2 in L13_2, L14_2, L15_2 do
        L18_2 = findMultiColorInRegionFuzzy
        L19_2 = L17_2[3]
        L19_2 = L19_2[1]
        L20_2 = L17_2[3]
        L20_2 = L20_2[2]
        L21_2 = L17_2[2]
        L21_2 = L21_2[1]
        L22_2 = A3_2[1]
        L23_2 = A3_2[2]
        L24_2 = A3_2[3]
        L25_2 = A3_2[4]
        L26_2 = {}
        L27_2 = A5_2[1]
        L26_2.main = L27_2
        L27_2 = A5_2[2]
        L26_2.list = L27_2
        L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L7_2 = L19_2
        L6_2 = L18_2
        if -1 < L6_2 then
          L18_2 = L17_2[2]
          L18_2 = L18_2[2]
          if L18_2 or A4_2 then
            L18_2 = L17_2[2]
            L18_2 = L18_2[3]
            if L18_2 == 1 then
              L18_2 = _tap
              L19_2 = A2_2[1]
              L19_2 = L6_2 + L19_2
              L20_2 = A2_2[2]
              L20_2 = L7_2 + L20_2
              L18_2(L19_2, L20_2)
            else
              L18_2 = L17_2[2]
              L18_2 = L18_2[3]
              if L18_2 == 3 then
                L18_2 = _tap
                L19_2 = A2_2[1]
                L19_2 = L6_2 + L19_2
                L20_2 = A2_2[2]
                L20_2 = L7_2 + L20_2
                L21_2 = 1
                L22_2 = L17_2[2]
                L22_2 = L22_2[4]
                L18_2(L19_2, L20_2, L21_2, L22_2)
              else
                L18_2 = L17_2[2]
                L18_2 = L18_2[3]
                if L18_2 == 4 then
                  L18_2 = _tap
                  L19_2 = A2_2[1]
                  L19_2 = L6_2 + L19_2
                  L20_2 = A2_2[2]
                  L20_2 = L7_2 + L20_2
                  L21_2 = 2
                  L22_2 = L17_2[2]
                  L22_2 = L22_2[4]
                  L18_2(L19_2, L20_2, L21_2, L22_2)
                else
                  L18_2 = _tap
                  L19_2 = A2_2[1]
                  L19_2 = L6_2 + L19_2
                  L20_2 = A2_2[2]
                  L20_2 = L7_2 + L20_2
                  L21_2 = 2
                  L18_2(L19_2, L20_2, L21_2)
                end
              end
            end
          end
          L18_2 = _print
          L19_2 = L17_2[1]
          L19_2 = L19_2[1]
          L18_2(L19_2)
          L18_2 = L17_2[1]
          L8_2 = L18_2[1]
          L18_2 = keepScreen
          L19_2 = false
          L18_2(L19_2)
          L18_2 = true
          L19_2 = L6_2
          L20_2 = L7_2
          L21_2 = L8_2
          return L18_2, L19_2, L20_2, L21_2
        end
      end
      L13_2 = keepScreen
      L14_2 = false
      L13_2(L14_2)
      L13_2 = mSleep
      L14_2 = A1_2[2]
      L13_2(L14_2)
    end
  else
    L9_2 = 1
    L10_2 = A1_2[1]
    L11_2 = 1
    for L12_2 = L9_2, L10_2, L11_2 do
      L13_2 = keepScreen
      L14_2 = true
      L13_2(L14_2)
      L13_2 = pairs
      L14_2 = A0_2
      L13_2, L14_2, L15_2 = L13_2(L14_2)
      for L16_2, L17_2 in L13_2, L14_2, L15_2 do
        L18_2 = findMultiColorInRegionFuzzy
        L19_2 = L17_2[3]
        L19_2 = L19_2[1]
        L20_2 = L17_2[3]
        L20_2 = L20_2[2]
        L21_2 = L17_2[2]
        L21_2 = L21_2[1]
        L22_2 = L17_2[1]
        L22_2 = L22_2[2]
        L23_2 = L17_2[1]
        L23_2 = L23_2[3]
        L24_2 = L17_2[1]
        L24_2 = L24_2[4]
        L25_2 = L17_2[1]
        L25_2 = L25_2[5]
        L26_2 = {}
        L27_2 = A5_2[1]
        L26_2.main = L27_2
        L27_2 = A5_2[2]
        L26_2.list = L27_2
        L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
        L7_2 = L19_2
        L6_2 = L18_2
        if -1 < L6_2 then
          L18_2 = L17_2[2]
          L18_2 = L18_2[2]
          if L18_2 or A4_2 then
            L18_2 = L17_2[2]
            L18_2 = L18_2[3]
            if L18_2 == 1 then
              L18_2 = _tap
              L19_2 = A2_2[1]
              L19_2 = L6_2 + L19_2
              L20_2 = A2_2[2]
              L20_2 = L7_2 + L20_2
              L18_2(L19_2, L20_2)
            else
              L18_2 = L17_2[2]
              L18_2 = L18_2[3]
              if L18_2 == 3 then
                L18_2 = _tap
                L19_2 = A2_2[1]
                L19_2 = L6_2 + L19_2
                L20_2 = A2_2[2]
                L20_2 = L7_2 + L20_2
                L21_2 = 1
                L22_2 = L17_2[2]
                L22_2 = L22_2[4]
                L18_2(L19_2, L20_2, L21_2, L22_2)
              else
                L18_2 = L17_2[2]
                L18_2 = L18_2[3]
                if L18_2 == 4 then
                  L18_2 = _tap
                  L19_2 = A2_2[1]
                  L19_2 = L6_2 + L19_2
                  L20_2 = A2_2[2]
                  L20_2 = L7_2 + L20_2
                  L21_2 = 2
                  L22_2 = L17_2[2]
                  L22_2 = L22_2[4]
                  L18_2(L19_2, L20_2, L21_2, L22_2)
                else
                  L18_2 = _tap
                  L19_2 = A2_2[1]
                  L19_2 = L6_2 + L19_2
                  L20_2 = A2_2[2]
                  L20_2 = L7_2 + L20_2
                  L21_2 = 2
                  L18_2(L19_2, L20_2, L21_2)
                end
              end
            end
          end
          L18_2 = _print
          L19_2 = L17_2[1]
          L19_2 = L19_2[1]
          L18_2(L19_2)
          L18_2 = L17_2[1]
          L8_2 = L18_2[1]
          L18_2 = keepScreen
          L19_2 = false
          L18_2(L19_2)
          L18_2 = true
          L19_2 = L6_2
          L20_2 = L7_2
          L21_2 = L8_2
          return L18_2, L19_2, L20_2, L21_2
        end
      end
      L13_2 = keepScreen
      L14_2 = false
      L13_2(L14_2)
      L13_2 = mSleep
      L14_2 = A1_2[2]
      L13_2(L14_2)
    end
  end
  L9_2 = keepScreen
  L10_2 = false
  L9_2(L10_2)
  L9_2 = false
  L10_2 = -1
  L11_2 = -1
  return L9_2, L10_2, L11_2
end

_find_ps_tb_cx = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2
  if not A2_2 then
    L5_2 = {}
    L6_2 = 1
    L7_2 = 0
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A2_2 = L5_2
  end
  if not A3_2 then
    A3_2 = 1
  end
  if not A4_2 then
    L5_2 = {}
    L6_2 = 2105376
    L7_2 = 2105376
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    A4_2 = L5_2
  end
  L5_2 = {}
  L6_2 = 1
  L7_2 = A2_2[1]
  L8_2 = 1
  for L9_2 = L6_2, L7_2, L8_2 do
    L10_2 = keepScreen
    L11_2 = true
    L10_2(L11_2)
    L10_2 = pairs
    L11_2 = A0_2
    L10_2, L11_2, L12_2 = L10_2(L11_2)
    for L13_2, L14_2 in L10_2, L11_2, L12_2 do
      L15_2 = findMultiColorInRegionFuzzyExt
      L16_2 = L14_2[3]
      L16_2 = L16_2[1]
      L17_2 = L14_2[3]
      L17_2 = L17_2[2]
      L18_2 = L14_2[2]
      L18_2 = L18_2[1]
      L19_2 = A1_2[1]
      L20_2 = A1_2[2]
      L21_2 = A1_2[3]
      L22_2 = A1_2[4]
      L23_2 = {}
      L24_2 = A4_2[1]
      L23_2.main = L24_2
      L24_2 = A4_2[2]
      L23_2.list = L24_2
      L23_2.orient = A3_2
      L15_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
      L16_2 = #L15_2
      if L16_2 ~= 0 then
        L16_2 = _print
        L17_2 = L14_2[1]
        L17_2 = L17_2[1]
        L16_2(L17_2)
        L16_2 = pairs
        L17_2 = L15_2
        L16_2, L17_2, L18_2 = L16_2(L17_2)
        for L19_2, L20_2 in L16_2, L17_2, L18_2 do
          L21_2 = #L5_2
          L21_2 = L21_2 + 1
          L5_2[L21_2] = L20_2
        end
      end
    end
    L10_2 = #L5_2
    if L10_2 ~= 0 then
      L10_2 = keepScreen
      L11_2 = false
      L10_2(L11_2)
      break
    end
    L10_2 = mSleep
    L11_2 = A2_2[2]
    L10_2(L11_2)
    L10_2 = keepScreen
    L11_2 = false
    L10_2(L11_2)
  end
  L6_2 = #L5_2
  if L6_2 ~= 0 then
    L6_2 = 1
    L7_2 = #L5_2
    L7_2 = L7_2 - 1
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = L9_2 + 1
      L11_2 = #L5_2
      L12_2 = 1
      for L13_2 = L10_2, L11_2, L12_2 do
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2.x
        L15_2 = L5_2[L13_2]
        L15_2 = L15_2.x
        L14_2 = L14_2 - L15_2
        L15_2 = L5_2[L9_2]
        L15_2 = L15_2.y
        L16_2 = L5_2[L13_2]
        L16_2 = L16_2.y
        L15_2 = L15_2 - L16_2
        L16_2 = math
        L16_2 = L16_2.abs
        L17_2 = L14_2
        L16_2 = L16_2(L17_2)
        if L16_2 < 15 then
          L16_2 = math
          L16_2 = L16_2.abs
          L17_2 = L15_2
          L16_2 = L16_2(L17_2)
          if L16_2 < 15 then
            L16_2 = L5_2[L13_2]
            L16_2.y = -1
          end
        end
      end
    end
    L6_2 = #L5_2
    L7_2 = 1
    L8_2 = -1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = L5_2[L9_2]
      L10_2 = L10_2.y
      if L10_2 == -1 then
        L10_2 = table
        L10_2 = L10_2.remove
        L11_2 = L5_2
        L12_2 = L9_2
        L10_2(L11_2, L12_2)
      end
    end
  end
  if A3_2 == 1 then
    L6_2 = table
    L6_2 = L6_2.sort
    L7_2 = L5_2
    
    function L8_2(A0_3, A1_3)
      local L2_3, L3_3
      L2_3 = A0_3.y
      L3_3 = A1_3.y
      L2_3 = L2_3 < L3_3
      return L2_3
    end
    
    L6_2(L7_2, L8_2)
  end
  return L5_2
end

_find_ps_ex = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L3_2 = "rb"
  L1_2 = L1_2(L2_2, L3_2)
  L3_2 = L1_2
  L2_2 = L1_2.read
  L4_2 = "*all"
  L2_2 = L2_2(L3_2, L4_2)
  L4_2 = L1_2
  L3_2 = L1_2.close
  L3_2(L4_2)
  L3_2 = L2_2
  L4_2 = bit32
  L4_2 = L4_2.bor
  L5_2 = bit32
  L5_2 = L5_2.band
  L6_2 = bit32
  L6_2 = L6_2.lshift
  L7_2 = bit32
  L7_2 = L7_2.rshift
  L8_2 = {}
  L9_2 = "A"
  L10_2 = "B"
  L11_2 = "C"
  L12_2 = "D"
  L13_2 = "E"
  L14_2 = "F"
  L15_2 = "G"
  L16_2 = "H"
  L17_2 = "I"
  L18_2 = "J"
  L19_2 = "K"
  L20_2 = "L"
  L21_2 = "M"
  L22_2 = "N"
  L23_2 = "O"
  L24_2 = "P"
  L25_2 = "Q"
  L26_2 = "R"
  L27_2 = "S"
  L28_2 = "T"
  L29_2 = "U"
  L30_2 = "V"
  L31_2 = "W"
  L32_2 = "X"
  L33_2 = "Y"
  L34_2 = "Z"
  L35_2 = "a"
  L36_2 = "b"
  L37_2 = "c"
  L38_2 = "d"
  L39_2 = "e"
  L40_2 = "f"
  L41_2 = "g"
  L42_2 = "h"
  L43_2 = "i"
  L44_2 = "j"
  L45_2 = "k"
  L46_2 = "l"
  L47_2 = "m"
  L48_2 = "n"
  L49_2 = "o"
  L50_2 = "p"
  L51_2 = "q"
  L52_2 = "r"
  L53_2 = "s"
  L54_2 = "t"
  L55_2 = "u"
  L56_2 = "v"
  L57_2 = "w"
  L58_2 = "x"
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L8_2[4] = L12_2
  L8_2[5] = L13_2
  L8_2[6] = L14_2
  L8_2[7] = L15_2
  L8_2[8] = L16_2
  L8_2[9] = L17_2
  L8_2[10] = L18_2
  L8_2[11] = L19_2
  L8_2[12] = L20_2
  L8_2[13] = L21_2
  L8_2[14] = L22_2
  L8_2[15] = L23_2
  L8_2[16] = L24_2
  L8_2[17] = L25_2
  L8_2[18] = L26_2
  L8_2[19] = L27_2
  L8_2[20] = L28_2
  L8_2[21] = L29_2
  L8_2[22] = L30_2
  L8_2[23] = L31_2
  L8_2[24] = L32_2
  L8_2[25] = L33_2
  L8_2[26] = L34_2
  L8_2[27] = L35_2
  L8_2[28] = L36_2
  L8_2[29] = L37_2
  L8_2[30] = L38_2
  L8_2[31] = L39_2
  L8_2[32] = L40_2
  L8_2[33] = L41_2
  L8_2[34] = L42_2
  L8_2[35] = L43_2
  L8_2[36] = L44_2
  L8_2[37] = L45_2
  L8_2[38] = L46_2
  L8_2[39] = L47_2
  L8_2[40] = L48_2
  L8_2[41] = L49_2
  L8_2[42] = L50_2
  L8_2[43] = L51_2
  L8_2[44] = L52_2
  L8_2[45] = L53_2
  L8_2[46] = L54_2
  L8_2[47] = L55_2
  L8_2[48] = L56_2
  L8_2[49] = L57_2
  L8_2[50] = L58_2
  L9_2 = "y"
  L10_2 = "z"
  L11_2 = "0"
  L12_2 = "1"
  L13_2 = "2"
  L14_2 = "3"
  L15_2 = "4"
  L16_2 = "5"
  L17_2 = "6"
  L18_2 = "7"
  L19_2 = "8"
  L20_2 = "9"
  L21_2 = "+"
  L22_2 = "/"
  L8_2[51] = L9_2
  L8_2[52] = L10_2
  L8_2[53] = L11_2
  L8_2[54] = L12_2
  L8_2[55] = L13_2
  L8_2[56] = L14_2
  L8_2[57] = L15_2
  L8_2[58] = L16_2
  L8_2[59] = L17_2
  L8_2[60] = L18_2
  L8_2[61] = L19_2
  L8_2[62] = L20_2
  L8_2[63] = L21_2
  L8_2[64] = L22_2
  cTable = L8_2
  L8_2 = {}
  L9_2 = nil
  L10_2 = nil
  L11_2 = nil
  L12_2 = nil
  L13_2 = nil
  L14_2 = nil
  L15_2 = nil
  L16_2 = math
  L16_2 = L16_2.floor
  L18_2 = L3_2
  L17_2 = L3_2.len
  L17_2 = L17_2(L18_2)
  L17_2 = L17_2 / 3
  L16_2 = L16_2(L17_2)
  L16_2 = L16_2 * 3
  L17_2 = 0
  L18_2 = 1
  L19_2 = L16_2
  L20_2 = 3
  for L21_2 = L18_2, L19_2, L20_2 do
    L17_2 = L17_2 + 1
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2
    L22_2 = L22_2(L23_2, L24_2)
    L9_2 = L22_2
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2 + 1
    L22_2 = L22_2(L23_2, L24_2)
    L10_2 = L22_2
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2 + 2
    L22_2 = L22_2(L23_2, L24_2)
    L11_2 = L22_2
    L22_2 = L7_2
    L23_2 = L9_2
    L24_2 = 2
    L22_2 = L22_2(L23_2, L24_2)
    L12_2 = L22_2
    L22_2 = L5_2
    L23_2 = L4_2
    L24_2 = L6_2
    L25_2 = L9_2
    L26_2 = 4
    L24_2 = L24_2(L25_2, L26_2)
    L25_2 = L7_2
    L26_2 = L10_2
    L27_2 = 4
    L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2 = L25_2(L26_2, L27_2)
    L23_2 = L23_2(L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2)
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L13_2 = L22_2
    L22_2 = L5_2
    L23_2 = L4_2
    L24_2 = L6_2
    L25_2 = L10_2
    L26_2 = 2
    L24_2 = L24_2(L25_2, L26_2)
    L25_2 = L7_2
    L26_2 = L11_2
    L27_2 = 6
    L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2 = L25_2(L26_2, L27_2)
    L23_2 = L23_2(L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2)
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L14_2 = L22_2
    L22_2 = L5_2
    L23_2 = L11_2
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L15_2 = L22_2
    L22_2 = cTable
    L23_2 = L12_2 + 1
    L22_2 = L22_2[L23_2]
    L23_2 = cTable
    L24_2 = L13_2 + 1
    L23_2 = L23_2[L24_2]
    L24_2 = cTable
    L25_2 = L14_2 + 1
    L24_2 = L24_2[L25_2]
    L25_2 = cTable
    L26_2 = L15_2 + 1
    L25_2 = L25_2[L26_2]
    L22_2 = L22_2 .. L23_2 .. L24_2 .. L25_2
    L8_2[L17_2] = L22_2
  end
  L17_2 = L17_2 + 1
  L19_2 = L3_2
  L18_2 = L3_2.len
  L18_2 = L18_2(L19_2)
  L18_2 = L18_2 % 3
  if L18_2 == 1 then
    L19_2 = L3_2
    L18_2 = L3_2.byte
    L20_2 = L16_2 + 1
    L18_2 = L18_2(L19_2, L20_2)
    L9_2 = L18_2
    L18_2 = L7_2
    L19_2 = L5_2
    L20_2 = L9_2
    L21_2 = 252
    L19_2 = L19_2(L20_2, L21_2)
    L20_2 = 2
    L18_2 = L18_2(L19_2, L20_2)
    L12_2 = L18_2
    L18_2 = L6_2
    L19_2 = L5_2
    L20_2 = L9_2
    L21_2 = 3
    L19_2 = L19_2(L20_2, L21_2)
    L20_2 = 4
    L18_2 = L18_2(L19_2, L20_2)
    L13_2 = L18_2
    L18_2 = cTable
    L19_2 = L12_2 + 1
    L18_2 = L18_2[L19_2]
    L19_2 = cTable
    L20_2 = L13_2 + 1
    L19_2 = L19_2[L20_2]
    L20_2 = "=="
    L18_2 = L18_2 .. L19_2 .. L20_2
    L8_2[L17_2] = L18_2
  else
    L19_2 = L3_2
    L18_2 = L3_2.len
    L18_2 = L18_2(L19_2)
    L18_2 = L18_2 % 3
    if L18_2 == 2 then
      L19_2 = L3_2
      L18_2 = L3_2.byte
      L20_2 = L16_2 + 1
      L18_2 = L18_2(L19_2, L20_2)
      L9_2 = L18_2
      L19_2 = L3_2
      L18_2 = L3_2.byte
      L20_2 = L16_2 + 2
      L18_2 = L18_2(L19_2, L20_2)
      L10_2 = L18_2
      L18_2 = L7_2
      L19_2 = L5_2
      L20_2 = L9_2
      L21_2 = 252
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = 2
      L18_2 = L18_2(L19_2, L20_2)
      L12_2 = L18_2
      L18_2 = L4_2
      L19_2 = L6_2
      L20_2 = L5_2
      L21_2 = L9_2
      L22_2 = 3
      L20_2 = L20_2(L21_2, L22_2)
      L21_2 = 4
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = L7_2
      L21_2 = L5_2
      L22_2 = L10_2
      L23_2 = 240
      L21_2 = L21_2(L22_2, L23_2)
      L22_2 = 4
      L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2 = L20_2(L21_2, L22_2)
      L18_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2)
      L13_2 = L18_2
      L18_2 = L6_2
      L19_2 = L5_2
      L20_2 = L10_2
      L21_2 = 15
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = 2
      L18_2 = L18_2(L19_2, L20_2)
      L14_2 = L18_2
      L18_2 = cTable
      L19_2 = L12_2 + 1
      L18_2 = L18_2[L19_2]
      L19_2 = cTable
      L20_2 = L13_2 + 1
      L19_2 = L19_2[L20_2]
      L20_2 = cTable
      L21_2 = L14_2 + 1
      L20_2 = L20_2[L21_2]
      L21_2 = "="
      L18_2 = L18_2 .. L19_2 .. L20_2 .. L21_2
      L8_2[L17_2] = L18_2
    end
  end
  L18_2 = table
  L18_2 = L18_2.concat
  L19_2 = L8_2
  return L18_2(L19_2)
end

_ReadBase64 = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = fwShowWnd
  L2_2 = "wid"
  L3_2 = 660
  L4_2 = 200
  L5_2 = 1375
  L6_2 = 905
  L7_2 = 1
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L1_2 = fwShowButton
  L2_2 = "wid"
  L3_2 = "vid"
  L4_2 = ""
  L5_2 = ""
  L6_2 = ""
  L7_2 = A0_2
  L8_2 = ".png"
  L7_2 = L7_2 .. L8_2
  L8_2 = 15
  L9_2 = 0
  L10_2 = 0
  L11_2 = 600
  L12_2 = 600
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L1_2 = nil
  L2_2 = os
  L2_2 = L2_2.time
  L2_2 = L2_2()
  L3_2 = _ENV["UI_贼王处理"]
  if L3_2 == "一直等待人工处理" then
    _ENV["错误等待时间"] = 9999
  else
    _ENV["错误等待时间"] = 300
  end
  while true do
    L3_2 = fwGetPressedButton
    L3_2 = L3_2()
    if L3_2 == "vid" then
      L4_2 = fwCloseWnd
      L5_2 = "wid"
      L4_2(L5_2)
      L4_2 = playAudio
      L5_2 = ""
      L4_2(L5_2)
      L4_2 = mSleep
      L5_2 = 2000
      L4_2(L5_2)
      L4_2 = true
      return L4_2
    end
    L4_2 = UI_msg
    if L4_2 == "震动提示" then
      if L1_2 ~= nil then
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L4_2 = L4_2 - L1_2
      end
      if 1 < L4_2 then
        L4_2 = vibrator
        L4_2()
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L1_2 = L4_2
      end
    else
      L4_2 = UI_msg
      if L4_2 == "响铃" then
        if L1_2 ~= nil then
          L4_2 = os
          L4_2 = L4_2.time
          L4_2 = L4_2()
          L4_2 = L4_2 - L1_2
          if not (30 < L4_2) then
            goto lbl_88
          end
        end
        L4_2 = setVolumeLevel
        L5_2 = 1
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = "msg.mp3"
        L4_2(L5_2)
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L1_2 = L4_2
      end
    end
    ::lbl_88::
    L4_2 = os
    L4_2 = L4_2.time
    L4_2 = L4_2()
    L4_2 = L4_2 - L2_2
    L5_2 = _ENV["错误等待时间"]
    if L4_2 > L5_2 then
      if A0_2 == "dd_zw" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = mSleep
        L5_2 = 2000
        L4_2(L5_2)
        L4_2 = false
        return L4_2
      elseif A0_2 == "dd_7" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = closeApp
        L5_2 = "com.netease.mhxyhtb"
        L4_2(L5_2)
        L4_2 = dialog
        L5_2 = "旗子处理提示超过5分钟,自动关闭游戏！"
        L6_2 = time
        L4_2(L5_2, L6_2)
        L4_2 = lua_exit
        L4_2()
        L4_2 = mSleep
        L5_2 = 10
        L4_2(L5_2)
      elseif A0_2 == "dd_wl" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = closeApp
        L5_2 = "com.netease.mhxyhtb"
        L4_2(L5_2)
        L4_2 = dialog
        L5_2 = "网络正常,无法通讯服务器,提示超过5分钟,自动关闭游戏！"
        L6_2 = time
        L4_2(L5_2, L6_2)
        L4_2 = lua_exit
        L4_2()
        L4_2 = mSleep
        L5_2 = 10
        L4_2(L5_2)
      end
    end
    L4_2 = mSleep
    L5_2 = 200
    L4_2(L5_2)
  end
end

_call_script = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = math
  L1_2 = L1_2.random
  L2_2 = 1
  L3_2 = #A0_2
  L1_2 = L1_2(L2_2, L3_2)
  L2_2 = A0_2[L1_2]
  return L2_2
end

randomList = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L7_2 = findImageInRegionFuzzy
  L8_2 = A0_2
  L9_2 = A6_2
  L10_2 = A1_2
  L11_2 = A2_2
  L12_2 = A3_2
  L13_2 = A4_2
  L14_2 = 0
  L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
  if -1 < L7_2 then
    if A5_2 then
      L9_2 = _tap
      L10_2 = L7_2
      L11_2 = L8_2
      L9_2(L10_2, L11_2)
    end
    L9_2 = _print
    L10_2 = A0_2
    L9_2(L10_2)
    L9_2 = true
    L10_2 = L7_2
    L11_2 = L8_2
    return L9_2, L10_2, L11_2
  else
    L9_2 = false
    return L9_2
  end
end

_findImg = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L2_2 = nil
  L3_2 = nil
  L4_2 = pairs
  L5_2 = A0_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = findMultiColorInRegionFuzzyByTable
    L10_2 = L8_2[2]
    L11_2 = 85
    L12_2 = A1_2[1]
    L13_2 = A1_2[2]
    L14_2 = A1_2[3]
    L15_2 = A1_2[4]
    L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L3_2 = L10_2
    L2_2 = L9_2
    if -1 < L2_2 then
      L9_2 = _tap
      L10_2 = L2_2
      L11_2 = L3_2
      L9_2(L10_2, L11_2)
      L9_2 = _print
      L10_2 = L8_2[1]
      L9_2(L10_2)
      L9_2 = true
      return L9_2
    end
  end
  L4_2 = false
  return L4_2
end

p_find_tb = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L3_2 = "rb"
  L1_2 = L1_2(L2_2, L3_2)
  L3_2 = L1_2
  L2_2 = L1_2.read
  L4_2 = "*all"
  L2_2 = L2_2(L3_2, L4_2)
  L4_2 = L1_2
  L3_2 = L1_2.close
  L3_2(L4_2)
  return L2_2
end

ReadFileByte = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  return L2_2
end

_ENV["_合并table"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = 1
  L2_2 = #A0_2
  L2_2 = L2_2 - 1
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = L4_2 + 1
    L6_2 = #A0_2
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = A0_2[L4_2]
      L9_2 = L9_2.game_x
      L10_2 = A0_2[L8_2]
      L10_2 = L10_2.game_x
      if L9_2 == L10_2 then
        L9_2 = A0_2[L4_2]
        L9_2 = L9_2.game_y
        L10_2 = A0_2[L8_2]
        L10_2 = L10_2.game_y
        if L9_2 == L10_2 then
          L9_2 = A0_2[L8_2]
          L9_2.game_x = -1
        end
      end
    end
  end
  L1_2 = #A0_2
  L2_2 = 1
  L3_2 = -1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = A0_2[L4_2]
    L5_2 = L5_2.game_x
    if L5_2 == -1 then
      L5_2 = table
      L5_2 = L5_2.remove
      L6_2 = A0_2
      L7_2 = L4_2
      L5_2(L6_2, L7_2)
    end
  end
end

_ENV["_table坐标去重"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = #L2_2
  L4_2 = 1
  L5_2 = L3_2 - 1
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L7_2 + 1
    L9_2 = L3_2
    L10_2 = 1
    for L11_2 = L8_2, L9_2, L10_2 do
      L12_2 = L2_2[L7_2]
      L12_2 = L12_2.y
      L13_2 = L2_2[L11_2]
      L13_2 = L13_2.y
      L12_2 = L12_2 - L13_2
      if L12_2 < 5 and -5 < L12_2 then
        L13_2 = L2_2[L11_2]
        L13_2.y = -1
      end
    end
  end
  L4_2 = #L2_2
  L5_2 = 1
  L6_2 = -1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L2_2[L7_2]
    L8_2 = L8_2.y
    if L8_2 == -1 then
      L8_2 = table
      L8_2 = L8_2.remove
      L9_2 = L2_2
      L10_2 = L7_2
      L8_2(L9_2, L10_2)
    end
  end
  return L2_2
end

_ENV["_table合并去重"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = 1
  L4_2 = #L2_2
  L4_2 = L4_2 - 1
  L5_2 = 1
  for L6_2 = L3_2, L4_2, L5_2 do
    L7_2 = L6_2 + 1
    L8_2 = #L2_2
    L9_2 = 1
    for L10_2 = L7_2, L8_2, L9_2 do
      L11_2 = L2_2[L6_2]
      L11_2 = L11_2.game_x
      L12_2 = L2_2[L10_2]
      L12_2 = L12_2.game_x
      if L11_2 == L12_2 then
        L11_2 = L2_2[L6_2]
        L11_2 = L11_2.game_y
        L12_2 = L2_2[L10_2]
        L12_2 = L12_2.game_y
        if L11_2 == L12_2 then
          L11_2 = L2_2[L10_2]
          L11_2.game_y = -1
        end
      end
    end
  end
  L3_2 = #L2_2
  L4_2 = 1
  L5_2 = -1
  for L6_2 = L3_2, L4_2, L5_2 do
    L7_2 = L2_2[L6_2]
    L7_2 = L7_2.game_y
    if L7_2 == -1 then
      L7_2 = table
      L7_2 = L7_2.remove
      L8_2 = L2_2
      L9_2 = L6_2
      L7_2(L8_2, L9_2)
    end
  end
  return L2_2
end

_ENV["_table合并去重g"] = L0_1
