#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lua优化器演示脚本
展示优化前后的对比效果
"""

import os
from simple_lua_optimizer import SimpleLuaOptimizer


def demo_optimization():
    """演示优化效果"""
    print("=== Lua代码优化器演示 ===\n")
    
    # 测试用例1：require语句优化
    print("1. require语句优化:")
    test_require = """local L0_1, L1_1, L2_1, L3_1
L0_1 = require
L1_1 = "TSLib"
L0_1(L1_1)
L0_1 = require
L1_1 = "红尘试炼"
L0_1(L1_1)"""
    
    optimizer = SimpleLuaOptimizer()
    optimized_require = optimizer.optimize_code(test_require)
    
    print("优化前:")
    print(test_require)
    print("\n优化后:")
    print(optimized_require)
    print("\n" + "="*50 + "\n")
    
    # 测试用例2：函数调用优化
    print("2. 函数调用优化:")
    test_function = """function test()
  local L0_2, L1_2, L2_2
  L0_2 = mSleep
  L1_2 = 1
  L0_2(L1_2)
  L0_2 = UINew
  L1_2 = 5
  L2_2 = "test"
  L0_2(L1_2, L2_2)
end"""
    
    optimized_function = optimizer.optimize_code(test_function)
    
    print("优化前:")
    print(test_function)
    print("\n优化后:")
    print(optimized_function)
    print("\n" + "="*50 + "\n")
    
    # 测试用例3：返回值赋值优化
    print("3. 返回值赋值优化:")
    test_return = """L0_1 = getScreenSize
L0_1, L1_1 = L0_1()
hhhh = L1_1
wwww = L0_1"""
    
    optimized_return = optimizer.optimize_code(test_return)
    
    print("优化前:")
    print(test_return)
    print("\n优化后:")
    print(optimized_return)
    print("\n" + "="*50 + "\n")
    
    # 测试用例4：复杂条件语句优化
    print("4. 条件语句优化:")
    test_condition = """if L0_2 == false then
  goto lbl_10
end
L0_2 = doSomething
L0_2()
::lbl_10::
L0_2 = doOtherThing
L0_2()"""
    
    optimized_condition = optimizer.optimize_code(test_condition)
    
    print("优化前:")
    print(test_condition)
    print("\n优化后:")
    print(optimized_condition)
    print("\n" + "="*50 + "\n")


def analyze_file_optimization(input_file: str):
    """分析文件优化效果"""
    if not os.path.exists(input_file):
        print(f"文件 {input_file} 不存在")
        return
    
    print(f"=== 分析文件 {input_file} 的优化效果 ===\n")
    
    # 读取原始文件
    with open(input_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # 优化文件
    optimizer = SimpleLuaOptimizer()
    optimized_content = optimizer.optimize_code(original_content)
    
    # 统计信息
    original_lines = original_content.split('\n')
    optimized_lines = optimized_content.split('\n')
    
    original_size = len(original_content)
    optimized_size = len(optimized_content)
    
    # 统计require语句
    original_requires = len([line for line in original_lines if 'require' in line])
    optimized_requires = len([line for line in optimized_lines if 'require' in line])
    
    # 统计L变量
    original_l_vars = len([line for line in original_lines if re.search(r'L\d+_\d+', line)])
    optimized_l_vars = len([line for line in optimized_lines if re.search(r'L\d+_\d+', line)])
    
    # 统计goto语句
    original_gotos = len([line for line in original_lines if 'goto' in line])
    optimized_gotos = len([line for line in optimized_lines if 'goto' in line])
    
    print(f"文件大小: {original_size} -> {optimized_size} 字节 ({(optimized_size-original_size)/original_size*100:+.1f}%)")
    print(f"代码行数: {len(original_lines)} -> {len(optimized_lines)} 行 ({(len(optimized_lines)-len(original_lines))/len(original_lines)*100:+.1f}%)")
    print(f"require语句: {original_requires} -> {optimized_requires} ({optimized_requires-original_requires:+d})")
    print(f"L变量使用: {original_l_vars} -> {optimized_l_vars} 行 ({(optimized_l_vars-original_l_vars)/original_l_vars*100:+.1f}%)")
    print(f"goto语句: {original_gotos} -> {optimized_gotos} ({optimized_gotos-original_gotos:+d})")
    
    # 显示优化示例
    print("\n优化示例对比:")
    print("原始代码前10行:")
    for i, line in enumerate(original_lines[:10]):
        print(f"{i+1:2d}: {line}")
    
    print("\n优化后代码前10行:")
    for i, line in enumerate(optimized_lines[:10]):
        print(f"{i+1:2d}: {line}")


def create_usage_guide():
    """创建使用指南"""
    guide = """
# Lua代码优化器使用指南

## 功能特性

1. **require语句优化**
   - 将分散的require调用合并为单行
   - 消除中间变量

2. **函数调用优化**
   - 简化函数调用模式
   - 减少临时变量使用

3. **返回值赋值优化**
   - 优化多返回值赋值
   - 直接赋值给目标变量

4. **条件语句优化**
   - 简化if-goto模式
   - 移除不必要的标签

## 使用方法

### 命令行使用
```bash
python simple_lua_optimizer.py input.lua output.lua
```

### Python代码使用
```python
from simple_lua_optimizer import SimpleLuaOptimizer

optimizer = SimpleLuaOptimizer()
optimized_code = optimizer.optimize_file("input.lua", "output.lua")
```

## 优化示例

### 优化前
```lua
local L0_1, L1_1, L2_1, L3_1
L0_1 = require
L1_1 = "TSLib"
L0_1(L1_1)
L0_1 = require
L1_1 = "红尘试炼"
L0_1(L1_1)
```

### 优化后
```lua
local L0_1, L1_1, L2_1, L3_1
require("TSLib")
require("红尘试炼")
```

## 注意事项

1. 优化器主要针对unluac反编译输出
2. 建议在优化前备份原始文件
3. 优化后请测试代码功能是否正常
4. 复杂的控制流可能需要手动调整

## 技术限制

- 目前主要处理简单的模式匹配
- 复杂的goto语句优化仍在改进中
- 变量作用域分析相对简单
"""
    
    with open("lua_optimizer_guide.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("使用指南已保存到 lua_optimizer_guide.md")


def main():
    """主函数"""
    print("Lua代码优化器演示程序\n")
    
    # 运行演示
    demo_optimization()
    
    # 分析实际文件
    if os.path.exists("main.lua"):
        analyze_file_optimization("main.lua")
    
    # 创建使用指南
    create_usage_guide()
    
    print("\n演示完成！")


if __name__ == "__main__":
    import re  # 添加缺失的导入
    main()
