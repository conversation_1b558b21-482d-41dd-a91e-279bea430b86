local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1
L0_1 = require
L1_1 = "sz"
L0_1 = L0_1(L1_1)
L1_1 = L0_1.json
L2_1 = require
L3_1 = "socket"
L2_1 = L2_1(L3_1)
L3_1 = require
L4_1 = "szocket.http"
L3_1 = L3_1(L4_1)
L4_1 = require
L5_1 = "mime"
L4_1 = L4_1(L5_1)
SERVER_HOST = "http://**************:5555/"
CARD_NUM = "HP6EUYYGL5SUVG4N84CK"
AUTH_KEY = "DWVAPX76BK9ADARRZAYU"
time = 0
L5_1 = userPath
L5_1 = L5_1()
L6_1 = "/res/"
L5_1 = L5_1 .. L6_1
image_dir = L5_1

function L5_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L4_2 = SERVER_HOST
  L5_2 = A1_2
  L4_2 = L4_2 .. L5_2
  L5_2 = {}
  L6_2 = SCRECT_KEY
  if L6_2 == nil then
    L6_2 = {}
    L6_2.card_num = A0_2
    L7_2 = AUTH_KEY
    L6_2.auth_key = L7_2
    L5_2 = L6_2
  else
    L6_2 = string
    L6_2 = L6_2.format
    L7_2 = "%s_%s"
    L8_2 = A0_2
    L9_2 = L2_1.gettime
    L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L9_2()
    L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
    L8_2 = L6_2
    L7_2 = L6_2.aes128_encrypt
    L9_2 = SCRECT_KEY
    L7_2 = L7_2(L8_2, L9_2)
    L8_2 = L7_2
    L7_2 = L7_2.base64_encode
    L7_2 = L7_2(L8_2)
    L8_2 = {}
    L9_2 = urlEncoder
    L10_2 = L7_2
    L9_2 = L9_2(L10_2)
    L8_2.card_num = L9_2
    L9_2 = AUTH_KEY
    L8_2.auth_key = L9_2
    L8_2.secret_key = 1
    L5_2 = L8_2
  end
  if A2_2 ~= nil then
    L6_2 = io
    L6_2 = L6_2.open
    L7_2 = A2_2
    L8_2 = "rb"
    L6_2 = L6_2(L7_2, L8_2)
    if L6_2 then
      L8_2 = L6_2
      L7_2 = L6_2.read
      L9_2 = "*a"
      L7_2 = L7_2(L8_2, L9_2)
      L9_2 = L6_2
      L8_2 = L6_2.close
      L8_2(L9_2)
      L8_2 = L4_1.b64
      L9_2 = L7_2
      L8_2 = L8_2(L9_2)
      L9_2 = urlEncoder
      L10_2 = L8_2
      L9_2 = L9_2(L10_2)
      L5_2.image = L9_2
    else
      L7_2 = {}
      L7_2.err_code = 1023
      L7_2.err_msg = "Failed to open image file"
      return L7_2
    end
  end
  if A3_2 ~= nil then
    L6_2 = pairs
    L7_2 = A3_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L5_2[L9_2] = L10_2
    end
  end
  L6_2 = ""
  L7_2 = pairs
  L8_2 = L5_2
  L7_2, L8_2, L9_2 = L7_2(L8_2)
  for L10_2, L11_2 in L7_2, L8_2, L9_2 do
    L12_2 = L6_2
    L13_2 = L10_2
    L14_2 = "="
    L15_2 = L11_2
    L16_2 = "&"
    L6_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2
  end
  L7_2 = {}
  L8_2 = L3_1.request
  L9_2 = {}
  L9_2.method = "POST"
  L9_2.url = L4_2
  L10_2 = {}
  L10_2.Expect = ""
  L10_2["Content-Type"] = "application/x-www-form-urlencoded"
  L11_2 = #L6_2
  L10_2["Content-Length"] = L11_2
  L9_2.headers = L10_2
  L10_2 = ltn12
  L10_2 = L10_2.source
  L10_2 = L10_2.string
  L11_2 = L6_2
  L10_2 = L10_2(L11_2)
  L9_2.source = L10_2
  L10_2 = ltn12
  L10_2 = L10_2.sink
  L10_2 = L10_2.table
  L11_2 = L7_2
  L10_2 = L10_2(L11_2)
  L9_2.sink = L10_2
  L8_2, L9_2 = L8_2(L9_2)
  if L9_2 == 200 then
    L10_2 = L1_1.decode
    L11_2 = L7_2[1]
    return L10_2(L11_2)
  else
    L10_2 = {}
    L10_2.err_code = 1354
    L10_2.err_msg = "No response from server"
    return L10_2
  end
end

postData = L5_1
wmBalance = ""

function L5_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = postData
  L1_2 = CARD_NUM
  L2_2 = "5001"
  L0_2 = L0_2(L1_2, L2_2)
  L1_2 = L0_2.err_code
  if 0 < L1_2 then
    L1_2 = nLog
    L2_2 = "Call failed: "
    L3_2 = L0_2.err_msg
    L2_2 = L2_2 .. L3_2
    L3_2 = time
    L1_2(L2_2, L3_2)
    L1_2 = false
    return L1_2
  else
    L1_2 = L0_2.ret
    wmBalance = L1_2
    L1_2 = true
    return L1_2
  end
end

getWmBalance = L5_1
