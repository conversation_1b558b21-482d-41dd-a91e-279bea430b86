local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1
L0_1 = 0
moveAddress = ""
Time = 0
Boolean = true

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  if A0_2 == 1 then
    L6_2 = touchDown
    L7_2 = A2_2
    L8_2 = A3_2
    L6_2(L7_2, L8_2)
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = 30
    L9_2 = 88
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L6_2 = touchMove
    L7_2 = A2_2
    L8_2 = A3_2
    L6_2(L7_2, L8_2)
    L6_2 = touchUp
    L7_2 = A2_2
    L8_2 = A3_2
    L6_2(L7_2, L8_2)
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = A1_2
    L9_2 = A1_2 * 2
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L6_2 = _ENV["_卡区延迟师门"]
    L6_2()
  elseif A0_2 == 2 then
    if not A4_2 then
      A4_2 = 5
    end
    L6_2 = randomTap
    L7_2 = A2_2
    L8_2 = A3_2
    L9_2 = A4_2
    L6_2(L7_2, L8_2, L9_2)
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = A1_2
    L9_2 = A1_2 * 2
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L6_2 = _ENV["_卡区延迟师门"]
    L6_2()
  else
    L6_2 = tostring
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = A2_2
    L9_2 = A4_2
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L7_2(L8_2, L9_2)
    L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L7_2 = tostring
    L8_2 = math
    L8_2 = L8_2.random
    L9_2 = A3_2
    L10_2 = A5_2
    L8_2, L9_2, L10_2, L11_2, L12_2 = L8_2(L9_2, L10_2)
    L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
    L8_2 = tap
    L9_2 = L6_2
    L10_2 = L7_2
    L8_2(L9_2, L10_2)
    L8_2 = mSleep
    L9_2 = math
    L9_2 = L9_2.random
    L10_2 = A1_2
    L11_2 = A1_2 * 2
    L9_2, L10_2, L11_2, L12_2 = L9_2(L10_2, L11_2)
    L8_2(L9_2, L10_2, L11_2, L12_2)
    L8_2 = _ENV["_卡区延迟师门"]
    L8_2()
  end
end

randomClick = L1_1

function L1_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["延迟模式"]
  if L0_2 == "1秒" then
    L0_2 = 1000
    return L0_2
  else
    L0_2 = _ENV["延迟模式"]
    if L0_2 == "2秒" then
      L0_2 = 2000
      return L0_2
    else
      L0_2 = _ENV["延迟模式"]
      if L0_2 == "3秒" then
        L0_2 = 3000
        return L0_2
      end
    end
  end
end

_ENV["延迟设置"] = L1_1

function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  while true do
    L0_2 = getColour
    L1_2 = _ENV["枯萎的金莲"]
    L2_2 = "关闭对话框"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = randomClick
      L1_2 = 2
      L2_2 = 300
      L3_2 = 1873
      L4_2 = 797
      L0_2(L1_2, L2_2, L3_2, L4_2)
    else
      break
    end
  end
end

closeSession = L1_1

function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L0_2 = isFileExist
  L1_2 = "/data/system/TimeFile.txt"
  L0_2 = L0_2(L1_2)
  if not L0_2 then
    L0_2 = writeFileString
    L1_2 = "/data/system/TimeFile.txt"
    L2_2 = tostring
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    L3_2 = L3_2 + 7199
    L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L2_2(L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
  end
  L0_2 = readFileString
  L1_2 = "/data/system/TimeFile.txt"
  L0_2 = L0_2(L1_2)
  L1_2 = os
  L1_2 = L1_2.time
  L1_2 = L1_2()
  L1_2 = L0_2 - L1_2
  L2_2 = L1_2 / 60
  L3_2 = math
  L3_2 = L3_2.modf
  L4_2 = L2_2 / 60
  L3_2 = L3_2(L4_2)
  L4_2 = math
  L4_2 = L4_2.modf
  L5_2 = L3_2 / 24
  L4_2 = L4_2(L5_2)
  L5_2 = math
  L5_2 = L5_2.fmod
  L6_2 = L3_2
  L7_2 = 24
  L5_2 = L5_2(L6_2, L7_2)
  L6_2 = math
  L6_2 = L6_2.fmod
  L7_2 = L2_2
  L8_2 = 60
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = math
  L7_2 = L7_2.fmod
  L8_2 = L1_2
  L9_2 = 60
  L7_2 = L7_2(L8_2, L9_2)
  L8_2 = math
  L8_2 = L8_2.ceil
  L9_2 = L6_2 - 1
  L8_2 = L8_2(L9_2)
  L9_2 = os
  L9_2 = L9_2.time
  L9_2 = L9_2()
  L9_2 = L0_2 - L9_2
  if L9_2 < 0 then
    L9_2 = dialog
    L10_2 = "脚本试用期已过 "
    L11_2 = "\n请前往购买正式版使用"
    L12_2 = [[

 QQ:997962984]]
    L10_2 = L10_2 .. L11_2 .. L12_2
    L9_2(L10_2)
    L9_2 = luaExit
    L9_2()
  elseif 0 < L4_2 then
    L9_2 = "试用时间剩余："
    L10_2 = L4_2
    L11_2 = "天"
    L12_2 = L5_2
    L13_2 = "小时"
    L14_2 = L8_2
    L15_2 = "分"
    L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
    return L9_2
  elseif L4_2 <= 0 and 0 < L5_2 then
    L9_2 = "试用时间剩余："
    L10_2 = L5_2
    L11_2 = "小时"
    L12_2 = L8_2
    L13_2 = "分"
    L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2
    return L9_2
  elseif L4_2 <= 0 and L5_2 <= 0 and 0 < L6_2 then
    L9_2 = "试用时间剩余："
    L10_2 = L8_2
    L11_2 = "分"
    L9_2 = L9_2 .. L10_2 .. L11_2
    return L9_2
  end
end

_ENV["计算剩余时间"] = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L2_2 = A1_2 or nil
  if not A1_2 then
    L2_2 = 85
  end
  L3_2 = addTSOcrDictEx
  L4_2 = _ENV["新春视图"]
  L4_2 = L4_2[A0_2]
  L4_2 = L4_2[1]
  L3_2 = L3_2(L4_2)
  L4_2 = tsFindText
  L5_2 = L3_2
  L6_2 = _ENV["新春视图"]
  L6_2 = L6_2[A0_2]
  L6_2 = L6_2[2]
  L7_2 = 154
  L8_2 = 37
  L9_2 = 308
  L10_2 = 78
  L11_2 = _ENV["新春视图"]
  L11_2 = L11_2[A0_2]
  L11_2 = L11_2[3]
  L12_2 = L2_2
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L6_2 = nLog
  L7_2 = A0_2
  L8_2 = " : "
  L9_2 = L4_2
  L10_2 = ","
  L11_2 = L5_2
  L7_2 = L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
  L6_2(L7_2)
  if 0 < L4_2 then
    L6_2 = true
    L7_2 = L4_2
    L8_2 = L5_2
    return L6_2, L7_2, L8_2
  else
    L6_2 = false
    L7_2 = L4_2
    L8_2 = L5_2
    return L6_2, L7_2, L8_2
  end
end

getNewYearView = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = addTSOcrDictEx
  L3_2 = addressList
  L2_2 = L2_2(L3_2)
  L3_2 = tsOcrText
  L4_2 = L2_2
  L5_2 = 142
  L6_2 = 89
  L7_2 = 310
  L8_2 = 124
  L9_2 = "EEF1F2,110F0E#FCF1ED , 040F13"
  L10_2 = 90
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L4_2 = Tap
  if L4_2 == L3_2 and L3_2 ~= "" and L3_2 ~= nil then
    L4_2 = #L3_2
    L5_2 = #A0_2
    L5_2 = L5_2 + 1
    if L4_2 >= L5_2 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = tonumber
      L6_2 = string
      L6_2 = L6_2.sub
      L7_2 = L3_2
      L8_2 = 1
      L9_2 = #A0_2
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L6_2(L7_2, L8_2, L9_2)
      L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
      L6_2 = tonumber
      L7_2 = A0_2
      L6_2 = L6_2(L7_2)
      L5_2 = L5_2 - L6_2
      L4_2 = L4_2(L5_2)
      if L4_2 < 2 then
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = tonumber
        L6_2 = string
        L6_2 = L6_2.sub
        L7_2 = L3_2
        L8_2 = #A0_2
        L8_2 = L8_2 + 1
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L6_2(L7_2, L8_2)
        L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
        L6_2 = tonumber
        L7_2 = A1_2
        L6_2 = L6_2(L7_2)
        L5_2 = L5_2 - L6_2
        L4_2 = L4_2(L5_2)
        if L4_2 < 2 then
          L4_2 = nLog
          L5_2 = "到达:"
          L6_2 = " map"
          L7_2 = "("
          L8_2 = A0_2
          L9_2 = ","
          L10_2 = A1_2
          L11_2 = ")"
          L5_2 = L5_2 .. L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
          L4_2(L5_2)
          L4_2 = true
          return L4_2
        else
        end
      end
    else
      L4_2 = nLog
      L5_2 = #L3_2
      L6_2 = "==="
      L7_2 = #A0_2
      L7_2 = L7_2 + 1
      L5_2 = L5_2 .. L6_2 .. L7_2
      L4_2(L5_2)
    end
  end
  Tap = L3_2
  L4_2 = nLog
  L5_2 = Tap
  L4_2(L5_2)
  L4_2 = false
  return L4_2
end

monitorAddress = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = _ENV["判断收缩充值"]
  L2_2()
  L2_2 = addTSOcrDictEx
  L3_2 = addressList
  L2_2 = L2_2(L3_2)
  L3_2 = tsOcrText
  L4_2 = L2_2
  L5_2 = 142
  L6_2 = 89
  L7_2 = 310
  L8_2 = 124
  L9_2 = "EEF1F2,110F0E#FCF1ED , 040F13"
  L10_2 = 90
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  if L3_2 ~= "" and L3_2 ~= nil then
    L4_2 = #L3_2
    L5_2 = #A0_2
    L5_2 = L5_2 + 1
    if L4_2 >= L5_2 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = tonumber
      L6_2 = string
      L6_2 = L6_2.sub
      L7_2 = L3_2
      L8_2 = 1
      L9_2 = #A0_2
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L6_2(L7_2, L8_2, L9_2)
      L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
      L6_2 = tonumber
      L7_2 = A0_2
      L6_2 = L6_2(L7_2)
      L5_2 = L5_2 - L6_2
      L4_2 = L4_2(L5_2)
      if L4_2 < 2 then
        L4_2 = math
        L4_2 = L4_2.abs
        L5_2 = tonumber
        L6_2 = string
        L6_2 = L6_2.sub
        L7_2 = L3_2
        L8_2 = #A0_2
        L8_2 = L8_2 + 1
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L6_2(L7_2, L8_2)
        L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
        L6_2 = tonumber
        L7_2 = A1_2
        L6_2 = L6_2(L7_2)
        L5_2 = L5_2 - L6_2
        L4_2 = L4_2(L5_2)
        if L4_2 < 2 then
          L4_2 = nLog
          L5_2 = "到达:"
          L6_2 = " map"
          L7_2 = "("
          L8_2 = A0_2
          L9_2 = ","
          L10_2 = A1_2
          L11_2 = ")"
          L5_2 = L5_2 .. L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
          L4_2(L5_2)
          L4_2 = true
          return L4_2
        else
        end
      end
    else
      L4_2 = nLog
      L5_2 = #L3_2
      L6_2 = "==="
      L7_2 = #A0_2
      L7_2 = L7_2 + 1
      L5_2 = L5_2 .. L6_2 .. L7_2
      L4_2(L5_2)
    end
  end
  L4_2 = false
  return L4_2
end

_ENV["monitorAddress改"] = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L2_2 = fwShowWnd
  L3_2 = "wid"
  L4_2 = 0
  L5_2 = 1030
  L6_2 = 600
  L7_2 = 1080
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = fwShowTextView
  L3_2 = "wid"
  L4_2 = "textid"
  L5_2 = A0_2
  L6_2 = "center"
  L7_2 = "ff0000"
  L8_2 = "000000"
  L9_2 = 11
  L10_2 = 0
  L11_2 = 0
  L12_2 = 0
  L13_2 = 600
  L14_2 = 50
  L15_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
end

printLog = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L1_2 = fwShowWnd
  L2_2 = "widps"
  L3_2 = 700
  L4_2 = 1030
  L5_2 = 1480
  L6_2 = 1080
  L7_2 = 0
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L1_2 = fwShowTextView
  L2_2 = "widps"
  L3_2 = "textid"
  L4_2 = A0_2
  L5_2 = "center"
  L6_2 = "ff0000"
  L7_2 = "000000"
  L8_2 = 12
  L9_2 = 1
  L10_2 = 0
  L11_2 = 0
  L12_2 = 780
  L13_2 = 50
  L14_2 = 1
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
end

_ENV["票数统计"] = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L7_2 = findMultiColorInRegionFuzzyByTable
  L8_2 = A0_2[A1_2]
  L9_2 = A2_2
  L10_2 = A3_2
  L11_2 = A4_2
  L12_2 = A5_2
  L13_2 = A6_2
  L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  if -1 < L7_2 then
    L9_2 = true
    L10_2 = L7_2
    L11_2 = L8_2
    return L9_2, L10_2, L11_2
  else
    L9_2 = false
    L10_2 = L7_2
    L11_2 = L8_2
    return L9_2, L10_2, L11_2
  end
end

getMultiColor = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = findMultiColorInRegionFuzzy
  L3_2 = A0_2[A1_2]
  L3_2 = L3_2[1]
  L4_2 = A0_2[A1_2]
  L4_2 = L4_2[2]
  L5_2 = A0_2[A1_2]
  L5_2 = L5_2[3]
  L6_2 = A0_2[A1_2]
  L6_2 = L6_2[4]
  L7_2 = A0_2[A1_2]
  L7_2 = L7_2[5]
  L8_2 = A0_2[A1_2]
  L8_2 = L8_2[6]
  L9_2 = A0_2[A1_2]
  L9_2 = L9_2[7]
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L4_2 = nLog
  L5_2 = A1_2
  L6_2 = " : "
  L7_2 = L2_2
  L8_2 = L3_2
  L5_2 = L5_2 .. L6_2 .. L7_2 .. L8_2
  L4_2(L5_2)
  if -1 < L2_2 then
    L4_2 = true
    L5_2 = L2_2
    L6_2 = L3_2
    return L4_2, L5_2, L6_2
  else
    L4_2 = false
    L5_2 = L2_2
    L6_2 = L3_2
    return L4_2, L5_2, L6_2
  end
end

getColour = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L6_2 = findMultiColorInRegionFuzzy
  L7_2 = A0_2[A1_2]
  L7_2 = L7_2[1]
  L8_2 = A0_2[A1_2]
  L8_2 = L8_2[2]
  L9_2 = 90
  L10_2 = A2_2
  L11_2 = A3_2
  L12_2 = A4_2
  L13_2 = A5_2
  L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  L8_2 = nLog
  L9_2 = A1_2
  L10_2 = " : "
  L11_2 = L6_2
  L12_2 = L7_2
  L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2
  L8_2(L9_2)
  if -1 < L6_2 then
    L8_2 = true
    L9_2 = L6_2
    L10_2 = L7_2
    return L8_2, L9_2, L10_2
  else
    L8_2 = false
    L9_2 = -1
    L10_2 = -1
    return L8_2, L9_2, L10_2
  end
end

getColors = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L7_2 = A6_2 or nil
  if not A6_2 then
    L7_2 = 90
  end
  L8_2 = addTSOcrDictEx
  L9_2 = A0_2[A1_2]
  L9_2 = L9_2[1]
  L8_2 = L8_2(L9_2)
  L9_2 = tsFindText
  L10_2 = L8_2
  L11_2 = A0_2[A1_2]
  L11_2 = L11_2[2]
  L12_2 = A2_2
  L13_2 = A3_2
  L14_2 = A4_2
  L15_2 = A5_2
  L16_2 = A0_2[A1_2]
  L16_2 = L16_2[3]
  L17_2 = L7_2
  L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
  L11_2 = nLog
  L12_2 = A1_2
  L13_2 = " : "
  L14_2 = L9_2
  L15_2 = ","
  L16_2 = L10_2
  L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2
  L11_2(L12_2)
  if 0 < L9_2 then
    L11_2 = true
    L12_2 = L9_2
    L13_2 = L10_2
    return L11_2, L12_2, L13_2
  else
    L11_2 = false
    L12_2 = L9_2
    L13_2 = L10_2
    return L11_2, L12_2, L13_2
  end
end

getWordStock = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L7_2 = addTSOcrDictEx
  L8_2 = A0_2[A1_2]
  L8_2 = L8_2[1]
  L7_2 = L7_2(L8_2)
  L8_2 = tsFindText
  L9_2 = L7_2
  L10_2 = A0_2[A1_2]
  L10_2 = L10_2[2]
  L11_2 = A2_2
  L12_2 = A3_2
  L13_2 = A4_2
  L14_2 = A5_2
  L15_2 = A0_2[A1_2]
  L15_2 = L15_2[3]
  L16_2 = A6_2
  L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
  if -1 < L8_2 then
    L10_2 = nLog
    L11_2 = "成功识别字体。。类型："
    L12_2 = A1_2
    L11_2 = L11_2 .. L12_2
    L10_2(L11_2)
    L10_2 = true
    L11_2 = L8_2
    L12_2 = L9_2
    return L10_2, L11_2, L12_2
  else
    L10_2 = nLog
    L11_2 = "无法识别字体。。类型："
    L12_2 = A1_2
    L11_2 = L11_2 .. L12_2
    L10_2(L11_2)
    L10_2 = false
    L11_2 = L8_2
    L12_2 = L9_2
    return L10_2, L11_2, L12_2
  end
end

getWordName = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2
  L2_2 = A1_2 or nil
  if not A1_2 then
    L2_2 = 80
  end
  L3_2 = multiColor
  L4_2 = A0_2
  L5_2 = L2_2
  L3_2 = L3_2(L4_2, L5_2)
  if L3_2 then
    L3_2 = true
    return L3_2
  else
    L3_2 = false
    return L3_2
  end
end

IsColor = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = mSleep
  L2_2 = 1000
  L1_2(L2_2)
  L1_2 = 0
  L2_2 = 3
  L3_2 = A0_2 or L3_2
  if not A0_2 then
    L3_2 = false
  end
  L4_2 = _ENV["人物加血"]
  if L4_2 ~= "不加" then
    while true do
      if L1_2 < L2_2 then
        L4_2 = getColour
        L5_2 = ZS
        L5_2 = L5_2["人血"]
        L6_2 = _ENV["人物加血"]
        L4_2 = L4_2(L5_2, L6_2)
        if not L4_2 then
          L4_2 = true
          while L4_2 do
            L5_2 = getColour
            L6_2 = CJColorList
            L7_2 = "人物加血"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1401
              L9_2 = 122
              L10_2 = 1603
              L11_2 = 196
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              L1_2 = L1_2 + 1
              if L3_2 then
                break
              end
              L5_2 = 1
              L6_2 = 20
              L7_2 = 1
              for L8_2 = L5_2, L6_2, L7_2 do
                L9_2 = getColour
                L10_2 = ZS
                L10_2 = L10_2["人血"]
                L11_2 = _ENV["人物加血"]
                L9_2 = L9_2(L10_2, L11_2)
                if L9_2 then
                  L4_2 = false
                  goto lbl_93
                else
                  L9_2 = mSleep
                  L10_2 = 100
                  L9_2(L10_2)
                end
              end
            else
              if 3 <= L1_2 then
                break
              end
              L5_2 = getColour
              L6_2 = CJColorList
              L7_2 = "超级巫医"
              L5_2 = L5_2(L6_2, L7_2)
              if L5_2 then
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 1500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              else
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1818
                L9_2 = 10
                L10_2 = 1915
                L11_2 = 67
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              end
            end
            ::lbl_93::
          end
          if L3_2 then
            break
          end
      end
      elseif 3 <= L1_2 then
        L4_2 = isItemExist
        L5_2 = "包子"
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L1_2 = 0
        else
          L4_2 = _ENV["仙灵买药"]
          if L4_2 then
            L4_2 = _ENV["仙灵买包子"]
            return L4_2()
          else
            L4_2 = _ENV["前往买包子"]
            L5_2 = false
            return L4_2(L5_2)
          end
        end
      else
        L1_2 = 0
        break
      end
    end
  end
  L4_2 = _ENV["人物加蓝"]
  if L4_2 ~= "不加" then
    while true do
      if L2_2 > L1_2 then
        L4_2 = getColour
        L5_2 = ZS
        L5_2 = L5_2["人蓝"]
        L6_2 = _ENV["人物加蓝"]
        L4_2 = L4_2(L5_2, L6_2)
        if not L4_2 then
          L4_2 = true
          while L4_2 do
            L5_2 = getColour
            L6_2 = CJColorList
            L7_2 = "人物加血"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1669
              L9_2 = 119
              L10_2 = 1877
              L11_2 = 199
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              L1_2 = L1_2 + 1
              if L3_2 then
                break
              end
              L5_2 = 1
              L6_2 = 20
              L7_2 = 1
              for L8_2 = L5_2, L6_2, L7_2 do
                L9_2 = getColour
                L10_2 = ZS
                L10_2 = L10_2["人蓝"]
                L11_2 = _ENV["人物加蓝"]
                L9_2 = L9_2(L10_2, L11_2)
                if L9_2 then
                  L4_2 = false
                  goto lbl_205
                else
                  L9_2 = mSleep
                  L10_2 = 100
                  L9_2(L10_2)
                end
              end
            else
              if 3 < L1_2 then
                break
              end
              L5_2 = getColour
              L6_2 = CJColorList
              L7_2 = "超级巫医"
              L5_2 = L5_2(L6_2, L7_2)
              if L5_2 then
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 1500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              else
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1818
                L9_2 = 10
                L10_2 = 1915
                L11_2 = 67
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              end
            end
            ::lbl_205::
          end
          if L3_2 then
            break
          end
      end
      elseif 3 <= L1_2 then
        L4_2 = isItemExist
        L5_2 = "佛手"
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L1_2 = 0
        else
          L4_2 = _ENV["前往买佛手"]
          L5_2 = true
          return L4_2(L5_2)
        end
      else
        L1_2 = 0
        break
      end
    end
  end
  L4_2 = _ENV["宠物加血"]
  if L4_2 ~= "不加" then
    while true do
      if L2_2 > L1_2 then
        L4_2 = getColour
        L5_2 = ZS
        L5_2 = L5_2["宠血"]
        L6_2 = _ENV["宠物加血"]
        L4_2 = L4_2(L5_2, L6_2)
        if not L4_2 then
          L4_2 = true
          while L4_2 do
            L5_2 = getColour
            L6_2 = CJColorList
            L7_2 = "宠物加血"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1113
              L9_2 = 120
              L10_2 = 1328
              L11_2 = 196
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              L1_2 = L1_2 + 1
              if L3_2 then
                break
              end
              L5_2 = 1
              L6_2 = 20
              L7_2 = 1
              for L8_2 = L5_2, L6_2, L7_2 do
                L9_2 = getColour
                L10_2 = ZS
                L10_2 = L10_2["宠血"]
                L11_2 = _ENV["宠物加血"]
                L9_2 = L9_2(L10_2, L11_2)
                if L9_2 then
                  L4_2 = false
                  goto lbl_316
                else
                  L9_2 = mSleep
                  L10_2 = 100
                  L9_2(L10_2)
                end
              end
            else
              if 3 <= L1_2 then
                break
              end
              L5_2 = getColour
              L6_2 = CJColorList
              L7_2 = "超级巫医"
              L5_2 = L5_2(L6_2, L7_2)
              if L5_2 then
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 1500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              else
                L5_2 = getColour
                L6_2 = CJColorList
                L7_2 = "没有宝宝"
                L5_2 = L5_2(L6_2, L7_2)
                if L5_2 then
                  break
                end
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1577
                L9_2 = 12
                L10_2 = 1671
                L11_2 = 50
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              end
            end
            ::lbl_316::
          end
          if L3_2 then
            break
          end
      end
      elseif L2_2 <= L1_2 then
        L4_2 = isItemExist
        L5_2 = "包子"
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L1_2 = 0
        else
          L4_2 = _ENV["仙灵买药"]
          if L4_2 then
            L4_2 = _ENV["仙灵买包子"]
            return L4_2()
          else
            L4_2 = _ENV["前往买包子"]
            L5_2 = false
            return L4_2(L5_2)
          end
        end
      else
        L1_2 = 0
        break
      end
    end
  end
  L4_2 = _ENV["宠物加蓝"]
  if L4_2 ~= "不加" then
    while true do
      if L2_2 > L1_2 then
        L4_2 = getColour
        L5_2 = ZS
        L5_2 = L5_2["宠蓝"]
        L6_2 = _ENV["宠物加蓝"]
        L4_2 = L4_2(L5_2, L6_2)
        if not L4_2 then
          L4_2 = true
          while L4_2 do
            L5_2 = getColour
            L6_2 = CJColorList
            L7_2 = "宠物加血"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 500
              L8_2 = 1381
              L9_2 = 116
              L10_2 = 1599
              L11_2 = 195
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              L1_2 = L1_2 + 1
              if L3_2 then
                break
              end
              L5_2 = 1
              L6_2 = 20
              L7_2 = 1
              for L8_2 = L5_2, L6_2, L7_2 do
                L9_2 = getColour
                L10_2 = ZS
                L10_2 = L10_2["宠蓝"]
                L11_2 = _ENV["宠物加蓝"]
                L9_2 = L9_2(L10_2, L11_2)
                if L9_2 then
                  L4_2 = false
                  goto lbl_434
                else
                  L9_2 = mSleep
                  L10_2 = 100
                  L9_2(L10_2)
                end
              end
            else
              if 3 <= L1_2 then
                break
              end
              L5_2 = getColour
              L6_2 = CJColorList
              L7_2 = "超级巫医"
              L5_2 = L5_2(L6_2, L7_2)
              if L5_2 then
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 1500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1415
                L9_2 = 474
                L10_2 = 1824
                L11_2 = 572
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              else
                L5_2 = getColour
                L6_2 = CJColorList
                L7_2 = "没有宝宝"
                L5_2 = L5_2(L6_2, L7_2)
                if L5_2 then
                  break
                end
                L5_2 = randomClick
                L6_2 = 0
                L7_2 = 500
                L8_2 = 1577
                L9_2 = 12
                L10_2 = 1671
                L11_2 = 50
                L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              end
            end
            ::lbl_434::
          end
          if L3_2 then
            break
          end
      end
      elseif 3 <= L1_2 then
        L4_2 = isItemExist
        L5_2 = "佛手"
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L1_2 = 0
        else
          L4_2 = _ENV["前往买佛手"]
          L5_2 = true
          return L4_2(L5_2)
        end
      else
        L1_2 = 0
        break
      end
    end
  end
end

_ENV["状态检查"] = L1_1
L1_1 = {}
global = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if A0_2 then
    L1_2 = randomTap
    L2_2 = 60
    L3_2 = 369
    L1_2(L2_2, L3_2)
    L1_2 = mSleep
    L2_2 = 1000
    L1_2(L2_2)
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 8366000
    L3_2 = "-26|-12|0xd5eff0,-42|-16|0x44839b,-58|1|0x407f92,-78|-5|0xa7d1dc,-90|-15|0x26687e,-54|38|0x28617b,-25|22|0x478094,-53|-49|0x7da3ad,-47|-4|0x629db1"
    L4_2 = 90
    L5_2 = 0
    L6_2 = 470
    L7_2 = 143
    L8_2 = 623
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    if L1_2 < 0 then
      L3_2 = randomTap
      L4_2 = 72
      L5_2 = 541
      L6_2 = 20
      L3_2(L4_2, L5_2, L6_2)
      L3_2 = mSleep
      L4_2 = 1000
      L3_2(L4_2)
    end
    L3_2 = findMultiColorInRegionFuzzy
    L4_2 = 6721694
    L5_2 = "-23|-3|0x385961,-43|-30|0x21424e,-11|-101|0x253d44,33|-67|0x1c3641,10|-66|0x80acb8,-26|-41|0x6393a3,-14|-56|0x233842,-6|-68|0x1d333a,-10|-81|0x88b4c0"
    L6_2 = 90
    L7_2 = -2
    L8_2 = 625
    L9_2 = 144
    L10_2 = 776
    L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    L2_2 = L4_2
    L1_2 = L3_2
    if 0 < L1_2 then
      L3_2 = randomTap
      L4_2 = 70
      L5_2 = 692
      L6_2 = 20
      L3_2(L4_2, L5_2, L6_2)
      L3_2 = mSleep
      L4_2 = 1000
      L3_2(L4_2)
    end
    L3_2 = findMultiColorInRegionFuzzy
    L4_2 = 13233392
    L5_2 = "-40|5|0xc0ecf0,-46|-16|0x468ba4,-17|-39|0x3f7c96,18|-8|0x2b6f85,6|21|0x2a627a,-32|30|0x458ca6,-47|-8|0x25607c,-21|4|0x8dbcc9,-29|55|0x9fd1db"
    L6_2 = 90
    L7_2 = 12
    L8_2 = 778
    L9_2 = 150
    L10_2 = 927
    L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    L2_2 = L4_2
    L1_2 = L3_2
    if 0 < L1_2 then
      L3_2 = randomTap
      L4_2 = 72
      L5_2 = 847
      L6_2 = 20
      L3_2(L4_2, L5_2, L6_2)
      L3_2 = mSleep
      L4_2 = 1000
      L3_2(L4_2)
    end
  else
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 11068656
    L3_2 = "-13|24|0x4186a2,31|10|0x3c7e97,54|-16|0xb2dee8,23|-58|0xabd0d6,-2|-65|0xc9e3e6,-50|-19|0x95d7e9,-21|-11|0xc0e8f0,-1|-36|0xd0eef8,0|3|0xa8e4f0"
    L4_2 = 90
    L5_2 = 321
    L6_2 = 933
    L7_2 = 473
    L8_2 = 1075
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L3_2 = findMultiColorInRegionFuzzy
    L4_2 = 3165264
    L5_2 = "-37|2|0x345058,-34|66|0x2f3f47,-5|77|0x385058,11|33|0x3b4e52,0|1|0x304c50,-36|8|0x304c50,-43|32|0x36464a,-21|75|0x385058,-36|50|0x899aa2"
    L6_2 = 90
    L7_2 = 1
    L8_2 = 920
    L9_2 = 140
    L10_2 = 1068
    L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    if L1_2 < 0 and L3_2 < 0 then
      L5_2 = randomTap
      L6_2 = 60
      L7_2 = 380
      L8_2 = 10
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 1000
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    end
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 12577776
    L7_2 = "-11|-31|0x47879e,-46|-63|0x9ed5e4,-17|-61|0xc4ecf0,20|-63|0x9ec9d6,53|-60|0xa7d8e0,37|-85|0x549ebb,12|-108|0x4a88a0,14|-115|0x97c1ce,2|-126|0x9de1ee"
    L8_2 = 90
    L9_2 = 4
    L10_2 = 777
    L11_2 = 155
    L12_2 = 926
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L4_2 = L6_2
    L1_2 = L5_2
    if 0 < L1_2 then
      L5_2 = randomTap
      L6_2 = 68
      L7_2 = 843
      L8_2 = 30
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 400
      L8_2 = 600
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    end
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 3963798
    L7_2 = "54|51|0x87afb9,-12|55|0x75a3b3,21|61|0xc0ecf0,-54|56|0xccecf0,16|116|0x2a586e,54|115|0x9ecdda"
    L8_2 = 90
    L9_2 = 1
    L10_2 = 690
    L11_2 = 216
    L12_2 = 986
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L4_2 = L6_2
    L1_2 = L5_2
    if 0 < L1_2 then
      L5_2 = randomTap
      L6_2 = 70
      L7_2 = 846
      L8_2 = 30
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 400
      L8_2 = 600
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    end
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 1980740
    L7_2 = "31|8|0x213d48,17|44|0x203b47,3|54|0x3d5e69,56|64|0x1b3643,-155|29|0x688d9a,-155|-5|0x1b343f,-130|52|0x233641"
    L8_2 = 90
    L9_2 = 141
    L10_2 = 921
    L11_2 = 500
    L12_2 = 1077
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L4_2 = L6_2
    L1_2 = L5_2
    if 0 < L1_2 then
      L5_2 = randomTap
      L6_2 = 400
      L7_2 = 990
      L8_2 = 30
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 400
      L8_2 = 600
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    end
  end
end

hiddenFunction = L1_1
L1_1 = global

function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L1_2 = 0
  if A0_2 == "飞行符" then
    L2_2 = printLog
    L3_2 = "抵达长安购买飞行符"
    L2_2(L3_2)
    while true do
      L2_2 = 0
      while true do
        L3_2 = os
        L3_2 = L3_2.time
        L3_2 = L3_2()
        L3_2 = L3_2 - L2_2
        if 30 < L3_2 and L2_2 ~= 0 then
          L1_2 = 0
          L2_2 = 0
        end
        L3_2 = getColour
        L4_2 = colorList
        L5_2 = "长安小地图"
        L3_2 = L3_2(L4_2, L5_2)
        if L3_2 then
          L1_2 = 1
          L3_2 = 923.8
          L4_2 = 790.8
          L5_2 = randomClick
          L6_2 = 2
          L7_2 = 200
          L8_2 = L3_2
          L9_2 = L4_2
          L10_2 = 50
          L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
          L5_2 = randomClick
          L6_2 = 1
          L7_2 = 1000
          L8_2 = L3_2
          L9_2 = L4_2
          L5_2(L6_2, L7_2, L8_2, L9_2)
          L5_2 = randomClick
          L6_2 = 2
          L7_2 = 400
          L8_2 = 1680
          L9_2 = 78
          L5_2(L6_2, L7_2, L8_2, L9_2)
          L5_2 = os
          L5_2 = L5_2.time
          L5_2 = L5_2()
          L2_2 = L5_2
        else
          if L1_2 == 1 then
            L3_2 = monitorAddress
            L4_2 = "258"
            L5_2 = "41"
            L3_2 = L3_2(L4_2, L5_2)
            if L3_2 then
              L3_2 = findMultiColorInRegionFuzzy
              L4_2 = 3165264
              L5_2 = "-37|2|0x345058,-34|66|0x2f3f47,-5|77|0x385058,11|33|0x3b4e52,0|1|0x304c50,-36|8|0x304c50,-43|32|0x36464a,-21|75|0x385058,-36|50|0x899aa2"
              L6_2 = 90
              L7_2 = 1
              L8_2 = 920
              L9_2 = 140
              L10_2 = 1068
              L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
              if 0 < L3_2 then
                L5_2 = randomClick
                L6_2 = 2
                L7_2 = 300
                L8_2 = 90
                L9_2 = 971
                L5_2(L6_2, L7_2, L8_2, L9_2)
              end
              L5_2 = wordStock
              L5_2 = L5_2["隐藏摊位"]
              L5_2()
              L5_2 = mSleep
              L6_2 = 5000
              L5_2(L6_2)
              L5_2 = true
              L6_2 = 0
              repeat
                L7_2 = getWordStock
                L8_2 = WordStock
                L9_2 = "购买"
                L10_2 = 1374
                L11_2 = 118
                L12_2 = 1521
                L13_2 = 745
                L7_2, L8_2, L9_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                L10_2 = getColour
                L11_2 = buyGoods
                L12_2 = "其他选项"
                L10_2 = L10_2(L11_2, L12_2)
                if L10_2 then
                  L10_2 = getColour
                  L11_2 = buyGoods
                  L12_2 = "其他选项"
                  L10_2, L11_2, L12_2 = L10_2(L11_2, L12_2)
                  L13_2 = printLog
                  L14_2 = "选择其他选项"
                  L13_2(L14_2)
                  L13_2 = randomClick
                  L14_2 = 2
                  L15_2 = 500
                  L16_2 = L11_2
                  L17_2 = L12_2
                  L13_2(L14_2, L15_2, L16_2, L17_2)
                elseif L7_2 then
                  L10_2 = randomClick
                  L11_2 = 0
                  L12_2 = 500
                  L13_2 = L8_2 + 50
                  L14_2 = L9_2 - 25
                  L15_2 = L8_2 + 250
                  L16_2 = L9_2 + 70
                  L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                else
                  L10_2 = os
                  L10_2 = L10_2.time
                  L10_2 = L10_2()
                  L10_2 = L10_2 - L6_2
                  if 20 < L10_2 and L6_2 ~= 0 and not L7_2 then
                    repeat
                      L10_2 = randomClick
                      L11_2 = 0
                      L12_2 = 300
                      L13_2 = 22
                      L14_2 = 25
                      L15_2 = 301
                      L16_2 = 117
                      L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                      L10_2 = getColour
                      L11_2 = colorList
                      L12_2 = "长安小地图"
                      L10_2 = L10_2(L11_2, L12_2)
                    until L10_2
                  elseif L5_2 then
                    L5_2 = false
                    L10_2 = printLog
                    L11_2 = "点击罗道人"
                    L10_2(L11_2)
                    L10_2 = randomClick
                    L11_2 = 1
                    L12_2 = 800
                    L13_2 = 921
                    L14_2 = 522
                    L10_2(L11_2, L12_2, L13_2, L14_2)
                    L10_2 = os
                    L10_2 = L10_2.time
                    L10_2 = L10_2()
                    L6_2 = L10_2
                  end
                end
                L10_2 = mSleep
                L11_2 = 100
                L10_2(L11_2)
                L10_2 = getColour
                L11_2 = buyGoods
                L12_2 = "商店内部"
                L10_2 = L10_2(L11_2, L12_2)
              until L10_2
              L7_2 = printLog
              L8_2 = "商店内部"
              L7_2(L8_2)
              while true do
                L7_2 = getColour
                L8_2 = buyGoods
                L9_2 = "商店内部"
                L7_2 = L7_2(L8_2, L9_2)
                if L7_2 and L1_2 ~= 4 then
                  L1_2 = 4
                  L7_2 = randomClick
                  L8_2 = 0
                  L9_2 = 500
                  L10_2 = 1481
                  L11_2 = 309
                  L12_2 = 1518
                  L13_2 = 345
                  L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                else
                  L7_2 = getColour
                  L8_2 = buyGoods
                  L9_2 = "飞行符"
                  L7_2 = L7_2(L8_2, L9_2)
                  if L7_2 and L1_2 == 4 then
                    L7_2 = printLog
                    L8_2 = "购买飞行符"
                    L7_2(L8_2)
                    L7_2 = randomClick
                    L8_2 = 2
                    L9_2 = 800
                    L10_2 = 1727
                    L11_2 = 780
                    L7_2(L8_2, L9_2, L10_2, L11_2)
                    L7_2 = randomClick
                    L8_2 = 0
                    L9_2 = 200
                    L10_2 = 1755
                    L11_2 = 297
                    L12_2 = 1844
                    L13_2 = 382
                    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                    L7_2 = randomClick
                    L8_2 = 0
                    L9_2 = 200
                    L10_2 = 1599
                    L11_2 = 295
                    L12_2 = 1688
                    L13_2 = 383
                    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                    L7_2 = randomClick
                    L8_2 = 0
                    L9_2 = 200
                    L10_2 = 1752
                    L11_2 = 445
                    L12_2 = 1842
                    L13_2 = 534
                    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                    L7_2 = randomClick
                    L8_2 = 0
                    L9_2 = 200
                    L10_2 = 1408
                    L11_2 = 947
                    L12_2 = 1606
                    L13_2 = 1001
                    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                    L7_2 = randomClick
                    L8_2 = 0
                    L9_2 = 200
                    L10_2 = 1408
                    L11_2 = 947
                    L12_2 = 1606
                    L13_2 = 1001
                    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                    L1_2 = 5
                  else
                    if L1_2 == 5 then
                      L7_2 = getColour
                      L8_2 = colorList
                      L9_2 = "关闭对话框"
                      L7_2 = L7_2(L8_2, L9_2)
                      if L7_2 then
                        L1_2 = 6
                        L7_2 = randomClick
                        L8_2 = 2
                        L9_2 = 200
                        L10_2 = 1848
                        L11_2 = 71
                        L12_2 = 20
                        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
                        L7_2 = randomClick
                        L8_2 = 2
                        L9_2 = 200
                        L10_2 = 1848
                        L11_2 = 71
                        L12_2 = 20
                        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
                    end
                    else
                      L7_2 = getColour
                      L8_2 = buyGoods
                      L9_2 = "飞行符"
                      L7_2 = L7_2(L8_2, L9_2)
                      if not L7_2 and L1_2 == 4 then
                        L1_2 = 3
                      elseif L1_2 == 6 then
                        L7_2 = randomClick
                        L8_2 = 2
                        L9_2 = 400
                        L10_2 = 60
                        L11_2 = 369
                        L7_2(L8_2, L9_2, L10_2, L11_2)
                        while true do
                          L7_2 = getColour
                          L8_2 = colorList
                          L9_2 = "打开背包"
                          L7_2 = L7_2(L8_2, L9_2)
                          if L7_2 then
                            return
                          else
                            L7_2 = printLog
                            L8_2 = "打开背包"
                            L7_2(L8_2)
                            L7_2 = randomClick
                            L8_2 = 2
                            L9_2 = 300
                            L10_2 = 1695
                            L11_2 = 985
                            L12_2 = 10
                            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
                          end
                        end
                      else
                        L7_2 = mSleep
                        L8_2 = 100
                        L7_2(L8_2)
                      end
                    end
                  end
                end
              end
          end
          else
            L3_2 = getWordStock
            L4_2 = WordStock
            L5_2 = "长安城"
            L6_2 = 154
            L7_2 = 37
            L8_2 = 308
            L9_2 = 78
            L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            if not L3_2 or L1_2 ~= 0 then
              L3_2 = getNewYearView
              L4_2 = "长安城"
              L3_2 = L3_2(L4_2)
              if not L3_2 or L1_2 ~= 0 then
                goto lbl_348
              end
            end
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 300
            L6_2 = 22
            L7_2 = 25
            L8_2 = 301
            L9_2 = 117
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          end
        end
        ::lbl_348::
      end
    end
  else
    while true do
      L2_2 = getColour
      L3_2 = colorList
      L4_2 = "长安小地图"
      L2_2 = L2_2(L3_2, L4_2)
      if L2_2 then
        L1_2 = 1
        L2_2 = 1609.4299999999998
        L3_2 = 552.98
        L4_2 = randomClick
        L5_2 = 1
        L6_2 = 1000
        L7_2 = L2_2
        L8_2 = L3_2
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = randomClick
        L5_2 = 2
        L6_2 = 400
        L7_2 = 1680
        L8_2 = 78
        L4_2(L5_2, L6_2, L7_2, L8_2)
      else
        if L1_2 == 1 then
          L2_2 = monitorAddress
          L3_2 = "533"
          L4_2 = "134"
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
            L2_2 = _ENV["隐藏任务"]
            L2_2()
            L2_2 = randomClick
            L3_2 = 0
            L4_2 = 1000
            L5_2 = 1377
            L6_2 = 322
            L7_2 = 1501
            L8_2 = 389
            L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
            L1_2 = 2
        end
        else
          L2_2 = getWordStock
          L3_2 = WordStock
          L4_2 = "南北杂货店"
          L5_2 = 132
          L6_2 = 31
          L7_2 = 324
          L8_2 = 79
          L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
          if not L2_2 or L1_2 ~= 2 then
            L2_2 = getNewYearView
            L3_2 = "南北杂货店"
            L2_2 = L2_2(L3_2)
            if not L2_2 or L1_2 ~= 2 then
              goto lbl_581
            end
          end
          while true do
            L2_2 = getColour
            L3_2 = buyGoods
            L4_2 = "购买三"
            L2_2 = L2_2(L3_2, L4_2)
            if L2_2 and L1_2 == 2 then
              L1_2 = 3
              L2_2 = randomClick
              L3_2 = 0
              L4_2 = 500
              L5_2 = 1432
              L6_2 = 486
              L7_2 = 1807
              L8_2 = 566
              L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
            else
              L2_2 = getColour
              L3_2 = buyGoods
              L4_2 = "重叠NPC"
              L2_2 = L2_2(L3_2, L4_2)
              if L2_2 and L1_2 == 2 then
                L2_2 = getWordStock
                L3_2 = WordStock
                L4_2 = "杂货老板"
                L5_2 = 506
                L6_2 = 169
                L7_2 = 1701
                L8_2 = 999
                L2_2, L3_2, L4_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                if L2_2 then
                  L5_2 = randomClick
                  L6_2 = 0
                  L7_2 = 300
                  L8_2 = L3_2
                  L9_2 = L4_2 - 20
                  L10_2 = L3_2 + 150
                  L11_2 = L4_2 + 70
                  L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
                end
              else
                L2_2 = getColour
                L3_2 = buyGoods
                L4_2 = "商店内部"
                L2_2 = L2_2(L3_2, L4_2)
                if L2_2 and L1_2 == 3 then
                  L1_2 = 4
                  L2_2 = randomClick
                  L3_2 = 0
                  L4_2 = 500
                  L5_2 = 1463
                  L6_2 = 150
                  L7_2 = 1550
                  L8_2 = 236
                  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                else
                  L2_2 = getColour
                  L3_2 = buyGoods
                  L4_2 = "摄妖香"
                  L2_2 = L2_2(L3_2, L4_2)
                  if L2_2 and L1_2 == 4 then
                    L2_2 = randomClick
                    L3_2 = 2
                    L4_2 = 800
                    L5_2 = 1727
                    L6_2 = 780
                    L2_2(L3_2, L4_2, L5_2, L6_2)
                    L2_2 = randomClick
                    L3_2 = 0
                    L4_2 = 200
                    L5_2 = 1755
                    L6_2 = 297
                    L7_2 = 1844
                    L8_2 = 382
                    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                    L2_2 = randomClick
                    L3_2 = 0
                    L4_2 = 200
                    L5_2 = 1599
                    L6_2 = 295
                    L7_2 = 1688
                    L8_2 = 383
                    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                    L2_2 = randomClick
                    L3_2 = 0
                    L4_2 = 200
                    L5_2 = 1752
                    L6_2 = 445
                    L7_2 = 1842
                    L8_2 = 534
                    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                    L2_2 = randomClick
                    L3_2 = 0
                    L4_2 = 200
                    L5_2 = 1408
                    L6_2 = 947
                    L7_2 = 1606
                    L8_2 = 1001
                    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                    L2_2 = randomClick
                    L3_2 = 0
                    L4_2 = 200
                    L5_2 = 1408
                    L6_2 = 947
                    L7_2 = 1606
                    L8_2 = 1001
                    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
                    L1_2 = 5
                  else
                    if L1_2 == 5 then
                      L2_2 = getColour
                      L3_2 = colorList
                      L4_2 = "关闭对话框"
                      L2_2 = L2_2(L3_2, L4_2)
                      if L2_2 then
                        L2_2 = randomClick
                        L3_2 = 2
                        L4_2 = 200
                        L5_2 = 1848
                        L6_2 = 71
                        L7_2 = 20
                        L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
                        L2_2 = randomClick
                        L3_2 = 2
                        L4_2 = 500
                        L5_2 = 1848
                        L6_2 = 71
                        L7_2 = 20
                        L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
                        L2_2 = randomClick
                        L3_2 = 2
                        L4_2 = 500
                        L5_2 = 62
                        L6_2 = 379
                        L7_2 = 5
                        L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
                        return
                    end
                    else
                      L2_2 = getColour
                      L3_2 = buyGoods
                      L4_2 = "摄妖香"
                      L2_2 = L2_2(L3_2, L4_2)
                      if not L2_2 and L1_2 == 4 then
                        L1_2 = 3
                      elseif L1_2 == 2 then
                        L2_2 = randomClick
                        L3_2 = 1
                        L4_2 = 800
                        L5_2 = 894
                        L6_2 = 338
                        L2_2(L3_2, L4_2, L5_2, L6_2)
                      end
                    end
                  end
                end
              end
            end
          end
          goto lbl_608
          ::lbl_581::
          L2_2 = getWordStock
          L3_2 = WordStock
          L4_2 = "长安城"
          L5_2 = 154
          L6_2 = 37
          L7_2 = 308
          L8_2 = 78
          L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
          if not L2_2 or L1_2 ~= 0 then
            L2_2 = getNewYearView
            L3_2 = "长安城"
            L2_2 = L2_2(L3_2)
            if not L2_2 or L1_2 ~= 0 then
              goto lbl_608
            end
          end
          L2_2 = randomClick
          L3_2 = 0
          L4_2 = 300
          L5_2 = 22
          L6_2 = 25
          L7_2 = 301
          L8_2 = 117
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
        end
      end
      ::lbl_608::
    end
  end
end

L1_1["前往购买道具"] = L2_1
L1_1 = global

function L2_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L0_2 = 0
  L1_2 = findMultiColorInRegionFuzzy
  L2_2 = 11056569
  L3_2 = "1|0|0x9cadb1,3|0|0x5c7074,6|0|0x283c40,9|0|0x95a9ad,12|0|0x2a3a41,15|0|0x73838b,18|0|0x8b9fa3,21|0|0x506264,24|0|0xa7babe"
  L4_2 = 90
  L5_2 = 15
  L6_2 = 929
  L7_2 = 91
  L8_2 = 995
  L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  y = L2_2
  x = L1_2
  L1_2 = x
  if 0 < L1_2 then
    L1_2 = randomTap
    L2_2 = x
    L3_2 = y
    L4_2 = 10
    L1_2(L2_2, L3_2, L4_2)
    L1_2 = mSleep
    L2_2 = math
    L2_2 = L2_2.random
    L3_2 = 400
    L4_2 = 800
    L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L2_2(L3_2, L4_2)
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
  end
  while true do
    L1_2 = printLog
    L2_2 = "寻找飞行符:"
    L3_2 = L0_2
    L2_2 = L2_2 .. L3_2
    L1_2(L2_2)
    L1_2 = getColour
    L2_2 = colorList
    L3_2 = "打开背包"
    L1_2 = L1_2(L2_2, L3_2)
    if L1_2 then
      L1_2 = 1
      L2_2 = 10
      L3_2 = 1
      for L4_2 = L1_2, L2_2, L3_2 do
        L5_2 = getColour
        L6_2 = colorList
        L7_2 = "飞行符"
        L5_2 = L5_2(L6_2, L7_2)
        if L5_2 then
          return
        elseif L4_2 == 10 then
          while true do
            L5_2 = getColour
            L6_2 = colorList
            L7_2 = "不是行囊"
            L5_2 = L5_2(L6_2, L7_2)
            if L5_2 then
              L5_2 = getWordStock
              L6_2 = WordStock
              L7_2 = "移动道具"
              L8_2 = 183
              L9_2 = 370
              L10_2 = 518
              L11_2 = 1056
              L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
              if L5_2 then
                while true do
                  L8_2 = getWordStock
                  L9_2 = WordStock
                  L10_2 = "移动道具"
                  L11_2 = 183
                  L12_2 = 370
                  L13_2 = 518
                  L14_2 = 1056
                  L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                  if L8_2 then
                    L8_2 = randomTap
                    L9_2 = L6_2
                    L10_2 = L7_2
                    L11_2 = 10
                    L8_2(L9_2, L10_2, L11_2)
                    L8_2 = mSleep
                    L9_2 = math
                    L9_2 = L9_2.random
                    L10_2 = 400
                    L11_2 = 800
                    L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2(L10_2, L11_2)
                    L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  else
                    L8_2 = randomClick
                    L9_2 = 0
                    L10_2 = 300
                    L11_2 = 885
                    L12_2 = 177
                    L13_2 = 1078
                    L14_2 = 233
                    L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                    L8_2 = randomClick
                    L9_2 = 0
                    L10_2 = setSleep
                    L10_2 = L10_2()
                    L11_2 = 885
                    L12_2 = 177
                    L13_2 = 1078
                    L14_2 = 233
                    L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                    return
                  end
                end
              else
                L8_2 = getColour
                L9_2 = colorList
                L10_2 = "飞行符"
                L8_2, L9_2, L10_2 = L8_2(L9_2, L10_2)
                if L8_2 then
                  L11_2 = randomTap
                  L12_2 = L9_2
                  L13_2 = L10_2
                  L14_2 = 20
                  L11_2(L12_2, L13_2, L14_2)
                  L11_2 = mSleep
                  L12_2 = math
                  L12_2 = L12_2.random
                  L13_2 = 300
                  L14_2 = 700
                  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2(L13_2, L14_2)
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                elseif L0_2 < 2 then
                  L0_2 = L0_2 + 1
                  L11_2 = randomClick
                  L12_2 = 0
                  L13_2 = 300
                  L14_2 = 885
                  L15_2 = 177
                  L16_2 = 1078
                  L17_2 = 233
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  break
                else
                  L11_2 = randomClick
                  L12_2 = 0
                  L13_2 = 300
                  L14_2 = 885
                  L15_2 = 177
                  L16_2 = 1078
                  L17_2 = 233
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  isBoolean = true
                  L11_2 = useCAFlag
                  L12_2 = 904
                  L13_2 = 745
                  L14_2 = 1006
                  L15_2 = 850
                  L16_2 = 1182
                  L17_2 = 509
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  L11_2 = global
                  L11_2 = L11_2["前往购买道具"]
                  L12_2 = "飞行符"
                  return L11_2(L12_2)
                end
              end
            else
              L5_2 = randomClick
              L6_2 = 0
              L7_2 = 300
              L8_2 = 1140
              L9_2 = 174
              L10_2 = 1335
              L11_2 = 234
              L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            end
          end
        else
          L5_2 = mSleep
          L6_2 = 100
          L5_2(L6_2)
        end
      end
    else
      L1_2 = printLog
      L2_2 = "打开背包"
      L1_2(L2_2)
      L1_2 = randomClick
      L2_2 = 2
      L3_2 = 300
      L4_2 = 1695
      L5_2 = 985
      L6_2 = 10
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    end
  end
end

L1_1["寻找飞行符"] = L2_1
L1_1 = global

function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2
  L3_2 = A2_2 or nil
  if not A2_2 then
    L3_2 = 800
  end
  while true do
    L4_2 = true
    L5_2 = 0
    while true do
      L6_2 = getColour
      L7_2 = colorList
      L8_2 = "打开背包"
      L6_2 = L6_2(L7_2, L8_2)
      if not L6_2 then
        goto lbl_209
      end
      while true do
        L6_2 = getColour
        L7_2 = colorList
        L8_2 = "子女"
        L6_2 = L6_2(L7_2, L8_2)
        if L6_2 then
          L6_2 = printLog
          L7_2 = "识别到点开子女"
          L6_2(L7_2)
          L6_2 = randomClick
          L7_2 = 0
          L8_2 = 800
          L9_2 = 1657
          L10_2 = 192
          L11_2 = 1709
          L12_2 = 322
          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
        else
          L6_2 = getWordStock
          L7_2 = WordStock
          L8_2 = "点击使用"
          L9_2 = 553
          L10_2 = 479
          L11_2 = 795
          L12_2 = 950
          L6_2, L7_2, L8_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
          if L6_2 then
            L9_2 = getColour
            L10_2 = colorList
            L11_2 = "确定飞行符"
            L9_2 = L9_2(L10_2, L11_2)
            if L9_2 then
              L9_2 = printLog
              L10_2 = "使用飞行符"
              L9_2(L10_2)
              L4_2 = false
              L9_2 = os
              L9_2 = L9_2.time
              L9_2 = L9_2()
              L5_2 = L9_2
              L9_2 = randomTap
              L10_2 = L7_2
              L11_2 = L8_2
              L12_2 = 10
              L9_2(L10_2, L11_2, L12_2)
              L9_2 = mSleep
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 200
              L12_2 = 500
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L10_2(L11_2, L12_2)
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
          end
          else
            L9_2 = getColour
            L10_2 = colorList
            L11_2 = "飞行符界面"
            L9_2 = L9_2(L10_2, L11_2)
            if L9_2 then
              L9_2 = printLog
              L10_2 = "前往坐标地点"
              L9_2(L10_2)
              L9_2 = randomClick
              L10_2 = 2
              L11_2 = 888
              L12_2 = A0_2
              L13_2 = A1_2
              L14_2 = 10
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              L9_2 = randomTap
              L10_2 = 1604
              L11_2 = 84
              L12_2 = 10
              L9_2(L10_2, L11_2, L12_2)
              L9_2 = mSleep
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 200
              L12_2 = 500
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2 = L10_2(L11_2, L12_2)
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2)
              L9_2 = true
              return L9_2
            else
              L9_2 = os
              L9_2 = L9_2.time
              L9_2 = L9_2()
              L9_2 = L9_2 - L5_2
              if 10 < L9_2 and L5_2 ~= 0 then
                L9_2 = getColour
                L10_2 = colorList
                L11_2 = "飞行符界面"
                L9_2 = L9_2(L10_2, L11_2)
                if not L9_2 then
                  L5_2 = 0
                  L4_2 = true
              end
              elseif L4_2 then
                L9_2 = printLog
                L10_2 = "点击飞行符"
                L9_2(L10_2)
                L9_2 = 1
                L10_2 = BBFW
                L10_2 = #L10_2
                L11_2 = 1
                for L12_2 = L9_2, L10_2, L11_2 do
                  L13_2 = getColour
                  L14_2 = colorList
                  L15_2 = "飞行符"
                  L16_2 = 80
                  L17_2 = BBFW
                  L17_2 = L17_2[L12_2]
                  L17_2 = L17_2[1]
                  L18_2 = BBFW
                  L18_2 = L18_2[L12_2]
                  L18_2 = L18_2[2]
                  L19_2 = BBFW
                  L19_2 = L19_2[L12_2]
                  L19_2 = L19_2[3]
                  L20_2 = BBFW
                  L20_2 = L20_2[L12_2]
                  L20_2 = L20_2[4]
                  L13_2, L14_2, L15_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                  if L13_2 then
                    L16_2 = true
                    while L16_2 do
                      L17_2 = getColors
                      L18_2 = colorList
                      L19_2 = "飞行符"
                      L20_2 = 893
                      L21_2 = 252
                      L22_2 = 1034
                      L23_2 = 393
                      L17_2 = L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
                      if L17_2 then
                        L17_2 = randomClick
                        L18_2 = 0
                        L19_2 = 300
                        L20_2 = 934
                        L21_2 = 294
                        L22_2 = 990
                        L23_2 = 351
                        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2)
                        L17_2 = 1
                        L18_2 = 30
                        L19_2 = 1
                        for L20_2 = L17_2, L18_2, L19_2 do
                          L21_2 = getColour
                          L22_2 = colorList
                          L23_2 = "确定飞行符"
                          L21_2 = L21_2(L22_2, L23_2)
                          if L21_2 then
                            L21_2 = os
                            L21_2 = L21_2.time
                            L21_2 = L21_2()
                            L5_2 = L21_2
                            L16_2 = false
                            goto lbl_197
                          else
                            L21_2 = mSleep
                            L22_2 = 100
                            L21_2(L22_2)
                          end
                        end
                      else
                        L17_2 = MoveObjects
                        L18_2 = L14_2
                        L19_2 = L15_2
                        L20_2 = 963
                        L21_2 = 324
                        L22_2 = 183
                        L23_2 = 370
                        L24_2 = 518
                        L25_2 = 1056
                        L17_2(L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                      end
                      ::lbl_197::
                    end
                    break
                  elseif L12_2 == 20 then
                    L16_2 = global
                    L16_2 = L16_2["寻找飞行符"]
                    L16_2()
                  end
                end
              end
            end
          end
        end
      end
    end
    goto lbl_219
    ::lbl_209::
    L6_2 = printLog
    L7_2 = "打开背包"
    L6_2(L7_2)
    L6_2 = randomClick
    L7_2 = 2
    L8_2 = 300
    L9_2 = 1695
    L10_2 = 985
    L11_2 = 10
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
    ::lbl_219::
  end
end

L1_1["使用飞行符"] = L2_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2)
  local L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  L8_2 = true
  L9_2 = 0
  while true do
    L10_2 = getWordStock
    L11_2 = WordStock
    L12_2 = "移动道具"
    L13_2 = A4_2
    L14_2 = A5_2
    L15_2 = A6_2
    L16_2 = A7_2
    L10_2, L11_2, L12_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
    if L10_2 then
      while true do
        L13_2 = getWordStock
        L14_2 = WordStock
        L15_2 = "移动道具"
        L16_2 = A4_2
        L17_2 = A5_2
        L18_2 = A6_2
        L19_2 = A7_2
        L13_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
        if L13_2 then
          L13_2 = randomTap
          L14_2 = L11_2
          L15_2 = L12_2
          L16_2 = 10
          L13_2(L14_2, L15_2, L16_2)
          L13_2 = mSleep
          L14_2 = math
          L14_2 = L14_2.random
          L15_2 = 400
          L16_2 = 800
          L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L14_2(L15_2, L16_2)
          L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L13_2 = randomClick
          L14_2 = 2
          L15_2 = setSleep
          L15_2 = L15_2()
          L16_2 = A2_2
          L17_2 = A3_2
          L13_2(L14_2, L15_2, L16_2, L17_2)
          return
        end
      end
    else
      if not L10_2 then
        L13_2 = os
        L13_2 = L13_2.time
        L13_2 = L13_2()
        L13_2 = L13_2 - L9_2
        if 5 < L13_2 and L9_2 ~= 0 then
          L8_2 = true
          L9_2 = 0
      end
      elseif L8_2 then
        L8_2 = false
        L13_2 = os
        L13_2 = L13_2.time
        L13_2 = L13_2()
        L9_2 = L13_2
        L13_2 = randomTap
        L14_2 = A0_2
        L15_2 = A1_2
        L16_2 = 20
        L13_2(L14_2, L15_2, L16_2)
        L13_2 = mSleep
        L14_2 = math
        L14_2 = L14_2.random
        L15_2 = 200
        L16_2 = 500
        L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L14_2(L15_2, L16_2)
        L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
      end
    end
  end
end

MoveObjects = L1_1
L1_1 = global

function L2_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  while true do
    L0_2 = printLog
    L1_2 = "行囊移动摄妖香"
    L0_2(L1_2)
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "打开背包"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = getColour
      L1_2 = colorList
      L2_2 = "摄妖香"
      L0_2 = L0_2(L1_2, L2_2)
      if L0_2 then
        return
      else
        while true do
          L0_2 = getColour
          L1_2 = colorList
          L2_2 = "不是行囊"
          L0_2 = L0_2(L1_2, L2_2)
          if L0_2 then
            L0_2 = getWordStock
            L1_2 = WordStock
            L2_2 = "移动道具"
            L3_2 = 183
            L4_2 = 370
            L5_2 = 518
            L6_2 = 1056
            L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
            if L0_2 then
              while true do
                L3_2 = getWordStock
                L4_2 = WordStock
                L5_2 = "移动道具"
                L6_2 = 183
                L7_2 = 370
                L8_2 = 518
                L9_2 = 1056
                L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                if L3_2 then
                  L3_2 = randomTap
                  L4_2 = L1_2
                  L5_2 = L2_2
                  L6_2 = 10
                  L3_2(L4_2, L5_2, L6_2)
                  L3_2 = mSleep
                  L4_2 = math
                  L4_2 = L4_2.random
                  L5_2 = 400
                  L6_2 = 800
                  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L4_2(L5_2, L6_2)
                  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                else
                  L3_2 = randomClick
                  L4_2 = 0
                  L5_2 = 300
                  L6_2 = 885
                  L7_2 = 177
                  L8_2 = 1078
                  L9_2 = 233
                  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                  L3_2 = randomClick
                  L4_2 = 0
                  L5_2 = setSleep
                  L5_2 = L5_2()
                  L6_2 = 885
                  L7_2 = 177
                  L8_2 = 1078
                  L9_2 = 233
                  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                  return
                end
              end
            else
              L3_2 = getColour
              L4_2 = colorList
              L5_2 = "摄妖香"
              L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
              if L3_2 then
                L6_2 = randomTap
                L7_2 = L4_2
                L8_2 = L5_2 + 20
                L9_2 = 20
                L6_2(L7_2, L8_2, L9_2)
                L6_2 = mSleep
                L7_2 = math
                L7_2 = L7_2.random
                L8_2 = 300
                L9_2 = 700
                L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L7_2(L8_2, L9_2)
                L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
              else
                L6_2 = randomClick
                L7_2 = 0
                L8_2 = 300
                L9_2 = 885
                L10_2 = 177
                L11_2 = 1078
                L12_2 = 233
                L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                L6_2 = useCAFlag
                L7_2 = 1396
                L8_2 = 445
                L9_2 = 1489
                L10_2 = 557
                L11_2 = 1182
                L12_2 = 509
                L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                L6_2 = global
                L6_2 = L6_2["前往购买道具"]
                L7_2 = "摄妖香"
                L6_2(L7_2)
                L6_2 = global
                L6_2 = L6_2["使用飞行符"]
                L7_2 = 745
                L8_2 = 438
                return L6_2(L7_2, L8_2)
              end
            end
          else
            L0_2 = randomClick
            L1_2 = 0
            L2_2 = 300
            L3_2 = 1140
            L4_2 = 174
            L5_2 = 1335
            L6_2 = 234
            L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
          end
        end
      end
    else
      L0_2 = printLog
      L1_2 = "打开背包"
      L0_2(L1_2)
      L0_2 = randomClick
      L1_2 = 2
      L2_2 = 300
      L3_2 = 1695
      L4_2 = 985
      L5_2 = 10
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
    end
  end
end

L1_1["寻找摄妖香"] = L2_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L2_2 = 1
  L3_2 = 12
  L4_2 = 1
  L5_2 = #A0_2
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = string
    L8_2 = L8_2.sub
    L9_2 = A0_2
    L10_2 = L2_2
    L11_2 = L3_2
    L8_2 = L8_2(L9_2, L10_2, L11_2)
    if A1_2 == "物品店：" and L8_2 == "物品店：" then
      L9_2 = string
      L9_2 = L9_2.sub
      L10_2 = A0_2
      L11_2 = L3_2 + 1
      L12_2 = #A0_2
      L9_2 = L9_2(L10_2, L11_2, L12_2)
      return L9_2
    elseif A1_2 == "宠物店：" and L8_2 == "物品店：" then
      L9_2 = string
      L9_2 = L9_2.sub
      L10_2 = A0_2
      L11_2 = 13
      L12_2 = L2_2 - 1
      L9_2 = L9_2(L10_2, L11_2, L12_2)
      return L9_2
    else
      L2_2 = L2_2 + 1
      L3_2 = L3_2 + 1
    end
  end
end

getSmGoodsStr = L1_1

function L1_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["延迟模式"]
  if L0_2 == "1秒" then
    L0_2 = 1000
    return L0_2
  else
    L0_2 = _ENV["延迟模式"]
    if L0_2 == "2秒" then
      L0_2 = 2000
      return L0_2
    else
      L0_2 = _ENV["延迟模式"]
      if L0_2 == "3秒" then
        L0_2 = 3000
        return L0_2
      else
        L0_2 = _ENV["延迟模式"]
        if L0_2 == "4秒" then
          L0_2 = 4000
          return L0_2
        else
          L0_2 = 1000
          return L0_2
        end
      end
    end
  end
end

setSleep = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = readFile
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  L3_2 = table
  L3_2 = L3_2.remove
  L4_2 = L2_2
  L5_2 = A1_2
  L3_2(L4_2, L5_2)
  L3_2 = io
  L3_2 = L3_2.open
  L4_2 = A0_2
  L5_2 = "w"
  L3_2 = L3_2(L4_2, L5_2)
  if L3_2 then
    L4_2 = writeFile
    L5_2 = L3_2
    L6_2 = L2_2
    L4_2(L5_2, L6_2)
    L5_2 = L3_2
    L4_2 = L3_2.close
    L4_2(L5_2)
  end
end

removeFirstLine = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  if L1_2 then
    L2_2 = assert
    L3_2 = L1_2
    L4_2 = "file open failed"
    L2_2(L3_2, L4_2)
    L2_2 = {}
    L4_2 = L1_2
    L3_2 = L1_2.read
    L3_2 = L3_2(L4_2)
    while L3_2 do
      L4_2 = table
      L4_2 = L4_2.insert
      L5_2 = L2_2
      L6_2 = L3_2
      L4_2(L5_2, L6_2)
      L5_2 = L1_2
      L4_2 = L1_2.read
      L4_2 = L4_2(L5_2)
      L3_2 = L4_2
    end
    return L2_2
  end
  L3_2 = L1_2
  L2_2 = L1_2.close
  L2_2(L3_2)
end

readFile = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = assert
  L3_2 = A0_2
  L4_2 = "file open failed"
  L2_2(L3_2, L4_2)
  L2_2 = ipairs
  L3_2 = A1_2
  L2_2, L3_2, L4_2 = L2_2(L3_2)
  for L5_2, L6_2 in L2_2, L3_2, L4_2 do
    L8_2 = A0_2
    L7_2 = A0_2.write
    L9_2 = L6_2
    L7_2(L8_2, L9_2)
    L8_2 = A0_2
    L7_2 = A0_2.write
    L9_2 = "\n"
    L7_2(L8_2, L9_2)
  end
end

writeFile = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L4_2 = 1
  L5_2 = 10
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = require
    L9_2 = "ts"
    L8_2 = L8_2(L9_2)
    L9_2 = L8_2.json
    L10_2 = "h7Wk5YthUqfOx4bvENGMHsMV"
    L11_2 = "NpGAyGmzVy05qKgaic6sC2zWhTwRlGCQ"
    L12_2 = userPath
    L12_2 = L12_2()
    L13_2 = "/res/baiduAI.jpg"
    L12_2 = L12_2 .. L13_2
    L13_2 = snapshot
    L14_2 = L12_2
    L15_2 = A0_2
    L16_2 = A1_2
    L17_2 = A2_2
    L18_2 = A3_2
    L13_2(L14_2, L15_2, L16_2, L17_2, L18_2)
    L13_2 = {}
    L13_2.language_type = "CHN_ENG"
    L13_2.ocrType = 4
    L14_2 = getAccessToken
    L15_2 = L10_2
    L16_2 = L11_2
    L14_2, L15_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      L16_2 = baiduAI
      L17_2 = L15_2
      L18_2 = L12_2
      L19_2 = L13_2
      L16_2, L17_2 = L16_2(L17_2, L18_2, L19_2)
      if L16_2 then
        L18_2 = L9_2.decode
        L19_2 = L17_2
        L18_2 = L18_2(L19_2)
        L19_2 = pairs
        L20_2 = L18_2.words_result
        L19_2, L20_2, L21_2 = L19_2(L20_2)
        for L22_2, L23_2 in L19_2, L20_2, L21_2 do
          L24_2 = L23_2.words
          if L24_2 == nil then
            break
          end
          L24_2 = L23_2.words
          if L24_2 ~= "" then
            L24_2 = L23_2.words
            return L24_2
          else
            break
          end
        end
      elseif L7_2 < 10 then
        L18_2 = printLog
        L19_2 = "识别失败,重新尝试"
        L18_2(L19_2)
        L18_2 = mSleep
        L19_2 = 2000
        L18_2(L19_2)
      end
    else
      L16_2 = printLog
      L17_2 = "获取 access_token 失败"
      L16_2(L17_2)
    end
  end
  L4_2 = printLog
  L5_2 = "识别失败"
  L4_2(L5_2)
  L4_2 = "识别失败"
  return L4_2
end

_ENV["百度AI"] = L1_1
L1_1 = {}
L2_1 = {}
L3_1 = {}
L4_1 = {}
L5_1 = {}
L6_1 = 1866
L7_1 = 14
L8_1 = 6687491
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["50%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1892
L7_1 = 14
L8_1 = 6687491
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["70%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1904
L7_1 = 14
L8_1 = 6687491
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["90%"] = L4_1
L2_1["角色"] = L3_1
L3_1 = {}
L4_1 = {}
L5_1 = {}
L6_1 = 1625
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["50%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1651
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["70%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1668
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["90%"] = L4_1
L2_1["宠物"] = L3_1
L1_1["加血"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = {}
L5_1 = {}
L6_1 = 1867
L7_1 = 33
L8_1 = 11253707
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["50%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1893
L7_1 = 33
L8_1 = 11253707
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["70%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1905
L7_1 = 33
L8_1 = 11253707
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["90%"] = L4_1
L2_1["角色"] = L3_1
L3_1 = {}
L4_1 = {}
L5_1 = {}
L6_1 = 1625
L7_1 = 34
L8_1 = 11385293
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["50%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1651
L7_1 = 34
L8_1 = 11385293
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["70%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1668
L7_1 = 34
L8_1 = 11385293
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["90%"] = L4_1
L2_1["宠物"] = L3_1
L1_1["加蓝"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = {}
L5_1 = {}
L6_1 = 1868
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["50%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1894
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["70%"] = L4_1
L4_1 = {}
L5_1 = {}
L6_1 = 1906
L7_1 = 13
L8_1 = 10727364
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L4_1[1] = L5_1
L3_1["90%"] = L4_1
L2_1["角色"] = L3_1
L1_1["加血伤势"] = L2_1
_ENV["颜色"] = L1_1
L1_1 = {}
L2_1 = {}
L3_1 = 11544656
L4_1 = "0|10|0x881830,-7|-10|0xb0aad3,8|-8|0xb2afd9"
L5_1 = 90
L6_1 = 595
L7_1 = 525
L8_1 = 672
L9_1 = 602
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.charFeat = L2_1
L2_1 = {}
L3_1 = 12226200
L4_1 = "2|0|0x72172a,5|0|0x701428,8|2|0xefe5e7,12|2|0xf6f1f2"
L5_1 = 90
L6_1 = 644
L7_1 = 929
L8_1 = 749
L9_1 = 976
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.talisman = L2_1
L2_1 = {}
L3_1 = 16777215
L4_1 = "34|-14|0xffffff,39|-20|0x182c38,-19|-7|0x384a54,-17|-19|0x56656e,-14|-27|0xfcfdfd"
L5_1 = 90
L6_1 = 814
L7_1 = 782
L8_1 = 946
L9_1 = 875
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.endOfEscorter = L2_1
L2_1 = {}
L3_1 = 10534088
L4_1 = "13|12|0xa0c0c8,-14|19|0x304048,2|17|0x98bcc8,22|20|0x98bcc8,22|-4|0xa0c0c8,10|-4|0x384c58,10|27|0x304048,28|10|0x304850"
L5_1 = 97
L6_1 = 1829
L7_1 = 769
L8_1 = 1909
L9_1 = 827
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.dialogClose = L2_1
L2_1 = {}
L3_1 = 15386959
L4_1 = "-7|-10|0xdb0c0b,-14|13|0xc8070f,-1|12|0xf8d04c,38|6|0x262e32"
L5_1 = 90
L6_1 = 1646
L7_1 = 943
L8_1 = 1747
L9_1 = 1042
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.openProps = L2_1
L2_1 = {}
L3_1 = 10865368
L4_1 = "-18|-16|0x1b2f38,15|-14|0x172b33,7|-7|0xb9d9e1,4|13|0x81adc0,-28|16|0x172b33"
L5_1 = 90
L6_1 = 1772
L7_1 = 929
L8_1 = 1889
L9_1 = 1047
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.expandBtn = L2_1
L2_1 = {}
L3_1 = 192514
L4_1 = "8|10|0x02eb03,38|2|0x01fe01,75|-2|0x02f302,81|8|0x02ec03,105|5|0x02f802,107|-2|0x02f602,110|14|0x02f902"
L5_1 = 90
L6_1 = 1592
L7_1 = 322
L8_1 = 1724
L9_1 = 362
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.merchantTask = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 15791288
L5_1 = "-6|0|0x920f06,-10|0|0xe3e0a9,-13|0|0x520805,15|-7|0x550d09,15|-14|0x9a2416"
L6_1 = 90
L7_1 = 916
L8_1 = 826
L9_1 = 1010
L10_1 = 901
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.logginBtn = L3_1
L3_1 = {}
L4_1 = 7345192
L5_1 = "-7|-4|0xe7d7db,-13|-8|0x701428,18|-11|0x8f4757,17|-16|0xffffff,17|8|0x691428"
L6_1 = 90
L7_1 = 1343
L8_1 = 858
L9_1 = 1606
L10_1 = 971
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.logoutBtn = L3_1
L3_1 = {}
L4_1 = 7345192
L5_1 = "-9|-4|0x8c4151,-10|-1|0xf0e6e8,-10|2|0x792336,14|-9|0x701428,9|-14|0xf9f5f6"
L6_1 = 90
L7_1 = 1104
L8_1 = 590
L9_1 = 1228
L10_1 = 669
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.sureLogout = L3_1
L3_1 = {}
L4_1 = 6820904
L5_1 = "-46|-13|0xf6f2f4,-48|-9|0x601028,-47|21|0xfbf9f9,-23|2|0xfbf9fa,-23|4|0x69162a"
L6_1 = 95
L7_1 = 873
L8_1 = 165
L9_1 = 1096
L10_1 = 242
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.propsBtn = L3_1
L3_1 = {}
L4_1 = 6820904
L5_1 = "-17|-15|0xfcfbfc,-37|-15|0x601028,-42|-15|0xfaf8f9,12|17|0xfefdfd"
L6_1 = 95
L7_1 = 1132
L8_1 = 164
L9_1 = 1347
L10_1 = 245
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.lugBtn = L3_1
L3_1 = {}
L4_1 = 14117889
L5_1 = "7|7|0xa53f13,-2|-24|0x9f2b00,-12|-6|0xf8ea7b,10|-4|0xf8d624,5|15|0xf8a70b"
L6_1 = 90
L7_1 = 610
L8_1 = 941
L9_1 = 718
L10_1 = 1046
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.systemBtn = L3_1
L3_1 = {}
L4_1 = 1587280
L5_1 = "-1|10|0x31628c,-2|38|0x275275,-36|-13|0x4e7b9e,-38|2|0x3a668a,-11|-2|0x335e81,13|-2|0x325e82,39|-1|0x3b6686,34|-13|0x3d6b8d,17|16|0x183951"
L6_1 = 90
L7_1 = 1268
L8_1 = 0
L9_1 = 1916
L10_1 = 500
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.allWndClose = L3_1
L3_1 = {}
L4_1 = 9485512
L5_1 = "9|-15|0xb8dce7,9|16|0x8eb6be,-15|2|0x91bcc8,10|-11|0x112826"
L6_1 = 90
L7_1 = 787
L8_1 = 167
L9_1 = 1580
L10_1 = 295
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.closeActivityBar = L3_1
L3_1 = {}
L4_1 = 6097154
L5_1 = "-65|-5|0x335399,-454|25|0xf8d535"
L6_1 = 90
L7_1 = 1809
L8_1 = 0
L9_1 = 1919
L10_1 = 24
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.charBlood = L3_1
L3_1 = {}
L4_1 = 10464191
L5_1 = "-59|-5|0x2c6379,-206|16|0xce6010,-214|26|0xf8e446,41|50|0x306a7e"
L6_1 = 90
L7_1 = 1567
L8_1 = 3
L9_1 = 1673
L10_1 = 22
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.petBlood = L3_1
L3_1 = {}
L4_1 = 3167336
L5_1 = "76|14|0xd4dadf,-77|1|0xf6f7f8,-12|89|0xffff01,-12|185|0xffff01"
L6_1 = 90
L7_1 = 1411
L8_1 = 127
L9_1 = 1592
L10_1 = 195
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.addCharBlood = L3_1
L3_1 = {}
L4_1 = 3167336
L5_1 = "-76|0|0xf7f8f9,75|14|0xd6dce1,-13|89|0xffff01,-13|143|0xffff01"
L6_1 = 90
L7_1 = 1131
L8_1 = 130
L9_1 = 1311
L10_1 = 193
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.addPetBlood = L3_1
L3_1 = {}
L4_1 = 16054007
L5_1 = "1|0|0xfafbfb,2|-2|0x445f71,2|-5|0x385468,2|-8|0x445e71"
L6_1 = 90
L7_1 = 1724
L8_1 = 135
L9_1 = 1806
L10_1 = 193
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.juesejialan = L3_1
L3_1 = {}
L4_1 = 3691624
L5_1 = "0|1|0x385468,-2|0|0xb5bfc7,-4|0|0xffffff,-7|0|0x95a4ae"
L6_1 = 90
L7_1 = 1391
L8_1 = 132
L9_1 = 1496
L10_1 = 189
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.chongwujialan = L3_1
L3_1 = {}
L4_1 = 3163208
L5_1 = "9|-13|0xcad6da,1|-46|0x405a60,0|38|0x385058,-34|-11|0xccd4d4"
L6_1 = 90
L7_1 = 1719
L8_1 = 877
L9_1 = 1850
L10_1 = 1008
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.giveINF = L3_1
L3_1 = {}
L4_1 = 15590582
L5_1 = "-4|10|0xec642c,-17|11|0xf5a65b,-3|-2|0x397a20"
L6_1 = 90
L7_1 = 852
L8_1 = 921
L9_1 = 933
L10_1 = 1039
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.giveBtn = L3_1
L3_1 = {}
L4_1 = 7345192
L5_1 = "-40|-17|0xffffff,-19|1|0x71162a,25|7|0x6a162a,38|17|0xfefdfd"
L6_1 = 90
L7_1 = 440
L8_1 = 936
L9_1 = 690
L10_1 = 1016
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.giveSure = L3_1
L3_1 = {}
L4_1 = 7571077
L5_1 = "-12|-1|0x829496,-17|-12|0x7f9292,-11|-12|0x8fa6a5,11|-12|0x7c918f"
L6_1 = 90
L7_1 = 488
L8_1 = 728
L9_1 = 572
L10_1 = 808
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.closeChatWnd = L3_1
L3_1 = {}
L4_1 = 16382715
L5_1 = "13|21|0xedeff2,32|29|0xe4e8ea,35|27|0x315068,-113|25|0xfdfdfd,-111|21|0x32526a,17|-20|0xf7f8f9,20|-22|0x536c7d,20|-29|0x486274"
L6_1 = 90
L7_1 = 1412
L8_1 = 460
L9_1 = 1837
L10_1 = 576
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.veterinary = L3_1
L3_1 = {}
L4_1 = 15791142
L5_1 = "-5|-5|0x324952,5|-5|0x334a51,-21|1|0xf0f02d,0|-21|0xe0e367,20|1|0xf0f223,1|22|0xcbcf0a,-5|6|0x30454a,5|6|0x31464a"
L6_1 = 90
L7_1 = 384
L8_1 = 204
L9_1 = 964
L10_1 = 812
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.tipsMove = L3_1
L3_1 = {}
L4_1 = 7477034
L5_1 = "-24|-11|0xfefefe,-12|-5|0x73192d,-14|-5|0xfefdfd,16|10|0xf2ebec,25|0|0x72182b,25|-2|0xfffeff,-29|1|0x701428,23|10|0x75283a"
L6_1 = 90
L7_1 = 1161
L8_1 = 792
L9_1 = 1452
L10_1 = 929
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.teamInvite = L3_1
L3_1 = {}
L4_1 = 7345192
L5_1 = "-22|-4|0xffffff,-30|-9|0x701428,19|3|0x701428,19|1|0xece0e3,-10|17|0xe7d9dd,37|-15|0x792235,21|-19|0xffffff,-21|-20|0xf2eaec"
L6_1 = 90
L7_1 = 808
L8_1 = 622
L9_1 = 1120
L10_1 = 741
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L2_1.shanheReady = L3_1
L3_1 = "joinDungeon"
L4_1 = {}
L5_1 = 3167336
L6_1 = "-211|-6|0xf3f4f6,-207|-6|0x435d70,-204|-6|0xffffff,-195|-1|0x325468,-162|1|0xfcfcfd,-159|4|0x305468,-147|8|0xf7f9fa,-147|5|0x385b6e"
L7_1 = 90
L8_1 = 1404
L9_1 = 467
L10_1 = 1842
L11_1 = 580
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "taskBtn"
L4_1 = {}
L5_1 = 3103591
L6_1 = "-7|0|0xbff2f6,-22|-6|0x2d555d,-9|17|0x8ed6de,21|6|0xafeff7,21|-11|0x18444d"
L7_1 = 90
L8_1 = 1725
L9_1 = 239
L10_1 = 1892
L11_1 = 313
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "openTaskBtn"
L4_1 = {}
L5_1 = 9551048
L6_1 = "3|-14|0xb1d5e0,15|-23|0xc9e5ec,15|23|0x7ea0a9,4|14|0x78a4b0,8|0|0x90bcc8"
L7_1 = 90
L8_1 = 1880
L9_1 = 274
L10_1 = 1918
L11_1 = 347
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "openTrading"
L4_1 = {}
L5_1 = 3167336
L6_1 = "-204|-17|0xffffff,-196|-16|0x385468,-155|-10|0x4a6375,-159|-10|0xf0f2f4,-104|-8|0xffffff"
L7_1 = 90
L8_1 = 1413
L9_1 = 477
L10_1 = 1822
L11_1 = 572
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "buyIn"
L4_1 = {}
L5_1 = 7345192
L6_1 = "-52|-8|0xfefcfd,-44|-8|0x701428,-40|8|0xffffff,26|6|0xfdfcfc,24|-6|0x701428"
L7_1 = 90
L8_1 = 494
L9_1 = 944
L10_1 = 664
L11_1 = 1008
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "buyMax"
L4_1 = {}
L5_1 = 3167336
L6_1 = "-12|4|0xfdfdfd,-12|6|0x305060,10|-6|0xfdfdfd,21|6|0xffffff,17|9|0x304e60"
L7_1 = 90
L8_1 = 821
L9_1 = 799
L10_1 = 911
L11_1 = 856
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "sellOut"
L4_1 = {}
L5_1 = 7345192
L6_1 = "-35|-12|0xffffff,-37|-3|0x741b2e,37|-5|0xffffff,34|-8|0x701428,53|15|0xffffff"
L7_1 = 90
L8_1 = 1273
L9_1 = 949
L10_1 = 1429
L11_1 = 1011
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "sellMax"
L4_1 = {}
L5_1 = 3167336
L6_1 = "-12|4|0xfdfdfd,-12|6|0x305060,10|-6|0xfdfdfd,21|6|0xffffff,17|9|0x304e60"
L7_1 = 90
L8_1 = 1594
L9_1 = 799
L10_1 = 1676
L11_1 = 853
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "giveMeTradingTask"
L4_1 = {}
L5_1 = 3691624
L6_1 = "-133|-1|0xf4f6f7,-121|-4|0x385468,-69|17|0xfdfdfe,-36|2|0x385468,-193|11|0x305468"
L7_1 = 90
L8_1 = 1421
L9_1 = 150
L10_1 = 1818
L11_1 = 238
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "tradingTask15w"
L4_1 = {}
L5_1 = 3167336
L6_1 = "40|-56|0xfcfcfc,66|-54|0xfbfcfc,66|-58|0x3b576a,-194|9|0xffffff,-194|14|0x385468"
L7_1 = 90
L8_1 = 1412
L9_1 = 315
L10_1 = 1825
L11_1 = 492
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "notBussinessMan"
L4_1 = {}
L5_1 = 16711422
L6_1 = "0|5|0x182c38,0|10|0xfefefe,-390|2|0xffffff,-394|-3|0x182c38,-518|-13|0xf4f5f6,-510|-13|0x182c38,-503|-9|0xffffff"
L7_1 = 90
L8_1 = 132
L9_1 = 795
L10_1 = 693
L11_1 = 852
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L3_1 = "becomeBussinessMan"
L4_1 = {}
L5_1 = 3757417
L6_1 = "-32|6|0xe4e8eb,-32|9|0x35586c,-32|13|0xe4e9ec,93|3|0x335468,-32|-12|0xe0e4e7"
L7_1 = 90
L8_1 = 1422
L9_1 = 622
L10_1 = 1803
L11_1 = 710
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L2_1[L3_1] = L4_1
L1_1.btn = L2_1
L2_1 = "map"
L3_1 = {}
L4_1 = "mark"
L5_1 = {}
L6_1 = 16308609
L7_1 = "-11|-12|0xf0bc48,2|-20|0xa86022,11|-14|0xf0b139,-9|-23|0x9e6027,11|-23|0xb16d28"
L8_1 = 90
L9_1 = 272
L10_1 = 188
L11_1 = 1654
L12_1 = 894
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "me"
L5_1 = {}
L6_1 = 8439320
L7_1 = "0|-1|0x84cf1c,0|-2|0x88d820,-4|-10|0xf3f4ea,-8|-5|0xb5ee6b,2|11|0x58840c,10|7|0x56830e,10|-4|0x7cb818,10|-10|0x7cb218,-9|0|0x88cc20"
L8_1 = 90
L9_1 = 272
L10_1 = 188
L11_1 = 1654
L12_1 = 894
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "search"
L5_1 = {}
L6_1 = 13685567
L7_1 = "-15|-1|0xa55293,-28|-9|0x2b737d,9|-14|0xdba14b,-20|8|0xefa952"
L8_1 = 90
L9_1 = 103
L10_1 = 1
L11_1 = 596
L12_1 = 331
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "filterINF"
L5_1 = {}
L6_1 = 131071
L7_1 = "67|-15|0x01f7f7,11|125|0xe7e9e8,47|231|0xffff01,-1|121|0x182022"
L8_1 = 90
L9_1 = 1509
L10_1 = 293
L11_1 = 1559
L12_1 = 341
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "filterBtn"
L5_1 = {}
L6_1 = 16777215
L7_1 = "-4|6|0x284859,-19|14|0xfdfdfd,-13|10|0x2f4e5e,4|-13|0xffffff,0|-11|0x305268"
L8_1 = 90
L9_1 = 409
L10_1 = 160
L11_1 = 1225
L12_1 = 989
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "filter"
L5_1 = {}
L6_1 = 5778472
L7_1 = "38|-3|0x01ffff,0|109|0x582c28,0|219|0x582c28,0|325|0x582c28,0|433|0x582c28,0|541|0x582c28,0|650|0x582c28,122|-15|0x02e9ea"
L8_1 = 95
L9_1 = 1327
L10_1 = 301
L11_1 = 1357
L12_1 = 330
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "flag"
L3_1 = {}
L4_1 = "red"
L5_1 = {}
L6_1 = 14063880
L7_1 = "-13|-9|0x220400,-7|-9|0xdb0d0d,-37|-22|0x74878b,5|-11|0xf01310"
L8_1 = 90
L9_1 = 902
L10_1 = 266
L11_1 = 1566
L12_1 = 792
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "blue"
L5_1 = {}
L6_1 = 12549638
L7_1 = "-18|-7|0x000b1d,2|-9|0x0058e8,-40|-21|0x2d60a0,10|-11|0xb4b54f"
L8_1 = 90
L9_1 = 902
L10_1 = 266
L11_1 = 1566
L12_1 = 792
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "yellow"
L5_1 = {}
L6_1 = 14797837
L7_1 = "-12|-19|0x040400,12|-24|0x6f14ca,-32|-20|0xe2cb00,-39|-25|0x541b8c"
L8_1 = 90
L9_1 = 902
L10_1 = 266
L11_1 = 1566
L12_1 = 792
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "green"
L5_1 = {}
L6_1 = 3527168
L7_1 = "-12|-20|0x050f00,-41|-26|0x737473,11|-24|0xc18002,6|-24|0x41ed00"
L8_1 = 90
L9_1 = 902
L10_1 = 266
L11_1 = 1566
L12_1 = 792
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "white"
L5_1 = {}
L6_1 = 13882568
L7_1 = "-12|-19|0x040706,-39|-27|0x707a7f,9|-17|0xe4d13c,-14|-5|0xf0f8ee"
L8_1 = 90
L9_1 = 902
L10_1 = 266
L11_1 = 1566
L12_1 = 792
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "flagPoint"
L3_1 = {}
L4_1 = "escort"
L5_1 = {}
L6_1 = 14828058
L7_1 = "-7|-17|0xf8ece1,-13|-11|0xf8dbc7,-1|18|0xf69a65,-16|5|0xc12d10"
L8_1 = 85
L9_1 = 1335
L10_1 = 436
L11_1 = 1645
L12_1 = 663
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "escortcs51"
L5_1 = {}
L6_1 = 14828058
L7_1 = "-7|-17|0xf8ece1,-13|-11|0xf8dbc7,-1|18|0xf69a65,-16|5|0xc12d10"
L8_1 = 85
L9_1 = 1082
L10_1 = 502
L11_1 = 1146
L12_1 = 561
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "fighting"
L3_1 = {}
L4_1 = "reset"
L5_1 = {}
L6_1 = 3362658
L7_1 = "0|-3|0xedf0f1,-1|16|0x294959,1|19|0xe6e9eb,42|5|0xeef1f3"
L8_1 = 90
L9_1 = 1331
L10_1 = 776
L11_1 = 1561
L12_1 = 869
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "shrinkAuto"
L5_1 = {}
L6_1 = 3689816
L7_1 = "-15|-16|0xf0f838,15|-14|0xf0f838,-16|7|0xeef12b,14|21|0x294048"
L8_1 = 90
L9_1 = 893
L10_1 = 362
L11_1 = 1493
L12_1 = 1038
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "auto"
L5_1 = {}
L6_1 = 8428705
L7_1 = "-26|22|0xbed4d8,-33|-2|0x809da9,22|24|0xcfe3e7,35|-21|0x304850"
L8_1 = 90
L9_1 = 1774
L10_1 = 922
L11_1 = 1904
L12_1 = 1047
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "doorPos"
L3_1 = {}
L4_1 = "DTJW"
L5_1 = {}
L6_1 = 7231285
L7_1 = "-24|18|0x48981f,-47|-15|0x5b971b,-41|-37|0x725b3a,-2|-23|0xad956a"
L8_1 = 90
L9_1 = 567
L10_1 = 40
L11_1 = 908
L12_1 = 390
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "MWZ"
L5_1 = {}
L6_1 = 9662777
L7_1 = "-12|-5|0xd7aa52,-19|-51|0x6a501e,-57|71|0xf6f3ae,-40|0|0xe3a363"
L8_1 = 90
L9_1 = 529
L10_1 = 182
L11_1 = 901
L12_1 = 485
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "MWJ"
L5_1 = {}
L6_1 = 8669743
L7_1 = "-55|57|0xabb98c,-65|66|0xb97752,-137|120|0xd4cb44,30|31|0x3d533c"
L8_1 = 90
L9_1 = 271
L10_1 = 182
L11_1 = 711
L12_1 = 510
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "QKD"
L5_1 = {}
L6_1 = 9061679
L7_1 = "-5|-37|0xd8b382,36|69|0x6b4823,60|22|0x321b14,7|-25|0xb77553"
L8_1 = 90
L9_1 = 1028
L10_1 = 70
L11_1 = 1494
L12_1 = 382
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CYJF"
L5_1 = {}
L6_1 = 15368742
L7_1 = "-18|-21|0x66141f,-17|-49|0xedad2f,63|-42|0xfc5d3f,62|-60|0xfc471f"
L8_1 = 90
L9_1 = 506
L10_1 = 10
L11_1 = 1073
L12_1 = 583
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CJG"
L5_1 = {}
L6_1 = 5480454
L7_1 = "-21|-45|0x2f4b56,31|-31|0x4f7467,6|-41|0x1c3440,104|-22|0x293e33"
L8_1 = 90
L9_1 = 1166
L10_1 = 235
L11_1 = 1515
L12_1 = 519
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CJG1"
L5_1 = {}
L6_1 = 797492
L7_1 = "4|-42|0x102626,4|-74|0x0c3039,4|-92|0x162b37,4|-112|0x3f1e17,8|-130|0x947a60,18|-158|0x7c845e,58|-118|0x62643f,58|-66|0x543425"
L8_1 = 90
L9_1 = 630
L10_1 = 20
L11_1 = 1504
L12_1 = 478
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DTGJ"
L5_1 = {}
L6_1 = 11627589
L7_1 = "-43|3|0x310c0b,-24|2|0x7d8688,-16|17|0x353739,76|26|0x866520"
L8_1 = 90
L9_1 = 156
L10_1 = 152
L11_1 = 456
L12_1 = 330
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DTGJ1"
L5_1 = {}
L6_1 = 7433797
L7_1 = "3|15|0x938250,4|35|0x8c8c55,24|40|0x6c3421,53|31|0x9d915d,83|12|0xd1d9ce,58|6|0xa1702d,26|13|0x989361,-12|29|0x9f7a2f"
L8_1 = 90
L9_1 = 200
L10_1 = 374
L11_1 = 644
L12_1 = 696
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "PTS"
L5_1 = {}
L6_1 = 5360696
L7_1 = "-28|-26|0x5c4224,-51|28|0x9be028,-41|77|0x0e8f0e,-65|-15|0x1b6e53"
L8_1 = 90
L9_1 = 176
L10_1 = 222
L11_1 = 483
L12_1 = 428
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CYD"
L5_1 = {}
L6_1 = 9198796
L7_1 = "20|-18|0x263a33,-5|-40|0x4b6440,8|52|0x4ff2b1,-53|1|0x3a5472"
L8_1 = 90
L9_1 = 992
L10_1 = 38
L11_1 = 1302
L12_1 = 199
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "PSL"
L5_1 = {}
L6_1 = 9081164
L7_1 = "-23|0|0x2a682d,-13|-12|0x0d2b13,-72|24|0x8d7e51,-32|27|0x178c41"
L8_1 = 93
L9_1 = 537
L10_1 = 4
L11_1 = 960
L12_1 = 263
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "PSD"
L5_1 = {}
L6_1 = 4152914
L7_1 = "-50|-6|0x816a70,-126|-44|0x6d5b73,-150|-53|0x420b1d,-153|-61|0x753d29"
L8_1 = 90
L9_1 = 1311
L10_1 = 83
L11_1 = 1684
L12_1 = 296
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CFBJ"
L5_1 = {}
L6_1 = 8493652
L7_1 = "-21|1|0x2a400e,-25|25|0x87612f,-28|19|0x312304,14|-5|0x2b3f19"
L8_1 = 90
L9_1 = 817
L10_1 = 14
L11_1 = 1353
L12_1 = 361
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CSC"
L5_1 = {}
L6_1 = 353375
L7_1 = "12|0|0x006059,22|2|0x2f2919,36|12|0x302c20,51|35|0x142527,70|43|0x1a4d40,87|46|0x415532,99|54|0x5f6840,-96|43|0x0c4e3b,-95|3|0x054230"
L8_1 = 90
L9_1 = 603
L10_1 = 3
L11_1 = 1466
L12_1 = 223
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CSC1"
L5_1 = {}
L6_1 = 7614512
L7_1 = "4|20|0x69342e,-9|2|0x6c322f,45|0|0x6c5e37,34|16|0x85703a,34|8|0x33220e,-19|-7|0x2b1e0e,47|22|0x48492a,65|53|0x145447,46|73|0xdec28f"
L8_1 = 93
L9_1 = 190
L10_1 = 225
L11_1 = 610
L12_1 = 522
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "FCS"
L5_1 = {}
L6_1 = 3895637
L7_1 = "-34|60|0x3f7c91,-60|12|0x3a677f,-98|36|0x2b5b5d,12|92|0x49b330"
L8_1 = 90
L9_1 = 1038
L10_1 = 134
L11_1 = 1512
L12_1 = 554
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "LTG"
L5_1 = {}
L6_1 = 6845269
L7_1 = "18|-37|0x5a5e3f,17|-59|0xdebf2e,-56|-10|0x959a68,-106|-25|0x46a545"
L8_1 = 90
L9_1 = 963
L10_1 = 29
L11_1 = 1335
L12_1 = 304
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "JYC"
L5_1 = {}
L6_1 = 12215094
L7_1 = "-12|-20|0xe0b860,11|-14|0x462e22,40|-51|0xd88f69,54|-27|0x57984b"
L8_1 = 90
L9_1 = 648
L10_1 = 138
L11_1 = 1140
L12_1 = 570
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "JYC1"
L5_1 = {}
L6_1 = 10655865
L7_1 = "-3|0|0xa16a56,-7|0|0xad5f4e,-11|0|0xbbbf8e,-16|0|0xb66654,-16|-5|0xae5c4c,-16|-14|0xb8564e,-2|-11|0x9e4b43,-4|-23|0x5d161e,-20|-23|0x693430"
L8_1 = 90
L9_1 = 1573
L10_1 = 88
L11_1 = 1916
L12_1 = 235
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DHW"
L5_1 = {}
L6_1 = 10069117
L7_1 = "15|-4|0x909f7b,-20|-6|0x839456,-40|66|0x4a2b0c,11|70|0x4e290e,-13|93|0x2e0a01,19|110|0xe67225,61|92|0xc4541d,85|104|0x516723,94|48|0xc76a2b"
L8_1 = 90
L9_1 = 1069
L10_1 = 241
L11_1 = 1885
L12_1 = 873
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "SJG"
L5_1 = {}
L6_1 = 3654037
L7_1 = "-1|23|0x488421,-2|43|0x69a244,-5|52|0x5d9541,-19|31|0x3c8737,-20|-2|0x3dbd9c,6|-18|0x46c99b,9|6|0x41892a,10|26|0x70b24e,7|40|0x73ac4c"
L8_1 = 90
L9_1 = 868
L10_1 = 18
L11_1 = 1079
L12_1 = 195
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "NEC"
L5_1 = {}
L6_1 = 5842711
L7_1 = "26|-8|0x51322d,32|-21|0x096e6b,-27|-17|0x175b60,-12|-43|0x075b64"
L8_1 = 90
L9_1 = 884
L10_1 = 57
L11_1 = 1330
L12_1 = 359
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "NECCZJ"
L5_1 = {}
L6_1 = 1841255
L7_1 = "68|18|0x5b686c,-52|-4|0x773284,-19|-50|0x4b3707,-10|-35|0x403691"
L8_1 = 90
L9_1 = 270
L10_1 = 122
L11_1 = 805
L12_1 = 452
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DF"
L5_1 = {}
L6_1 = 4934287
L7_1 = "0|21|0xc67390,2|27|0x945374,13|33|0x7d5a99,21|33|0xe68896,27|43|0xc87271,30|53|0xd2837f,22|47|0x7d7cd7,2|56|0x9376d5,-11|60|0x9088e4"
L8_1 = 90
L9_1 = 1113
L10_1 = 7
L11_1 = 1506
L12_1 = 363
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "SLD"
L5_1 = {}
L6_1 = 865372
L7_1 = "15|4|0x52b1c2,-34|72|0x78edfc,-14|20|0x4da9bc,-17|-18|0x1c405f"
L8_1 = 90
L9_1 = 278
L10_1 = 156
L11_1 = 661
L12_1 = 465
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "WZG"
L5_1 = {}
L6_1 = 6383438
L7_1 = "-17|0|0xd5c9a1,-26|-24|0xdac699,2|-68|0xddd2af,31|-14|0x3b4230"
L8_1 = 90
L9_1 = 1012
L10_1 = 249
L11_1 = 1321
L12_1 = 786
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "STL"
L5_1 = {}
L6_1 = 13274451
L7_1 = "17|-13|0x7f392a,24|-14|0xab8661,-106|23|0x6e592b,-17|-61|0xe7b261"
L8_1 = 90
L9_1 = 658
L10_1 = 194
L11_1 = 984
L12_1 = 870
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DTJW1"
L5_1 = {}
L6_1 = 4928565
L7_1 = "1|0|0x5b2f32,3|0|0x6c2524,6|0|0x7d0203,10|0|0x8d443f,10|10|0x7a9b93,10|12|0x65877f,10|14|0x68807c,10|15|0x778e88"
L8_1 = 90
L9_1 = 37
L10_1 = 11
L11_1 = 967
L12_1 = 707
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DXD"
L5_1 = {}
L6_1 = 8734761
L7_1 = "-16|24|0x9f7433,3|48|0x9fa831,-18|-28|0xd28b55,-95|17|0xc49d42"
L8_1 = 90
L9_1 = 1055
L10_1 = 329
L11_1 = 1483
L12_1 = 769
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "LDD"
L5_1 = {}
L6_1 = 1250352
L7_1 = "-116|-16|0x2a314b,-2|74|0x202543,-84|99|0x3c4d7d,42|-56|0x6d676c"
L8_1 = 90
L9_1 = 1225
L10_1 = 112
L11_1 = 1512
L12_1 = 638
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "SWD"
L5_1 = {}
L6_1 = 13007470
L7_1 = "-12|-44|0x965f73,60|34|0xe4e2d1,74|16|0x7c5e4f,-40|96|0x5a2f38"
L8_1 = 90
L9_1 = 632
L10_1 = 224
L11_1 = 1306
L12_1 = 816
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "LXBD"
L5_1 = {}
L6_1 = 9530420
L7_1 = "-15|-7|0x777587,6|-15|0x5a4b4b,-4|-41|0x9e7443,23|-10|0xa67d40"
L8_1 = 90
L9_1 = 561
L10_1 = 91
L11_1 = 903
L12_1 = 393
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "JNYW"
L5_1 = {}
L6_1 = 3040552
L7_1 = "-61|5|0x5a281c,-45|-66|0x462a23,-41|-88|0x474a4a,42|-69|0x143d0f"
L8_1 = 90
L9_1 = 460
L10_1 = 56
L11_1 = 940
L12_1 = 443
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "JNYW1"
L5_1 = {}
L6_1 = 6775895
L7_1 = "10|-2|0x3d1112,26|-2|0x5c3b38,48|0|0x8a6726,48|16|0x754f17,44|46|0x7d878b,44|36|0x533612,66|22|0x6e5317,90|4|0x3b1808,104|-30|0x4a5158"
L8_1 = 90
L9_1 = 18
L10_1 = 272
L11_1 = 888
L12_1 = 652
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "LHS"
L5_1 = {}
L6_1 = 16710976
L7_1 = "7|17|0x79551e,21|17|0x3b1d03,33|8|0xa04d18,41|-2|0x2f1603,36|-15|0xfdff4e,7|57|0x758b29,24|57|0x2c3f4d,-21|33|0x2f4b41,-76|2|0x2f8d79"
L8_1 = 90
L9_1 = 1109
L10_1 = 26
L11_1 = 1354
L12_1 = 282
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DHW2"
L5_1 = {}
L6_1 = 16377000
L7_1 = "9|0|0x8f1d00,-8|1|0x851f05,-14|6|0xffefad,-15|14|0xfbe99f,-9|14|0x8d1c06,1|14|0xf4df9a,9|14|0x8b1c05,18|23|0xfcedb2,15|-64|0xfe8f3b"
L8_1 = 90
L9_1 = 1595
L10_1 = 752
L11_1 = 1764
L12_1 = 953
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "npcPos"
L3_1 = {}
L4_1 = "escortBoss"
L5_1 = {}
L6_1 = 14664754
L7_1 = "-2|26|0xe1ce32,16|-1|0xe7d534,24|12|0xe9d535,35|9|0xebd834,61|23|0xe8d533,73|25|0xe8d533,98|27|0xcab72d,97|14|0xdac930"
L8_1 = 87
L9_1 = 967
L10_1 = 593
L11_1 = 1414
L12_1 = 919
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postMan"
L5_1 = {}
L6_1 = 15259192
L7_1 = "8|-6|0xe9d735,10|11|0xe8d533,21|11|0xebd834,50|7|0xe3d133,56|1|0xe8d633,64|15|0xe8d634,21|13|0xe9d633,27|12|0xe9d734"
L8_1 = 90
L9_1 = 356
L10_1 = 250
L11_1 = 1268
L12_1 = 826
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postGirl"
L5_1 = {}
L6_1 = 15062324
L7_1 = "27|17|0xe9d734,62|20|0xe0d333,-54|-5|0xcebd2e,-61|21|0xd6c62f,-21|10|0xe4d436,-11|18|0xe4d534,-110|16|0xebd834,-143|15|0xead834"
L8_1 = 90
L9_1 = 484
L10_1 = 132
L11_1 = 1514
L12_1 = 890
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postUncle"
L5_1 = {}
L6_1 = 15127860
L7_1 = "10|0|0xebd834,20|0|0xe6d535,-2|20|0xebd834,33|9|0xe9d735,54|-1|0x544d14,50|11|0xe1d134,52|21|0xd8c630,33|1|0xebd834"
L8_1 = 90
L9_1 = 535
L10_1 = 218
L11_1 = 1494
L12_1 = 832
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postSoldier"
L5_1 = {}
L6_1 = 12890676
L7_1 = "0|9|0xebd834,12|14|0xe8d534,26|26|0xd8c630,41|7|0xead734,51|21|0xdfcd32,55|15|0xd8c630,61|23|0xdcca31,59|3|0xebd834"
L8_1 = 90
L9_1 = 240
L10_1 = 188
L11_1 = 1062
L12_1 = 852
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postSoldier1"
L5_1 = {}
L6_1 = 14338353
L7_1 = "0|-3|0x9dc7c9,17|5|0xe4d232,17|0|0xe4d132,17|-6|0x6f9cc8,22|-20|0x80c0ff,1|-18|0x83bff9,-4|10|0xebd834,17|14|0xe5d233,17|18|0xe3d132"
L8_1 = 90
L9_1 = 340
L10_1 = 192
L11_1 = 996
L12_1 = 750
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "天宫传送天将"
L5_1 = {}
L6_1 = 5791795
L7_1 = "-13|56|0x269f5c,-129|256|0x1c988a,-130|232|0xa67f5d,-114|126|0x165543,-102|74|0xba8b64,-94|22|0x557159,-154|190|0xd0a87e,-153|168|0x55c952,-129|149|0x42a53b"
L8_1 = 90
L9_1 = 1065
L10_1 = 226
L11_1 = 1509
L12_1 = 925
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "机关人"
L5_1 = {}
L6_1 = 14667059
L7_1 = "0|5|0xebd834,0|14|0xebd834,-6|19|0xe4cf33,9|2|0xe4d133,8|15|0xe0ce33,18|5|0xe4d133,18|15|0xe5d234,18|19|0xe5d233,2|14|0xebd834"
L8_1 = 89
L9_1 = 474
L10_1 = 199
L11_1 = 1294
L12_1 = 933
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "机关人红"
L5_1 = {}
L6_1 = 16711937
L7_1 = "0|10|0xfe0101,-3|12|0xe00201,4|10|0xfd0101,9|-4|0xe80906,16|-4|0xf70101,9|6|0xf10101,8|14|0xf60101,18|18|0xf70201,24|21|0xfa0503"
L8_1 = 90
L9_1 = 474
L10_1 = 199
L11_1 = 1294
L12_1 = 933
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "白虎堂红"
L5_1 = {}
L6_1 = 16711937
L7_1 = "0|4|0xfa0101,9|5|0xf00101,10|13|0xf40101,0|17|0xff0101,32|5|0xfb0101,44|5|0xfa0101,47|15|0xfc0303,45|26|0xf00202,29|23|0xe50101"
L8_1 = 90
L9_1 = 324
L10_1 = 152
L11_1 = 1592
L12_1 = 858
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "书院红"
L5_1 = {}
L6_1 = 16515329
L7_1 = "-1|13|0xff0202,8|13|0xfe0101,-37|15|0xf80101,-54|15|0xf40101,-44|10|0xfd0704,-37|16|0xfd0101,-14|8|0xff0101,-14|6|0xfe0101"
L8_1 = 90
L9_1 = 218
L10_1 = 160
L11_1 = 1615
L12_1 = 945
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "书院"
L5_1 = {}
L6_1 = 15456309
L7_1 = "0|11|0xebd835,-13|8|0xebd834,-29|17|0xead734,-30|3|0xd8c630,-43|11|0xead635,-53|10|0xebd834,8|20|0xe9d735,41|10|0xead734,23|21|0xead734"
L8_1 = 85
L9_1 = 218
L10_1 = 160
L11_1 = 1615
L12_1 = 945
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "商人红"
L5_1 = {}
L6_1 = 15991556
L7_1 = "-8|4|0xba0101,7|3|0xfe0101,-8|10|0xed0101,9|10|0xed0101,7|20|0xf40101,-6|20|0xfc0101,-5|26|0xea0203,6|26|0xf00101,12|30|0xf20101"
L8_1 = 90
L9_1 = 610
L10_1 = 248
L11_1 = 1364
L12_1 = 794
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "花果山g北俱"
L5_1 = {}
L6_1 = 13328200
L7_1 = "-14|-2|0xfba26d,-13|-13|0x61e3a8,-5|-21|0x177a59,14|-27|0x13855b,17|-13|0x5d484a,22|-6|0x825358,-30|-6|0xa7867d,-16|1|0xfb7c56,-14|21|0x257658"
L8_1 = 87
L9_1 = 1171
L10_1 = 265
L11_1 = 1679
L12_1 = 737
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "北俱g花果山"
L5_1 = {}
L6_1 = 6848378
L7_1 = "0|15|0x8a9c9c,16|18|0x474457,18|28|0x4c4049,0|28|0x627b7b,-15|24|0x8698a8,-25|6|0x6a8077,-7|-10|0x687e82,29|31|0x453b45,17|44|0x14386b"
L8_1 = 97
L9_1 = 1351
L10_1 = 682
L11_1 = 1602
L12_1 = 902
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "郊外g北俱"
L5_1 = {}
L6_1 = 5626841
L7_1 = "4|7|0x77ce9f,-1|18|0x58e3d5,-1|24|0x7fe5be,16|11|0x529a78,14|-2|0x45cadd,2|-8|0x278ea8,-1|-32|0x3092a5,11|-59|0x3592ab,33|-61|0x387e70"
L8_1 = 87
L9_1 = 588
L10_1 = 164
L11_1 = 1056
L12_1 = 549
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "白无常"
L5_1 = {}
L6_1 = 15390516
L7_1 = "-10|9|0xe6d433,15|13|0xe8d735,15|27|0xb5a729,14|2|0xdccf3a,-9|25|0xebd834,38|0|0xebd834,38|10|0xebd834,27|26|0xe2d53a,27|9|0xdecf35"
L8_1 = 90
L9_1 = 521
L10_1 = 93
L11_1 = 1051
L12_1 = 864
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "白无常2"
L5_1 = {}
L6_1 = 6995379
L7_1 = "5|-4|0x1d4f69,13|-3|0x1f4962,22|0|0x274334,32|15|0x458745,31|36|0x4b854f,14|41|0x192537,-39|32|0x4ab6d9,-41|20|0x2e7e88,-41|9|0x3585a9"
L8_1 = 90
L9_1 = 800
L10_1 = 205
L11_1 = 1460
L12_1 = 748
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "北俱g花果山2"
L5_1 = {}
L6_1 = 7427154
L7_1 = "-1|-13|0x4b3b43,-4|-22|0x2b252e,-13|-22|0x3f3944,-15|-29|0x596d7c,2|-43|0x3e586f,25|-30|0x252332,24|-13|0x292530,17|13|0x524245,-16|6|0x606e9b"
L8_1 = 90
L9_1 = 1495
L10_1 = 650
L11_1 = 1722
L12_1 = 823
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "境外to郊外"
L5_1 = {}
L6_1 = 8421143
L7_1 = "1|5|0x625b0f,-1|16|0x565108,9|18|0xd1904c,-7|-30|0x887811,19|103|0x9d850b,62|132|0xceb905,82|132|0x9b9008,46|77|0xae7236,14|133|0xc08842"
L8_1 = 90
L9_1 = 741
L10_1 = 24
L11_1 = 1011
L12_1 = 264
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postRabbittuzi"
L5_1 = {}
L6_1 = 9791564
L7_1 = "-27|3|0x8a7e75,-37|-24|0xac967e,-15|-34|0x9e6e53,5|-39|0x92644a,29|-39|0x49342d,31|-26|0x977e62,26|-15|0x2b1e22,2|15|0xc5a380,-9|16|0xc5a17d"
L8_1 = 90
L9_1 = 1306
L10_1 = 431
L11_1 = 1829
L12_1 = 904
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postShrimp"
L5_1 = {}
L6_1 = 14337845
L7_1 = "19|1|0xe9d634,24|9|0xe2d034,6|17|0xebd834,47|17|0xe4d234,36|20|0xe6d433,64|7|0xe1cf33,57|20|0xc8b82d,46|3|0xe8d533"
L8_1 = 90
L9_1 = 1354
L10_1 = 222
L11_1 = 1906
L12_1 = 916
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postRabbit"
L5_1 = {}
L6_1 = 15390516
L7_1 = "17|1|0xd9c730,19|1|0x0e0f11,17|-4|0xe8d533,55|0|0xd6c530,55|2|0x171507,84|7|0xebd834,87|9|0x060603,96|-1|0xebd834"
L8_1 = 90
L9_1 = 998
L10_1 = 580
L11_1 = 1428
L12_1 = 840
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "boatMan"
L5_1 = {}
L6_1 = 15126582
L7_1 = "0|14|0xe5d037,9|10|0xddcb31,9|21|0xddca31,24|2|0xe2ce34,50|5|0xebd834,50|13|0xebd834,60|10|0xd9c730,43|10|0xd9c730"
L8_1 = 90
L9_1 = 842
L10_1 = 488
L11_1 = 1320
L12_1 = 826
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "gangster"
L5_1 = {}
L6_1 = 14338098
L7_1 = "5|0|0xead734,13|13|0xebd834,50|11|0xdfcd33,57|9|0xebd834,85|8|0xebd834,92|9|0xe9d635,115|0|0xe2cf33,57|9|0xebd834"
L8_1 = 90
L9_1 = 588
L10_1 = 100
L11_1 = 1034
L12_1 = 520
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "banpaijf"
L5_1 = {}
L6_1 = 8417587
L7_1 = "-6|15|0x3f683a,2|27|0xbe1008,-22|-76|0x84504c,-20|-82|0x482418,-29|-65|0xa0d4b8,5|-7|0x7e5f30"
L8_1 = 90
L9_1 = 835
L10_1 = 364
L11_1 = 835
L12_1 = 365
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "exchequerDirector"
L5_1 = {}
L6_1 = 15061557
L7_1 = "1|6|0xebd834,38|1|0xebd834,38|6|0xddcb31,29|13|0xe6d435,39|18|0xebd834,39|23|0xebd834,69|6|0xebd834,80|5|0xecd834,64|16|0xe4d132"
L8_1 = 90
L9_1 = 285
L10_1 = 256
L11_1 = 1462
L12_1 = 936
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "gangWarMan"
L5_1 = {}
L6_1 = 14732601
L7_1 = "8|3|0x0c0a07,4|11|0xdecc34,20|19|0xdcca31,41|4|0xebd834,20|3|0x211e08"
L8_1 = 90
L9_1 = 1080
L10_1 = 432
L11_1 = 1406
L12_1 = 657
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "gangWarOp1"
L5_1 = {}
L6_1 = 15264749
L7_1 = "4|-2|0x3f5a6e,4|-11|0x3a566a,7|-14|0xf2f4f5,-43|1|0xf6f7f8,-37|-5|0x385468"
L8_1 = 90
L9_1 = 1408
L10_1 = 152
L11_1 = 1561
L12_1 = 688
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "gangWarOp2"
L5_1 = {}
L6_1 = 15856628
L7_1 = "5|14|0x405b6e,-5|15|0x385468,-119|38|0xf0f3f4,-115|46|0x3a586f,-109|55|0x305068,-106|55|0xffffff"
L8_1 = 90
L9_1 = 1417
L10_1 = 153
L11_1 = 1839
L12_1 = 707
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "enterGang"
L5_1 = {}
L6_1 = 3691624
L7_1 = "-59|-3|0xffffff,-58|-8|0x385468,102|12|0xffffff,102|8|0x34576b"
L8_1 = 90
L9_1 = 1413
L10_1 = 144
L11_1 = 1828
L12_1 = 248
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "postOK"
L5_1 = {}
L6_1 = 16777215
L7_1 = "-4|2|0x33566a,40|-21|0xe5e9eb,47|-14|0x385468,74|5|0xf7f9f9"
L8_1 = 85
L9_1 = 1404
L10_1 = 456
L11_1 = 1594
L12_1 = 568
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "cs51"
L5_1 = {}
L6_1 = 10932554
L7_1 = "25|-23|0xcab442,25|-2|0xbfcb62,89|-62|0xaf4f52,65|-40|0xa89229,4|-62|0xab5241,26|-41|0xe4df51,53|-20|0x998f3a,51|-26|0x958e3e"
L8_1 = 90
L9_1 = 410
L10_1 = 100
L11_1 = 1447
L12_1 = 730
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "masterPos"
L3_1 = {}
L4_1 = "GYJJ"
L5_1 = {}
L6_1 = 14404157
L7_1 = "4|8|0xebd834,11|-2|0xdfcc31,38|2|0xe8d637,58|15|0xd7c630,78|0|0xe3d336,97|-4|0xd9c730,119|-6|0xead734,133|5|0xdecd31"
L8_1 = 90
L9_1 = 216
L10_1 = 72
L11_1 = 1368
L12_1 = 708
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "BBGN"
L5_1 = {}
L6_1 = 14272304
L7_1 = "16|-9|0xebd834,14|10|0xdfcc31,25|5|0xe9d634,52|-10|0xebd834,49|11|0xd7c630,79|7|0xe6d433,90|-5|0xebd834,119|-3|0xecd934"
L8_1 = 90
L9_1 = 696
L10_1 = 100
L11_1 = 1624
L12_1 = 804
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "LJ"
L5_1 = {}
L6_1 = 15324980
L7_1 = "19|3|0xecd834,9|20|0xe4d133,35|4|0xd5c331,36|18|0xdbc932,47|7|0xe3d03d,62|11|0xead734,62|19|0xd4c32f,48|18|0xebd834"
L8_1 = 90
L9_1 = 362
L10_1 = 140
L11_1 = 1224
L12_1 = 916
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "PTZS"
L5_1 = {}
L6_1 = 15324468
L7_1 = "0|2|0x423d0f,21|7|0xe3d132,49|13|0xd1c02e,54|1|0x38340d,77|11|0xebd834,88|8|0xd8c730,98|16|0xe7d433,113|6|0xdbcf42"
L8_1 = 81
L9_1 = 460
L10_1 = 88
L11_1 = 1632
L12_1 = 959
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "QQ"
L5_1 = {}
L6_1 = 14206256
L7_1 = "5|16|0xe1cd31,27|0|0xd8c730,37|-10|0xdcc830,38|16|0xe1ce32,53|5|0xdfcc31,64|15|0xdecb31,64|-1|0xd7c530,56|-3|0xdcc931"
L8_1 = 90
L9_1 = 272
L10_1 = 164
L11_1 = 1488
L12_1 = 780
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DHLW"
L5_1 = {}
L6_1 = 15456308
L7_1 = "20|1|0xe7d534,8|15|0xd6c530,31|12|0xdccb31,48|1|0xe5d837,74|7|0xdcca31,91|1|0xc9bb2f,117|-5|0xebd834,128|13|0xe3db3a"
L8_1 = 90
L9_1 = 46
L10_1 = 164
L11_1 = 1834
L12_1 = 966
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "SDW"
L5_1 = {}
L6_1 = 14075184
L7_1 = "21|0|0xd1c02f,-1|13|0xebd834,34|12|0xe3d132,58|12|0xe2d436,73|-12|0xcfbe2e,83|0|0xebd834,94|0|0xc4b930,94|14|0xd3c22f"
L8_1 = 90
L9_1 = 968
L10_1 = 494
L11_1 = 1500
L12_1 = 866
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "YJ"
L5_1 = {}
L6_1 = 15522101
L7_1 = "4|10|0xebd834,16|-7|0xebd834,28|2|0xddcb31,36|-5|0xead736,48|11|0xebd834,56|-7|0xebd834,62|-1|0xead834,64|10|0xe8d634"
L8_1 = 90
L9_1 = 292
L10_1 = 164
L11_1 = 1332
L12_1 = 824
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "CYJ"
L5_1 = {}
L6_1 = 14469179
L7_1 = "0|13|0xead834,14|6|0xebd834,21|13|0xead734,27|21|0xbbac2a,42|6|0xe6d837,64|5|0xddcb31,78|8|0xe3d938,96|15|0xe3d637"
L8_1 = 90
L9_1 = 470
L10_1 = 330
L11_1 = 920
L12_1 = 548
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "KDCS"
L5_1 = {}
L6_1 = 14666546
L7_1 = "26|4|0xe7d534,15|18|0xead835,38|7|0xe1d033,60|1|0xead734,58|20|0xe7d433,77|9|0xebd834,93|6|0xe1d032,122|15|0xe9d734"
L8_1 = 90
L9_1 = 660
L10_1 = 396
L11_1 = 961
L12_1 = 581
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "EDW"
L5_1 = {}
L6_1 = 12365098
L7_1 = "6|23|0xccbb2e,22|23|0xccbb2e,36|1|0xe2d338,46|9|0xebd834,60|23|0xe8d635,74|-3|0xe6d636,92|9|0xead73a,91|23|0xebd737"
L8_1 = 90
L9_1 = 873
L10_1 = 230
L11_1 = 1232
L12_1 = 462
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DDW"
L5_1 = {}
L6_1 = 15258679
L7_1 = "13|-2|0xebd834,3|19|0xcdbd2e,22|17|0xe5d63b,41|0|0xe9d73a,49|6|0xebd834,58|18|0xead835,85|-2|0xebd834,94|22|0xe9d838"
L8_1 = 90
L9_1 = 996
L10_1 = 224
L11_1 = 1368
L12_1 = 418
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "ZYDX"
L5_1 = {}
L6_1 = 14075189
L7_1 = "4|19|0xe6d436,16|-7|0xe0d03b,26|-2|0xe8d533,19|10|0xd4c22f,59|20|0xd7c630,85|-2|0xebd834,110|20|0xddce3d,136|2|0xe5d337"
L8_1 = 90
L9_1 = 794
L10_1 = 510
L11_1 = 1158
L12_1 = 705
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "HSN"
L5_1 = {}
L6_1 = 15390773
L7_1 = "23|10|0xebd835,23|27|0xbcac2a,48|10|0xebd834,60|10|0xebd834,74|2|0xebd834,71|15|0xecda41,90|12|0xe9d433,98|18|0xe1ce32"
L8_1 = 90
L9_1 = 917
L10_1 = 161
L11_1 = 1277
L12_1 = 362
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "DZW"
L5_1 = {}
L6_1 = 15390772
L7_1 = "2|17|0xebd834,19|-1|0xebd834,27|10|0xebd834,58|0|0xebd834,60|17|0xe0ce32,86|5|0xebd834,98|20|0xe2d237,96|6|0xd6c839"
L8_1 = 90
L9_1 = 533
L10_1 = 436
L11_1 = 884
L12_1 = 622
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "NMW"
L5_1 = {}
L6_1 = 14403889
L7_1 = "14|-2|0xe8d734,14|15|0xe1d436,37|4|0xcdbd2d,51|8|0xe9d634,57|16|0xe4d132,86|-6|0xebd834,87|17|0xead734,99|17|0xe9d234"
L8_1 = 90
L9_1 = 852
L10_1 = 418
L11_1 = 1338
L12_1 = 702
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "SPP"
L5_1 = {}
L6_1 = 15193396
L7_1 = "0|22|0xcaba2d,25|5|0xe8d634,42|14|0xebd834,49|-1|0xead835,55|20|0xe1cf32,79|-1|0xead733,91|5|0xead834,89|18|0xebd834"
L8_1 = 90
L9_1 = 520
L10_1 = 374
L11_1 = 806
L12_1 = 520
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "merchant"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 14864434
L7_1 = "8|-5|0xebd834,28|-3|0xe1d03f,28|13|0xead734,43|8|0xead734,50|-3|0xebd834,50|-8|0xebd834,57|8|0xe9d634,15|12|0xdfcf3a"
L8_1 = 90
L9_1 = 610
L10_1 = 248
L11_1 = 1364
L12_1 = 794
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 13614382
L7_1 = "-2|11|0xd5c42f,9|5|0xebd834,17|12|0xebd834,17|24|0xead934,2|27|0xe4d132,30|23|0xe2d032,44|1|0xead734,50|8|0xe2cf32,58|25|0xe9d734"
L8_1 = 90
L9_1 = 610
L10_1 = 248
L11_1 = 1525
L12_1 = 794
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "CAC"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 7557419
L7_1 = "2|-17|0x808540,20|-27|0xa1a490,32|-41|0x99977d,-13|-39|0xb1b1a0,-37|-33|0x635b28,-47|-40|0x6c7938,-39|-8|0x513215,-14|29|0x353f1c,90|-2|0x4f8a3c"
L8_1 = 90
L9_1 = 1017
L10_1 = 676
L11_1 = 1388
L12_1 = 948
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 12539960
L7_1 = "0|0|0xbf5838,-9|0|0xc35a40,-20|0|0xaa4d2b,-34|0|0x8e5a3f,-61|6|0x4d150a,-63|-7|0x936c3a,-59|-16|0x492309,-44|-24|0x8e6d37,-26|-28|0x542515,3|-19|0x7d361a,31|-2|0x624a1b,38|32|0xb15029,40|55|0x903913,9|51|0x966f1c,-16|37|0x7d531f,-32|26|0xfde47f,-1|20|0xbd533a,6|12|0xc15537,-19|12|0x763016,2|20|0xbb572f"
L8_1 = 90
L9_1 = 1148
L10_1 = 482
L11_1 = 1546
L12_1 = 824
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "DF"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 5002263
L7_1 = "12|7|0x404700,23|14|0x292806,-40|-13|0x2b6273,-43|-20|0x437f8b,-46|-6|0x1b070e,19|18|0x444807,30|18|0x2c3110,22|-11|0xcc6b24,25|-25|0xce6e1e"
L8_1 = 90
L9_1 = 387
L10_1 = 295
L11_1 = 993
L12_1 = 665
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 1167103
L7_1 = "-19|18|0x1a3d2f,-40|-4|0xbdb172,-26|-17|0x3ae6fd,21|-37|0x1259ac,35|-33|0x173b34,-67|32|0xaf6c41,23|43|0xc87973,8|63|0x3ac4fb,-30|-32|0x34eafb"
L8_1 = 90
L9_1 = 1141
L10_1 = 68
L11_1 = 1714
L12_1 = 432
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "ALG"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 10464092
L7_1 = "-3|15|0xcb8257,-24|28|0x60971d,-20|41|0x5a7808,-2|56|0xedaa74,-16|61|0x7f962c,6|99|0xc18757,-5|106|0x5c3611,-33|116|0x2c4a00,-33|129|0x284e00"
L8_1 = 90
L9_1 = 1110
L10_1 = 747
L11_1 = 1421
L12_1 = 967
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 2596181
L7_1 = "16|0|0xd87f0d,23|0|0xd58a00,30|1|0xaf9904,26|12|0x997248,3|33|0xaf8259,-34|45|0x797b30,-30|73|0xb18e5a,66|36|0xa44c02,76|65|0x1c7f1d"
L8_1 = 90
L9_1 = 412
L10_1 = 370
L11_1 = 988
L12_1 = 765
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "BJLZ"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 2042676
L7_1 = "8|-16|0x3a3a3a,2|-33|0x51525e,-12|-27|0x5b6882,-130|-10|0xa59473,-135|-9|0xebd9ab,-206|-16|0xded0b8,-170|-9|0x5b4c3a,-75|-32|0x61739a,-82|-76|0xb9cbde"
L8_1 = 93
L9_1 = 1250
L10_1 = 630
L11_1 = 1600
L12_1 = 900
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 6180931
L7_1 = "0|0|0x5e5043,0|7|0xb5bdc8,3|-8|0xbfc9e1,-7|-15|0x635541,-46|-51|0x4a6446,-39|-59|0x3b5335,-68|31|0x517273,3|35|0x493c39,-8|6|0x8b8c8a,-8|10|0xc0cce1,-3|45|0x6b625d,0|52|0x6c6659,1|63|0x777069,8|56|0x8b8890,14|50|0xb8c5dd,-88|-49|0x4f643a,-79|-48|0x718e5b,-97|-52|0x4d6247,-110|-49|0x5c7059,-108|-47|0x687b70"
L8_1 = 93
L9_1 = 1044
L10_1 = 106
L11_1 = 1514
L12_1 = 510
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "CSC"
L3_1 = {}
L4_1 = "A"
L5_1 = {}
L6_1 = 6441872
L7_1 = "-4|-9|0x9e65b8,-6|-22|0x513456,-8|-34|0x7a56ab,-9|-47|0xae65c0,-13|-60|0xebda57,-10|-67|0xc2ae2c,6|-76|0x482e18,40|-76|0x418c63,71|-67|0x24643f"
L8_1 = 90
L9_1 = 814
L10_1 = 424
L11_1 = 1065
L12_1 = 652
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "B"
L5_1 = {}
L6_1 = 10894404
L7_1 = "23|17|0x453710,43|5|0x918934,47|30|0x59603c,60|63|0xa0b89c,11|52|0x1c4c04,-22|54|0x26670b,47|90|0x9bbca7,27|115|0x9ebeab,81|91|0x97b6a4"
L8_1 = 90
L9_1 = 93
L10_1 = 363
L11_1 = 460
L12_1 = 736
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "goods"
L3_1 = {}
L4_1 = "DF"
L5_1 = {}
L6_1 = "baozhu"
L7_1 = {}
L8_1 = 7671048
L9_1 = "-23|1|0xe3becb,-35|3|0xa42606,-34|-21|0xbe8672,-2|-23|0xc8e8f0,19|-24|0x285a68,36|-22|0xf0dbde,38|-5|0x611108,28|-7|0xdc8e75,21|0|0xd05848"
L10_1 = 88
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "money"
L7_1 = {}
L8_1 = 12370112
L9_1 = "11|-8|0xdece4f,18|-14|0xecda2b,26|-5|0xf0e31f,33|-9|0xd7bf19,39|-9|0xe7ba04,33|-9|0xd7bf19,34|19|0xd6e2e5,49|16|0x504945,50|13|0x48401f"
L10_1 = 88
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "shoushi"
L7_1 = {}
L8_1 = 9677235
L9_1 = "6|-8|0xad954f,14|-12|0x794729,8|-22|0xd29a3f,14|-32|0xb46767,34|-11|0xb65a65,46|-22|0xd47657,71|-29|0xd65d47,78|-40|0xb6825c,54|-38|0x74718d"
L10_1 = 88
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L3_1[L4_1] = L5_1
L4_1 = "ALG"
L5_1 = {}
L6_1 = "salt"
L7_1 = {}
L8_1 = 6978687
L9_1 = "11|0|0xc8c1a3,14|0|0xa05e3f,23|1|0xd2ceab,28|2|0xa86145,38|1|0x72898f,41|-6|0xf0f0d8,33|16|0xc0a170,17|15|0x405c68,3|15|0x7c5b39"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "wine"
L7_1 = {}
L8_1 = 14935001
L9_1 = "7|2|0xa21422,17|3|0xa00c18,22|3|0x1f0000,36|4|0x500000,50|3|0x381d29,30|22|0x2a1c21,6|21|0x674655,-3|14|0xd2a995,21|-12|0xa8cad2"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "hat"
L7_1 = {}
L8_1 = 12434597
L9_1 = "12|1|0x2a3028,27|-1|0x332d26,34|6|0x282b25,39|14|0x3b3d38,23|34|0x9d9887,4|35|0xb0ad98,-8|29|0xcac4aa,36|34|0x7f7871,44|33|0x9a8a80"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L3_1[L4_1] = L5_1
L4_1 = "CSC"
L5_1 = {}
L6_1 = "mianfen"
L7_1 = {}
L8_1 = 15656113
L9_1 = "7|0|0x5c5f51,-3|9|0x89835d,-6|13|0xded792,-28|14|0xb3b6b1,4|36|0xb0b1a7,13|14|0xdee1dc,33|3|0xcaccc3,36|-2|0xd8d8d0,-16|3|0xe0e1dc"
L10_1 = 88
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "antler"
L7_1 = {}
L8_1 = 14673346
L9_1 = "21|-2|0xa07b74,7|-9|0x9f7c7a,-23|-4|0xa08380,-39|-4|0x957470,-47|-4|0x66443f,-39|-28|0xae8c81,-1|-32|0xededc9,35|-22|0x9a7975,29|-3|0x8d716e"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "amulet"
L7_1 = {}
L8_1 = 10054204
L9_1 = "11|-4|0x4b311c,15|-4|0x200c00,22|-4|0xf0f0e0,29|3|0xcfb548,15|19|0xb9b048,8|25|0x8e7750,-11|19|0xdece7b,-18|17|0xd5c063,-6|12|0xc0a145"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L3_1[L4_1] = L5_1
L4_1 = "BJLZ"
L5_1 = {}
L6_1 = "renshen"
L7_1 = {}
L8_1 = 11773011
L9_1 = "0|0|0xb3a453,-30|-3|0x0b6ad7,-30|7|0x685430,-7|17|0x7c6c41,6|15|0x4e5f59,14|9|0x2d5b69,26|0|0x376e90,29|-9|0x1e79ca,33|-22|0x3a95e7,11|-18|0x0175e7,-4|-20|0x3f79a9,-7|-5|0xf0ec98,-18|-12|0x30762c,-18|-8|0x189418,-20|7|0x57492a,-6|18|0x7c6b3e,-1|23|0x8a7b46"
L10_1 = 85
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "dengyou"
L7_1 = {}
L8_1 = 13026943
L9_1 = "0|0|0xc6c67f,4|-13|0xaba367,7|-23|0x88c4c4,-2|-23|0x86bdb8,-10|-22|0xa8ac80,-15|-24|0x7f9e8d,3|-24|0x98e8e8,18|-26|0xa0a270,36|-18|0xaaa66d,14|10|0x5b4323,-35|-19|0x85c0c3,-35|-17|0x95e0e1,-35|-15|0x8bbfbb,-35|-9|0x9fd1d3,-5|4|0x908458,-5|13|0xdad27a"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "lingdang"
L7_1 = {}
L8_1 = 9208924
L9_1 = "0|0|0x8c845c,-23|-3|0xd6ce9a,-30|0|0xeff7cf,-23|10|0x493109,-7|22|0xcccfa8,-5|31|0xccc185,-2|23|0x1f2717,7|23|0x7e6e3d,8|14|0x392d09,7|6|0x1a1501,29|4|0x7f6d46,40|-5|0xbaad8a,-3|-6|0xd5bf7e,-17|-3|0xab955d,-22|-8|0xbaa672"
L10_1 = 85
L11_1 = 251
L12_1 = 192
L13_1 = 782
L14_1 = 307
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L3_1[L4_1] = L5_1
L4_1 = "CAC"
L5_1 = {}
L6_1 = "dao"
L7_1 = {}
L8_1 = 11970111
L9_1 = "-1|5|0x303539,-13|22|0xc8b428,-23|30|0x353d45,-35|43|0xc8b429,-39|31|0xd9cd3c,33|-8|0xef676f,38|-17|0xba2e52,34|-24|0xab2d4e,31|-19|0x8a3441"
L10_1 = 88
L11_1 = 253
L12_1 = 189
L13_1 = 917
L14_1 = 312
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "shanzi"
L7_1 = {}
L8_1 = 2714368
L9_1 = "-42|-3|0x612000,-27|-23|0xd2ebea,-1|-22|0xcae4e5,36|-28|0x501400,24|-31|0xe0f4f0,12|-16|0xc9e5e1,20|-14|0x480c00,-2|22|0x13c617,-2|33|0x2cb736"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L6_1 = "fozhu"
L7_1 = {}
L8_1 = 4794391
L9_1 = "-6|-2|0x9a8865,7|2|0x87765d,-5|13|0x2a750d,-15|34|0x3ac328,40|1|0x88684b,51|-8|0x380700,52|-32|0x341912,41|-43|0x1e0500,-6|-20|0x69432e"
L10_1 = 88
L11_1 = 251
L12_1 = 180
L13_1 = 915
L14_1 = 315
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L5_1[L6_1] = L7_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "props"
L3_1 = {}
L4_1 = "escort2"
L5_1 = {}
L6_1 = 11233910
L7_1 = "-21|-20|0xf0f8f0,18|22|0x6e3140,9|16|0x491d24,-32|-3|0xbeb1a6"
L8_1 = 90
L9_1 = 870
L10_1 = 432
L11_1 = 1552
L12_1 = 997
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "escort4"
L5_1 = {}
L6_1 = 12877388
L7_1 = "-9|-8|0xf0e0f0,-16|-17|0x9b4723,-23|-8|0xdeb996,17|-7|0x7c3d2c,-5|-35|0xa55b32"
L8_1 = 90
L9_1 = 880
L10_1 = 453
L11_1 = 1543
L12_1 = 991
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "escort3"
L5_1 = {}
L6_1 = 3626879
L7_1 = "6|-14|0x4d72a8,12|-24|0xf0f8f0,-5|-26|0xf0f8f0,6|-11|0x4870a8,12|-17|0x5175a8"
L8_1 = 90
L9_1 = 870
L10_1 = 432
L11_1 = 1552
L12_1 = 997
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "escort1"
L5_1 = {}
L6_1 = 15792368
L7_1 = "21|-2|0xa07050,33|-5|0x9d7050,27|-16|0xf0f8f0,17|-15|0xeef0f0,18|7|0xa48366"
L8_1 = 90
L9_1 = 870
L10_1 = 432
L11_1 = 1552
L12_1 = 997
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "flyRune"
L5_1 = {}
L6_1 = 7090866
L7_1 = "-8|-16|0xc9b57d,5|-29|0x8f5522,2|21|0x9a5e28,1|9|0xd3c4c9"
L8_1 = 90
L9_1 = 902
L10_1 = 261
L11_1 = 1563
L12_1 = 797
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "tradingTicket"
L5_1 = {}
L6_1 = 14871527
L7_1 = "7|2|0xbe8077,19|0|0xb3afa8,-14|2|0x9aa3a7,-23|-7|0x727976,-17|-7|0xccdfe2,-9|15|0xe9f4ed"
L8_1 = 90
L9_1 = 885
L10_1 = 451
L11_1 = 1544
L12_1 = 986
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "dodgeCoil"
L5_1 = {}
L6_1 = 9409120
L7_1 = "-5|-12|0xd7d595,13|-13|0xa3a776,1|-24|0xc5c88e,-36|-8|0xb7b97d,32|-14|0x8f8c57"
L8_1 = 90
L9_1 = 903
L10_1 = 264
L11_1 = 1569
L12_1 = 793
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
L2_1 = "VERINF"
L3_1 = {}
L4_1 = "VERidiom"
L5_1 = {}
L6_1 = 14935268
L7_1 = "28|-1|0xfafafa,2|30|0xe5e6e6,33|26|0xffffff,62|18|0xffffff,73|29|0xfafbfb,96|13|0xf9f9f9,96|21|0xebecec,111|29|0xf3f4f4"
L8_1 = 91
L9_1 = 414
L10_1 = 30
L11_1 = 875
L12_1 = 370
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "VER4Man_1"
L5_1 = {}
L6_1 = 16187906
L7_1 = "11|10|0xeb1214,11|16|0xfd0303,11|22|0xfd0202,40|10|0xe70304,48|22|0xff0101,116|12|0xfc0101,98|22|0xfe0101,147|0|0xf90101"
L8_1 = 91
L9_1 = 436
L10_1 = 62
L11_1 = 1162
L12_1 = 492
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "VER4Man_2"
L5_1 = {}
L6_1 = 16448250
L7_1 = "-147|2|0xefeeee,-108|10|0xf0efef,-64|19|0xfdfdfd,-64|22|0xf0efef,374|7|0xffffff,374|19|0xffffff,62|2|0xf4f3f3,46|0|0xfdfdfd"
L8_1 = 91
L9_1 = 436
L10_1 = 62
L11_1 = 1162
L12_1 = 492
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "VER4Man_3"
L5_1 = {}
L6_1 = 16514043
L7_1 = "4|18|0xfafafa,97|0|0xebebeb,199|0|0xfd0101,313|12|0xfb0101,292|22|0xfd0101,424|0|0xfdfdfd,175|16|0xfdfdfd,118|20|0xfefefe"
L8_1 = 91
L9_1 = 438
L10_1 = 64
L11_1 = 1136
L12_1 = 494
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "VERmoveWord"
L5_1 = {}
L6_1 = 15001061
L7_1 = "24|10|0xffffff,25|28|0xffffff,51|1|0xf7f7f7,52|26|0xfefefe,73|17|0xfbfcfc,82|20|0xf8f9f9,122|17|0xf8f8f8,145|21|0xf9f9f9"
L8_1 = 90
L9_1 = 548
L10_1 = 168
L11_1 = 1152
L12_1 = 780
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L4_1 = "VER错误"
L5_1 = {}
L6_1 = 16711169
L7_1 = "0|1|0xffff01,0|2|0xfefe01,-2|1|0xf5f502,-3|-2|0xfafa01,3|-2|0xfcfc01,5|-2|0xf7f702,6|-2|0xf6f702,0|4|0xf5f502,0|2|0xfefe01"
L8_1 = 90
L9_1 = 522
L10_1 = 146
L11_1 = 1466
L12_1 = 662
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L3_1[L4_1] = L5_1
L1_1[L2_1] = L3_1
color = L1_1
L1_1 = "singleTap"

function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  if A1_2 then
    L2_2 = math
    L2_2 = L2_2.randomseed
    L3_2 = os
    L3_2 = L3_2.time
    L3_2, L4_2, L5_2, L6_2, L7_2 = L3_2()
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
    L2_2 = A0_2[1]
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = -A1_2
    L5_2 = A1_2
    L3_2 = L3_2(L4_2, L5_2)
    L2_2 = L2_2 + L3_2
    L3_2 = A0_2[2]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = tap
    L5_2 = L2_2
    L6_2 = L3_2
    L4_2(L5_2, L6_2)
    L4_2 = {}
    L5_2 = L2_2
    L6_2 = L3_2
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    return L4_2
  else
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2(L3_2, L4_2)
    L2_2 = {}
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    return L2_2
  end
end

_ENV[L1_1] = L2_1
L1_1 = "doubleTap"

function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = math
  L2_2 = L2_2.randomseed
  L3_2 = os
  L3_2 = L3_2.time
  L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2()
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  if A1_2 then
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -A1_2
    L7_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 15
    L5_2 = 25
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2(L4_2, L5_2)
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -A1_2
    L7_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
  else
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 15
    L5_2 = 25
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2(L4_2, L5_2)
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -5
    L6_2 = 5
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -5
    L7_2 = 5
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
  end
end

_ENV[L1_1] = L2_1
L1_1 = "scrnLog"

function L2_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = printLog
  L2_2 = A0_2
  L1_2(L2_2)
end

_ENV[L1_1] = L2_1
L1_1 = "closeScrnLog"

function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  L1_2 = fwCloseView
  L2_2 = "logWnd"
  L3_2 = A0_2
  L1_2(L2_2, L3_2)
end

_ENV[L1_1] = L2_1
L1_1 = "slide"

function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = math
  L1_2 = L1_2.randomseed
  L2_2 = os
  L2_2 = L2_2.time
  L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L2_2()
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L1_2 = A0_2[1]
  L2_2 = A0_2[2]
  L3_2 = L1_2[1]
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = 20
  L6_2 = 30
  L4_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2 + L4_2
  L4_2 = L1_2[2]
  L5_2 = math
  L5_2 = L5_2.random
  L6_2 = 20
  L7_2 = 30
  L5_2 = L5_2(L6_2, L7_2)
  L4_2 = L4_2 + L5_2
  L5_2 = L2_2[1]
  L6_2 = math
  L6_2 = L6_2.random
  L7_2 = 20
  L8_2 = 30
  L6_2 = L6_2(L7_2, L8_2)
  L5_2 = L5_2 + L6_2
  L6_2 = L2_2[2]
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = 20
  L9_2 = 30
  L7_2 = L7_2(L8_2, L9_2)
  L6_2 = L6_2 + L7_2
  L7_2 = _moveto
  L8_2 = L3_2
  L9_2 = L4_2
  L10_2 = L5_2
  L11_2 = L6_2
  L7_2(L8_2, L9_2, L10_2, L11_2)
end

_ENV[L1_1] = L2_1
L1_1 = "findFeat"

function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = -1
  L2_2 = -1
  L3_2 = #A0_2
  if L3_2 == 7 then
    L3_2 = findMultiColorInRegionFuzzy
    L4_2 = A0_2[1]
    L5_2 = A0_2[2]
    L6_2 = A0_2[3]
    L7_2 = A0_2[4]
    L8_2 = A0_2[5]
    L9_2 = A0_2[6]
    L10_2 = A0_2[7]
    L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    L2_2 = L4_2
    L1_2 = L3_2
  else
    L3_2 = #A0_2
    if L3_2 == 8 then
      L3_2 = tsFindText
      L4_2 = A0_2[1]
      L5_2 = A0_2[2]
      L6_2 = A0_2[3]
      L7_2 = A0_2[4]
      L8_2 = A0_2[5]
      L9_2 = A0_2[6]
      L10_2 = A0_2[7]
      L11_2 = A0_2[8]
      L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      L2_2 = L4_2
      L1_2 = L3_2
    else
      L3_2 = #A0_2
      if L3_2 == 5 then
        L3_2 = findImage
        L4_2 = A0_2[1]
        L5_2 = A0_2[2]
        L6_2 = A0_2[3]
        L7_2 = A0_2[4]
        L8_2 = A0_2[5]
        L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
        L2_2 = L4_2
        L1_2 = L3_2
      end
    end
  end
  if 0 < L1_2 then
    L3_2 = L1_2
    L4_2 = L2_2
    return L3_2, L4_2
  else
    L3_2 = -1
    L4_2 = -1
    return L3_2, L4_2
  end
end

_ENV[L1_1] = L2_1
L1_1 = "findAndTap"

function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = findFeat
  L4_2 = A0_2
  L3_2, L4_2 = L3_2(L4_2)
  if 0 < L3_2 then
    L5_2 = {}
    L6_2 = L3_2
    L7_2 = L4_2
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    if A2_2 then
      if A1_2 == "singleTap" then
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = A2_2
        L6_2(L7_2, L8_2)
        L6_2 = true
        return L6_2
      elseif A1_2 == "doubleTap" then
        L6_2 = doubleTap
        L7_2 = L5_2
        L8_2 = A2_2
        L6_2(L7_2, L8_2)
        L6_2 = true
        return L6_2
      else
        L6_2 = nLog
        L7_2 = "点击类型传入错误"
        L6_2(L7_2)
        L6_2 = false
        return L6_2
      end
    elseif A1_2 == "singleTap" then
      L6_2 = singleTap
      L7_2 = L5_2
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif A1_2 == "doubleTap" then
      L6_2 = doubleTap
      L7_2 = L5_2
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    else
      L6_2 = nLog
      L7_2 = "点击类型传入错误"
      L6_2(L7_2)
      L6_2 = false
      return L6_2
    end
  else
    L5_2 = false
    return L5_2
  end
end

_ENV[L1_1] = L2_1
L1_1 = "supplement"

function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L1_2 = _ENV["UI_角色回复"]
  L2_2 = _ENV["UI_宠物回复"]
  L3_2 = _ENV["UI_角色回蓝"]
  L4_2 = _ENV["UI_宠物回蓝"]
  L5_2 = 0
  if A0_2 == 1 then
    L6_2 = 1
    L7_2 = 15
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1867
        L12_2 = 40
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.addCharBlood
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 201
        L10_2(L11_2)
      end
    end
    L6_2 = 1
    L7_2 = 15
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1867
        L12_2 = 40
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.juesejialan
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 201
        L10_2(L11_2)
      end
    end
  end
  if A0_2 == 2 then
    L6_2 = 1
    L7_2 = 15
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L12_2 = 91
      L10_2 = L10_2(L11_2, L12_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1867
        L12_2 = 40
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.addCharBlood
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L12_2 = 91
      L10_2 = L10_2(L11_2, L12_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 201
        L10_2(L11_2)
      end
    end
    L6_2 = 1
    L7_2 = 15
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1867
        L12_2 = 40
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.juesejialan
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 201
        L10_2(L11_2)
      end
    end
    L6_2 = 1
    L7_2 = 15
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L2_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1622
        L12_2 = 30
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.addPetBlood
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L2_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 200
        L10_2(L11_2)
      end
    end
  end
  if not A0_2 then
    L6_2 = 1
    L7_2 = 50
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L10_2 = L10_2(L11_2)
      if L10_2 ~= true then
        L10_2 = multiColor
        L11_2 = _ENV["颜色"]
        L11_2 = L11_2["加血伤势"]
        L11_2 = L11_2["角色"]
        L11_2 = L11_2[L1_2]
        L10_2 = L10_2(L11_2)
        if L10_2 ~= true then
          goto lbl_359
        end
      end
      L10_2 = {}
      L11_2 = 1867
      L12_2 = 40
      L10_2[1] = L11_2
      L10_2[2] = L12_2
      L11_2 = singleTap
      L12_2 = L10_2
      L13_2 = 20
      L11_2(L12_2, L13_2)
      L11_2 = mSleep
      L12_2 = math
      L12_2 = L12_2.random
      L13_2 = 450
      L14_2 = 550
      L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
      L11_2(L12_2, L13_2, L14_2, L15_2)
      L11_2 = findAndTap
      L12_2 = color
      L12_2 = L12_2.btn
      L12_2 = L12_2.addCharBlood
      L13_2 = "singleTap"
      L14_2 = 20
      L11_2(L12_2, L13_2, L14_2)
      L11_2 = mSleep
      L12_2 = math
      L12_2 = L12_2.random
      L13_2 = 450
      L14_2 = 550
      L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
      L11_2(L12_2, L13_2, L14_2, L15_2)
      L5_2 = L5_2 + 1
      ::lbl_359::
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L1_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      elseif 3 <= L5_2 then
        L10_2 = isItemExist
        L11_2 = "包子"
        L10_2 = L10_2(L11_2)
        if L10_2 then
          L5_2 = 0
        else
          L10_2 = _ENV["前往买包子"]
          L10_2()
        end
      else
        L10_2 = mSleep
        L11_2 = 200
        L10_2(L11_2)
      end
    end
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = 1
    L9_2 = 11
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L6_2 = 1
    L7_2 = 50
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1867
        L12_2 = 40
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.juesejialan
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["角色"]
      L11_2 = L11_2[L3_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      elseif 3 <= L5_2 then
        L10_2 = isItemExist
        L11_2 = "佛手"
        L10_2 = L10_2(L11_2)
        if L10_2 then
          L5_2 = 0
        else
          L10_2 = _ENV["前往买佛手"]
          L10_2()
        end
      else
        L10_2 = mSleep
        L11_2 = 200
        L10_2(L11_2)
      end
    end
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = 1
    L9_2 = 11
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L6_2 = 1
    L7_2 = 50
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L2_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1622
        L12_2 = 30
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.addPetBlood
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加血"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L2_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      elseif 3 <= L5_2 then
        L10_2 = isItemExist
        L11_2 = "包子"
        L10_2 = L10_2(L11_2)
        if L10_2 then
          L5_2 = 0
        else
          L10_2 = _ENV["前往买包子"]
          L10_2()
        end
      else
        L10_2 = mSleep
        L11_2 = 200
        L10_2(L11_2)
      end
    end
    L6_2 = mSleep
    L7_2 = math
    L7_2 = L7_2.random
    L8_2 = 1
    L9_2 = 11
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L6_2 = 1
    L7_2 = 50
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L4_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == true then
        L10_2 = {}
        L11_2 = 1622
        L12_2 = 30
        L10_2[1] = L11_2
        L10_2[2] = L12_2
        L11_2 = singleTap
        L12_2 = L10_2
        L13_2 = 20
        L11_2(L12_2, L13_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L11_2 = findAndTap
        L12_2 = color
        L12_2 = L12_2.btn
        L12_2 = L12_2.chongwujialan
        L13_2 = "singleTap"
        L14_2 = 20
        L11_2(L12_2, L13_2, L14_2)
        L11_2 = mSleep
        L12_2 = math
        L12_2 = L12_2.random
        L13_2 = 450
        L14_2 = 550
        L12_2, L13_2, L14_2, L15_2 = L12_2(L13_2, L14_2)
        L11_2(L12_2, L13_2, L14_2, L15_2)
        L5_2 = L5_2 + 1
      end
      L10_2 = multiColor
      L11_2 = _ENV["颜色"]
      L11_2 = L11_2["加蓝"]
      L11_2 = L11_2["宠物"]
      L11_2 = L11_2[L4_2]
      L10_2 = L10_2(L11_2)
      if L10_2 == false then
        L5_2 = 0
        break
      else
        if 3 <= L5_2 then
          break
        end
        L10_2 = mSleep
        L11_2 = 200
        L10_2(L11_2)
      end
    end
  end
end

_ENV[L1_1] = L2_1
