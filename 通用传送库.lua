local L0_1, L1_1, L2_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  if A1_2 then
    L2_2 = math
    L2_2 = L2_2.randomseed
    L3_2 = os
    L3_2 = L3_2.time
    L3_2, L4_2, L5_2, L6_2, L7_2 = L3_2()
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
    L2_2 = A0_2[1]
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = -A1_2
    L5_2 = A1_2
    L3_2 = L3_2(L4_2, L5_2)
    L2_2 = L2_2 + L3_2
    L3_2 = A0_2[2]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = tap
    L5_2 = L2_2
    L6_2 = L3_2
    L4_2(L5_2, L6_2)
    L4_2 = {}
    L5_2 = L2_2
    L6_2 = L3_2
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    return L4_2
  else
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2(L3_2, L4_2)
    L2_2 = {}
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    return L2_2
  end
end

singleTap = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = math
  L2_2 = L2_2.randomseed
  L3_2 = os
  L3_2 = L3_2.time
  L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2()
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  if A1_2 then
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -A1_2
    L7_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 15
    L5_2 = 20
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2(L4_2, L5_2)
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -A1_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -A1_2
    L7_2 = A1_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
  else
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = A0_2[2]
    L2_2(L3_2, L4_2)
    L2_2 = mSleep
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 15
    L5_2 = 20
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2(L4_2, L5_2)
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    L2_2 = tap
    L3_2 = A0_2[1]
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = -5
    L6_2 = 5
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L3_2 + L4_2
    L4_2 = A0_2[2]
    L5_2 = math
    L5_2 = L5_2.random
    L6_2 = -5
    L7_2 = 5
    L5_2 = L5_2(L6_2, L7_2)
    L4_2 = L4_2 + L5_2
    L2_2(L3_2, L4_2)
  end
end

doubleTap = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  L1_2 = printLog
  L2_2 = A0_2
  L3_2 = Type
  L1_2(L2_2, L3_2)
end

scrnLog = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  if A0_2 ~= nil then
    L1_2 = fwCloseView
    L2_2 = "wid"
    L3_2 = A0_2
    L1_2(L2_2, L3_2)
  else
    L1_2 = toast
    L2_2 = "1"
    L3_2 = 1
    L1_2(L2_2, L3_2)
  end
end

closeScrnLog = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  L6_2 = A4_2 or nil
  if not A4_2 then
    L6_2 = 10
  end
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = -L6_2
  L9_2 = L6_2
  L7_2 = L7_2(L8_2, L9_2)
  L7_2 = A0_2 + L7_2
  L8_2 = math
  L8_2 = L8_2.random
  L9_2 = -L6_2
  L10_2 = L6_2
  L8_2 = L8_2(L9_2, L10_2)
  L8_2 = A1_2 + L8_2
  L9_2 = math
  L9_2 = L9_2.random
  L10_2 = -L6_2
  L11_2 = L6_2
  L9_2 = L9_2(L10_2, L11_2)
  L9_2 = A2_2 + L9_2
  L10_2 = math
  L10_2 = L10_2.random
  L11_2 = -L6_2
  L12_2 = L6_2
  L10_2 = L10_2(L11_2, L12_2)
  L10_2 = A3_2 + L10_2
  L11_2 = A5_2 or L11_2
  if not A5_2 then
    L11_2 = math
    L11_2 = L11_2.random
    L12_2 = 15
    L13_2 = 25
    L11_2 = L11_2(L12_2, L13_2)
  end
  L12_2 = math
  L12_2 = L12_2.floor
  L13_2 = L9_2 - L7_2
  L13_2 = L13_2 ^ 2
  L14_2 = L10_2 - L8_2
  L14_2 = L14_2 ^ 2
  L13_2 = L13_2 + L14_2
  L13_2 = L13_2 ^ 0.5
  L13_2 = L13_2 / L11_2
  L12_2 = L12_2(L13_2)
  L13_2 = L9_2 - L7_2
  L13_2 = L13_2 / L12_2
  L14_2 = L10_2 - L8_2
  L14_2 = L14_2 / L12_2
  L15_2 = touchDown
  L16_2 = 1
  L17_2 = L7_2
  L18_2 = L8_2
  L15_2(L16_2, L17_2, L18_2)
  L15_2 = 1
  L16_2 = L12_2
  L17_2 = 1
  for L18_2 = L15_2, L16_2, L17_2 do
    L19_2 = touchMove
    L20_2 = 1
    L21_2 = L13_2 * L18_2
    L21_2 = L7_2 + L21_2
    L22_2 = L14_2 * L18_2
    L22_2 = L8_2 + L22_2
    L19_2(L20_2, L21_2, L22_2)
    L19_2 = _Sleep
    L20_2 = 15
    L21_2 = 30
    L19_2(L20_2, L21_2)
  end
  L15_2 = touchUp
  L16_2 = 1
  L17_2 = L9_2
  L18_2 = L10_2
  L15_2(L16_2, L17_2, L18_2)
  L15_2 = _Sleep
  L16_2 = 300
  L17_2 = 500
  L15_2(L16_2, L17_2)
end

_moveto = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = math
  L1_2 = L1_2.randomseed
  L2_2 = os
  L2_2 = L2_2.time
  L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L2_2()
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L1_2 = A0_2[1]
  L2_2 = A0_2[2]
  L3_2 = L1_2[1]
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = 20
  L6_2 = 30
  L4_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2 + L4_2
  L4_2 = L1_2[2]
  L5_2 = math
  L5_2 = L5_2.random
  L6_2 = 20
  L7_2 = 30
  L5_2 = L5_2(L6_2, L7_2)
  L4_2 = L4_2 + L5_2
  L5_2 = L2_2[1]
  L6_2 = math
  L6_2 = L6_2.random
  L7_2 = 20
  L8_2 = 30
  L6_2 = L6_2(L7_2, L8_2)
  L5_2 = L5_2 + L6_2
  L6_2 = L2_2[2]
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = 20
  L9_2 = 30
  L7_2 = L7_2(L8_2, L9_2)
  L6_2 = L6_2 + L7_2
  L7_2 = _moveto
  L8_2 = L3_2
  L9_2 = L4_2
  L10_2 = L5_2
  L11_2 = L6_2
  L7_2(L8_2, L9_2, L10_2, L11_2)
end

slide = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = -1
  L2_2 = -1
  L3_2 = #A0_2
  if L3_2 == 7 then
    L3_2 = findMultiColorInRegionFuzzy
    L4_2 = A0_2[1]
    L5_2 = A0_2[2]
    L6_2 = A0_2[3]
    L7_2 = A0_2[4]
    L8_2 = A0_2[5]
    L9_2 = A0_2[6]
    L10_2 = A0_2[7]
    L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    L2_2 = L4_2
    L1_2 = L3_2
  else
    L3_2 = #A0_2
    if L3_2 == 8 then
      L3_2 = tsFindText
      L4_2 = A0_2[1]
      L5_2 = A0_2[2]
      L6_2 = A0_2[3]
      L7_2 = A0_2[4]
      L8_2 = A0_2[5]
      L9_2 = A0_2[6]
      L10_2 = A0_2[7]
      L11_2 = A0_2[8]
      L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      L2_2 = L4_2
      L1_2 = L3_2
    else
      L3_2 = #A0_2
      if L3_2 == 5 then
        L3_2 = findImage
        L4_2 = A0_2[1]
        L5_2 = A0_2[2]
        L6_2 = A0_2[3]
        L7_2 = A0_2[4]
        L8_2 = A0_2[5]
        L3_2, L4_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
        L2_2 = L4_2
        L1_2 = L3_2
      end
    end
  end
  if 0 < L1_2 then
    L3_2 = L1_2
    L4_2 = L2_2
    return L3_2, L4_2
  else
    L3_2 = -1
    L4_2 = -1
    return L3_2, L4_2
  end
end

findFeat = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = findFeat
  L4_2 = A0_2
  L3_2, L4_2 = L3_2(L4_2)
  if 0 < L3_2 then
    L5_2 = {}
    L6_2 = L3_2
    L7_2 = L4_2
    L5_2[1] = L6_2
    L5_2[2] = L7_2
    if A2_2 then
      if A1_2 == "singleTap" then
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = A2_2
        L6_2(L7_2, L8_2)
        L6_2 = true
        return L6_2
      elseif A1_2 == "doubleTap" then
        L6_2 = doubleTap
        L7_2 = L5_2
        L8_2 = A2_2
        L6_2(L7_2, L8_2)
        L6_2 = true
        return L6_2
      else
        L6_2 = nLog
        L7_2 = "点击类型传入错误"
        L6_2(L7_2)
        L6_2 = false
        return L6_2
      end
    elseif A1_2 == "singleTap" then
      L6_2 = singleTap
      L7_2 = L5_2
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif A1_2 == "doubleTap" then
      L6_2 = doubleTap
      L7_2 = L5_2
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    else
      L6_2 = nLog
      L7_2 = "点击类型传入错误"
      L6_2(L7_2)
      L6_2 = false
      return L6_2
    end
  else
    L5_2 = false
    return L5_2
  end
end

findAndTap = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["_功能"]
  L0_2 = L0_2["背包"]
  L1_2 = "open"
  L0_2(L1_2)
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["背包"]
  L1_2 = L1_2["背包进入法宝"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
  end
  L0_2 = 1
  L1_2 = 50
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["提示框"]
    L4_2, L5_2, L6_2 = L4_2(L5_2)
    if L4_2 then
      if not (530 <= L5_2 and 124 <= L6_2 and L5_2 <= 550 and L6_2 <= 144) then
        goto lbl_35
      end
      do break end
      ::lbl_35::
      L7_2 = touchDown
      L8_2 = 0
      L9_2 = L5_2
      L10_2 = L6_2
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 350
      L7_2(L8_2)
      L7_2 = touchMove
      L8_2 = 0
      L9_2 = 540
      L10_2 = 134
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 250
      L7_2(L8_2)
      L7_2 = touchUp
      L8_2 = 0
      L9_2 = 540
      L10_2 = 134
      L7_2(L8_2, L9_2, L10_2)
      break
    end
    if 5 < L3_2 then
      L7_2 = _cmp
      L8_2 = Color
      L8_2 = L8_2["背包"]
      L8_2 = L8_2["法宝点击道具"]
      L7_2 = L7_2(L8_2)
      if L7_2 then
        L7_2 = _Sleep
        L8_2 = 500
        L9_2 = 1000
        L7_2(L8_2, L9_2)
      end
    end
    L7_2 = mSleep
    L8_2 = 60
    L7_2(L8_2)
  end
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["背包"]
  L1_2 = L1_2["法宝返回背包"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 1000
    L0_2(L1_2, L2_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["检测提示框"] = L0_1
