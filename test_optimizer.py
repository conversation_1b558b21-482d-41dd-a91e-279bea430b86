#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lua优化器测试脚本
"""

import os
import sys
from lua_optimizer import LuaOptimizer


def test_basic_optimization():
    """测试基本优化功能"""
    print("=== 测试基本优化功能 ===")
    
    # 测试代码
    test_code = """local L0_1, L1_1, L2_1, L3_1
L0_1 = require
L1_1 = "TSLib"
L0_1(L1_1)
L0_1 = require
L1_1 = "红尘试炼"
L0_1(L1_1)
L0_1 = getScreenSize
L0_1, L1_1 = L0_1()
hhhh = L1_1
wwww = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = mSleep
  L1_2 = 1
  L0_2(L1_2)
end

MyGetRunningAccess = L0_1"""
    
    optimizer = LuaOptimizer()
    result = optimizer.optimize_code(test_code)
    
    print("原始代码:")
    print(test_code)
    print("\n优化后代码:")
    print(result)
    print("\n" + "="*50 + "\n")


def test_goto_optimization():
    """测试goto语句优化"""
    print("=== 测试goto语句优化 ===")
    
    test_code = """function test()
  local L0_2, L1_2
  L0_2 = condition
  if L0_2 == false then
    goto lbl_10
  end
  L0_2 = doSomething
  L0_2()
  ::lbl_10::
  L0_2 = doOtherThing
  L0_2()
end"""
    
    optimizer = LuaOptimizer()
    result = optimizer.optimize_code(test_code)
    
    print("原始代码:")
    print(test_code)
    print("\n优化后代码:")
    print(result)
    print("\n" + "="*50 + "\n")


def test_complex_conditions():
    """测试复杂条件优化"""
    print("=== 测试复杂条件优化 ===")
    
    test_code = """function complex_test()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["UI_跑商路线"]
  if L0_2 ~= "地府北俱" then
    L0_2 = _ENV["UI_跑商路线"]
    if L0_2 ~= "比价换线" then
      L0_2 = _ENV["UI_跑商路线"]
      if L0_2 ~= "长安长寿" then
        L0_2 = _ENV["UI_跑商模式"]
        if L0_2 ~= "抢货模式" then
          goto lbl_19
        end
      end
    end
  end
  _ENV["北俱地府"] = true
  goto lbl_20
  ::lbl_19::
  _ENV["北俱地府"] = false
  ::lbl_20::
  return
end"""
    
    optimizer = LuaOptimizer()
    result = optimizer.optimize_code(test_code)
    
    print("原始代码:")
    print(test_code)
    print("\n优化后代码:")
    print(result)
    print("\n" + "="*50 + "\n")


def test_file_optimization():
    """测试文件优化"""
    print("=== 测试文件优化 ===")
    
    input_file = "main.lua"
    output_file = "main_test_optimized.lua"
    
    if not os.path.exists(input_file):
        print(f"测试文件 {input_file} 不存在，跳过文件测试")
        return
    
    optimizer = LuaOptimizer()
    
    try:
        result = optimizer.optimize_file(input_file, output_file)
        print(f"文件优化完成，输出: {output_file}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_file)
        optimized_size = os.path.getsize(output_file)
        
        print(f"原始文件大小: {original_size} 字节")
        print(f"优化后大小: {optimized_size} 字节")
        print(f"压缩率: {(1 - optimized_size/original_size)*100:.1f}%")
        
        # 显示前几行对比
        with open(input_file, 'r', encoding='utf-8') as f:
            original_lines = f.readlines()[:20]
        
        with open(output_file, 'r', encoding='utf-8') as f:
            optimized_lines = f.readlines()[:20]
        
        print("\n原始文件前20行:")
        print(''.join(original_lines))
        
        print("\n优化后文件前20行:")
        print(''.join(optimized_lines))
        
    except Exception as e:
        print(f"文件优化失败: {e}")
    
    print("\n" + "="*50 + "\n")


def test_variable_elimination():
    """测试变量消除"""
    print("=== 测试变量消除 ===")
    
    test_code = """function test_vars()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = UINew
  L1_2 = 5
  L2_2 = "test"
  L3_2 = "button"
  L0_2(L1_2, L2_2, L3_2)
  
  L0_2 = UILabel
  L1_2 = "label"
  L2_2 = 15
  L0_2(L1_2, L2_2)
end"""
    
    optimizer = LuaOptimizer()
    result = optimizer.optimize_code(test_code)
    
    print("原始代码:")
    print(test_code)
    print("\n优化后代码:")
    print(result)
    print("\n" + "="*50 + "\n")


def run_all_tests():
    """运行所有测试"""
    print("开始运行Lua优化器测试...\n")
    
    test_basic_optimization()
    test_goto_optimization()
    test_complex_conditions()
    test_variable_elimination()
    test_file_optimization()
    
    print("所有测试完成！")


if __name__ == "__main__":
    run_all_tests()
