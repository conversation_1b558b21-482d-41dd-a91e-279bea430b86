local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1, L16_1, <PERSON>17_1, L18_1, <PERSON>19_1, L20_1, <PERSON>21_1, L22_1, <PERSON>23_1, L24_1, L25_1, L26_1, L27_1, L28_1, L29_1, L30_1, L31_1, L32_1, L33_1, L34_1, L35_1, L36_1, L37_1, L38_1, L39_1, L40_1, L41_1, L42_1, L43_1, L44_1, L45_1, L46_1, L47_1, L48_1, L49_1
L0_1 = {}
L1_1 = {}
L2_1 = 597
L3_1 = 8.1
L4_1 = 940
L5_1 = 8.12
L6_1 = 700
L7_1 = 360
L8_1 = 1547
L9_1 = 923
L10_1 = 1586
L11_1 = 113
L12_1 = 68
L13_1 = 67
L14_1 = 1164
L15_1 = 499
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L1_1[8] = L9_1
L1_1[9] = L10_1
L1_1[10] = L11_1
L1_1[11] = L12_1
L1_1[12] = L13_1
L1_1[13] = L14_1
L1_1[14] = L15_1
L0_1["地三"] = L1_1
L1_1 = {}
L2_1 = 224
L3_1 = 2.3
L4_1 = 648
L5_1 = 2.354
L6_1 = 1179
L7_1 = 433
L8_1 = 1680
L9_1 = 643
L10_1 = 1720
L11_1 = 321
L12_1 = 542
L13_1 = 72
L14_1 = 1199
L15_1 = 586
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L1_1[8] = L9_1
L1_1[9] = L10_1
L1_1[10] = L11_1
L1_1[11] = L12_1
L1_1[12] = L13_1
L1_1[13] = L14_1
L1_1[14] = L15_1
L0_1["丝路右"] = L1_1
L1_1 = {}
L2_1 = 224
L3_1 = 2.3
L4_1 = 648
L5_1 = 2.354
L6_1 = 1179
L7_1 = 433
L8_1 = 1680
L9_1 = 643
L10_1 = 1720
L11_1 = 321
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L1_1[8] = L9_1
L1_1[9] = L10_1
L1_1[10] = L11_1
L0_1["丝路中"] = L1_1
L1_1 = {}
L2_1 = 224
L3_1 = 2.3
L4_1 = 648
L5_1 = 2.354
L6_1 = 1179
L7_1 = 433
L8_1 = 1680
L9_1 = 643
L10_1 = 1720
L11_1 = 321
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L1_1[8] = L9_1
L1_1[9] = L10_1
L1_1[10] = L11_1
L0_1["丝路左"] = L1_1
mapList = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = 7345192
L3_1 = "-6|15|0x681428,-46|26|0x681028,-113|28|0x681028,-149|23|0x681028,-178|2|0x701630,-166|-13|0x701830,-100|-23|0x701428,-79|-24|0x701428,-25|-19|0x701428"
L4_1 = 90
L5_1 = 325
L6_1 = 312
L7_1 = 583
L8_1 = 408
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["地三"] = L1_1
L1_1 = {}
L2_1 = 7347248
L3_1 = "4|27|0x701428,143|42|0x681028,162|8|0x701428,116|-6|0x701428,21|18|0x701428,82|45|0x681028,150|26|0x701428,81|20|0x701428,3|19|0x701430"
L4_1 = 90
L5_1 = 1448
L6_1 = 661
L7_1 = 1707
L8_1 = 759
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["丝路左"] = L1_1
L1_1 = {}
L2_1 = 7347248
L3_1 = "4|27|0x701428,143|42|0x681028,162|8|0x701428,116|-6|0x701428,21|18|0x701428,82|45|0x681028,150|26|0x701428,81|20|0x701428,3|19|0x701430"
L4_1 = 90
L5_1 = 1448
L6_1 = 661
L7_1 = 1707
L8_1 = 759
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["丝路右"] = L1_1
L1_1 = {}
L2_1 = 7347248
L3_1 = "4|27|0x701428,143|42|0x681028,162|8|0x701428,116|-6|0x701428,21|18|0x701428,82|45|0x681028,150|26|0x701428,81|20|0x701428,3|19|0x701430"
L4_1 = 90
L5_1 = 1448
L6_1 = 661
L7_1 = 1707
L8_1 = 759
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["丝路中"] = L1_1
L1_1 = {}
L2_1 = 3691624
L3_1 = "3|47|0x305068,-115|2|0x385468,65|-92|0x305068,70|-155|0x385868,64|-251|0x305468,62|-308|0x385868,63|-389|0x305468,68|-426|0x385868,-12|-444|0x385868"
L4_1 = 90
L5_1 = 1382
L6_1 = 105
L7_1 = 1868
L8_1 = 753
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["超级巫医"] = L1_1
L1_1 = {}
L2_1 = 3169392
L3_1 = "-56|0|0x305060,-124|-1|0x305060,-201|-1|0x305060,-257|-2|0x305060,-324|-91|0x395969,-242|-92|0x385868,-148|-92|0x385868,-40|-93|0x385868,-6|-93|0x385868"
L4_1 = 90
L5_1 = 270
L6_1 = 137
L7_1 = 1846
L8_1 = 1049
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["重叠状态"] = L1_1
L1_1 = {}
L2_1 = 16296715
L3_1 = "-14|12|0xf8b720,-34|-20|0xb1540f,-20|-26|0xefc65b,72|-22|0xeed7ab,80|-29|0xf8fbf4,86|-7|0xeccc8c,86|2|0xf4cf89,196|-4|0x6fdc30,205|-10|0x55cf29"
L4_1 = 90
L5_1 = 623
L6_1 = 961
L7_1 = 946
L8_1 = 1074
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["战斗检测"] = L1_1
L1_1 = {}
L2_1 = 10662328
L3_1 = "1|11|0xfbfcfc,-2|23|0xffffff,-2|29|0xfefefe,9|32|0xeceef0,27|30|0xc2cbd0,88|30|0x2d4c5c,92|27|0xe2e6e9,90|12|0xe8ebed,82|0|0x8297a1"
L4_1 = 90
L5_1 = 992
L6_1 = 400
L7_1 = 1761
L8_1 = 929
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["攻击按钮"] = L1_1
L1_1 = {}
L2_1 = 2662597
L3_1 = "-7|-16|0x40b8d8,-20|-9|0x5ac7e0,-45|1|0xe1ceb3,-33|13|0xd8926a,-8|11|0xd47e5a,0|18|0xa43c25,-30|0|0x1f7d9d,-29|-16|0x7f989c"
L4_1 = 90
L5_1 = 1788
L6_1 = 193
L7_1 = 1902
L8_1 = 446
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["战斗攻击"] = L1_1
L1_1 = {}
L2_1 = 16296715
L3_1 = "-14|12|0xf8b720,-34|-20|0xb1540f,-20|-26|0xefc65b,72|-22|0xeed7ab,80|-29|0xf8fbf4,86|-7|0xeccc8c,86|2|0xf4cf89,196|-4|0x6fdc30,205|-10|0x55cf29"
L4_1 = 90
L5_1 = 623
L6_1 = 961
L7_1 = 946
L8_1 = 1074
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["退出战斗"] = L1_1
L1_1 = {}
L2_1 = 9416639
L3_1 = "12|-16|0xc8dee3,-7|7|0x83a3b0,9|25|0x708f99,-306|197|0x98b2bb,-293|207|0xa0bec8,-287|223|0x84a6b0,-302|246|0x7f9b9f,-284|228|0x80a0a8,-292|215|0x90b2bc"
L4_1 = 90
L5_1 = -3
L6_1 = 0
L7_1 = 438
L8_1 = 330
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["战斗状态"] = L1_1
L1_1 = {}
L2_1 = 3692648
L3_1 = "33|50|0x305068,176|5|0x385468,256|-10|0x3b5f6b,335|54|0x305068,438|1|0x385868,361|-6|0x385868,330|63|0x305060,174|36|0x305468,66|-16|0x385868"
L4_1 = 90
L5_1 = 1374
L6_1 = 95
L7_1 = 1906
L8_1 = 218
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["人物加血"] = L1_1
L1_1 = {}
L2_1 = 3692904
L3_1 = "153|2|0x385868,174|70|0x305060,32|77|0x305060,30|30|0xf4f5f7,118|42|0xffffff,182|32|0x385468,107|71|0x305060,104|47|0x305468,128|1|0x385868"
L4_1 = 90
L5_1 = 1085
L6_1 = 96
L7_1 = 1355
L8_1 = 218
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["宠物加血"] = L1_1
L1_1 = {}
L2_1 = 16308072
L3_1 = "-13|16|0xf2bf50,-33|42|0xf8b72d,-21|19|0xea8d00,-33|4|0xf8eca0,-1|33|0xf8ae08,2|45|0xf8ad07,6|22|0xf8a818,-15|29|0xe49b28,-19|38|0xa84c08"
L4_1 = 90
L5_1 = 592
L6_1 = 916
L7_1 = 727
L8_1 = 1067
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["设置按钮"] = L1_1
L1_1 = {}
L2_1 = 16376175
L3_1 = "-17|7|0xf9ee73,-14|30|0xf9dc52,-10|47|0xec2617,9|38|0xf12314,29|29|0xdc110d,8|33|0xf94833,-11|7|0xf9f47a,-11|2|0xf9e76d,-32|20|0xeed963"
L4_1 = 90
L5_1 = 942
L6_1 = 931
L7_1 = 1071
L8_1 = 1074
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["动作按钮"] = L1_1
L1_1 = {}
L2_1 = 16203823
L3_1 = "23|7|0xd89134,20|28|0xa40d08,-8|25|0xefbe31,-92|6|0xd77116,-115|10|0xf8f64d,-115|34|0xe9982b,-113|12|0xf9f55b,-92|4|0xe58018,-105|33|0xbe6019"
L4_1 = 90
L5_1 = 1638
L6_1 = 921
L7_1 = 1765
L8_1 = 1078
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["道具按钮"] = L1_1
L1_1 = {}
L2_1 = 14934429
L3_1 = "41|-24|0x9a7080,-47|-25|0x8b6372,-39|39|0x7c78ae,-8|49|0xd2d288,46|41|0x5e6696,24|19|0xecefba,16|25|0x080604,-15|25|0x100705,0|34|0x2f1c16"
L4_1 = 90
L5_1 = 1449
L6_1 = 2
L7_1 = 1568
L8_1 = 117
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L1_1[5] = L6_1
L1_1[6] = L7_1
L1_1[7] = L8_1
L0_1["没有宝宝"] = L1_1
CJColorList = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = {}
L3_1 = {}
L4_1 = 5693293
L5_1 = "2|0|0x55dc6c,21|-1|0x52d367,24|-5|0x4cc560,33|-10|0x55dc6c,50|6|0x4bb55a,60|11|0x4bc25f,67|12|0x51d066,85|8|0x55d96b,86|-11|0x4abf5e"
L6_1 = 90
L7_1 = 974
L8_1 = 316
L9_1 = 1313
L10_1 = 368
L11_1 = 481
L12_1 = 557
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5627244
L6_1 = "14|3|0x46b559,10|9|0x55dd6c,-9|9|0x55dd6c,-14|7|0x4fcd64,-6|18|0x338441,8|28|0x3fa450,9|21|0x55dd6c,46|27|0x55dc6b,45|2|0x42aa54"
L7_1 = 90
L8_1 = 930
L9_1 = 232
L10_1 = 1180
L11_1 = 272
L12_1 = 1039
L13_1 = 163
L14_1 = 10
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L1_1["宝宝"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5693293
L5_1 = "26|-11|0x51d267,25|16|0x42a953,34|10|0x3b974a,29|12|0x192e17,38|0|0x3d9f4d,59|-5|0x51cc64,86|-10|0x52d166,84|14|0x55cd67,84|11|0x456f3e"
L6_1 = 90
L7_1 = 387
L8_1 = 609
L9_1 = 687
L10_1 = 648
L11_1 = 481
L12_1 = 557
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5693036
L6_1 = "2|3|0x48b95a,30|-8|0x52d367,27|4|0x50d066,27|14|0x4cc460,34|12|0x4dc761,61|11|0x56de6c,87|0|0x4dc460,86|-10|0x52d266,79|14|0x53c062"
L7_1 = 90
L8_1 = 387
L9_1 = 609
L10_1 = 687
L11_1 = 648
L12_1 = 481
L13_1 = 557
L14_1 = 1
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 5693036
L7_1 = "27|0|0x56de6d,25|14|0x50d066,24|-9|0x54d267,43|-15|0x51ca63,50|-1|0x3d9d4d,62|12|0x56df6d,66|3|0x53d86a,87|12|0x4cc460,87|-11|0x53d167"
L8_1 = 90
L9_1 = 537
L10_1 = 539
L11_1 = 873
L12_1 = 577
L13_1 = 640
L14_1 = 480
L15_1 = 2
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5693036
L8_1 = "2|3|0x48b95a,30|-8|0x52d367,27|4|0x50d066,27|14|0x4cc460,34|12|0x4dc761,61|11|0x56de6c,87|0|0x4dc460,86|-10|0x52d266,79|14|0x53c062"
L9_1 = 90
L10_1 = 537
L11_1 = 539
L12_1 = 873
L13_1 = 577
L14_1 = 640
L15_1 = 480
L16_1 = 2
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5693293
L9_1 = "26|-11|0x51d267,25|16|0x42a953,34|10|0x3b974a,29|12|0x192e17,38|0|0x3d9f4d,59|-5|0x51cc64,86|-10|0x52d166,84|14|0x55cd67,84|11|0x456f3e"
L10_1 = 90
L11_1 = 705
L12_1 = 461
L13_1 = 976
L14_1 = 509
L15_1 = 806
L16_1 = 405
L17_1 = 3
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5693036
L10_1 = "2|3|0x48b95a,30|-8|0x52d367,27|4|0x50d066,27|14|0x4cc460,34|12|0x4dc761,61|11|0x56de6c,87|0|0x4dc460,86|-10|0x52d266,79|14|0x53c062"
L11_1 = 90
L12_1 = 685
L13_1 = 463
L14_1 = 948
L15_1 = 510
L16_1 = 806
L17_1 = 405
L18_1 = 3
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 5627244
L11_1 = "-2|0|0x56df6d,24|1|0x56de6d,24|14|0x4fcd64,31|14|0x2e783b,34|-1|0x53d669,48|-10|0x55db6b,57|0|0x52d368,68|15|0x419a4b,85|-10|0x54d668"
L12_1 = 90
L13_1 = 880
L14_1 = 390
L15_1 = 1139
L16_1 = 437
L17_1 = 967
L18_1 = 347
L19_1 = 4
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5625450
L12_1 = "4|0|0x4abf5d,24|1|0x44ae55,25|12|0x50c560,34|11|0x41a752,36|-2|0x54da6b,51|-2|0x358a43,50|-12|0x4fcd64,71|-3|0x56df6d,87|11|0x51d166"
L13_1 = 90
L14_1 = 826
L15_1 = 385
L16_1 = 1130
L17_1 = 435
L18_1 = 967
L19_1 = 347
L20_1 = 4
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 5693293
L13_1 = "26|-11|0x51d267,25|16|0x42a953,34|10|0x3b974a,29|12|0x192e17,38|0|0x3d9f4d,59|-5|0x51cc64,86|-10|0x52d166,84|14|0x55cd67,84|11|0x456f3e"
L14_1 = 90
L15_1 = 974
L16_1 = 316
L17_1 = 1313
L18_1 = 368
L19_1 = 1121
L20_1 = 271
L21_1 = 5
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5693036
L14_1 = "2|3|0x48b95a,30|-8|0x52d367,27|4|0x50d066,27|14|0x4cc460,34|12|0x4dc761,61|11|0x56de6c,87|0|0x4dc460,86|-10|0x52d266,79|14|0x53c062"
L15_1 = 90
L16_1 = 974
L17_1 = 316
L18_1 = 1313
L19_1 = 368
L20_1 = 1121
L21_1 = 271
L22_1 = 5
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L13_1 = {}
L14_1 = 5693293
L15_1 = "25|-9|0x53d166,25|3|0x51d267,25|16|0x44b056,33|12|0x4fcc64,48|-1|0x4ecb63,50|-10|0x55d86a,43|-13|0x56df6d,62|-6|0x48bb5b,60|14|0x55dc6c"
L16_1 = 90
L17_1 = 308
L18_1 = 512
L19_1 = 557
L20_1 = 567
L21_1 = 401
L22_1 = 472
L23_1 = 6
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L13_1[5] = L18_1
L13_1[6] = L19_1
L13_1[7] = L20_1
L13_1[8] = L21_1
L13_1[9] = L22_1
L13_1[10] = L23_1
L14_1 = {}
L15_1 = 5693293
L16_1 = "0|1|0x56df6d,21|4|0x47b659,21|4|0x47b659,22|17|0x48ba5b,31|11|0x50cc64,28|9|0x0a1008,32|13|0x4cc35f,87|-9|0x4dbd5e,87|-11|0x447d45"
L17_1 = 90
L18_1 = 308
L19_1 = 512
L20_1 = 557
L21_1 = 567
L22_1 = 401
L23_1 = 472
L24_1 = 6
L14_1[1] = L15_1
L14_1[2] = L16_1
L14_1[3] = L17_1
L14_1[4] = L18_1
L14_1[5] = L19_1
L14_1[6] = L20_1
L14_1[7] = L21_1
L14_1[8] = L22_1
L14_1[9] = L23_1
L14_1[10] = L24_1
L15_1 = {}
L16_1 = 5693293
L17_1 = "0|2|0x56df6d,22|16|0x52d367,31|15|0x4dc761,33|10|0x48bb5c,25|12|0x52d568,20|4|0x4bb357,29|12|0x1a3218,30|15|0x4abd5c,27|14|0x388d45"
L18_1 = 90
L19_1 = 291
L20_1 = 520
L21_1 = 515
L22_1 = 561
L23_1 = 401
L24_1 = 472
L25_1 = 6
L15_1[1] = L16_1
L15_1[2] = L17_1
L15_1[3] = L18_1
L15_1[4] = L19_1
L15_1[5] = L20_1
L15_1[6] = L21_1
L15_1[7] = L22_1
L15_1[8] = L23_1
L15_1[9] = L24_1
L15_1[10] = L25_1
L16_1 = {}
L17_1 = 5693293
L18_1 = "0|1|0x56df6d,21|4|0x47b659,21|4|0x47b659,22|17|0x48ba5b,31|11|0x50cc64,28|9|0x0a1008,32|13|0x4cc35f,87|-9|0x4dbd5e,87|-11|0x447d45"
L19_1 = 90
L20_1 = 450
L21_1 = 443
L22_1 = 691
L23_1 = 490
L24_1 = 563
L25_1 = 401
L26_1 = 7
L16_1[1] = L17_1
L16_1[2] = L18_1
L16_1[3] = L19_1
L16_1[4] = L20_1
L16_1[5] = L21_1
L16_1[6] = L22_1
L16_1[7] = L23_1
L16_1[8] = L24_1
L16_1[9] = L25_1
L16_1[10] = L26_1
L17_1 = {}
L18_1 = 5693293
L19_1 = "0|2|0x56df6d,22|16|0x52d367,31|15|0x4dc761,33|10|0x48bb5c,25|12|0x52d568,20|4|0x4bb357,29|12|0x1a3218,30|15|0x4abd5c,27|14|0x388d45"
L20_1 = 90
L21_1 = 450
L22_1 = 443
L23_1 = 691
L24_1 = 490
L25_1 = 563
L26_1 = 401
L27_1 = 7
L17_1[1] = L18_1
L17_1[2] = L19_1
L17_1[3] = L20_1
L17_1[4] = L21_1
L17_1[5] = L22_1
L17_1[6] = L23_1
L17_1[7] = L24_1
L17_1[8] = L25_1
L17_1[9] = L26_1
L17_1[10] = L27_1
L18_1 = {}
L19_1 = 5693037
L20_1 = "1|-4|0x469a56,25|15|0x44b056,28|8|0x44b056,30|11|0x112312,34|8|0x54d96a,35|-2|0x56de6c,36|-11|0x56dd6c,28|-14|0x45b158,23|-9|0x54d76a"
L21_1 = 90
L22_1 = 632
L23_1 = 370
L24_1 = 860
L25_1 = 414
L26_1 = 724
L27_1 = 333
L28_1 = 8
L18_1[1] = L19_1
L18_1[2] = L20_1
L18_1[3] = L21_1
L18_1[4] = L22_1
L18_1[5] = L23_1
L18_1[6] = L24_1
L18_1[7] = L25_1
L18_1[8] = L26_1
L18_1[9] = L27_1
L18_1[10] = L28_1
L19_1 = {}
L20_1 = 5693293
L21_1 = "0|1|0x56df6d,21|4|0x47b659,21|4|0x47b659,22|17|0x48ba5b,31|11|0x50cc64,28|9|0x0a1008,32|13|0x4cc35f,87|-9|0x4dbd5e,87|-11|0x447d45"
L22_1 = 90
L23_1 = 632
L24_1 = 370
L25_1 = 860
L26_1 = 414
L27_1 = 724
L28_1 = 333
L29_1 = 8
L19_1[1] = L20_1
L19_1[2] = L21_1
L19_1[3] = L22_1
L19_1[4] = L23_1
L19_1[5] = L24_1
L19_1[6] = L25_1
L19_1[7] = L26_1
L19_1[8] = L27_1
L19_1[9] = L28_1
L19_1[10] = L29_1
L20_1 = {}
L21_1 = 5693293
L22_1 = "0|2|0x56df6d,22|16|0x52d367,31|15|0x4dc761,33|10|0x48bb5c,25|12|0x52d568,20|4|0x4bb357,29|12|0x1a3218,30|15|0x4abd5c,27|14|0x388d45"
L23_1 = 90
L24_1 = 632
L25_1 = 370
L26_1 = 860
L27_1 = 414
L28_1 = 724
L29_1 = 333
L30_1 = 8
L20_1[1] = L21_1
L20_1[2] = L22_1
L20_1[3] = L23_1
L20_1[4] = L24_1
L20_1[5] = L25_1
L20_1[6] = L26_1
L20_1[7] = L27_1
L20_1[8] = L28_1
L20_1[9] = L29_1
L20_1[10] = L30_1
L21_1 = {}
L22_1 = 5693037
L23_1 = "2|1|0x56df6d,30|-8|0x55db6b,27|-14|0x50ce65,26|15|0x4bc35f,34|10|0x55d96a,30|12|0x183419,42|-1|0x49bd5c,51|-2|0x55db6b,44|-13|0x56df6d"
L24_1 = 90
L25_1 = 775
L26_1 = 298
L27_1 = 1016
L28_1 = 349
L29_1 = 885
L30_1 = 255
L31_1 = 9
L21_1[1] = L22_1
L21_1[2] = L23_1
L21_1[3] = L24_1
L21_1[4] = L25_1
L21_1[5] = L26_1
L21_1[6] = L27_1
L21_1[7] = L28_1
L21_1[8] = L29_1
L21_1[9] = L30_1
L21_1[10] = L31_1
L22_1 = {}
L23_1 = 5693293
L24_1 = "0|1|0x56df6d,21|4|0x47b659,21|4|0x47b659,22|17|0x48ba5b,31|11|0x50cc64,28|9|0x0a1008,32|13|0x4cc35f,87|-9|0x4dbd5e,87|-11|0x447d45"
L25_1 = 90
L26_1 = 775
L27_1 = 298
L28_1 = 1016
L29_1 = 349
L30_1 = 885
L31_1 = 255
L32_1 = 9
L22_1[1] = L23_1
L22_1[2] = L24_1
L22_1[3] = L25_1
L22_1[4] = L26_1
L22_1[5] = L27_1
L22_1[6] = L28_1
L22_1[7] = L29_1
L22_1[8] = L30_1
L22_1[9] = L31_1
L22_1[10] = L32_1
L23_1 = {}
L24_1 = 5693293
L25_1 = "0|2|0x56df6d,22|16|0x52d367,31|15|0x4dc761,33|10|0x48bb5c,25|12|0x52d568,20|4|0x4bb357,29|12|0x1a3218,30|15|0x4abd5c,27|14|0x388d45"
L26_1 = 90
L27_1 = 775
L28_1 = 298
L29_1 = 1016
L30_1 = 349
L31_1 = 885
L32_1 = 255
L33_1 = 9
L23_1[1] = L24_1
L23_1[2] = L25_1
L23_1[3] = L26_1
L23_1[4] = L27_1
L23_1[5] = L28_1
L23_1[6] = L29_1
L23_1[7] = L30_1
L23_1[8] = L31_1
L23_1[9] = L32_1
L23_1[10] = L33_1
L24_1 = {}
L25_1 = 5360739
L26_1 = "2|5|0x3ea04e,26|-6|0x56df6d,26|6|0x41a852,25|18|0x44b257,33|16|0x4cc661,48|0|0x55dd6c,79|0|0x55dd6c,86|16|0x55dd6c,88|-8|0x56de6d"
L27_1 = 90
L28_1 = 930
L29_1 = 232
L30_1 = 1180
L31_1 = 272
L32_1 = 1039
L33_1 = 163
L34_1 = 10
L24_1[1] = L25_1
L24_1[2] = L26_1
L24_1[3] = L27_1
L24_1[4] = L28_1
L24_1[5] = L29_1
L24_1[6] = L30_1
L24_1[7] = L31_1
L24_1[8] = L32_1
L24_1[9] = L33_1
L24_1[10] = L34_1
L25_1 = {}
L26_1 = 5693293
L27_1 = "0|1|0x56df6d,21|4|0x47b659,21|4|0x47b659,22|17|0x48ba5b,31|11|0x50cc64,28|9|0x0a1008,32|13|0x4cc35f,87|-9|0x4dbd5e,87|-11|0x447d45"
L28_1 = 90
L29_1 = 930
L30_1 = 232
L31_1 = 1180
L32_1 = 272
L33_1 = 1039
L34_1 = 163
L35_1 = 10
L25_1[1] = L26_1
L25_1[2] = L27_1
L25_1[3] = L28_1
L25_1[4] = L29_1
L25_1[5] = L30_1
L25_1[6] = L31_1
L25_1[7] = L32_1
L25_1[8] = L33_1
L25_1[9] = L34_1
L25_1[10] = L35_1
L26_1 = {}
L27_1 = 5693293
L28_1 = "0|2|0x56df6d,22|16|0x52d367,31|15|0x4dc761,33|10|0x48bb5c,25|12|0x52d568,20|4|0x4bb357,29|12|0x1a3218,30|15|0x4abd5c,27|14|0x388d45"
L29_1 = 90
L30_1 = 930
L31_1 = 232
L32_1 = 1180
L33_1 = 272
L34_1 = 1039
L35_1 = 163
L36_1 = 10
L26_1[1] = L27_1
L26_1[2] = L28_1
L26_1[3] = L29_1
L26_1[4] = L30_1
L26_1[5] = L31_1
L26_1[6] = L32_1
L26_1[7] = L33_1
L26_1[8] = L34_1
L26_1[9] = L35_1
L26_1[10] = L36_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L2_1[12] = L14_1
L2_1[13] = L15_1
L2_1[14] = L16_1
L2_1[15] = L17_1
L2_1[16] = L18_1
L2_1[17] = L19_1
L2_1[18] = L20_1
L2_1[19] = L21_1
L2_1[20] = L22_1
L2_1[21] = L23_1
L2_1[22] = L24_1
L2_1[23] = L25_1
L2_1[24] = L26_1
L1_1["护佑"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5693293
L5_1 = "2|0|0x54da6b,48|5|0x4abf5d,48|-7|0x40a451,47|-15|0x50d066,64|-9|0x55db6b,67|11|0x4eb95d,79|12|0x399348,87|6|0x52c162,87|-3|0x45aa54"
L6_1 = 90
L7_1 = 387
L8_1 = 609
L9_1 = 687
L10_1 = 648
L11_1 = 481
L12_1 = 557
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5626475
L6_1 = "27|-7|0x55dd6c,25|12|0x4cc560,44|13|0x358943,54|14|0x408045,89|-12|0x4eca63,83|-2|0x47b85a,81|7|0x53d86a,74|-9|0x53d468,64|-7|0x4bba5c"
L7_1 = 90
L8_1 = 537
L9_1 = 539
L10_1 = 873
L11_1 = 577
L12_1 = 640
L13_1 = 480
L14_1 = 2
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 5693293
L7_1 = "2|0|0x55dc6c,21|-1|0x52d367,24|-5|0x4cc560,33|-10|0x55dc6c,50|6|0x4bb55a,60|11|0x4bc25f,67|12|0x51d066,85|8|0x55d96b,86|-11|0x4abf5e"
L8_1 = 90
L9_1 = 705
L10_1 = 461
L11_1 = 976
L12_1 = 509
L13_1 = 806
L14_1 = 405
L15_1 = 3
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5693293
L8_1 = "2|0|0x55dc6c,21|-1|0x52d367,24|-5|0x4cc560,33|-10|0x55dc6c,50|6|0x4bb55a,60|11|0x4bc25f,67|12|0x51d066,85|8|0x55d96b,86|-11|0x4abf5e"
L9_1 = 90
L10_1 = 880
L11_1 = 390
L12_1 = 1139
L13_1 = 437
L14_1 = 967
L15_1 = 347
L16_1 = 4
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5693293
L9_1 = "2|0|0x55dc6c,21|-1|0x52d367,24|-5|0x4cc560,33|-10|0x55dc6c,50|6|0x4bb55a,60|11|0x4bc25f,67|12|0x51d066,85|8|0x55d96b,86|-11|0x4abf5e"
L10_1 = 90
L11_1 = 974
L12_1 = 316
L13_1 = 1313
L14_1 = 368
L15_1 = 1121
L16_1 = 271
L17_1 = 5
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5423456
L10_1 = "4|-1|0x56de6c,34|13|0x48b257,43|13|0x4eca63,51|12|0x49ba5b,52|5|0x4dc862,52|0|0x50ce65,51|-16|0x51c962,40|4|0x0d2010,46|4|0x275b2e"
L11_1 = 90
L12_1 = 308
L13_1 = 512
L14_1 = 557
L15_1 = 567
L16_1 = 401
L17_1 = 472
L18_1 = 6
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 5560169
L11_1 = "0|-3|0x51ca62,87|7|0x55dd6c,86|-3|0x56de6c,88|-12|0x48b95b,87|7|0x55dd6c,79|14|0x51d367,2|1|0x56df6d,39|-9|0x875829,59|-6|0xd19f54"
L12_1 = 90
L13_1 = 308
L14_1 = 512
L15_1 = 557
L16_1 = 567
L17_1 = 401
L18_1 = 472
L19_1 = 6
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5494890
L12_1 = "0|-2|0x55dc6c,20|-4|0x4fcd64,28|-11|0x53d669,26|7|0x51cc64,22|11|0x3b984a,32|6|0x358842,83|-5|0x4d8c4b,84|6|0x54ce66,76|9|0x4ec863"
L13_1 = 90
L14_1 = 308
L15_1 = 512
L16_1 = 557
L17_1 = 567
L18_1 = 401
L19_1 = 472
L20_1 = 6
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 4766042
L13_1 = "-4|0|0x54d769,38|10|0x4fca63,46|11|0x42a14e,49|6|0x4cbf5e,46|0|0x53d86a,29|12|0x53d668,20|11|0x47b75a,43|-1|0x50d066,31|4|0x54da6a"
L14_1 = 90
L15_1 = 308
L16_1 = 512
L17_1 = 557
L18_1 = 567
L19_1 = 401
L20_1 = 472
L21_1 = 6
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5693293
L14_1 = "-3|2|0x51c962,2|1|0x50ce65,84|-11|0x4dc862,84|-2|0x55d268,85|7|0x51b15c,54|1|0x3d131b,52|-14|0x0c2943,21|-6|0x4fc962,-1|2|0x56df6d"
L15_1 = 90
L16_1 = 308
L17_1 = 512
L18_1 = 557
L19_1 = 567
L20_1 = 401
L21_1 = 472
L22_1 = 6
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L13_1 = {}
L14_1 = 3902787
L15_1 = "-9|-1|0x4abe5d,8|-8|0x2f6d34,9|-14|0x42ab54,14|-22|0x51d167,-31|-13|0x56df6d,-31|-15|0x56df6d,-10|-2|0x4bbd5b,1|-6|0x2e6e35,2|-5|0x070c06"
L16_1 = 90
L17_1 = 308
L18_1 = 512
L19_1 = 557
L20_1 = 567
L21_1 = 401
L22_1 = 472
L23_1 = 6
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L13_1[5] = L18_1
L13_1[6] = L19_1
L13_1[7] = L20_1
L13_1[8] = L21_1
L13_1[9] = L22_1
L13_1[10] = L23_1
L14_1 = {}
L15_1 = 4303187
L16_1 = "0|-11|0x4ebf5d,0|-21|0x4ab056,-84|-9|0x56df6d,-63|5|0x4fcd64,-48|-10|0x56df6d,-36|-17|0x55dd6c,-22|-10|0x4ec861,-12|-15|0x389046,-22|-2|0x55de6c"
L17_1 = 90
L18_1 = 308
L19_1 = 512
L20_1 = 557
L21_1 = 567
L22_1 = 401
L23_1 = 472
L24_1 = 6
L14_1[1] = L15_1
L14_1[2] = L16_1
L14_1[3] = L17_1
L14_1[4] = L18_1
L14_1[5] = L19_1
L14_1[6] = L20_1
L14_1[7] = L21_1
L14_1[8] = L22_1
L14_1[9] = L23_1
L14_1[10] = L24_1
L15_1 = {}
L16_1 = 5423456
L17_1 = "4|-1|0x56de6c,34|13|0x48b257,43|13|0x4eca63,51|12|0x49ba5b,52|5|0x4dc862,52|0|0x50ce65,51|-16|0x51c962,40|4|0x0d2010,46|4|0x275b2e"
L18_1 = 90
L19_1 = 450
L20_1 = 443
L21_1 = 691
L22_1 = 490
L23_1 = 563
L24_1 = 401
L25_1 = 7
L15_1[1] = L16_1
L15_1[2] = L17_1
L15_1[3] = L18_1
L15_1[4] = L19_1
L15_1[5] = L20_1
L15_1[6] = L21_1
L15_1[7] = L22_1
L15_1[8] = L23_1
L15_1[9] = L24_1
L15_1[10] = L25_1
L16_1 = {}
L17_1 = 5560169
L18_1 = "0|-3|0x51ca62,87|7|0x55dd6c,86|-3|0x56de6c,88|-12|0x48b95b,87|7|0x55dd6c,79|14|0x51d367,2|1|0x56df6d,39|-9|0x875829,59|-6|0xd19f54"
L19_1 = 90
L20_1 = 450
L21_1 = 443
L22_1 = 691
L23_1 = 490
L24_1 = 563
L25_1 = 401
L26_1 = 7
L16_1[1] = L17_1
L16_1[2] = L18_1
L16_1[3] = L19_1
L16_1[4] = L20_1
L16_1[5] = L21_1
L16_1[6] = L22_1
L16_1[7] = L23_1
L16_1[8] = L24_1
L16_1[9] = L25_1
L16_1[10] = L26_1
L17_1 = {}
L18_1 = 5494890
L19_1 = "0|-2|0x55dc6c,20|-4|0x4fcd64,28|-11|0x53d669,26|7|0x51cc64,22|11|0x3b984a,32|6|0x358842,83|-5|0x4d8c4b,84|6|0x54ce66,76|9|0x4ec863"
L20_1 = 90
L21_1 = 450
L22_1 = 443
L23_1 = 691
L24_1 = 490
L25_1 = 563
L26_1 = 401
L27_1 = 7
L17_1[1] = L18_1
L17_1[2] = L19_1
L17_1[3] = L20_1
L17_1[4] = L21_1
L17_1[5] = L22_1
L17_1[6] = L23_1
L17_1[7] = L24_1
L17_1[8] = L25_1
L17_1[9] = L26_1
L17_1[10] = L27_1
L18_1 = {}
L19_1 = 4766042
L20_1 = "-4|0|0x54d769,38|10|0x4fca63,46|11|0x42a14e,49|6|0x4cbf5e,46|0|0x53d86a,29|12|0x53d668,20|11|0x47b75a,43|-1|0x50d066,31|4|0x54da6a"
L21_1 = 90
L22_1 = 450
L23_1 = 443
L24_1 = 691
L25_1 = 490
L26_1 = 563
L27_1 = 401
L28_1 = 7
L18_1[1] = L19_1
L18_1[2] = L20_1
L18_1[3] = L21_1
L18_1[4] = L22_1
L18_1[5] = L23_1
L18_1[6] = L24_1
L18_1[7] = L25_1
L18_1[8] = L26_1
L18_1[9] = L27_1
L18_1[10] = L28_1
L19_1 = {}
L20_1 = 5693293
L21_1 = "-3|2|0x51c962,2|1|0x50ce65,84|-11|0x4dc862,84|-2|0x55d268,85|7|0x51b15c,54|1|0x3d131b,52|-14|0x0c2943,21|-6|0x4fc962,-1|2|0x56df6d"
L22_1 = 90
L23_1 = 450
L24_1 = 443
L25_1 = 691
L26_1 = 490
L27_1 = 563
L28_1 = 401
L29_1 = 7
L19_1[1] = L20_1
L19_1[2] = L21_1
L19_1[3] = L22_1
L19_1[4] = L23_1
L19_1[5] = L24_1
L19_1[6] = L25_1
L19_1[7] = L26_1
L19_1[8] = L27_1
L19_1[9] = L28_1
L19_1[10] = L29_1
L20_1 = {}
L21_1 = 5423456
L22_1 = "4|-1|0x56de6c,34|13|0x48b257,43|13|0x4eca63,51|12|0x49ba5b,52|5|0x4dc862,52|0|0x50ce65,51|-16|0x51c962,40|4|0x0d2010,46|4|0x275b2e"
L23_1 = 90
L24_1 = 632
L25_1 = 370
L26_1 = 860
L27_1 = 414
L28_1 = 724
L29_1 = 333
L30_1 = 8
L20_1[1] = L21_1
L20_1[2] = L22_1
L20_1[3] = L23_1
L20_1[4] = L24_1
L20_1[5] = L25_1
L20_1[6] = L26_1
L20_1[7] = L27_1
L20_1[8] = L28_1
L20_1[9] = L29_1
L20_1[10] = L30_1
L21_1 = {}
L22_1 = 5560169
L23_1 = "0|-3|0x51ca62,87|7|0x55dd6c,86|-3|0x56de6c,88|-12|0x48b95b,87|7|0x55dd6c,79|14|0x51d367,2|1|0x56df6d,39|-9|0x875829,59|-6|0xd19f54"
L24_1 = 90
L25_1 = 632
L26_1 = 370
L27_1 = 860
L28_1 = 414
L29_1 = 724
L30_1 = 333
L31_1 = 8
L21_1[1] = L22_1
L21_1[2] = L23_1
L21_1[3] = L24_1
L21_1[4] = L25_1
L21_1[5] = L26_1
L21_1[6] = L27_1
L21_1[7] = L28_1
L21_1[8] = L29_1
L21_1[9] = L30_1
L21_1[10] = L31_1
L22_1 = {}
L23_1 = 5693293
L24_1 = "24|11|0x54d96a,26|6|0x55dd6c,22|-3|0x54d569,33|7|0x4dc762,33|15|0x409f4e,25|7|0x54d669,88|-11|0x4abf5e,85|-12|0x53cb65,1|1|0x56df6d"
L25_1 = 90
L26_1 = 632
L27_1 = 370
L28_1 = 860
L29_1 = 414
L30_1 = 724
L31_1 = 333
L32_1 = 8
L22_1[1] = L23_1
L22_1[2] = L24_1
L22_1[3] = L25_1
L22_1[4] = L26_1
L22_1[5] = L27_1
L22_1[6] = L28_1
L22_1[7] = L29_1
L22_1[8] = L30_1
L22_1[9] = L31_1
L22_1[10] = L32_1
L23_1 = {}
L24_1 = 4766042
L25_1 = "-4|0|0x54d769,38|10|0x4fca63,46|11|0x42a14e,49|6|0x4cbf5e,46|0|0x53d86a,29|12|0x53d668,20|11|0x47b75a,43|-1|0x50d066,31|4|0x54da6a"
L26_1 = 90
L27_1 = 632
L28_1 = 370
L29_1 = 860
L30_1 = 414
L31_1 = 724
L32_1 = 333
L33_1 = 8
L23_1[1] = L24_1
L23_1[2] = L25_1
L23_1[3] = L26_1
L23_1[4] = L27_1
L23_1[5] = L28_1
L23_1[6] = L29_1
L23_1[7] = L30_1
L23_1[8] = L31_1
L23_1[9] = L32_1
L23_1[10] = L33_1
L24_1 = {}
L25_1 = 5693293
L26_1 = "-3|2|0x51c962,2|1|0x50ce65,84|-11|0x4dc862,84|-2|0x55d268,85|7|0x51b15c,54|1|0x3d131b,52|-14|0x0c2943,21|-6|0x4fc962,-1|2|0x56df6d"
L27_1 = 90
L28_1 = 632
L29_1 = 370
L30_1 = 860
L31_1 = 414
L32_1 = 724
L33_1 = 333
L34_1 = 8
L24_1[1] = L25_1
L24_1[2] = L26_1
L24_1[3] = L27_1
L24_1[4] = L28_1
L24_1[5] = L29_1
L24_1[6] = L30_1
L24_1[7] = L31_1
L24_1[8] = L32_1
L24_1[9] = L33_1
L24_1[10] = L34_1
L25_1 = {}
L26_1 = 3902787
L27_1 = "-9|-1|0x4abe5d,8|-8|0x2f6d34,9|-14|0x42ab54,14|-22|0x51d167,-31|-13|0x56df6d,-31|-15|0x56df6d,-10|-2|0x4bbd5b,1|-6|0x2e6e35,2|-5|0x070c06"
L28_1 = 90
L29_1 = 632
L30_1 = 370
L31_1 = 860
L32_1 = 414
L33_1 = 724
L34_1 = 333
L35_1 = 8
L25_1[1] = L26_1
L25_1[2] = L27_1
L25_1[3] = L28_1
L25_1[4] = L29_1
L25_1[5] = L30_1
L25_1[6] = L31_1
L25_1[7] = L32_1
L25_1[8] = L33_1
L25_1[9] = L34_1
L25_1[10] = L35_1
L26_1 = {}
L27_1 = 5423456
L28_1 = "4|-1|0x56de6c,34|13|0x48b257,43|13|0x4eca63,51|12|0x49ba5b,52|5|0x4dc862,52|0|0x50ce65,51|-16|0x51c962,40|4|0x0d2010,46|4|0x275b2e"
L29_1 = 90
L30_1 = 775
L31_1 = 298
L32_1 = 1016
L33_1 = 349
L34_1 = 885
L35_1 = 255
L36_1 = 9
L26_1[1] = L27_1
L26_1[2] = L28_1
L26_1[3] = L29_1
L26_1[4] = L30_1
L26_1[5] = L31_1
L26_1[6] = L32_1
L26_1[7] = L33_1
L26_1[8] = L34_1
L26_1[9] = L35_1
L26_1[10] = L36_1
L27_1 = {}
L28_1 = 5560169
L29_1 = "0|-3|0x51ca62,87|7|0x55dd6c,86|-3|0x56de6c,88|-12|0x48b95b,87|7|0x55dd6c,79|14|0x51d367,2|1|0x56df6d,39|-9|0x875829,59|-6|0xd19f54"
L30_1 = 90
L31_1 = 775
L32_1 = 298
L33_1 = 1016
L34_1 = 349
L35_1 = 885
L36_1 = 255
L37_1 = 9
L27_1[1] = L28_1
L27_1[2] = L29_1
L27_1[3] = L30_1
L27_1[4] = L31_1
L27_1[5] = L32_1
L27_1[6] = L33_1
L27_1[7] = L34_1
L27_1[8] = L35_1
L27_1[9] = L36_1
L27_1[10] = L37_1
L28_1 = {}
L29_1 = 5494890
L30_1 = "0|-2|0x55dc6c,20|-4|0x4fcd64,28|-11|0x53d669,26|7|0x51cc64,22|11|0x3b984a,32|6|0x358842,83|-5|0x4d8c4b,84|6|0x54ce66,76|9|0x4ec863"
L31_1 = 90
L32_1 = 775
L33_1 = 298
L34_1 = 1016
L35_1 = 349
L36_1 = 885
L37_1 = 255
L38_1 = 9
L28_1[1] = L29_1
L28_1[2] = L30_1
L28_1[3] = L31_1
L28_1[4] = L32_1
L28_1[5] = L33_1
L28_1[6] = L34_1
L28_1[7] = L35_1
L28_1[8] = L36_1
L28_1[9] = L37_1
L28_1[10] = L38_1
L29_1 = {}
L30_1 = 4766042
L31_1 = "-4|0|0x54d769,38|10|0x4fca63,46|11|0x42a14e,49|6|0x4cbf5e,46|0|0x53d86a,29|12|0x53d668,20|11|0x47b75a,43|-1|0x50d066,31|4|0x54da6a"
L32_1 = 90
L33_1 = 775
L34_1 = 298
L35_1 = 1016
L36_1 = 349
L37_1 = 885
L38_1 = 255
L39_1 = 9
L29_1[1] = L30_1
L29_1[2] = L31_1
L29_1[3] = L32_1
L29_1[4] = L33_1
L29_1[5] = L34_1
L29_1[6] = L35_1
L29_1[7] = L36_1
L29_1[8] = L37_1
L29_1[9] = L38_1
L29_1[10] = L39_1
L30_1 = {}
L31_1 = 5693293
L32_1 = "-3|2|0x51c962,2|1|0x50ce65,84|-11|0x4dc862,84|-2|0x55d268,85|7|0x51b15c,54|1|0x3d131b,52|-14|0x0c2943,21|-6|0x4fc962,-1|2|0x56df6d"
L33_1 = 90
L34_1 = 775
L35_1 = 298
L36_1 = 1016
L37_1 = 349
L38_1 = 885
L39_1 = 255
L40_1 = 9
L30_1[1] = L31_1
L30_1[2] = L32_1
L30_1[3] = L33_1
L30_1[4] = L34_1
L30_1[5] = L35_1
L30_1[6] = L36_1
L30_1[7] = L37_1
L30_1[8] = L38_1
L30_1[9] = L39_1
L30_1[10] = L40_1
L31_1 = {}
L32_1 = 3902787
L33_1 = "-9|-1|0x4abe5d,8|-8|0x2f6d34,9|-14|0x42ab54,14|-22|0x51d167,-31|-13|0x56df6d,-31|-15|0x56df6d,-10|-2|0x4bbd5b,1|-6|0x2e6e35,2|-5|0x070c06"
L34_1 = 90
L35_1 = 775
L36_1 = 298
L37_1 = 1016
L38_1 = 349
L39_1 = 885
L40_1 = 255
L41_1 = 9
L31_1[1] = L32_1
L31_1[2] = L33_1
L31_1[3] = L34_1
L31_1[4] = L35_1
L31_1[5] = L36_1
L31_1[6] = L37_1
L31_1[7] = L38_1
L31_1[8] = L39_1
L31_1[9] = L40_1
L31_1[10] = L41_1
L32_1 = {}
L33_1 = 5423456
L34_1 = "4|-1|0x56de6c,34|13|0x48b257,43|13|0x4eca63,51|12|0x49ba5b,52|5|0x4dc862,52|0|0x50ce65,51|-16|0x51c962,40|4|0x0d2010,46|4|0x275b2e"
L35_1 = 90
L36_1 = 930
L37_1 = 232
L38_1 = 1180
L39_1 = 272
L40_1 = 1039
L41_1 = 163
L42_1 = 10
L32_1[1] = L33_1
L32_1[2] = L34_1
L32_1[3] = L35_1
L32_1[4] = L36_1
L32_1[5] = L37_1
L32_1[6] = L38_1
L32_1[7] = L39_1
L32_1[8] = L40_1
L32_1[9] = L41_1
L32_1[10] = L42_1
L33_1 = {}
L34_1 = 5560169
L35_1 = "0|-3|0x51ca62,87|7|0x55dd6c,86|-3|0x56de6c,88|-12|0x48b95b,87|7|0x55dd6c,79|14|0x51d367,2|1|0x56df6d,39|-9|0x875829,59|-6|0xd19f54"
L36_1 = 90
L37_1 = 930
L38_1 = 232
L39_1 = 1180
L40_1 = 272
L41_1 = 1039
L42_1 = 163
L43_1 = 10
L33_1[1] = L34_1
L33_1[2] = L35_1
L33_1[3] = L36_1
L33_1[4] = L37_1
L33_1[5] = L38_1
L33_1[6] = L39_1
L33_1[7] = L40_1
L33_1[8] = L41_1
L33_1[9] = L42_1
L33_1[10] = L43_1
L34_1 = {}
L35_1 = 5494890
L36_1 = "0|-2|0x55dc6c,20|-4|0x4fcd64,28|-11|0x53d669,26|7|0x51cc64,22|11|0x3b984a,32|6|0x358842,83|-5|0x4d8c4b,84|6|0x54ce66,76|9|0x4ec863"
L37_1 = 90
L38_1 = 930
L39_1 = 232
L40_1 = 1180
L41_1 = 272
L42_1 = 1039
L43_1 = 163
L44_1 = 10
L34_1[1] = L35_1
L34_1[2] = L36_1
L34_1[3] = L37_1
L34_1[4] = L38_1
L34_1[5] = L39_1
L34_1[6] = L40_1
L34_1[7] = L41_1
L34_1[8] = L42_1
L34_1[9] = L43_1
L34_1[10] = L44_1
L35_1 = {}
L36_1 = 4766042
L37_1 = "-4|0|0x54d769,38|10|0x4fca63,46|11|0x42a14e,49|6|0x4cbf5e,46|0|0x53d86a,29|12|0x53d668,20|11|0x47b75a,43|-1|0x50d066,31|4|0x54da6a"
L38_1 = 90
L39_1 = 930
L40_1 = 232
L41_1 = 1180
L42_1 = 272
L43_1 = 1039
L44_1 = 163
L45_1 = 10
L35_1[1] = L36_1
L35_1[2] = L37_1
L35_1[3] = L38_1
L35_1[4] = L39_1
L35_1[5] = L40_1
L35_1[6] = L41_1
L35_1[7] = L42_1
L35_1[8] = L43_1
L35_1[9] = L44_1
L35_1[10] = L45_1
L36_1 = {}
L37_1 = 5693293
L38_1 = "-3|2|0x51c962,2|1|0x50ce65,84|-11|0x4dc862,84|-2|0x55d268,85|7|0x51b15c,54|1|0x3d131b,52|-14|0x0c2943,21|-6|0x4fc962,-1|2|0x56df6d"
L39_1 = 90
L40_1 = 930
L41_1 = 232
L42_1 = 1180
L43_1 = 272
L44_1 = 1039
L45_1 = 163
L46_1 = 10
L36_1[1] = L37_1
L36_1[2] = L38_1
L36_1[3] = L39_1
L36_1[4] = L40_1
L36_1[5] = L41_1
L36_1[6] = L42_1
L36_1[7] = L43_1
L36_1[8] = L44_1
L36_1[9] = L45_1
L36_1[10] = L46_1
L37_1 = {}
L38_1 = 5693293
L39_1 = "25|-7|0x4fcb63,22|14|0x4eca63,34|14|0x4dc761,49|14|0x4bc35f,59|12|0x4ec561,67|11|0x4bc15f,82|7|0x49b95a,82|-2|0x55dd6c,82|-12|0x56de6d"
L40_1 = 90
L41_1 = 930
L42_1 = 232
L43_1 = 1180
L44_1 = 272
L45_1 = 1039
L46_1 = 163
L47_1 = 10
L37_1[1] = L38_1
L37_1[2] = L39_1
L37_1[3] = L40_1
L37_1[4] = L41_1
L37_1[5] = L42_1
L37_1[6] = L43_1
L37_1[7] = L44_1
L37_1[8] = L45_1
L37_1[9] = L46_1
L37_1[10] = L47_1
L38_1 = {}
L39_1 = 3902787
L40_1 = "-9|-1|0x4abe5d,8|-8|0x2f6d34,9|-14|0x42ab54,14|-22|0x51d167,-31|-13|0x56df6d,-31|-15|0x56df6d,-10|-2|0x4bbd5b,1|-6|0x2e6e35,2|-5|0x070c06"
L41_1 = 90
L42_1 = 930
L43_1 = 232
L44_1 = 1180
L45_1 = 272
L46_1 = 1039
L47_1 = 163
L48_1 = 10
L38_1[1] = L39_1
L38_1[2] = L40_1
L38_1[3] = L41_1
L38_1[4] = L42_1
L38_1[5] = L43_1
L38_1[6] = L44_1
L38_1[7] = L45_1
L38_1[8] = L46_1
L38_1[9] = L47_1
L38_1[10] = L48_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L2_1[12] = L14_1
L2_1[13] = L15_1
L2_1[14] = L16_1
L2_1[15] = L17_1
L2_1[16] = L18_1
L2_1[17] = L19_1
L2_1[18] = L20_1
L2_1[19] = L21_1
L2_1[20] = L22_1
L2_1[21] = L23_1
L2_1[22] = L24_1
L2_1[23] = L25_1
L2_1[24] = L26_1
L2_1[25] = L27_1
L2_1[26] = L28_1
L2_1[27] = L29_1
L2_1[28] = L30_1
L2_1[29] = L31_1
L2_1[30] = L32_1
L2_1[31] = L33_1
L2_1[32] = L34_1
L2_1[33] = L35_1
L2_1[34] = L36_1
L2_1[35] = L37_1
L2_1[36] = L38_1
L1_1["爆炸"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5693293
L5_1 = "3|1|0x54d86a,29|6|0x51d367,49|-3|0x53c965,69|-1|0x348641,84|-11|0x56de6c,85|14|0x42a552,85|18|0x41a451,108|16|0x50d066,158|1|0x55d569"
L6_1 = 90
L7_1 = 387
L8_1 = 609
L9_1 = 687
L10_1 = 648
L11_1 = 481
L12_1 = 557
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5693293
L6_1 = "3|1|0x54d86a,29|6|0x51d367,49|-3|0x53c965,69|-1|0x348641,84|-11|0x56de6c,85|14|0x42a552,85|18|0x41a451,108|16|0x50d066,158|1|0x55d569"
L7_1 = 90
L8_1 = 495
L9_1 = 532
L10_1 = 815
L11_1 = 584
L12_1 = 640
L13_1 = 480
L14_1 = 2
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 5693293
L7_1 = "3|1|0x54d86a,29|6|0x51d367,49|-3|0x53c965,69|-1|0x348641,84|-11|0x56de6c,85|14|0x42a552,85|18|0x41a451,108|16|0x50d066,158|1|0x55d569"
L8_1 = 90
L9_1 = 705
L10_1 = 461
L11_1 = 976
L12_1 = 509
L13_1 = 806
L14_1 = 405
L15_1 = 3
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5693293
L8_1 = "3|1|0x54d86a,29|6|0x51d367,49|-3|0x53c965,69|-1|0x348641,84|-11|0x56de6c,85|14|0x42a552,85|18|0x41a451,108|16|0x50d066,158|1|0x55d569"
L9_1 = 90
L10_1 = 880
L11_1 = 390
L12_1 = 1139
L13_1 = 437
L14_1 = 967
L15_1 = 347
L16_1 = 4
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5693293
L9_1 = "3|1|0x54d86a,29|6|0x51d367,49|-3|0x53c965,69|-1|0x348641,84|-11|0x56de6c,85|14|0x42a552,85|18|0x41a451,108|16|0x50d066,158|1|0x55d569"
L10_1 = 90
L11_1 = 974
L12_1 = 316
L13_1 = 1313
L14_1 = 368
L15_1 = 1121
L16_1 = 271
L17_1 = 5
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5693293
L10_1 = "40|16|0x3fa450,52|6|0x42aa54,47|-3|0x55dd6c,46|-13|0x56de6d,63|-12|0x4fcc64,82|-7|0x56de6d,85|17|0x51d166,120|15|0x56de6d,158|0|0x4ec660"
L11_1 = 90
L12_1 = 308
L13_1 = 512
L14_1 = 557
L15_1 = 567
L16_1 = 401
L17_1 = 472
L18_1 = 6
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 4628819
L11_1 = "5|0|0x52d467,5|5|0x55dd6c,3|6|0x51d166,1|12|0x53d468,3|18|0x21552a,3|16|0x102814,10|19|0x0f0d07,10|9|0x091109,8|24|0x50ce65"
L12_1 = 90
L13_1 = 826
L14_1 = 385
L15_1 = 1130
L16_1 = 435
L17_1 = 401
L18_1 = 472
L19_1 = 6
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5161054
L12_1 = "-2|4|0x54d769,21|8|0x51c762,30|17|0x45af56,38|12|0x4abf5d,36|-2|0x4ab659,46|-10|0x41a852,22|-10|0x4cb95b,23|-3|0x53d769,25|7|0x143319"
L13_1 = 90
L14_1 = 308
L15_1 = 512
L16_1 = 557
L17_1 = 567
L18_1 = 401
L19_1 = 472
L20_1 = 6
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 5693293
L13_1 = "25|1|0x111c10,23|9|0x55dd6c,34|14|0x47b759,35|0|0x456c41,31|-13|0x50c562,39|-12|0x398d46,46|-3|0x46b458,49|7|0x55de6c,40|12|0x338441"
L14_1 = 90
L15_1 = 308
L16_1 = 512
L17_1 = 557
L18_1 = 567
L19_1 = 401
L20_1 = 472
L21_1 = 6
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 4628819
L14_1 = "5|0|0x52d467,5|5|0x55dd6c,3|6|0x51d166,1|12|0x53d468,3|18|0x21552a,3|16|0x102814,10|19|0x0f0d07,10|9|0x091109,8|24|0x50ce65"
L15_1 = 90
L16_1 = 450
L17_1 = 443
L18_1 = 691
L19_1 = 490
L20_1 = 563
L21_1 = 401
L22_1 = 7
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L13_1 = {}
L14_1 = 5693293
L15_1 = "25|1|0x111c10,23|9|0x55dd6c,34|14|0x47b759,35|0|0x456c41,31|-13|0x50c562,39|-12|0x398d46,46|-3|0x46b458,49|7|0x55de6c,40|12|0x338441"
L16_1 = 90
L17_1 = 450
L18_1 = 443
L19_1 = 691
L20_1 = 490
L21_1 = 563
L22_1 = 401
L23_1 = 7
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L13_1[5] = L18_1
L13_1[6] = L19_1
L13_1[7] = L20_1
L13_1[8] = L21_1
L13_1[9] = L22_1
L13_1[10] = L23_1
L14_1 = {}
L15_1 = 4628819
L16_1 = "5|0|0x52d467,5|5|0x55dd6c,3|6|0x51d166,1|12|0x53d468,3|18|0x21552a,3|16|0x102814,10|19|0x0f0d07,10|9|0x091109,8|24|0x50ce65"
L17_1 = 90
L18_1 = 632
L19_1 = 370
L20_1 = 860
L21_1 = 414
L22_1 = 724
L23_1 = 333
L24_1 = 8
L14_1[1] = L15_1
L14_1[2] = L16_1
L14_1[3] = L17_1
L14_1[4] = L18_1
L14_1[5] = L19_1
L14_1[6] = L20_1
L14_1[7] = L21_1
L14_1[8] = L22_1
L14_1[9] = L23_1
L14_1[10] = L24_1
L15_1 = {}
L16_1 = 5693293
L17_1 = "25|1|0x111c10,23|9|0x55dd6c,34|14|0x47b759,35|0|0x456c41,31|-13|0x50c562,39|-12|0x398d46,46|-3|0x46b458,49|7|0x55de6c,40|12|0x338441"
L18_1 = 90
L19_1 = 632
L20_1 = 370
L21_1 = 860
L22_1 = 414
L23_1 = 724
L24_1 = 333
L25_1 = 8
L15_1[1] = L16_1
L15_1[2] = L17_1
L15_1[3] = L18_1
L15_1[4] = L19_1
L15_1[5] = L20_1
L15_1[6] = L21_1
L15_1[7] = L22_1
L15_1[8] = L23_1
L15_1[9] = L24_1
L15_1[10] = L25_1
L16_1 = {}
L17_1 = 5693293
L18_1 = "22|3|0x4dbf5c,31|3|0x4fcd64,30|14|0x55dd6c,26|6|0x1d4a24,25|-2|0x0f1c0d,25|-9|0x243d21,27|-11|0x52d467,31|-12|0x54d769,28|-3|0x48ba5b"
L19_1 = 90
L20_1 = 775
L21_1 = 298
L22_1 = 1016
L23_1 = 349
L24_1 = 885
L25_1 = 255
L26_1 = 9
L16_1[1] = L17_1
L16_1[2] = L18_1
L16_1[3] = L19_1
L16_1[4] = L20_1
L16_1[5] = L21_1
L16_1[6] = L22_1
L16_1[7] = L23_1
L16_1[8] = L24_1
L16_1[9] = L25_1
L16_1[10] = L26_1
L17_1 = {}
L18_1 = 4628819
L19_1 = "5|0|0x52d467,5|5|0x55dd6c,3|6|0x51d166,1|12|0x53d468,3|18|0x21552a,3|16|0x102814,10|19|0x0f0d07,10|9|0x091109,8|24|0x50ce65"
L20_1 = 90
L21_1 = 775
L22_1 = 298
L23_1 = 1016
L24_1 = 349
L25_1 = 885
L26_1 = 255
L27_1 = 9
L17_1[1] = L18_1
L17_1[2] = L19_1
L17_1[3] = L20_1
L17_1[4] = L21_1
L17_1[5] = L22_1
L17_1[6] = L23_1
L17_1[7] = L24_1
L17_1[8] = L25_1
L17_1[9] = L26_1
L17_1[10] = L27_1
L18_1 = {}
L19_1 = 5693293
L20_1 = "25|1|0x111c10,23|9|0x55dd6c,34|14|0x47b759,35|0|0x456c41,31|-13|0x50c562,39|-12|0x398d46,46|-3|0x46b458,49|7|0x55de6c,40|12|0x338441"
L21_1 = 90
L22_1 = 775
L23_1 = 298
L24_1 = 1016
L25_1 = 349
L26_1 = 885
L27_1 = 255
L28_1 = 9
L18_1[1] = L19_1
L18_1[2] = L20_1
L18_1[3] = L21_1
L18_1[4] = L22_1
L18_1[5] = L23_1
L18_1[6] = L24_1
L18_1[7] = L25_1
L18_1[8] = L26_1
L18_1[9] = L27_1
L18_1[10] = L28_1
L19_1 = {}
L20_1 = 5693293
L21_1 = "40|16|0x3fa450,52|6|0x42aa54,47|-3|0x55dd6c,46|-13|0x56de6d,63|-12|0x4fcc64,82|-7|0x56de6d,85|17|0x51d166,120|15|0x56de6d,158|0|0x4ec660"
L22_1 = 90
L23_1 = 930
L24_1 = 232
L25_1 = 1180
L26_1 = 272
L27_1 = 1039
L28_1 = 163
L29_1 = 10
L19_1[1] = L20_1
L19_1[2] = L21_1
L19_1[3] = L22_1
L19_1[4] = L23_1
L19_1[5] = L24_1
L19_1[6] = L25_1
L19_1[7] = L26_1
L19_1[8] = L27_1
L19_1[9] = L28_1
L19_1[10] = L29_1
L20_1 = {}
L21_1 = 5693293
L22_1 = "25|1|0x111c10,23|9|0x55dd6c,34|14|0x47b759,35|0|0x456c41,31|-13|0x50c562,39|-12|0x398d46,46|-3|0x46b458,49|7|0x55de6c,40|12|0x338441"
L23_1 = 90
L24_1 = 930
L25_1 = 232
L26_1 = 1180
L27_1 = 272
L28_1 = 1039
L29_1 = 163
L30_1 = 10
L20_1[1] = L21_1
L20_1[2] = L22_1
L20_1[3] = L23_1
L20_1[4] = L24_1
L20_1[5] = L25_1
L20_1[6] = L26_1
L20_1[7] = L27_1
L20_1[8] = L28_1
L20_1[9] = L29_1
L20_1[10] = L30_1
L21_1 = {}
L22_1 = 5693293
L23_1 = "5|7|0x4bbf5d,12|19|0x0f180b,22|26|0x55db6b,36|28|0x50d166,56|28|0x51d166,72|-2|0x56df6d,77|2|0x368943,81|8|0x56de6c,68|11|0x3e9e4c"
L24_1 = 90
L25_1 = 930
L26_1 = 232
L27_1 = 1180
L28_1 = 272
L29_1 = 1039
L30_1 = 163
L31_1 = 10
L21_1[1] = L22_1
L21_1[2] = L23_1
L21_1[3] = L24_1
L21_1[4] = L25_1
L21_1[5] = L26_1
L21_1[6] = L27_1
L21_1[7] = L28_1
L21_1[8] = L29_1
L21_1[9] = L30_1
L21_1[10] = L31_1
L22_1 = {}
L23_1 = 4628819
L24_1 = "5|0|0x52d467,5|5|0x55dd6c,3|6|0x51d166,1|12|0x53d468,3|18|0x21552a,3|16|0x102814,10|19|0x0f0d07,10|9|0x091109,8|24|0x50ce65"
L25_1 = 90
L26_1 = 930
L27_1 = 232
L28_1 = 1180
L29_1 = 272
L30_1 = 1039
L31_1 = 163
L32_1 = 10
L22_1[1] = L23_1
L22_1[2] = L24_1
L22_1[3] = L25_1
L22_1[4] = L26_1
L22_1[5] = L27_1
L22_1[6] = L28_1
L22_1[7] = L29_1
L22_1[8] = L30_1
L22_1[9] = L31_1
L22_1[10] = L32_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L2_1[12] = L14_1
L2_1[13] = L15_1
L2_1[14] = L16_1
L2_1[15] = L17_1
L2_1[16] = L18_1
L2_1[17] = L19_1
L2_1[18] = L20_1
L2_1[19] = L21_1
L2_1[20] = L22_1
L1_1["超量"] = L2_1
L0_1["丝路右"] = L1_1
L1_1 = {}
L2_1 = {}
L1_1["宝宝"] = L2_1
L2_1 = {}
L1_1["护佑"] = L2_1
L2_1 = {}
L1_1["爆炸"] = L2_1
L2_1 = {}
L1_1["超量"] = L2_1
L0_1["丝路左"] = L1_1
L1_1 = {}
L2_1 = {}
L1_1["宝宝"] = L2_1
L2_1 = {}
L1_1["护佑"] = L2_1
L2_1 = {}
L1_1["爆炸"] = L2_1
L2_1 = {}
L1_1["超量"] = L2_1
L0_1["丝路中"] = L1_1
L1_1 = {}
L2_1 = {}
L3_1 = {}
L4_1 = 4898397
L5_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L6_1 = 90
L7_1 = 400
L8_1 = 605
L9_1 = 557
L10_1 = 653
L11_1 = 480
L12_1 = 533
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 4898397
L6_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L7_1 = 90
L8_1 = 562
L9_1 = 530
L10_1 = 732
L11_1 = 584
L12_1 = 640
L13_1 = 459
L14_1 = 2
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 4898397
L7_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L8_1 = 90
L9_1 = 732
L10_1 = 460
L11_1 = 884
L12_1 = 509
L13_1 = 798
L14_1 = 372
L15_1 = 3
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 4898397
L8_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L9_1 = 90
L10_1 = 883
L11_1 = 385
L12_1 = 1076
L13_1 = 435
L14_1 = 959
L15_1 = 297
L16_1 = 4
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 4898397
L9_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L10_1 = 90
L11_1 = 971
L12_1 = 317
L13_1 = 1290
L14_1 = 369
L15_1 = 1134
L16_1 = 255
L17_1 = 5
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 4898397
L10_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L11_1 = 90
L12_1 = 348
L13_1 = 514
L14_1 = 565
L15_1 = 565
L16_1 = 392
L17_1 = 450
L18_1 = 6
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 4898397
L11_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L12_1 = 90
L13_1 = 480
L14_1 = 442
L15_1 = 698
L16_1 = 491
L17_1 = 554
L18_1 = 382
L19_1 = 7
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 4898397
L12_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L13_1 = 90
L14_1 = 620
L15_1 = 372
L16_1 = 869
L17_1 = 419
L18_1 = 717
L19_1 = 302
L20_1 = 8
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 4898397
L13_1 = "-11|3|0x49bd5d,-7|6|0x0b190e,11|6|0x0d1f10,8|19|0x48ba5b,9|23|0x53d669,12|31|0x1f4926,-11|29|0x48ba5b,-7|19|0x40a551,-8|10|0x53d769"
L14_1 = 90
L15_1 = 785
L16_1 = 297
L17_1 = 1055
L18_1 = 350
L19_1 = 870
L20_1 = 217
L21_1 = 9
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5560426
L14_1 = "-11|2|0x42aa54,-14|4|0x4eca63,-6|8|0x4eca63,5|9|0x56df6d,0|16|0x56df6d,-7|18|0x338441,0|24|0x55dd6c,-1|27|0x56df6d,-13|27|0x54d96a"
L15_1 = 90
L16_1 = 969
L17_1 = 225
L18_1 = 1129
L19_1 = 274
L20_1 = 1028
L21_1 = 135
L22_1 = 10
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L1_1["宝宝"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5560939
L5_1 = "24|-1|0x56df6d,34|-1|0x49bd5d,41|-15|0x56df6d,49|-6|0x4fc563,59|-1|0x56de6d,69|6|0x51d167,84|12|0x53d769,84|-1|0x54da6a,72|-11|0x55dc6c"
L6_1 = 90
L7_1 = 378
L8_1 = 598
L9_1 = 712
L10_1 = 664
L11_1 = 492
L12_1 = 537
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5626987
L6_1 = "4|0|0x388745,13|0|0x3fa350,23|-4|0x429b51,16|-11|0x50d066,33|-3|0x56df6d,45|-1|0x56df6d,56|-1|0x50cf65,56|14|0x4bb65d,-26|0|0x56df6d"
L7_1 = 90
L8_1 = 540
L9_1 = 530
L10_1 = 910
L11_1 = 582
L12_1 = 638
L13_1 = 464
L14_1 = 2
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 5693293
L7_1 = "25|-1|0x4dc561,26|9|0x4fcd64,27|-10|0x53d669,41|-10|0x43ad55,35|-10|0x55da6b,34|1|0x51d367,45|-2|0x4dba5e,50|-2|0x54d66a,59|-2|0x56df6d"
L8_1 = 90
L9_1 = 705
L10_1 = 455
L11_1 = 1013
L12_1 = 513
L13_1 = 806
L14_1 = 400
L15_1 = 3
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5561195
L8_1 = "24|-6|0x51d367,24|5|0x51d367,24|12|0x50d066,32|-3|0x55dc6c,41|-2|0x4bc25f,49|-4|0x51d166,47|-12|0x50cf66,59|-2|0x56df6d,71|-3|0x4fcc64"
L9_1 = 90
L10_1 = 869
L11_1 = 390
L12_1 = 1171
L13_1 = 435
L14_1 = 967
L15_1 = 310
L16_1 = 4
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5626987
L9_1 = "25|-7|0x4bc35f,25|0|0x55dc6c,23|12|0x54da6b,28|13|0x3ea04e,35|-1|0x49bd5c,47|-11|0x53d469,57|-3|0x56df6d,57|6|0x55dc6c,68|3|0x51d267"
L10_1 = 90
L11_1 = 1015
L12_1 = 310
L13_1 = 1296
L14_1 = 363
L15_1 = 1134
L16_1 = 255
L17_1 = 5
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5693293
L10_1 = "0|2|0x56df6d,-2|3|0x50cf65,24|-4|0x3a954a,24|1|0x47b85b,25|7|0x53d669,33|13|0x3e9f4e,35|-1|0x56de6c,41|0|0x47b759,42|-12|0x56df6d"
L11_1 = 90
L12_1 = 317
L13_1 = 518
L14_1 = 548
L15_1 = 560
L16_1 = 392
L17_1 = 450
L18_1 = 6
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 5693293
L11_1 = "26|-2|0x55db6b,27|6|0x48bb5c,27|13|0x42aa53,28|-8|0x4eca63,23|-9|0x50cd65,27|-12|0x4cc460,35|-11|0x52d468,36|-4|0x358943,34|5|0x4dc761"
L12_1 = 90
L13_1 = 292
L14_1 = 509
L15_1 = 513
L16_1 = 561
L17_1 = 392
L18_1 = 450
L19_1 = 6
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5693293
L12_1 = "27|5|0x4bc35f,27|9|0x48bb5c,27|1|0x54db6b,27|-8|0x56df6d,34|-6|0x50cf65,34|-2|0x51d267,34|2|0x54d96a,32|10|0x3c974c,41|0|0x4ecb63"
L13_1 = 90
L14_1 = 293
L15_1 = 514
L16_1 = 550
L17_1 = 564
L18_1 = 392
L19_1 = 450
L20_1 = 6
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 5693293
L13_1 = "0|3|0x56df6d,27|-6|0x4ecb64,27|-10|0x45b358,26|3|0x55db6b,24|16|0x4fcd64,33|12|0x4ec962,34|7|0x3a974a,34|0|0x55dd6c,35|-6|0x328140"
L14_1 = 90
L15_1 = 466
L16_1 = 442
L17_1 = 673
L18_1 = 490
L19_1 = 554
L20_1 = 382
L21_1 = 7
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5693293
L14_1 = "25|-4|0x4fcd64,25|4|0x4dc962,24|10|0x50d066,33|-2|0x55de6c,34|-10|0x4bc25f,25|-13|0x4ec962,25|-13|0x4ec962,27|-5|0x296533,25|1|0x55dc6c"
L15_1 = 90
L16_1 = 627
L17_1 = 371
L18_1 = 863
L19_1 = 417
L20_1 = 717
L21_1 = 302
L22_1 = 8
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L13_1 = {}
L14_1 = 5693293
L15_1 = "25|1|0x51d167,28|1|0x4bc25f,26|11|0x50cf66,33|13|0x3b994b,34|2|0x4cc560,35|-5|0x3a974a,35|-10|0x55dd6c,26|-11|0x53d669,26|-13|0x50d066"
L16_1 = 90
L17_1 = 787
L18_1 = 298
L19_1 = 978
L20_1 = 346
L21_1 = 870
L22_1 = 217
L23_1 = 9
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L13_1[5] = L18_1
L13_1[6] = L19_1
L13_1[7] = L20_1
L13_1[8] = L21_1
L13_1[9] = L22_1
L13_1[10] = L23_1
L14_1 = {}
L15_1 = 5693293
L16_1 = "0|2|0x56df6d,-2|3|0x50cf65,24|-4|0x3a954a,24|1|0x47b85b,25|7|0x53d669,33|13|0x3e9f4e,35|-1|0x56de6c,41|0|0x47b759,42|-12|0x56df6d"
L17_1 = 90
L18_1 = 962
L19_1 = 222
L20_1 = 1134
L21_1 = 273
L22_1 = 1028
L23_1 = 135
L24_1 = 10
L14_1[1] = L15_1
L14_1[2] = L16_1
L14_1[3] = L17_1
L14_1[4] = L18_1
L14_1[5] = L19_1
L14_1[6] = L20_1
L14_1[7] = L21_1
L14_1[8] = L22_1
L14_1[9] = L23_1
L14_1[10] = L24_1
L15_1 = {}
L16_1 = 5693293
L17_1 = "24|-4|0x3b964b,27|2|0x47b85a,25|12|0x53d669,22|15|0x4cc561,31|15|0x3c9a4c,33|10|0x49bc5c,37|-1|0x47b759,36|-11|0x4cc460,27|-10|0x51d367"
L18_1 = 90
L19_1 = 934
L20_1 = 226
L21_1 = 1266
L22_1 = 278
L23_1 = 1028
L24_1 = 135
L25_1 = 10
L15_1[1] = L16_1
L15_1[2] = L17_1
L15_1[3] = L18_1
L15_1[4] = L19_1
L15_1[5] = L20_1
L15_1[6] = L21_1
L15_1[7] = L22_1
L15_1[8] = L23_1
L15_1[9] = L24_1
L15_1[10] = L25_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L2_1[12] = L14_1
L2_1[13] = L15_1
L1_1["护佑"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5693293
L5_1 = "0|2|0x56df6d,-2|3|0x50cf65,24|-4|0x3a954a,24|1|0x47b85b,25|7|0x53d669,33|13|0x3e9f4e,35|-1|0x56de6c,41|0|0x47b759,42|-12|0x56df6d"
L6_1 = 90
L7_1 = 317
L8_1 = 518
L9_1 = 548
L10_1 = 560
L11_1 = 492
L12_1 = 537
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 5693037
L6_1 = "23|-2|0x44b156,33|0|0x4bc15f,37|5|0x214e29,40|11|0x52d568,46|0|0x399449,42|-5|0x50d066,48|-10|0x52d468,44|-14|0x4fcd64"
L7_1 = 90
L8_1 = 342
L9_1 = 603
L10_1 = 700
L11_1 = 656
L12_1 = 492
L13_1 = 537
L14_1 = 1
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 4169807
L7_1 = "-40|1|0x4dbf61,-42|5|0x56df6d,1|-3|0x4cc360,1|8|0x2d7238,-1|10|0x47b059,38|1|0x4fcd64,37|-6|0x4ecb63,22|4|0x55dd6c,4|14|0x43aa54"
L8_1 = 90
L9_1 = 537
L10_1 = 531
L11_1 = 856
L12_1 = 581
L13_1 = 648
L14_1 = 471
L15_1 = 2
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5693037
L8_1 = "34|-7|0x4abf5e,38|-6|0x54da6b,37|0|0x56df6d,47|1|0x4ec962,50|-1|0x48bb5c,44|-6|0x50d066,65|3|0x3b984b,66|-6|0x53d769,71|-8|0x2b6d36"
L9_1 = 90
L10_1 = 661
L11_1 = 446
L12_1 = 1011
L13_1 = 512
L14_1 = 806
L15_1 = 372
L16_1 = 3
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5693293
L9_1 = "35|-7|0x4fcd64,41|-1|0x379047,44|7|0x3d9d4d,39|12|0x3fa04f,39|7|0x2a6a35,47|-8|0x4fcd65,48|-12|0x45b358,42|-12|0x399449,40|-1|0x379047"
L10_1 = 90
L11_1 = 832
L12_1 = 382
L13_1 = 1103
L14_1 = 441
L15_1 = 965
L16_1 = 314
L17_1 = 4
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5693293
L10_1 = "26|0|0x4eca63,27|-5|0x54da6b,34|-10|0x55dc6c,41|-10|0x4bc15f,41|-2|0x45b257,38|0|0x54db6b,35|4|0x4dc862,42|12|0x51d267,78|-10|0x55dc6c"
L11_1 = 90
L12_1 = 1032
L13_1 = 316
L14_1 = 1226
L15_1 = 362
L16_1 = 1124
L17_1 = 238
L18_1 = 5
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 5693293
L11_1 = "25|1|0x49bc5c,25|6|0x48bc5c,24|11|0x41a953,25|-9|0x4cc460,31|-9|0x44af56,33|-9|0x53d769,35|-4|0x56df6d,35|-1|0x56df6d,35|3|0x56df6d"
L12_1 = 90
L13_1 = 260
L14_1 = 515
L15_1 = 532
L16_1 = 562
L17_1 = 392
L18_1 = 450
L19_1 = 6
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5693293
L12_1 = "26|-8|0x55de6c,26|-11|0x52d568,21|-2|0x42aa53,27|7|0x4eca63,24|8|0x55dd6c,23|12|0x4ecb63,35|0|0x4bc35f,36|-5|0x56df6d,36|-10|0x4abf5e"
L13_1 = 90
L14_1 = 445
L15_1 = 436
L16_1 = 718
L17_1 = 495
L18_1 = 560
L19_1 = 393
L20_1 = 7
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 3706182
L13_1 = "-32|5|0x56df6d,-7|1|0x4abf5e,-8|7|0x53d86a,-9|11|0x52d568,-10|15|0x54d96a,-11|19|0x338441,0|-4|0x54d96a,-1|-6|0x4bc05e,-4|-3|0x42ac54"
L14_1 = 90
L15_1 = 619
L16_1 = 369
L17_1 = 851
L18_1 = 417
L19_1 = 717
L20_1 = 302
L21_1 = 8
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5693293
L14_1 = "20|-2|0x4ac25f,23|-6|0x48ba5b,26|-6|0x55dd6c,25|-12|0x3d9e4e,21|13|0x50d066,26|9|0x52d668,34|0|0x4fcd64,34|-6|0x50d267,34|-11|0x41a752"
L15_1 = 90
L16_1 = 783
L17_1 = 296
L18_1 = 1008
L19_1 = 348
L20_1 = 887
L21_1 = 228
L22_1 = 9
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L13_1 = {}
L14_1 = 5693293
L15_1 = "26|-3|0x54d96a,25|3|0x48bb5b,21|14|0x52d668,28|10|0x3d9d4e,20|-4|0x3c9a4c,31|-8|0x41a852,25|-8|0x50d066,35|-1|0x56df6d,39|2|0x44b056"
L16_1 = 90
L17_1 = 944
L18_1 = 226
L19_1 = 1204
L20_1 = 277
L21_1 = 1041
L22_1 = 148
L23_1 = 10
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L13_1[5] = L18_1
L13_1[6] = L19_1
L13_1[7] = L20_1
L13_1[8] = L21_1
L13_1[9] = L22_1
L13_1[10] = L23_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L1_1["爆炸"] = L2_1
L2_1 = {}
L3_1 = {}
L4_1 = 5362279
L5_1 = "2|14|0x378f46,11|23|0x4eca63,22|17|0x55db6b,44|24|0x53d669,51|-4|0x47b85a,81|1|0x50ce65,89|13|0x55dd6c,93|22|0x50c764,117|9|0x54da6a"
L6_1 = 90
L7_1 = 339
L8_1 = 598
L9_1 = 729
L10_1 = 655
L11_1 = 492
L12_1 = 537
L13_1 = 1
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L3_1[5] = L8_1
L3_1[6] = L9_1
L3_1[7] = L10_1
L3_1[8] = L11_1
L3_1[9] = L12_1
L3_1[10] = L13_1
L4_1 = {}
L5_1 = 4169807
L6_1 = "-40|1|0x4dbf61,-42|5|0x56df6d,1|-3|0x4cc360,1|8|0x2d7238,-1|10|0x47b059,38|1|0x4fcd64,37|-6|0x4ecb63,22|4|0x55dd6c,4|14|0x43aa54"
L7_1 = 90
L8_1 = 537
L9_1 = 531
L10_1 = 856
L11_1 = 581
L12_1 = 648
L13_1 = 471
L14_1 = 2
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L4_1[5] = L9_1
L4_1[6] = L10_1
L4_1[7] = L11_1
L4_1[8] = L12_1
L4_1[9] = L13_1
L4_1[10] = L14_1
L5_1 = {}
L6_1 = 5693293
L7_1 = "37|-2|0x4dc762,35|8|0x3e8d4d,40|10|0x48ba5b,42|15|0x307d3d,40|-9|0x4dc561,41|-15|0x48b65a,62|-4|0x48ba5b,84|12|0x40a552,108|4|0x4abf5d"
L8_1 = 90
L9_1 = 669
L10_1 = 454
L11_1 = 965
L12_1 = 512
L13_1 = 806
L14_1 = 372
L15_1 = 3
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L5_1[5] = L10_1
L5_1[6] = L11_1
L5_1[7] = L12_1
L5_1[8] = L13_1
L5_1[9] = L14_1
L5_1[10] = L15_1
L6_1 = {}
L7_1 = 5693293
L8_1 = "27|5|0x53d669,37|5|0x41a852,40|1|0x49be5d,51|5|0x3e9e4f,48|8|0x45af57,46|14|0x4fcd64,146|0|0x54da6b,144|-10|0x56dd6c,102|-6|0x56df6d"
L9_1 = 90
L10_1 = 832
L11_1 = 389
L12_1 = 1092
L13_1 = 440
L14_1 = 965
L15_1 = 314
L16_1 = 4
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L6_1[5] = L11_1
L6_1[6] = L12_1
L6_1[7] = L13_1
L6_1[8] = L14_1
L6_1[9] = L15_1
L6_1[10] = L16_1
L7_1 = {}
L8_1 = 5560939
L9_1 = "11|2|0x317e3e,45|4|0x56df6d,75|-3|0x56df6d,88|-10|0x4cc460,93|-10|0x53d86a,114|-4|0x327e40,117|9|0x4cc561,129|9|0x51d166,126|12|0x44b057"
L10_1 = 90
L11_1 = 349
L12_1 = 602
L13_1 = 613
L14_1 = 650
L15_1 = 1124
L16_1 = 238
L17_1 = 5
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L7_1[5] = L12_1
L7_1[6] = L13_1
L7_1[7] = L14_1
L7_1[8] = L15_1
L7_1[9] = L16_1
L7_1[10] = L17_1
L8_1 = {}
L9_1 = 5693293
L10_1 = "25|5|0x143219,28|7|0x42ab54,27|11|0x4eca62,20|14|0x47b85a,23|-3|0x4dc661,26|-12|0x49bd5d,30|-11|0x49bd5d,39|-7|0x47b85a,46|1|0x42ab54"
L11_1 = 90
L12_1 = 252
L13_1 = 512
L14_1 = 541
L15_1 = 570
L16_1 = 392
L17_1 = 450
L18_1 = 6
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L8_1[5] = L13_1
L8_1[6] = L14_1
L8_1[7] = L15_1
L8_1[8] = L16_1
L8_1[9] = L17_1
L8_1[10] = L18_1
L9_1 = {}
L10_1 = 5693037
L11_1 = "25|-8|0x3c914b,27|-6|0x52d468,27|3|0x56df6d,25|11|0x53d669,32|14|0x4abf5e,35|7|0x4cc460,35|1|0x3c8a49,37|-3|0x49bc5c,40|-9|0x51d166"
L12_1 = 90
L13_1 = 428
L14_1 = 436
L15_1 = 717
L16_1 = 491
L17_1 = 560
L18_1 = 393
L19_1 = 7
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L9_1[5] = L14_1
L9_1[6] = L15_1
L9_1[7] = L16_1
L9_1[8] = L17_1
L9_1[9] = L18_1
L9_1[10] = L19_1
L10_1 = {}
L11_1 = 5693293
L12_1 = "3|1|0x53d669,24|4|0x56dd6c,32|-6|0x4cc460,42|-8|0x55dd6c,47|-14|0x4fc864,48|-1|0x45b157,51|3|0x51d368,42|11|0x44b056,37|7|0x48bb5c"
L13_1 = 90
L14_1 = 572
L15_1 = 366
L16_1 = 910
L17_1 = 417
L18_1 = 717
L19_1 = 302
L20_1 = 8
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L10_1[5] = L15_1
L10_1[6] = L16_1
L10_1[7] = L17_1
L10_1[8] = L18_1
L10_1[9] = L19_1
L10_1[10] = L20_1
L11_1 = {}
L12_1 = 5693293
L13_1 = "29|-10|0x399448,28|-10|0x47b95b,28|-6|0x54d96a,26|-1|0x47b85a,27|5|0x51d267,25|11|0x4fcc63,21|10|0x56df6d,20|5|0x3b984b,32|4|0x3d9d4d"
L14_1 = 90
L15_1 = 750
L16_1 = 296
L17_1 = 1015
L18_1 = 345
L19_1 = 887
L20_1 = 228
L21_1 = 9
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L11_1[5] = L16_1
L11_1[6] = L17_1
L11_1[7] = L18_1
L11_1[8] = L19_1
L11_1[9] = L20_1
L11_1[10] = L21_1
L12_1 = {}
L13_1 = 5693293
L14_1 = "25|-5|0x50cf65,30|-6|0x54de6c,27|-7|0x52d668,26|-12|0x56df6d,24|-12|0x55de6d,30|-12|0x56de6d,28|-5|0x4fcf65,35|7|0x41a953,34|15|0x3da04e"
L15_1 = 90
L16_1 = 907
L17_1 = 221
L18_1 = 1189
L19_1 = 276
L20_1 = 1041
L21_1 = 148
L22_1 = 10
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L12_1[5] = L17_1
L12_1[6] = L18_1
L12_1[7] = L19_1
L12_1[8] = L20_1
L12_1[9] = L21_1
L12_1[10] = L22_1
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L1_1["超量"] = L2_1
L0_1["地三"] = L1_1
monster = L0_1
L0_1 = "监控打码"

function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  L0_2 = {}
  L1_2 = {}
  L2_2 = 12698049
  L3_2 = "-2|3|0xfafafa,-5|6|0xf5f5f5,-11|7|0xe3e3e3,-11|-3|0xdedede,1|-5|0xefefef,-1|-9|0xd7d7d7,3|-11|0xf9f9f9,19|5|0xf8f8f8,22|-8|0xdad9d9"
  L4_2 = 90
  L5_2 = 988
  L6_2 = 89
  L7_2 = 1346
  L8_2 = 384
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L1_2[5] = L6_2
  L1_2[6] = L7_2
  L1_2[7] = L8_2
  L2_2 = {}
  L3_2 = 16122370
  L4_2 = "0|-3|0xfe0101,7|-4|0xa80707,9|3|0xf00202,8|10|0xa20707,8|13|0xf90101,14|12|0xda0303,16|9|0xed0202,16|3|0xed0202,3|-6|0xd80404"
  L5_2 = 90
  L6_2 = 988
  L7_2 = 89
  L8_2 = 1346
  L9_2 = 384
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L2_2[5] = L7_2
  L2_2[6] = L8_2
  L2_2[7] = L9_2
  L3_2 = {}
  L4_2 = 16777215
  L5_2 = "-1|3|0xffffff,-7|3|0xffffff,-20|6|0xfcfcfc,-25|6|0xf6f6f6,-29|7|0xa7a7a4,-31|7|0xbabab8,-34|9|0xcfcfce,5|0|0x878684,4|2|0xffffff"
  L6_2 = 90
  L7_2 = 386
  L8_2 = 89
  L9_2 = 811
  L10_2 = 336
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L3_2[5] = L8_2
  L3_2[6] = L9_2
  L3_2[7] = L10_2
  L4_2 = {}
  L5_2 = 14026760
  L6_2 = "-1|-6|0xfc0101,0|-8|0xd30807,0|-10|0xff0101,11|-15|0xd20705,19|1|0xf90101,18|-6|0xd90404,36|-4|0xd00504,39|-11|0xfe0101,47|-3|0xfe0101"
  L7_2 = 90
  L8_2 = 386
  L9_2 = 89
  L10_2 = 811
  L11_2 = 336
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L1_2 = 1
  L2_2 = #L0_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = L0_2[L4_2]
    L6_2 = L6_2[1]
    L7_2 = L0_2[L4_2]
    L7_2 = L7_2[2]
    L8_2 = L0_2[L4_2]
    L8_2 = L8_2[3]
    L9_2 = L0_2[L4_2]
    L9_2 = L9_2[4]
    L10_2 = L0_2[L4_2]
    L10_2 = L10_2[5]
    L11_2 = L0_2[L4_2]
    L11_2 = L11_2[6]
    L12_2 = L0_2[L4_2]
    L12_2 = L12_2[7]
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    y = L6_2
    x = L5_2
    L5_2 = x
    if 0 < L5_2 then
      L5_2 = {}
      L6_2 = {}
      L7_2 = {}
      L8_2 = "000e00000fc00003f8000fffffe3fffffefe7fff3e00000107006001e01e03fc3f80ff0ff03f8ff84fc3f81c783c0387000070e7fffe1fffffc3fffff07ffffc0e1e0001c08000383f000707f000fe7fe01fc1fe01f01f8@00$你$346$27$26"
      L7_2[1] = L8_2
      L8_2 = 0
      L9_2 = 70
      L10_2 = 869
      L11_2 = 300
      L12_2 = "你"
      L13_2 = 445
      L14_2 = 86
      L15_2 = 650
      L16_2 = 337
      L17_2 = "36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # C5100F , 4F100F"
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L6_2[3] = L9_2
      L6_2[4] = L10_2
      L6_2[5] = L11_2
      L6_2[6] = L12_2
      L6_2[7] = L13_2
      L6_2[8] = L14_2
      L6_2[9] = L15_2
      L6_2[10] = L16_2
      L6_2[11] = L17_2
      L7_2 = {}
      L8_2 = {}
      L9_2 = "0fffff0fffffcfffffe7c1c07fe0e03ff0701ff8380e3c1c071fffff87ffffc1ffffc0780003f80007fc003bfef01df87c077c3fc39e07e1cf01f0e7803873c00079e000fcfffffc7ffffc1ffffc@0$的$371$25$25"
      L8_2[1] = L9_2
      L9_2 = 29
      L10_2 = 70
      L11_2 = 840
      L12_2 = 300
      L13_2 = "的"
      L14_2 = 445
      L15_2 = 86
      L16_2 = 650
      L17_2 = 337
      L18_2 = "36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # C5100F , 4F100F # C5100F , 4F100F"
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L7_2[3] = L10_2
      L7_2[4] = L11_2
      L7_2[5] = L12_2
      L7_2[6] = L13_2
      L7_2[7] = L14_2
      L7_2[8] = L15_2
      L7_2[9] = L16_2
      L7_2[10] = L17_2
      L7_2[11] = L18_2
      L8_2 = {}
      L9_2 = {}
      L10_2 = "01000f81c00fc1ffffc1ffffc3ffffc1fdf703fefb81f77dc0fbbee07ddf703eefb81f77dc0fbffff7dfffffffffffff7dc77fbee3bfde71c7ef38e0779c703bee381df7dc0ffffe07ffff01ffff@0$角$428$25$25"
      L9_2[1] = L10_2
      L10_2 = 47
      L11_2 = 70
      L12_2 = 812
      L13_2 = 300
      L14_2 = "角"
      L15_2 = 445
      L16_2 = 86
      L17_2 = 718
      L18_2 = 337
      L19_2 = "36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # C5100F , 4F100F # C5100F , 4F100F # C5100F , 4F100F"
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L8_2[3] = L11_2
      L8_2[4] = L12_2
      L8_2[5] = L13_2
      L8_2[6] = L14_2
      L8_2[7] = L15_2
      L8_2[8] = L16_2
      L8_2[9] = L17_2
      L8_2[10] = L18_2
      L8_2[11] = L19_2
      L9_2 = {}
      L10_2 = {}
      L11_2 = "03800003fffe03ffffc1fffff1fffff9fc1c1fee0e0ff70707f38383f9c1c1fcffe0ee7ff0773ff83b9ffc1dff8e0eff07077f8383bfc1c1c6e0e0e07070703c38381ffc1c0ffe3e03ff3f00ff1f@0$色$366$25$25"
      L10_2[1] = L11_2
      L11_2 = 75
      L12_2 = 70
      L13_2 = 784
      L14_2 = 300
      L15_2 = "色"
      L16_2 = 445
      L17_2 = 86
      L18_2 = 718
      L19_2 = 337
      L20_2 = "36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # 36B03D , 36113D # C5100F , 4F100F # C5100F , 4F100F # C5100F , 4F100F # C5100F , 4F100F"
      L9_2[1] = L10_2
      L9_2[2] = L11_2
      L9_2[3] = L12_2
      L9_2[4] = L13_2
      L9_2[5] = L14_2
      L9_2[6] = L15_2
      L9_2[7] = L16_2
      L9_2[8] = L17_2
      L9_2[9] = L18_2
      L9_2[10] = L19_2
      L9_2[11] = L20_2
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L5_2[3] = L8_2
      L5_2[4] = L9_2
      L6_2 = 1
      L7_2 = #L5_2
      L8_2 = 1
      for L9_2 = L6_2, L7_2, L8_2 do
        L10_2 = addTSOcrDictEx
        L11_2 = L5_2[L9_2]
        L11_2 = L11_2[1]
        L10_2 = L10_2(L11_2)
        L11_2 = tsFindText
        L12_2 = L10_2
        L13_2 = L5_2[L9_2]
        L13_2 = L13_2[6]
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2[7]
        L15_2 = L5_2[L9_2]
        L15_2 = L15_2[8]
        L16_2 = L5_2[L9_2]
        L16_2 = L16_2[9]
        L17_2 = L5_2[L9_2]
        L17_2 = L17_2[10]
        L18_2 = L5_2[L9_2]
        L18_2 = L18_2[11]
        L19_2 = 90
        L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
        if 0 < L11_2 then
          L13_2 = snapshot
          L14_2 = "3.png"
          L15_2 = L5_2[L9_2]
          L15_2 = L15_2[2]
          L15_2 = L11_2 - L15_2
          L16_2 = L5_2[L9_2]
          L16_2 = L16_2[3]
          L16_2 = L12_2 + L16_2
          L17_2 = L5_2[L9_2]
          L17_2 = L17_2[4]
          L17_2 = L11_2 + L17_2
          L18_2 = L5_2[L9_2]
          L18_2 = L18_2[5]
          L18_2 = L12_2 + L18_2
          L13_2(L14_2, L15_2, L16_2, L17_2, L18_2)
          L13_2 = true
          L14_2 = L5_2[L9_2]
          L14_2 = L14_2[2]
          L14_2 = L11_2 - L14_2
          L15_2 = L5_2[L9_2]
          L15_2 = L15_2[3]
          L15_2 = L12_2 + L15_2
          L16_2 = L5_2[L9_2]
          L16_2 = L16_2[4]
          L16_2 = L11_2 + L16_2
          L17_2 = L5_2[L9_2]
          L17_2 = L17_2[5]
          L17_2 = L12_2 + L17_2
          return L13_2, L14_2, L15_2, L16_2, L17_2
        end
      end
    end
  end
  L1_2 = {}
  L2_2 = {}
  L3_2 = {}
  L4_2 = "80000187ffff9c007f30003e60007cc000f9c001f3ffffe7fffffefb0ff8e61ff1cc3fe3987cef1079de21f3fce3e7ffffcfffff9fc03f30003e60007ce001f9fe07f1ffffc3ffc@000$面$328$23$25"
  L3_2[1] = L4_2
  L4_2 = 138
  L5_2 = 80
  L6_2 = 732
  L7_2 = 310
  L8_2 = "面"
  L9_2 = 426
  L10_2 = 39
  L11_2 = 1324
  L12_2 = 499
  L13_2 = "C60B0F , 360B0F # C61519 , 361519 # CB0B0F , 310B0F # D51519 , 3B1519 # DF1F23 , 451F23 # D51519 , 3B1519"
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L2_2[5] = L7_2
  L2_2[6] = L8_2
  L2_2[7] = L9_2
  L2_2[8] = L10_2
  L2_2[9] = L11_2
  L2_2[10] = L12_2
  L2_2[11] = L13_2
  L3_2 = {}
  L4_2 = {}
  L5_2 = "00000138c0039c7803ce1e03c70783c381ff81c0ffc0f1ffe03ffff81ff01e07f00600000001c00000e3f01871fc0c381f061c01030e00008f0000c7c00077fffff7fffff9fffffc3800001c0000@0$对$261$25$25"
  L4_2[1] = L5_2
  L5_2 = 170
  L6_2 = 80
  L7_2 = 700
  L8_2 = 310
  L9_2 = "对"
  L10_2 = 426
  L11_2 = 39
  L12_2 = 1324
  L13_2 = 499
  L14_2 = "D51519 , 3B1519 # D51519 , 3B1519"
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L3_2[5] = L8_2
  L3_2[6] = L9_2
  L3_2[7] = L10_2
  L3_2[8] = L11_2
  L3_2[9] = L12_2
  L3_2[10] = L13_2
  L3_2[11] = L14_2
  L4_2 = {}
  L5_2 = {}
  L6_2 = "0000c00820e00e38f007fdf007ffffe7fffffbfffffffffdfffffefbbff77dcffbbee7fddf73feefb9ff77dcffbbfe7fddff7feeffbff77ffffbbffffddff7fefff9ff7ff8f3bffc21c000$着$449$25$24"
  L5_2[1] = L6_2
  L6_2 = 202
  L7_2 = 80
  L8_2 = 668
  L9_2 = 310
  L10_2 = "着"
  L11_2 = 426
  L12_2 = 39
  L13_2 = 1324
  L14_2 = 499
  L15_2 = "D51519 , 3B1519 # D51519 , 3B1519 # D51519 , 3B1519"
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L4_2[8] = L12_2
  L4_2[9] = L13_2
  L4_2[10] = L14_2
  L4_2[11] = L15_2
  L5_2 = {}
  L6_2 = {}
  L7_2 = "001000001c00003e00007fc003fffffbfffffbc0000080000001c03801c03c0fc0fc0fe0fc0f83f01381e008c0000420000619ffff0dffff86ffff0306000100000080800040fc00303f001f03f80f80fc070000@000$你$238$25$27"
  L6_2[1] = L7_2
  L7_2 = 234
  L8_2 = 80
  L9_2 = 636
  L10_2 = 310
  L11_2 = "你"
  L12_2 = 426
  L13_2 = 39
  L14_2 = 1324
  L15_2 = 499
  L16_2 = "D51519 , 3B1519 # D51519 , 3B1519 # D51519 , 3B1519 # D51519 , 3B1519"
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L5_2[4] = L9_2
  L5_2[5] = L10_2
  L5_2[6] = L11_2
  L5_2[7] = L12_2
  L5_2[8] = L13_2
  L5_2[9] = L14_2
  L5_2[10] = L15_2
  L5_2[11] = L16_2
  L6_2 = {}
  L7_2 = {}
  L8_2 = "0fffff07ffff8f0300c780806fc04013e020087010041c1c060fffff03ffff80600000600003f80003f8000bf0e00cf83806380f810c03e0860010630000618000f0e000f87ffff83ffff8$的$241$25$24"
  L7_2[1] = L8_2
  L8_2 = 266
  L9_2 = 80
  L10_2 = 604
  L11_2 = 310
  L12_2 = "的"
  L13_2 = 426
  L14_2 = 39
  L15_2 = 1324
  L16_2 = 499
  L17_2 = "D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519"
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L6_2[4] = L10_2
  L6_2[5] = L11_2
  L6_2[6] = L12_2
  L6_2[7] = L13_2
  L6_2[8] = L14_2
  L6_2[9] = L15_2
  L6_2[10] = L16_2
  L6_2[11] = L17_2
  L7_2 = {}
  L8_2 = {}
  L9_2 = "01000701c00781ffff01ffff01fbee01f9e703ec7181e638c0731c60398e301cc7180e779c073ffff3dffff9fef38cfe39c23f1c610f8e3080c71860638c3033ce381df79c0ffffc03fffe$角$315$25$24"
  L8_2[1] = L9_2
  L9_2 = 298
  L10_2 = 80
  L11_2 = 572
  L12_2 = 310
  L13_2 = "角"
  L14_2 = 426
  L15_2 = 39
  L16_2 = 1324
  L17_2 = 499
  L18_2 = "D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519"
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L7_2[5] = L12_2
  L7_2[6] = L13_2
  L7_2[7] = L14_2
  L7_2[8] = L15_2
  L7_2[9] = L16_2
  L7_2[10] = L17_2
  L7_2[11] = L18_2
  L8_2 = {}
  L9_2 = {}
  L10_2 = "03000003dffc01ffffc1ffffe1f83831dc0c0bce0605e30302718181b8c1c0dc7fe06e3ff0371ff81b9f3c0dfe0e06ff03033f81818dc0c0c0e060403070201c38100ff81807fc7c01fe3e$色$275$25$24"
  L9_2[1] = L10_2
  L10_2 = 330
  L11_2 = 80
  L12_2 = 540
  L13_2 = 310
  L14_2 = "色"
  L15_2 = 568
  L16_2 = 146
  L17_2 = 1063
  L18_2 = 312
  L19_2 = "D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519 # D51519, 3B1519"
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L8_2[4] = L12_2
  L8_2[5] = L13_2
  L8_2[6] = L14_2
  L8_2[7] = L15_2
  L8_2[8] = L16_2
  L8_2[9] = L17_2
  L8_2[10] = L18_2
  L8_2[11] = L19_2
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L1_2[5] = L6_2
  L1_2[6] = L7_2
  L1_2[7] = L8_2
  L2_2 = 1
  L3_2 = #L1_2
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = addTSOcrDictEx
    L7_2 = L1_2[L5_2]
    L7_2 = L7_2[1]
    L6_2 = L6_2(L7_2)
    L7_2 = tsFindText
    L8_2 = L6_2
    L9_2 = L1_2[L5_2]
    L9_2 = L9_2[6]
    L10_2 = L1_2[L5_2]
    L10_2 = L10_2[7]
    L11_2 = L1_2[L5_2]
    L11_2 = L11_2[8]
    L12_2 = L1_2[L5_2]
    L12_2 = L12_2[9]
    L13_2 = L1_2[L5_2]
    L13_2 = L13_2[10]
    L14_2 = L1_2[L5_2]
    L14_2 = L14_2[11]
    L15_2 = 90
    L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    if 0 < L7_2 then
      L9_2 = snapshot
      L10_2 = "3.png"
      L11_2 = L1_2[L5_2]
      L11_2 = L11_2[2]
      L11_2 = L7_2 - L11_2
      L12_2 = L1_2[L5_2]
      L12_2 = L12_2[3]
      L12_2 = L8_2 + L12_2
      L13_2 = L1_2[L5_2]
      L13_2 = L13_2[4]
      L13_2 = L7_2 + L13_2
      L14_2 = L1_2[L5_2]
      L14_2 = L14_2[5]
      L14_2 = L8_2 + L14_2
      L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
      L9_2 = true
      L10_2 = L1_2[L5_2]
      L10_2 = L10_2[2]
      L10_2 = L7_2 - L10_2
      L11_2 = L1_2[L5_2]
      L11_2 = L11_2[3]
      L11_2 = L8_2 + L11_2
      L12_2 = L1_2[L5_2]
      L12_2 = L12_2[4]
      L12_2 = L7_2 + L12_2
      L13_2 = L1_2[L5_2]
      L13_2 = L13_2[5]
      L13_2 = L8_2 + L13_2
      return L9_2, L10_2, L11_2, L12_2, L13_2
    end
  end
  L2_2 = false
  return L2_2
end

_ENV[L0_1] = L1_1
