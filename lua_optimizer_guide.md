
# Lua代码优化器使用指南

## 功能特性

1. **require语句优化**
   - 将分散的require调用合并为单行
   - 消除中间变量

2. **函数调用优化**
   - 简化函数调用模式
   - 减少临时变量使用

3. **返回值赋值优化**
   - 优化多返回值赋值
   - 直接赋值给目标变量

4. **条件语句优化**
   - 简化if-goto模式
   - 移除不必要的标签

## 使用方法

### 命令行使用
```bash
python simple_lua_optimizer.py input.lua output.lua
```

### Python代码使用
```python
from simple_lua_optimizer import SimpleLuaOptimizer

optimizer = SimpleLuaOptimizer()
optimized_code = optimizer.optimize_file("input.lua", "output.lua")
```

## 优化示例

### 优化前
```lua
local L0_1, L1_1, L2_1, L3_1
L0_1 = require
L1_1 = "TSLib"
L0_1(L1_1)
L0_1 = require
L1_1 = "红尘试炼"
L0_1(L1_1)
```

### 优化后
```lua
local L0_1, L1_1, L2_1, L3_1
require("TSLib")
require("红尘试炼")
```

## 注意事项

1. 优化器主要针对unluac反编译输出
2. 建议在优化前备份原始文件
3. 优化后请测试代码功能是否正常
4. 复杂的控制流可能需要手动调整

## 技术限制

- 目前主要处理简单的模式匹配
- 复杂的goto语句优化仍在改进中
- 变量作用域分析相对简单
