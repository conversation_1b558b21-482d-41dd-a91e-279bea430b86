local L0_1, L1_1, L2_1
lzzh = "asd13546"
lzmm = "asd13546|D2D166EA59CDC351"

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = ocrInfo
  L1_2 = "haoi23"
  L2_2 = lzzh
  L3_2 = lzmm
  L0_2(L1_2, L2_2, L3_2)
  L0_2 = ocrScreen
  L1_2 = 345
  L2_2 = 50
  L3_2 = 1014
  L4_2 = 516
  L5_2 = 6001
  L6_2 = 60
  L7_2 = 1
  L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  tid = L1_2
  text = L0_2
  L0_2 = snapshot
  L1_2 = "验证.png"
  L2_2 = 345
  L3_2 = 50
  L4_2 = 1014
  L5_2 = 516
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
  L0_2 = type
  L1_2 = text
  L0_2 = L0_2(L1_2)
  if L0_2 == "string" then
    L0_2 = nLog
    L1_2 = text
    L2_2 = 1
    L0_2(L1_2, L2_2)
    L0_2 = nLog
    L1_2 = text
    L0_2(L1_2)
    L0_2 = nLog
    L1_2 = tid
    L2_2 = 1
    L0_2(L1_2, L2_2)
    L0_2 = strSplit
    L1_2 = text
    L2_2 = "|"
    L0_2 = L0_2(L1_2, L2_2)
    data = L0_2
    L0_2 = 1
    L1_2 = data
    L1_2 = #L1_2
    L2_2 = 1
    for L3_2 = L0_2, L1_2, L2_2 do
      L4_2 = strSplit
      L5_2 = data
      L5_2 = L5_2[L3_2]
      L6_2 = ","
      L4_2 = L4_2(L5_2, L6_2)
      data2 = L4_2
      L4_2 = data2
      L4_2 = L4_2[1]
      L4_2 = L4_2 + 342
      x = L4_2
      L4_2 = data2
      L4_2 = L4_2[2]
      L4_2 = L4_2 + 60
      y = L4_2
      L4_2 = tap
      L5_2 = x
      L6_2 = y
      L4_2(L5_2, L6_2)
    end
  else
    L0_2 = toast
    L1_2 = "打码识别，重新打码"
    L2_2 = 1
    L0_2(L1_2, L2_2)
    L0_2 = _ENV["速安打码点击"]
    return L0_2()
  end
end

_ENV["速安打码点击"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L2_2 = {}
  while true do
    L3_2 = string
    L3_2 = L3_2.find
    L4_2 = A0_2
    L5_2 = A1_2
    L3_2 = L3_2(L4_2, L5_2)
    if not L3_2 then
      L4_2 = #L2_2
      L4_2 = L4_2 + 1
      L2_2[L4_2] = A0_2
      break
    end
    L4_2 = string
    L4_2 = L4_2.sub
    L5_2 = A0_2
    L6_2 = 1
    L7_2 = L3_2 - 1
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    L5_2 = #L2_2
    L5_2 = L5_2 + 1
    L2_2[L5_2] = L4_2
    L5_2 = string
    L5_2 = L5_2.sub
    L6_2 = A0_2
    L7_2 = L3_2 + 1
    L8_2 = #A0_2
    L5_2 = L5_2(L6_2, L7_2, L8_2)
    A0_2 = L5_2
  end
  L3_2 = ""
  L4_2 = 1
  while true do
    L5_2 = L2_2[L4_2]
    if L5_2 then
      L5_2 = L3_2
      L6_2 = L2_2[L4_2]
      L3_2 = L5_2 .. L6_2
      L4_2 = L4_2 + 1
    else
      break
    end
  end
  return L3_2
end

LuaReomve = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  h = 1
  b = 12
  L1_2 = A0_2
  L2_2 = 1
  L3_2 = #L1_2
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = h
    L6_2 = L6_2 + 3
    h = L6_2
    L6_2 = b
    L6_2 = L6_2 + 3
    b = L6_2
    L6_2 = string
    L6_2 = L6_2.sub
    L7_2 = L1_2
    L8_2 = h
    L9_2 = b
    L6_2 = L6_2(L7_2, L8_2, L9_2)
    _ENV["返回四字成语"] = L6_2
    _ENV["分割成语"] = "冲昏头脑,患难之交,荒无人烟,光芒万丈,回光返照,回天之力,踌躇不决,福星高照,花容月貌,欢天喜地,画地为牢,回天乏术,赤膊上阵,杜渐防微,一夫当关,万夫莫开,马革裹尸,赤膊上阵,乌合之众,四面楚歌,用兵如神,有勇无谋,全民皆兵,声东击西,攻其不备,出其不意,坚壁清野,揭竿而起,围魏救赵,四面楚歌,作壁上观,暗渡陈仓,城下之盟,杀身成仁,纸上谈兵,风声鹤唳,草木皆兵,步步为营,望梅止渴,运畴帷幄,一鼓作气,再衰三竭,打草惊蛇,坚壁清野,揭竿而起,知己知彼,百战百胜,运筹帷幄,决胜千里,余勇可贾,乌合之众,枪林弹雨,硝烟弥漫,刀光剑影,金戈铁马,炮火连天,血肉横飞,胡服骑射,纸上谈兵,邯郸学步,负荆请罪,花好月圆,冲锋陷阵,愁肠百结"
    L6_2 = 1
    L7_2 = 9999
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = strSplit
      L11_2 = _ENV["分割成语"]
      L12_2 = ","
      L10_2 = L10_2(L11_2, L12_2)
      _ENV["分割"] = L10_2
      L10_2 = _ENV["分割"]
      L10_2 = L10_2[L9_2]
      _ENV["哈哈哈"] = L10_2
      L10_2 = _ENV["哈哈哈"]
      if L10_2 ~= nil then
        L10_2 = _ENV["返回四字成语"]
        L11_2 = _ENV["哈哈哈"]
        if L10_2 == L11_2 then
          L10_2 = _ENV["返回四字成语"]
          return L10_2
        end
      else
        break
      end
    end
  end
end

_ENV["获取屏幕上的字匹配"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  str1 = A0_2
  number = 1
  ii = 0
  while "c" do
    L1_2 = string
    L1_2 = L1_2.sub
    L2_2 = str1
    L3_2 = number
    L4_2 = number
    L1_2 = L1_2(L2_2, L3_2, L4_2)
    c = L1_2
    L1_2 = string
    L1_2 = L1_2.byte
    L2_2 = c
    L1_2 = L1_2(L2_2)
    b = L1_2
    L1_2 = b
    if 128 < L1_2 then
      L1_2 = string
      L1_2 = L1_2.sub
      L2_2 = str1
      L3_2 = number
      L4_2 = number
      L4_2 = L4_2 + 2
      L1_2 = L1_2(L2_2, L3_2, L4_2)
      hhhh = L1_2
      L1_2 = number
      L1_2 = L1_2 + 3
      number = L1_2
      L1_2 = ii
      L1_2 = L1_2 + 1
      ii = L1_2
      L1_2 = fwShowWnd
      L2_2 = "wid"
      L3_2 = 0
      L4_2 = 0
      L5_2 = 961
      L6_2 = 219
      L7_2 = 1
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
      L1_2 = fwShowTextView
      L2_2 = "wid"
      L3_2 = "textid"
      L4_2 = "请点击下面   "
      L5_2 = "[  "
      L6_2 = hhhh
      L7_2 = "  ]"
      L8_2 = "   字"
      L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2 .. L8_2
      L5_2 = "center"
      L6_2 = "FFFFFF"
      L7_2 = "FF0000"
      L8_2 = 20
      L9_2 = 0
      L10_2 = 352
      L11_2 = 141
      L12_2 = 961
      L13_2 = 219
      L14_2 = 1
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      L1_2 = mSleep
      L2_2 = 2000
      L1_2(L2_2)
    end
    L1_2 = number
    L2_2 = str1
    L2_2 = #L2_2
    if L1_2 > L2_2 then
      L1_2 = tap
      L2_2 = 1234
      L3_2 = 700
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = 3000
      L1_2(L2_2)
      break
    end
  end
end

_ENV["梦幻西游手游"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L0_2 = require
  L1_2 = "TSLib"
  L0_2(L1_2)
  L0_2 = require
  L1_2 = "sz"
  L0_2 = L0_2(L1_2)
  L1_2 = "SaeVmhnuu7dGWGjlDC9bHEGo"
  L2_2 = "L6G8oAwV4uafNTnwhWWZkO2ZXcqRtNK9"
  L3_2 = userPath
  L3_2 = L3_2()
  L4_2 = "/res/baiduAI.jpg"
  L3_2 = L3_2 .. L4_2
  L4_2 = snapshot
  L5_2 = L3_2
  L6_2 = 530
  L7_2 = 74
  L8_2 = 1510
  L9_2 = 278
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L4_2 = {}
  L4_2.ocrType = 4
  L5_2 = getAccessToken
  L6_2 = L1_2
  L7_2 = L2_2
  L5_2, L6_2 = L5_2(L6_2, L7_2)
  if L5_2 then
    L7_2 = baiduAI
    L8_2 = L6_2
    L9_2 = L3_2
    L10_2 = L4_2
    L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2)
    if L7_2 then
      L9_2 = L0_2.json
      L9_2 = L9_2.decode
      L10_2 = L8_2
      L9_2 = L9_2(L10_2)
      a = L9_2
      L9_2 = 0
      L10_2 = a
      L10_2 = #L10_2
      L11_2 = 1
      for L12_2 = L9_2, L10_2, L11_2 do
        L13_2 = nLog
        L14_2 = L12_2
        L13_2(L14_2)
      end
    else
      L9_2 = dialog
      L10_2 = "识别失败\n"
      L11_2 = L8_2
      L10_2 = L10_2 .. L11_2
      L9_2(L10_2)
    end
  else
    L7_2 = dialog
    L8_2 = "获取 access_token 失败\n"
    L9_2 = L6_2
    L8_2 = L8_2 .. L9_2
    L7_2(L8_2)
  end
end

_ENV["返回成语"] = L0_1
