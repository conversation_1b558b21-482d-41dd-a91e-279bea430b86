#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lua代码优化器
用于优化反编译后的Lua源码，提高可读性
主要功能：
1. 消除goto语句，还原复合条件语句
2. 消除冗余的中间变量
3. 简化代码结构
"""

import re
import os
import sys
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class VariableAssignment:
    """变量赋值信息"""
    var_name: str
    value: str
    line_num: int
    is_used: bool = False


class LuaOptimizer:
    """Lua代码优化器"""
    
    def __init__(self):
        self.variable_map: Dict[str, str] = {}  # 变量映射表
        self.assignments: List[VariableAssignment] = []  # 变量赋值记录
        self.goto_labels: Dict[str, int] = {}  # goto标签位置
        self.goto_statements: List[Tuple[int, str]] = []  # goto语句
        self.function_blocks: List[Tuple[int, int]] = []  # 函数块范围
        
    def optimize_file(self, input_file: str, output_file: str = None) -> str:
        """优化Lua文件"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
            
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        optimized_content = self.optimize_code(content)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            print(f"优化完成，输出文件: {output_file}")
        
        return optimized_content
    
    def optimize_code(self, code: str) -> str:
        """优化Lua代码"""
        lines = code.split('\n')
        
        # 第一步：分析代码结构
        self._analyze_code_structure(lines)
        
        # 第二步：消除冗余变量
        lines = self._eliminate_redundant_variables(lines)
        
        # 第三步：优化goto语句
        lines = self._optimize_goto_statements(lines)
        
        # 第四步：清理空行和格式化
        lines = self._cleanup_formatting(lines)
        
        return '\n'.join(lines)
    
    def _analyze_code_structure(self, lines: List[str]) -> None:
        """分析代码结构"""
        self.variable_map.clear()
        self.assignments.clear()
        self.goto_labels.clear()
        self.goto_statements.clear()
        self.function_blocks.clear()
        
        function_stack = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 检测函数开始
            if re.match(r'function\s+\w+\s*\(', stripped) or re.match(r'\w+\s*=\s*function\s*\(', stripped):
                function_stack.append(i)
            
            # 检测函数结束
            elif stripped == 'end' and function_stack:
                start = function_stack.pop()
                self.function_blocks.append((start, i))
            
            # 检测变量赋值
            self._detect_variable_assignment(line, i)
            
            # 检测goto标签
            self._detect_goto_labels(line, i)
            
            # 检测goto语句
            self._detect_goto_statements(line, i)
    
    def _detect_variable_assignment(self, line: str, line_num: int) -> None:
        """检测变量赋值"""
        # 匹配形如 L0_1 = xxx 的赋值
        match = re.match(r'\s*(L\d+_\d+)\s*=\s*(.+)', line)
        if match:
            var_name = match.group(1)
            value = match.group(2).strip()
            
            # 如果是简单的变量引用，建立映射关系
            if re.match(r'^[a-zA-Z_]\w*$', value):
                self.variable_map[var_name] = value
            
            assignment = VariableAssignment(var_name, value, line_num)
            self.assignments.append(assignment)
    
    def _detect_goto_labels(self, line: str, line_num: int) -> None:
        """检测goto标签"""
        # 匹配形如 ::label_name:: 的标签
        match = re.match(r'\s*::([\w_]+)::', line.strip())
        if match:
            label_name = match.group(1)
            self.goto_labels[label_name] = line_num
    
    def _detect_goto_statements(self, line: str, line_num: int) -> None:
        """检测goto语句"""
        # 匹配形如 goto label_name 的语句
        match = re.search(r'goto\s+([\w_]+)', line)
        if match:
            label_name = match.group(1)
            self.goto_statements.append((line_num, label_name))
    
    def _eliminate_redundant_variables(self, lines: List[str]) -> List[str]:
        """消除冗余的中间变量"""
        # 重新构建更智能的变量映射
        self._build_smart_variable_mapping(lines)

        result_lines = []
        skip_lines = set()

        # 分析变量的生命周期和使用模式
        var_lifecycle = self._analyze_variable_lifecycle(lines)

        for i, line in enumerate(lines):
            if i in skip_lines:
                continue

            # 检查是否可以内联这个赋值
            if self._can_inline_assignment(line, i, lines, var_lifecycle):
                # 将赋值内联到使用处
                inlined_result = self._inline_assignment(line, i, lines)
                if inlined_result:
                    result_lines.extend(inlined_result['lines'])
                    skip_lines.update(inlined_result['skip_lines'])
                    continue

            # 应用安全的变量替换
            optimized_line = self._safe_replace_variables(line, var_lifecycle)
            result_lines.append(optimized_line)

        return result_lines

    def _build_smart_variable_mapping(self, lines: List[str]) -> None:
        """构建智能的变量映射"""
        self.variable_map.clear()

        for i, line in enumerate(lines):
            # 匹配简单的变量赋值：L0_1 = someVar
            match = re.match(r'\s*(L\d+_\d+)\s*=\s*([a-zA-Z_]\w*)\s*$', line)
            if match:
                temp_var = match.group(1)
                real_var = match.group(2)

                # 确保这不是函数调用或复杂表达式
                if not self._is_function_or_complex_expr(real_var, lines, i):
                    self.variable_map[temp_var] = real_var

    def _is_function_or_complex_expr(self, var_name: str, lines: List[str], line_idx: int) -> bool:
        """检查变量是否是函数或复杂表达式"""
        # 检查下一行是否是函数调用
        if line_idx + 1 < len(lines):
            next_line = lines[line_idx + 1].strip()
            if re.match(f'{re.escape(var_name)}\\s*\\(', next_line):
                return True

        # 检查是否是已知的函数名
        common_functions = {'require', 'print', 'mSleep', 'getScreenSize', 'UINew', 'UILabel', 'UICombo'}
        return var_name in common_functions

    def _analyze_variable_lifecycle(self, lines: List[str]) -> Dict[str, Dict]:
        """分析变量的生命周期"""
        lifecycle = {}

        for var_name in self.variable_map.keys():
            lifecycle[var_name] = {
                'assignments': [],
                'usages': [],
                'scope_start': -1,
                'scope_end': -1
            }

            for i, line in enumerate(lines):
                # 查找赋值
                if re.search(f'\\b{re.escape(var_name)}\\s*=', line):
                    lifecycle[var_name]['assignments'].append(i)
                    if lifecycle[var_name]['scope_start'] == -1:
                        lifecycle[var_name]['scope_start'] = i

                # 查找使用
                elif re.search(f'\\b{re.escape(var_name)}\\b', line):
                    lifecycle[var_name]['usages'].append(i)
                    lifecycle[var_name]['scope_end'] = i

        return lifecycle
    
    def _can_inline_assignment(self, line: str, line_idx: int, lines: List[str], lifecycle: Dict) -> bool:
        """检查是否可以内联赋值"""
        match = re.match(r'\s*(L\d+_\d+)\s*=\s*(.+)', line)
        if not match:
            return False

        var_name = match.group(1)
        value = match.group(2).strip()

        # 只内联简单的赋值
        if not (re.match(r'^[a-zA-Z_]\w*$', value) or
                re.match(r'^"[^"]*"$', value) or
                re.match(r'^\d+$', value)):
            return False

        # 检查变量是否只使用一次，并且在合理的范围内
        if var_name in lifecycle:
            usages = lifecycle[var_name]['usages']
            assignments = lifecycle[var_name]['assignments']

            # 确保只有一次赋值和使用，且使用在赋值之后不远处
            if len(usages) == 1 and len(assignments) == 1:
                usage_line = usages[0]
                return usage_line > line_idx and usage_line < line_idx + 5

        return False

    def _inline_assignment(self, line: str, line_idx: int, lines: List[str]) -> Optional[Dict]:
        """内联赋值到使用处"""
        match = re.match(r'\s*(L\d+_\d+)\s*=\s*(.+)', line)
        if not match:
            return None

        var_name = match.group(1)
        value = match.group(2).strip()

        # 查找使用处
        for i in range(line_idx + 1, min(line_idx + 10, len(lines))):  # 限制搜索范围
            if re.search(f'\\b{re.escape(var_name)}\\b', lines[i]):
                # 替换使用处
                new_line = re.sub(f'\\b{re.escape(var_name)}\\b', value, lines[i])
                return {
                    'lines': [new_line],
                    'skip_lines': {i}
                }

        return None

    def _safe_replace_variables(self, line: str, lifecycle: Dict) -> str:
        """安全地替换变量引用"""
        result = line

        # 只替换确定安全的变量
        for var_name, replacement in self.variable_map.items():
            if var_name in lifecycle:
                # 确保这个变量有明确的生命周期
                if (len(lifecycle[var_name]['assignments']) == 1 and
                    len(lifecycle[var_name]['usages']) <= 2):

                    # 避免在赋值语句中替换左值
                    if not re.match(f'\\s*{re.escape(var_name)}\\s*=', line):
                        pattern = f'\\b{re.escape(var_name)}\\b'
                        result = re.sub(pattern, replacement, result)

        return result
    
    def _optimize_goto_statements(self, lines: List[str]) -> List[str]:
        """优化goto语句"""
        result_lines = []
        skip_lines = set()

        # 分析goto模式
        goto_patterns = self._analyze_goto_patterns(lines)

        # 处理复合条件语句
        i = 0
        while i < len(lines):
            if i in skip_lines:
                i += 1
                continue

            line = lines[i]

            # 尝试识别和优化if-goto模式
            if_goto_result = self._optimize_if_goto_pattern(i, lines)
            if if_goto_result:
                result_lines.extend(if_goto_result['lines'])
                skip_lines.update(if_goto_result['skip_lines'])
                i = max(if_goto_result['skip_lines']) + 1 if if_goto_result['skip_lines'] else i + 1
                continue

            # 检查是否是可以优化的goto语句
            optimized = self._try_optimize_goto(line, i, lines, goto_patterns)
            if optimized:
                result_lines.extend(optimized['lines'])
                skip_lines.update(optimized['skip_lines'])
            else:
                # 移除goto标签行
                if not re.match(r'\s*::\w+::', line.strip()):
                    result_lines.append(line)

            i += 1

        return result_lines
    
    def _analyze_goto_patterns(self, lines: List[str]) -> Dict:
        """分析goto模式"""
        patterns = {
            'if_else_chains': [],
            'loop_breaks': [],
            'simple_jumps': []
        }

        # 分析goto模式（简化版本）
        for i, line in enumerate(lines):
            if 'goto' in line and 'if' in line:
                patterns['if_else_chains'].append(i)

        return patterns
    
    def _optimize_if_goto_pattern(self, start_line: int, lines: List[str]) -> Optional[Dict]:
        """优化if-goto模式，还原复合条件语句"""
        line = lines[start_line].strip()

        # 匹配if条件语句后跟goto的模式
        if_match = re.match(r'if\s+(.+?)\s+then', line)
        if not if_match:
            return None

        condition = if_match.group(1)

        # 查找下一行是否是goto语句
        next_line_idx = start_line + 1
        if next_line_idx >= len(lines):
            return None

        next_line = lines[next_line_idx].strip()
        goto_match = re.match(r'goto\s+([\w_]+)', next_line)
        if not goto_match:
            return None

        label_name = goto_match.group(1)

        # 查找标签位置
        label_line = self._find_label_line(label_name, lines)
        if label_line is None:
            return None

        # 查找else分支的内容
        else_start = next_line_idx + 1
        else_content = []

        for i in range(else_start, label_line):
            if not re.match(r'\s*::\w+::', lines[i].strip()):
                else_content.append(lines[i])

        # 查找if分支的内容（标签之后的内容）
        if_content = []
        if_end = self._find_if_block_end(label_line + 1, lines)

        for i in range(label_line + 1, if_end):
            if not re.match(r'\s*::\w+::', lines[i].strip()):
                if_content.append(lines[i])

        # 构建优化后的if-else语句
        result_lines = []
        indent = self._get_line_indent(lines[start_line])

        # 反转条件逻辑
        reversed_condition = self._reverse_condition(condition)

        if else_content and if_content:
            # 完整的if-else结构
            result_lines.append(f"{indent}if {reversed_condition} then")
            result_lines.extend([indent + "  " + line.lstrip() for line in else_content])
            result_lines.append(f"{indent}else")
            result_lines.extend([indent + "  " + line.lstrip() for line in if_content])
            result_lines.append(f"{indent}end")
        elif else_content:
            # 只有else分支
            result_lines.append(f"{indent}if {reversed_condition} then")
            result_lines.extend([indent + "  " + line.lstrip() for line in else_content])
            result_lines.append(f"{indent}end")
        elif if_content:
            # 只有if分支
            result_lines.append(f"{indent}if {condition} then")
            result_lines.extend([indent + "  " + line.lstrip() for line in if_content])
            result_lines.append(f"{indent}end")

        # 计算需要跳过的行
        skip_lines = set(range(start_line, if_end))

        return {
            'lines': result_lines,
            'skip_lines': skip_lines
        }

    def _reverse_condition(self, condition: str) -> str:
        """反转条件表达式"""
        condition = condition.strip()

        # 处理比较操作符
        if ' == ' in condition:
            return condition.replace(' == ', ' ~= ')
        elif ' ~= ' in condition:
            return condition.replace(' ~= ', ' == ')
        elif ' < ' in condition:
            return condition.replace(' < ', ' >= ')
        elif ' > ' in condition:
            return condition.replace(' > ', ' <= ')
        elif ' <= ' in condition:
            return condition.replace(' <= ', ' > ')
        elif ' >= ' in condition:
            return condition.replace(' >= ', ' < ')
        elif condition.startswith('not '):
            return condition[4:].strip()
        else:
            return f"not ({condition})"

    def _find_label_line(self, label_name: str, lines: List[str]) -> Optional[int]:
        """查找标签行"""
        for i, line in enumerate(lines):
            if re.match(f'\\s*::{re.escape(label_name)}::', line.strip()):
                return i
        return None

    def _find_if_block_end(self, start: int, lines: List[str]) -> int:
        """查找if块的结束位置"""
        # 简单实现：查找下一个函数或文件结束
        for i in range(start, len(lines)):
            line = lines[i].strip()
            if (line.startswith('function ') or
                line.startswith('end') or
                line.startswith('local ') or
                re.match(r'\w+\s*=\s*function', line)):
                return i
        return len(lines)

    def _get_line_indent(self, line: str) -> str:
        """获取行的缩进"""
        return line[:len(line) - len(line.lstrip())]

    def _try_optimize_goto(self, line: str, line_num: int, lines: List[str], patterns: Dict) -> Optional[Dict]:
        """尝试优化goto语句"""
        # 检查是否包含goto
        goto_match = re.search(r'goto\s+([\w_]+)', line)
        if not goto_match:
            return None

        label_name = goto_match.group(1)

        # 查找对应的标签位置
        label_line = self._find_label_line(label_name, lines)
        if label_line is None:
            return None

        # 简单的goto优化：如果goto跳转到下一个有效行，则删除goto
        next_valid_line = self._find_next_valid_line(line_num, lines)
        if next_valid_line == label_line:
            return {
                'lines': [re.sub(r'goto\s+[\w_]+', '', line).strip()],
                'skip_lines': set()
            }

        # 使用模式信息进行更复杂的优化（预留接口）
        if patterns and line_num in patterns.get('if_else_chains', []):
            # 这里可以添加更复杂的优化逻辑
            pass

        return None
    
    def _find_next_valid_line(self, start_line: int, lines: List[str]) -> int:
        """查找下一个有效代码行"""
        for i in range(start_line + 1, len(lines)):
            line = lines[i].strip()
            if line and not line.startswith('--') and not re.match(r'::\w+::', line):
                return i
        return -1
    
    def _cleanup_formatting(self, lines: List[str]) -> List[str]:
        """清理格式化"""
        result = []
        prev_empty = False
        
        for line in lines:
            # 移除多余的空行
            if not line.strip():
                if not prev_empty:
                    result.append(line)
                    prev_empty = True
            else:
                result.append(line)
                prev_empty = False
        
        return result


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python lua_optimizer.py <输入文件> [输出文件]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not output_file:
        # 自动生成输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_optimized.lua"
    
    optimizer = LuaOptimizer()
    
    try:
        optimizer.optimize_file(input_file, output_file)
        print("优化完成！")
    except Exception as e:
        print(f"优化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
