local start, getRndNum, L2_1, L3_1
start = require
getRndNum = "TSLib"
start(getRndNum)
start = require
getRndNum = "红尘试炼"
start(getRndNum)
start = require
getRndNum = "核心调用库"
start(getRndNum)
start = require
getRndNum = "无名打码"
start(getRndNum)
start = require
getRndNum = "通用传送库"
start(getRndNum)
start = require
getRndNum = "颜色库"
start(getRndNum)
start = require
getRndNum = "res"
start(getRndNum)
start = require
getRndNum = "share"
start(getRndNum)
start = require
getRndNum = "Colorful"
start(getRndNum)
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
start = require
getRndNum = "押镖调用库"
start(getRndNum)
start = require
getRndNum = "PublicFunc"
start(getRndNum)
start = require
getRndNum = "FlightFlag"
start(getRndNum)
start = require
getRndNum = "MyGameData"
start(getRndNum)
start = require
getRndNum = "PetTreatment"
start(getRndNum)
start = require
getRndNum = "登录模式"
start(getRndNum)
start = require
getRndNum = "初出茅庐"
start(getRndNum)
start = init
getRndNum = 1
start(getRndNum)
start = require
getRndNum = "无名打码"
start(getRndNum)
start = require
getRndNum = "GameCJData"
start(getRndNum)
start = require
getRndNum = "GameTaskData"
start(getRndNum)
start = require
getRndNum = "Calligraphy"
start(getRndNum)
start = readFileString
getRndNum = userPath
getRndNum = getRndNum()
getRndNum = getRndNum .. L2_1
start = start(getRndNum)
text = start

function start(...)
  local lua_exit, Color, time
  lua_exit = mSleep
  Color = 1
  lua_exit(Color)
end

MyGetRunningAccess = start
start = getScreenSize
start, getRndNum = start()
hhhh = getRndNum
wwww = start
start = math
start = start.randomseed
getRndNum = getRndNum
getRndNum, L2_1, L3_1 = getRndNum()
start(getRndNum, L2_1, L3_1)
startTask = false
_ENV["卡顿掉帧"] = 1
_ENV["没点角色"] = ""

function start()
  local lua_exit, Color, time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2, L19_2
  lua_exit = getScreenSize
  lua_exit, Color = lua_exit()
  time = UINew
  L0_2 = 5
  os = "【基础设置】,【坐标价格】,【战斗设置】,【特殊设置】,【必看！！】"
  GOGAME = "运行脚本"
  os = "退出脚本"
  mSleep = "uiconfigfuben.dat"
  os = 0
  L0_2 = 180
  Color = 1920
  Color = 1080
  false = "255,255,250"
  time = "142,229,238"
  os = "tab"
  os = 31
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
  time = UILabel
  L0_2 = "功能选择:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "Gameorder1"
  os = "跑商,青龙,定时买药,摆摊卖二药,转移二药"
  GOGAME = "3"
  os = 280
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "循环上号:"
  os = 15
  GOGAME = "left"
  os = "0,0,0"
  mSleep = 220
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "循环上号"
  os = "单号模式,普通循环"
  GOGAME = "0"
  os = 300
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "循环数量:"
  os = 15
  GOGAME = "left"
  os = "0,0,0"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "角色数量"
  os = ""
  GOGAME = ""
  os = 12
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 150
  Color = 0
  false = false
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = "跑商票数:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = 1
  os = "UI_跑商票数"
  GOGAME = ""
  os = "999"
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 200
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = "青龙次数:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = 1
  os = "UI_青龙次数"
  GOGAME = ""
  os = "999"
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 200
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = "卡顿掉帧:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = 1
  os = "UI_卡顿掉帧"
  GOGAME = "1000=1秒"
  os = ""
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 260
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = "跑商路线:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_跑商路线"
  os = "鬼区智能,地府北俱,长安长寿,比价换线"
  GOGAME = "0"
  os = 280
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "赏金选择:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_赏金选择"
  os = "无赏金下号,跑满20赏金下号,优先赏金,不跑赏金"
  GOGAME = "3"
  os = 310
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "跑商模式:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_跑商模式"
  os = "普通跑商,联动跑商,抢货模式"
  GOGAME = "3"
  os = 310
  mSleep = 0
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = 1
  os = "跑商等级:"
  GOGAME = 15
  os = "left"
  mSleep = "0,0,0"
  os = 200
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 1
  os = "UI_跑商等级"
  GOGAME = "80,40,60"
  os = ""
  mSleep = 280
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 1
  os = "购买二药:"
  GOGAME = 15
  os = "left"
  mSleep = "0,0,0"
  os = 200
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = "UI_二药频率"
  os = "每小时买二药,每票买二药,不买二药"
  GOGAME = "3"
  os = 310
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UICheck
  L0_2 = 1
  os = "UI_完成不下线"
  GOGAME = "完成任务不下线"
  os = ""
  mSleep = 510
  os = 1
  L0_2 = "-"
  Color = 1
  Color = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UICheck
  L0_2 = 1
  os = "UI_签到"
  GOGAME = "签到"
  os = ""
  mSleep = 350
  os = 0
  L0_2 = "-"
  Color = 1
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = "卖体间隔:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = 1
  os = "卖体间隔time"
  GOGAME = "分钟"
  os = "180"
  mSleep = 14
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 240
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = "活力处置:"
  os = 15
  GOGAME = "left"
  os = "0,0,5"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_活力处置"
  os = "换修业,烹饪,飞行符,炼药,不操作"
  GOGAME = "0"
  os = 310
  mSleep = 0
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILine
  L0_2 = "center"
  time(L0_2)
  time = UILabel
  L0_2 = "旗帜设置"
  os = 16
  GOGAME = "center"
  os = "0,168,233"
  mSleep = -1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "长安城"
  os = 14
  GOGAME = "center"
  os = "0,0,5"
  mSleep = 150
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_qizi_长安"
  os = "红旗,白旗,黄旗,绿旗,蓝旗"
  GOGAME = "0"
  os = 190
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "傲来国"
  os = 14
  GOGAME = "center"
  os = "0,0,2"
  mSleep = 150
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_qizi_傲来"
  os = "关闭,黄旗,红旗,白旗,绿旗,蓝旗"
  GOGAME = "0"
  os = 190
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "朱紫国"
  os = 14
  GOGAME = "center"
  os = "0,0,2"
  mSleep = 150
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_qizi_朱紫"
  os = "关闭,白旗,红旗,黄旗,绿旗,蓝旗"
  GOGAME = "0"
  os = 190
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "长寿村"
  os = 14
  GOGAME = "center"
  os = "0,0,2"
  mSleep = 150
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_qizi_长寿"
  os = "关闭,绿旗,红旗,白旗,黄旗,蓝旗"
  GOGAME = "0"
  os = 190
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "帮派"
  os = 14
  GOGAME = "center"
  os = "0,0,2"
  mSleep = 150
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = "UI_qizi_帮派"
  os = "关闭,绿旗,红旗,白旗,黄旗,蓝旗"
  GOGAME = "0"
  os = 190
  time(L0_2, os, GOGAME, os)
  time = UILine
  L0_2 = "center"
  time(L0_2)
  time = UILabel
  L0_2 = "物品补充"
  os = 16
  GOGAME = "center"
  os = "0,168,233"
  mSleep = -1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "旗子价格"
  os = 14
  GOGAME = "left"
  os = "0,0,0"
  mSleep = 180
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_goumai_qizi_price"
  os = ""
  GOGAME = "80000"
  os = 14
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 223
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UICheck
  L0_2 = "UI_goumai_qizi_1"
  os = "购买位置"
  GOGAME = ""
  os = 360
  mSleep = 1
  os = "-"
  L0_2 = 1
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color)
  time = UICombo
  L0_2 = "UI_goumai_qizi_1_la"
  os = "长安城,傲来国,朱紫国,长寿村"
  GOGAME = "0"
  os = 270
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "摆摊坐标"
  os = 14
  GOGAME = "right"
  os = "0,0,2"
  mSleep = 180
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = "X"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_goumai_qizi_1_X"
  os = ""
  GOGAME = "457"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = "Y"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_goumai_qizi_1_Y"
  os = ""
  GOGAME = "166"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  Color = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = "上面四色常规下面蓝色"
  os = 14
  GOGAME = "left"
  os = "0,0,0"
  mSleep = 430
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = "UI_goumai_qizi_2"
  os = "购买位置"
  GOGAME = ""
  os = 360
  mSleep = 1
  os = "-"
  L0_2 = 1
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color)
  time = UICombo
  L0_2 = "UI_goumai_qizi_2_la"
  os = "长安城,傲来国,朱紫国,长寿村"
  GOGAME = "0"
  os = 270
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "摆摊坐标"
  os = 14
  GOGAME = "right"
  os = "0,0,2"
  mSleep = 180
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = "X"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_goumai_qizi_2_X"
  os = ""
  GOGAME = "457"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = "Y"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_goumai_qizi_2_Y"
  os = ""
  GOGAME = "166"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color)
  time = UILabel
  L0_2 = "物品出售"
  os = 16
  GOGAME = "center"
  os = "0,168,233"
  mSleep = -1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "二药价格"
  os = 14
  GOGAME = "left"
  os = "0,0,0"
  mSleep = 180
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_卖二药价格"
  os = ""
  GOGAME = "1222"
  os = 14
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 223
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UICombo
  L0_2 = "UI_卖二药地址"
  os = "建邺城,长寿村,傲来国,长安城"
  GOGAME = "0"
  os = 270
  mSleep = 1
  time(L0_2, os, GOGAME, os, mSleep)
  time = UILabel
  L0_2 = "摆摊坐标"
  os = 14
  GOGAME = "right"
  os = "0,0,2"
  mSleep = 180
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = "X"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_卖药坐标X"
  os = ""
  GOGAME = "85"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  Color = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = "Y"
  os = 14
  GOGAME = "center"
  os = "0,0,0"
  mSleep = 25
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = "UI_卖药坐标Y"
  os = ""
  GOGAME = "28"
  os = 15
  mSleep = "left"
  os = "0,0,0"
  L0_2 = "default"
  Color = 190
  Color = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color)
  time = UILabel
  L0_2 = 1
  os = "清空任务:"
  GOGAME = 15
  os = "left"
  mSleep = "0,0,0"
  os = 200
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 1
  os = "清空任务栏啊"
  GOGAME = "是,否"
  os = "1"
  mSleep = 250
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 1
  os = "转移FF:"
  GOGAME = 15
  os = "left"
  mSleep = "0,0,0"
  os = 200
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 1
  os = "UI_转移FF"
  GOGAME = "是,否"
  os = "1"
  mSleep = 250
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 2
  os = "长安:"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = 100
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "刀"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_刀"
  GOGAME = "6200"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "扇子"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_扇子"
  GOGAME = "5500"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "佛珠"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_佛珠"
  GOGAME = "9200"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "车夫坐标X"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 210
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_车夫x"
  GOGAME = "x"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "Y"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 30
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_车夫y"
  GOGAME = "y"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "长寿:"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = 100
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "面粉"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_面粉"
  GOGAME = "3950"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "鹿茸"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_鹿茸"
  GOGAME = "8900"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "符咒"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_符咒"
  GOGAME = "6500"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "出口坐标X"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 210
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_出口x"
  GOGAME = "x"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "Y"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 30
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_出口y"
  GOGAME = "y"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "地府:"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = 100
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "纸钱"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_纸钱"
  GOGAME = "4120"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "首饰"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_首饰"
  GOGAME = "6540"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "宝珠"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_宝珠"
  GOGAME = "10000"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "青龙入口X"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 210
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_青龙x"
  GOGAME = "x"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "Y"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 30
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_青龙y"
  GOGAME = "y"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "北俱:"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = 100
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "灯油"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_灯油"
  GOGAME = "5420"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "人参"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_人参"
  GOGAME = "9500"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "铃铛"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_铃铛"
  GOGAME = "6410"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "青龙中转X"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 210
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_青龙中转x"
  GOGAME = "x"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "Y"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 30
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_青龙中转y"
  GOGAME = "y"
  os = ""
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "傲来:"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = 100
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "酒"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_酒"
  GOGAME = "5920"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "帽子"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_帽子"
  GOGAME = "4800"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "盐"
  GOGAME = 13
  os = "left"
  mSleep = "0,0,0"
  os = 90
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 2
  os = "UI_盐"
  GOGAME = "8000"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 195
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 2
  os = "长期总有烤火风险，不建议大号跑"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "不写则按默认,可以不写,但别写错.不想买某个物品价格写1"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "如商品价格刷的超过上面限额，就不会购买了。可以适当提高默认限价"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 2
  os = "价格过低可能导致频繁换线或等刷不买物品！！"
  GOGAME = 14
  os = "left"
  mSleep = "222,0,0"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "角色战斗"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,18"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "rw_zdfs"
  GOGAME = "普通攻击,便捷法术1"
  os = "0"
  mSleep = 270
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 3
  os = "宝宝战斗"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,5"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "bb_zdfs"
  GOGAME = "攻宝宝,法宝宝"
  os = "0"
  mSleep = 270
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICombo
  L0_2 = 3
  os = "UI_zhandou_BB_selet"
  GOGAME = "地狱烈火,泰山压顶,奔雷咒,水漫金山"
  os = "0"
  mSleep = 270
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 3
  os = "人物加血:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_角色回复"
  GOGAME = "50%,70%,90%"
  os = "2"
  mSleep = 200
  os = 1
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "人物加蓝:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_角色回蓝"
  GOGAME = "50%,70%,90%"
  os = "2"
  mSleep = 250
  os = 1
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "宠物加血:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_宠物回复"
  GOGAME = "50%,70%,90%"
  os = "2"
  mSleep = 250
  os = 1
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "宠物加蓝:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_宠物回蓝"
  GOGAME = "50%,70%,90%"
  os = "2"
  mSleep = 250
  os = 0
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "补给方式"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,2"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_回复方式"
  GOGAME = "道具,住店/巫医"
  os = "0"
  mSleep = 200
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 3
  os = "补血道具:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "补血道具"
  GOGAME = "包子1,红罗羹"
  os = "0"
  mSleep = 250
  os = 1
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "补蓝道具:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 190
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "补蓝道具"
  GOGAME = "佛手1,绿罗羹"
  os = "0"
  mSleep = 250
  os = 0
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "是否转移二药:"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 300
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_转移二药"
  GOGAME = "不转移,下线前转移二药,每X票转移"
  os = "0"
  mSleep = 330
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UIEdit
  L0_2 = 3
  os = "UI_转移二药间隔"
  GOGAME = "每几票转移？"
  os = ""
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 350
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UIEdit
  L0_2 = 3
  os = "添加丢钱好友ID"
  GOGAME = "填写转移id"
  os = ""
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 400
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UICheck
  L0_2 = 3
  os = "UI_添加好友"
  GOGAME = "添加好友"
  os = ""
  mSleep = 450
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 3
  os = "上下不相关！暂时测试"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 220
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 3
  os = "UI_goumai_宝图_la"
  GOGAME = "长安城,建邺城"
  os = "0"
  mSleep = 270
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 3
  os = "购买宝图"
  GOGAME = 15
  os = "right"
  mSleep = "0,0,0"
  os = 180
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 3
  os = "X"
  GOGAME = 15
  os = "center"
  mSleep = "0,0,0"
  os = 25
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 3
  os = "UI_goumai_宝图_X"
  GOGAME = ""
  os = "455"
  mSleep = 14
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 190
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 3
  os = "Y"
  GOGAME = 15
  os = "center"
  mSleep = "0,0,0"
  os = 25
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 3
  os = "UI_goumai_宝图_Y"
  GOGAME = ""
  os = "155"
  mSleep = 14
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 190
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 3
  os = "宝图价格"
  GOGAME = 15
  os = "left"
  mSleep = "0,0,0"
  os = 180
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 3
  os = "UI_goumai_宝图_price"
  GOGAME = ""
  os = "25999"
  mSleep = 14
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 223
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UIEdit
  L0_2 = 3
  os = "预留金额"
  GOGAME = "准备剩多少钱"
  os = ""
  mSleep = 12
  os = "left"
  L0_2 = "5,0,0"
  Color = "default"
  Color = 320
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UICheck
  L0_2 = 4
  os = "UI_1级帮"
  GOGAME = "点不到总管勾选"
  os = ""
  mSleep = 520
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 4
  os = "UI_卡小地图"
  GOGAME = "卡小地图"
  os = ""
  mSleep = 450
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "跑商:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 150
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICheck
  L0_2 = 4
  os = "UI_跑商_屏蔽摆摊"
  GOGAME = "摆摊地图屏蔽摆摊"
  os = ""
  mSleep = 560
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 4
  os = "UI_合适都买"
  GOGAME = "便宜卖空去隔壁买"
  os = ""
  mSleep = 570
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 4
  os = "UI_抢货价钱"
  GOGAME = "抢货不看价钱"
  os = ""
  mSleep = 570
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "青龙:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 150
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICheck
  L0_2 = 4
  os = "UI_走入青龙"
  GOGAME = "步行到仓库"
  os = "0"
  mSleep = 500
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 4
  os = "UI_走入青龙中转"
  GOGAME = "步行先中转"
  os = ""
  mSleep = 500
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "定时买药:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 220
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 4
  os = "UI_定时买药"
  GOGAME = "维护时间,写分钟"
  os = ""
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 450
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 4
  os = "车夫和出口寻找方式:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 550
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 4
  os = "UI_车夫寻找方式"
  GOGAME = "填入坐标,自动识别"
  os = "0"
  mSleep = 290
  os = 1
  L0_2 = true
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 4
  os = "自动识别部分帮派不适用"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 550
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 4
  os = "XX:X0整分刷价后第几秒点商人:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 690
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 4
  os = "UI_跑商刷价等待"
  GOGAME = "默认第12秒,写数字"
  os = ""
  mSleep = 15
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 470
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 4
  os = "抢货模式用的！！！"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 550
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICheck
  L0_2 = 4
  os = "UI_回到地府"
  GOGAME = "抢货回地府"
  os = ""
  mSleep = 500
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 4
  os = "UI_仙玉补旗"
  GOGAME = "仙玉补旗"
  os = ""
  mSleep = 500
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "提醒方式:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 230
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 4
  os = "UI_msg"
  GOGAME = "响铃,震动提示,无声"
  os = ""
  mSleep = 250
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "家具放弃方式:"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = 150
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICombo
  L0_2 = 4
  os = "UI_家具放弃方式"
  GOGAME = "下线放弃,等待3分钟放弃"
  os = "0"
  mSleep = 290
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UILabel
  L0_2 = 4
  os = "帮派总管X"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 210
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 4
  os = "UI_帮派总管x"
  GOGAME = "x"
  os = "21"
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 4
  os = "Y"
  GOGAME = 14
  os = "left"
  mSleep = "0,0,0"
  os = 30
  L0_2 = 1
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UIEdit
  L0_2 = 4
  os = "UI_帮派总管y"
  GOGAME = "y"
  os = "23"
  mSleep = 13
  os = "left"
  L0_2 = "0,0,0"
  Color = "default"
  Color = 170
  false = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false)
  time = UILabel
  L0_2 = 5
  os = "本机分辨率高为："
  GOGAME = "hhhh"
  GOGAME = _ENV[GOGAME]
  os = "宽为："
  mSleep = "wwww"
  mSleep = _ENV[mSleep]
  os = os .. GOGAME .. os .. mSleep
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "抢货模式：适用于比较新的区，价格合适直接购买不去比价。刷价时间有波动你可以看你区情况和你网络情况去调整，抢货只支持长安长寿和地府北俱。别的路线利润太低。"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "联动模式：不适合比较新或货物不充足例如刷价后1分钟左右物品就卖完了，适合能买到第二次物品的区例如刷价后2-5分钟还有货的区，一个号也可以开联动"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "普通模式：不看时辰，不等刷价，只比胖瘦商人价格，低于你设定就会买"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "青龙需准备点位【帮派】【吴举人】【王夫人】【陈员外】青龙旗子,如果没有标准点位则选关闭！！！如果想步行进入帮派仓库需写仓库入口坐标，可以调试一下进入为止。部分太绕的地图不可使用"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "必须有长安旗子！！！长安城旗子必须有点位【驿站】【江南野外口】【酒店】【大唐国境口】！！！！"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "如果点补到车夫！！！注意修改关掉自动识别，选填入坐标，坐标是和车夫完全重叠的坐标"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "活力处置会按体力时间一并操作，获得的东西会存仓库，把仓库设置成普通仓库"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "如果帮派屋子格局特殊，太小或太大导致点总管不顺畅勾选特殊设置里的点不到总管"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UILabel
  L0_2 = 5
  os = "清理进程会关掉后台运行的软件！！可能包括本机ip工具！！"
  GOGAME = 16
  os = "left"
  mSleep = "0,168,233"
  os = -1
  L0_2 = 0
  time(L0_2, os, GOGAME, os, mSleep, os, L0_2)
  time = UICheck
  L0_2 = 5
  os = "UI_清理后台进程"
  GOGAME = "清理后台进程"
  os = ""
  mSleep = 550
  os = 0
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = UICheck
  L0_2 = 5
  os = "UI_上传截图"
  GOGAME = "上传截图"
  os = ""
  mSleep = 355
  os = 1
  time(L0_2, os, GOGAME, os, mSleep, os)
  time = "UIShow"
  time = _ENV[time]
  time()
end

SmUI = start

function start()
  local lua_exit, Color, time, L0_2, os, GOGAME
  lua_exit = delFile
  Color = userPath
  Color = Color()
  time = "/log/hblog.log"
  Color = Color .. time
  lua_exit(Color)
  lua_exit = _cmp_tb_cx
  Color = Color
  Color = Color["主界面"]
  time = {}
  L0_2 = 10
  os = 100
  time[1] = L0_2
  time[2] = os
  lua_exit = lua_exit(Color, time)
  if lua_exit == false then
    lua_exit = _print
    Color = "未进入游戏,开始执行进入游戏操作！"
    lua_exit(Color)
    lua_exit = _ENV["_游戏"]
    lua_exit = lua_exit["进入"]
    lua_exit()
  else
    lua_exit = _ENV["_功能"]
    lua_exit = lua_exit["屏蔽"]
    Color = "close"
    lua_exit(Color)
    lua_exit = _print
    Color = "正在游戏中"
    lua_exit(Color)
  end
  lua_exit = toast
  Color = "正在游戏中"
  time = 1
  lua_exit(Color, time)
  lua_exit = UI_DATU
  if lua_exit == "自动打图" then
    lua_exit = _ENV["_打图"]
    lua_exit = lua_exit["流程"]
    lua_exit = lua_exit()
    if lua_exit then
      lua_exit = _ENV["UI_自动卖图"]
      if lua_exit == "自动卖图" then
        lua_exit = _ENV["_卖图"]
        lua_exit = lua_exit["自动卖图"]
        Color = _ENV["UI_图源"]
        lua_exit(Color)
      end
    end
  else
    lua_exit = _ENV["UI_自动卖图"]
    if lua_exit == "自动卖图" then
      lua_exit = _ENV["_卖图"]
      lua_exit = lua_exit["自动卖图"]
      Color = _ENV["UI_图源"]
      lua_exit(Color)
      return
    else
      lua_exit = _ENV["UI_只丢图"]
      if lua_exit == "自动丢图" then
        lua_exit = _ENV["_卖图"]
        lua_exit = lua_exit["自动丢图"]
        Color = _ENV["UI_图源"]
        lua_exit(Color)
        return
      end
    end
  end
  lua_exit = _ENV["UI_完成后_操作"]
  if lua_exit == "完成后转移宝图" then
    lua_exit = _ENV["_卖图"]
    lua_exit = lua_exit["自动丢图"]
    Color = _ENV["UI_图源"]
    lua_exit(Color)
    return
  else
    return
  end
end

_ENV["打图流程"] = start

function start()
  local lua_exit, Color, time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2, L19_2, L20_2, L21_2
  _ENV["金钱不足"] = false
  _ENV["门派名字"] = ""
  _ENV["放弃豆斋"] = false
  _ENV["正在师门"] = false
  lua_exit = _ENV["UI_跑商路线"]
  if lua_exit ~= "地府北俱" then
    lua_exit = _ENV["UI_跑商路线"]
    if lua_exit ~= "比价换线" then
      lua_exit = _ENV["UI_跑商路线"]
      if lua_exit ~= "长安长寿" then
        lua_exit = _ENV["UI_跑商模式"]
        if lua_exit == "抢货模式" then
          end
          end
          end
          end
          _ENV["北俱地府"] = true
          goto lbl_20
        else
          _ENV["北俱地府"] = false
          lua_exit = _ENV["UI_赏金选择"]
          if lua_exit ~= "无赏金下号" then
          lua_exit = _ENV["UI_赏金选择"]
          if lua_exit ~= "跑满20赏金下号" then
          lua_exit = _ENV["UI_赏金选择"]
          if lua_exit ~= "优先赏金" then
          goto lbl_30
        end
      end
    end
  end
  _ENV["UI_赏金任务"] = "赏金任务"
  lua_exit = _ENV["UI_刀"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_刀"]
    lua_exit = lua_exit(Color)
    _ENV["刀价格"] = lua_exit
  else
    _ENV["刀价格"] = 6200
  end
  lua_exit = _ENV["UI_扇子"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_扇子"]
    lua_exit = lua_exit(Color)
    _ENV["扇子价格"] = lua_exit
  else
    _ENV["扇子价格"] = 5500
  end
  lua_exit = _ENV["UI_佛珠"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_佛珠"]
    lua_exit = lua_exit(Color)
    _ENV["佛珠价格"] = lua_exit
  else
    _ENV["佛珠价格"] = 9200
  end
  lua_exit = _ENV["UI_面粉"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_面粉"]
    lua_exit = lua_exit(Color)
    _ENV["面粉价格"] = lua_exit
  else
    _ENV["面粉价格"] = 3950
  end
  lua_exit = _ENV["UI_鹿茸"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_鹿茸"]
    lua_exit = lua_exit(Color)
    _ENV["鹿茸价格"] = lua_exit
  else
    _ENV["鹿茸价格"] = 8900
  end
  lua_exit = _ENV["UI_符咒"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_符咒"]
    lua_exit = lua_exit(Color)
    _ENV["符咒价格"] = lua_exit
  else
    _ENV["符咒价格"] = 6500
  end
  lua_exit = _ENV["UI_纸钱"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_纸钱"]
    lua_exit = lua_exit(Color)
    _ENV["纸钱价格"] = lua_exit
  else
    _ENV["纸钱价格"] = 4120
  end
  lua_exit = _ENV["UI_首饰"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_首饰"]
    lua_exit = lua_exit(Color)
    _ENV["首饰价格"] = lua_exit
  else
    _ENV["首饰价格"] = 6540
  end
  lua_exit = _ENV["UI_宝珠"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_宝珠"]
    lua_exit = lua_exit(Color)
    _ENV["宝珠价格"] = lua_exit
  else
    _ENV["宝珠价格"] = 10000
  end
  lua_exit = _ENV["UI_灯油"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_灯油"]
    lua_exit = lua_exit(Color)
    _ENV["灯油价格"] = lua_exit
  else
    _ENV["灯油价格"] = 5420
  end
  lua_exit = _ENV["UI_人参"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_人参"]
    lua_exit = lua_exit(Color)
    _ENV["人参价格"] = lua_exit
  else
    _ENV["人参价格"] = 9500
  end
  lua_exit = _ENV["UI_铃铛"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_铃铛"]
    lua_exit = lua_exit(Color)
    _ENV["铃铛价格"] = lua_exit
  else
    _ENV["铃铛价格"] = 6410
  end
  lua_exit = _ENV["UI_酒"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_酒"]
    lua_exit = lua_exit(Color)
    _ENV["酒价格"] = lua_exit
  else
    _ENV["酒价格"] = 5920
  end
  lua_exit = _ENV["UI_帽子"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_帽子"]
    lua_exit = lua_exit(Color)
    _ENV["帽子价格"] = lua_exit
  else
    _ENV["帽子价格"] = 4800
  end
  lua_exit = _ENV["UI_盐"]
  if lua_exit ~= "" then
    lua_exit = tonumber
    Color = _ENV["UI_盐"]
    lua_exit = lua_exit(Color)
    _ENV["盐价格"] = lua_exit
  else
    _ENV["盐价格"] = 8000
  end
  lua_exit = {}
  Color = _ENV["刀价格"]
  lua_exit.dao = Color
  Color = _ENV["佛珠价格"]
  lua_exit.fozhu = Color
  Color = _ENV["扇子价格"]
  lua_exit.shanzi = Color
  Color = _ENV["宝珠价格"]
  lua_exit.baozhu = Color
  Color = _ENV["纸钱价格"]
  lua_exit.money = Color
  Color = _ENV["首饰价格"]
  lua_exit.shoushi = Color
  Color = _ENV["盐价格"]
  lua_exit.salt = Color
  Color = _ENV["酒价格"]
  lua_exit.wine = Color
  Color = _ENV["帽子价格"]
  lua_exit.hat = Color
  Color = _ENV["面粉价格"]
  lua_exit.mianfen = Color
  Color = _ENV["鹿茸价格"]
  lua_exit.antler = Color
  Color = _ENV["符咒价格"]
  lua_exit.amulet = Color
  Color = _ENV["人参价格"]
  lua_exit.renshen = Color
  Color = _ENV["灯油价格"]
  lua_exit.dengyou = Color
  Color = _ENV["铃铛价格"]
  lua_exit.lingdang = Color
  proposedPrice = lua_exit
  lua_exit = init
  Color = 1
  lua_exit(Color)
  lua_exit = {}
  Color = {}
  time = "40-69师门"
  L0_2 = _ENV["获取门派名称"]
  Color[1] = time
  Color[2] = L0_2
  time = {}
  L0_2 = "卖体转钱"
  os = _ENV["卖体转钱任务"]
  time[1] = L0_2
  time[2] = os
  L0_2 = {}
  os = "押镖测试"
  GOGAME = _ENV["押镖任务"]
  L0_2[1] = os
  L0_2[2] = GOGAME
  os = {}
  GOGAME = "打图"
  os = _ENV["打图流程"]
  os[1] = GOGAME
  os[2] = os
  GOGAME = {}
  os = "探访奇闻"
  mSleep = _ENV["探访奇闻任务"]
  GOGAME[1] = os
  GOGAME[2] = mSleep
  os = {}
  mSleep = "红尘"
  os = _ENV["红尘试炼任务"]
  os[1] = mSleep
  os[2] = os
  mSleep = {}
  os = "起号"
  L0_2 = _ENV["起号任务"]
  mSleep[1] = os
  mSleep[2] = L0_2
  os = {}
  L0_2 = "70-155师门"
  Color = _ENV["百级师门任务"]
  os[1] = L0_2
  os[2] = Color
  L0_2 = {}
  Color = "摆摊卖二药"
  Color = _ENV["摆摊卖二药"]
  L0_2[1] = Color
  L0_2[2] = Color
  Color = {}
  Color = "跟队模式"
  false = _ENV["跟队模式"]
  Color[1] = Color
  Color[2] = false
  Color = {}
  false = "定时买药"
  time = _ENV["定时买药任务"]
  Color[1] = false
  Color[2] = time
  false = {}
  time = "青龙"
  L14_2 = _ENV["青龙任务"]
  false[1] = time
  false[2] = L14_2
  time = {}
  os = _ENV["跑商任务"]
  time[1] = L14_2
  time[2] = os
  L14_2 = {}
  os = "初出茅庐"
  L16_2 = _ENV["初出茅庐任务"]
  L14_2[1] = os
  L14_2[2] = L16_2
  os = {}
  os = _ENV["建邺探案任务"]
  os[1] = L16_2
  os[2] = os
  L16_2 = {}
  os = "测试邮箱识别"
  L18_2 = _ENV["获取充值账号"]
  L16_2[1] = os
  L16_2[2] = L18_2
  os = {}
  L19_2 = _ENV["买图转移"]
  os[1] = L18_2
  os[2] = L19_2
  L18_2 = {}
  L20_2 = _ENV["转移二药总流程"]
  L18_2[1] = L19_2
  L18_2[2] = L20_2
  lua_exit[1] = Color
  lua_exit[2] = time
  lua_exit[3] = L0_2
  lua_exit[4] = os
  lua_exit[5] = GOGAME
  lua_exit[6] = os
  lua_exit[7] = mSleep
  lua_exit[8] = os
  lua_exit[9] = L0_2
  lua_exit[10] = Color
  lua_exit[11] = Color
  lua_exit[12] = false
  lua_exit[13] = time
  lua_exit[14] = L14_2
  lua_exit[15] = os
  lua_exit[16] = L16_2
  lua_exit[17] = os
  lua_exit[18] = L18_2
  loginArray = lua_exit
  lua_exit = {}
  Color = Gameorder1
  time = Gameorder2
  L0_2 = Gameorder3
  lua_exit[1] = Color
  lua_exit[2] = time
  lua_exit[3] = L0_2
  sxljAR = lua_exit
  lua_exit = _ENV["UI_清理后台进程"]
  if lua_exit then
    lua_exit = closeAllApp
    Color = "com.touchsprite.android,com.netease.mhxyhtb"
    lua_exit(Color)
  end
  lua_exit = 1
  Color = sxljAR
  Color = #Color
  time = 1
  for L0_2 = lua_exit, Color, time do
    os = 1
    GOGAME = loginArray
    GOGAME = #GOGAME
    os = 1
    for mSleep = os, GOGAME, os do
      os = sxljAR
      os = os[L0_2]
      L0_2 = loginArray
      L0_2 = L0_2[mSleep]
      L0_2 = L0_2[1]
      if os == L0_2 then
        os = setVolumeLevel
        L0_2 = 0
        os(L0_2)
        os = loginArray
        os = os[mSleep]
        os = os[1]
        if os ~= "起号" then
          os = loginArray
          os = os[mSleep]
          os = os[1]
          if os == "红尘" then
            end
            end
            os = mSleep
            L0_2 = 10
            os(L0_2)
            os = _ENV["_打图"]
            os = os["初始"]
            os()
            goto lbl_337
          else
            os = loginArray
            os = os[mSleep]
            os = os[1]
            if os == "定时买药" then
            os = loginArray
            os = os[mSleep]
            os = os[2]
            os()
            else
            os = _ENV["_打图"]
            os = os["初始"]
            os()
          end
        end
        os = toast
        L0_2 = "开始执行:"
        Color = loginArray
        Color = Color[mSleep]
        Color = Color[1]
        L0_2 = L0_2 .. Color
        os(L0_2)
        os = _ENV["通用功能"]
        os = os["关闭"]
        os()
        os = _ENV["通用功能"]
        os = os["任务栏打开"]
        os()
        os = _ENV["清空任务栏啊"]
        if os == "是" then
          os = myBlockcolor
          L0_2 = sevenColor
          L0_2 = L0_2["跑商任务"]
          os = os(L0_2)
          if os == false then
            os = _ENV["通用功能"]
            os = os["任务栏清理"]
            os()
          end
        end
        os = _ENV["UI_测试上号充值"]
        if os then
          os = _ENV["自动充值流程"]
          os()
        end
        os = loginArray
        os = os[mSleep]
        os = os[1]
        if os == "跑商" then
          os = myBlockcolor
          L0_2 = sevenColor
          L0_2 = L0_2["跑商任务"]
          os = os(L0_2)
          if os == false then
            os = _ENV["通用功能"]
            os = os["任务栏清理"]
            os()
          end
        else
          os = loginArray
          os = os[mSleep]
          os = os[1]
          if os == "青龙" then
            os = _find
            L0_2 = Color
            L0_2 = L0_2["青龙"]
            L0_2 = L0_2["青龙任务绿"]
            os = os(L0_2)
            if os == false then
              os = _ENV["通用功能"]
              os = os["任务栏清理"]
              os()
            end
          end
        end
        os = loginArray
        os = os[mSleep]
        os = os[2]
        os()
      end
    end
  end
  lua_exit = _ENV["UI_卖体"]
  if lua_exit == "干完卖体" then
    lua_exit = _ENV["_功能"]
    lua_exit = lua_exit["卖体"]
    lua_exit()
  end
  lua_exit = _ENV["是否丢钱"]
  if lua_exit == "丢钱" then
    lua_exit = _ENV["检测钱数"]
    lua_exit()
  end
  lua_exit = _ENV["是否存钱"]
  if lua_exit == "存钱" then
    lua_exit = _ENV["检测钱数存钱"]
    lua_exit()
  end
  lua_exit = _ENV["UI_转移二药"]
  if lua_exit == "下线前转移二药" then
    lua_exit = _ENV["转移二药总流程"]
    lua_exit()
  end
  lua_exit = _ENV["是否丢物"]
  if lua_exit == "丢物" then
    lua_exit = _ENV["UI_转物前开玲珑石"]
    if lua_exit == "转物前开玲珑石" then
      lua_exit = _ENV["_功能"]
      lua_exit = lua_exit["背包"]
      Color = "open"
      lua_exit(Color)
      while true do
        lua_exit = myBlockcolor
        Color = sevenColor
        Color = Color["大礼包"]
        lua_exit = lua_exit(Color)
        if lua_exit then
          lua_exit = _tap
          Color = x
          time = y
          L0_2 = 2
          os = 10
          lua_exit(Color, time, L0_2, os)
          lua_exit = _ENV["_随机延时"]
          Color = 200
          lua_exit(Color)
        end
        lua_exit = myBlockcolor
        Color = sevenColor
        Color = Color["小礼包"]
        lua_exit = lua_exit(Color)
        if lua_exit then
          lua_exit = _tap
          Color = x
          time = y
          L0_2 = 2
          os = 10
          lua_exit(Color, time, L0_2, os)
          lua_exit = _ENV["_随机延时"]
          Color = 200
          lua_exit(Color)
        end
        lua_exit = myBlockcolor
        Color = sevenColor
        Color = Color["大礼包"]
        lua_exit = lua_exit(Color)
        if lua_exit == false then
          lua_exit = myBlockcolor
          Color = sevenColor
          Color = Color["小礼包"]
          lua_exit = lua_exit(Color)
          if lua_exit == false then
            lua_exit = _ENV["通用功能"]
            lua_exit = lua_exit["叉叉"]
            lua_exit()
            break
          end
        end
      end
    end
    lua_exit = _ENV["转移物品流程"]
    lua_exit()
  end
  lua_exit = _ENV["UI_检查武器修理"]
  if lua_exit == "检查武器修理" then
    lua_exit = _ENV["_功能"]
    lua_exit = lua_exit["修武器"]
    lua_exit()
  end
  lua_exit = _ENV["UI_宠物加点"]
  if lua_exit == "下线宠物加点" then
    lua_exit = _ENV["宠物加点"]
    lua_exit()
  end
  lua_exit = _ENV["UI_自动升级"]
  if lua_exit == "自动升级" then
    lua_exit = _ENV["升级"]
    lua_exit()
  end
  lua_exit = _ENV["UI_自动加点"]
  if lua_exit == "自动加点" then
    lua_exit = _ENV["加点"]
    lua_exit()
  end
  lua_exit = _ENV["UI_太清符"]
  if lua_exit == "领太清符" then
    lua_exit = _ENV["领取太清符"]
    lua_exit()
  end
  lua_exit = _ENV["UI_领礼物"]
  if lua_exit == "领礼物" then
    lua_exit = _ENV["_功能"]
    lua_exit = lua_exit["领礼物"]
    lua_exit()
  end
  lua_exit = _ENV["UI_完成不下线"]
  if lua_exit == "完成任务不下线" then
    lua_exit = UI_msg
    if lua_exit == "响铃" then
      lua_exit = setVolumeLevel
      Color = 1
      lua_exit(Color)
      lua_exit = playAudio
      Color = userPath
      Color = Color()
      time = "/res/GameGX.mp3"
      Color = Color .. time
      lua_exit(Color)
    end
    lua_exit = dialog
    Color = "完成任务"
    time = time
    lua_exit(Color, time)
    lua_exit = lua_exit
    lua_exit()
  end
  lua_exit = _ENV["_功能"]
  lua_exit = lua_exit["屏蔽"]
  Color = "close"
  lua_exit(Color)
  ju_shehunxiang = 0
  ju_shehunxiang2 = 0
  one_ocr_datu_num = 0
  datu_num = 0
  dodgeTime = -1
end

GOGAME = start

function start()
  local lua_exit, Color, time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2
  lua_exit = SmUI
  lua_exit()
  lua_exit = _ENV["UI_上传截图"]
  if lua_exit then
    lua_exit = mSleep
    Color = 2222
    lua_exit(Color)
    lua_exit = _ENV["_记录图片"]
    lua_exit()
    lua_exit = mSleep
    Color = 1111
    lua_exit(Color)
    lua_exit = dialog
    Color = "上传成功"
    time = 5
    lua_exit(Color, time)
    lua_exit = lua_exit
    lua_exit()
  end
  gameStart = true
  lua_exit = _ENV["UI_爱购_充值"]
  if lua_exit then
    lua_exit = _ENV["爱购_账号"]
    if lua_exit ~= "" then
      lua_exit = _ENV["爱购_密码"]
      if lua_exit == "" then
        end
        end
        lua_exit = dialog
        Color = "自动充值:没有填写账号密码"
        time = time
        lua_exit(Color, time)
        lua_exit = lua_exit
        lua_exit()
      else
        lua_exit = _ENV["爱购_交易密码"]
        if lua_exit == "" then
        lua_exit = _ENV["爱购_密码"]
        _ENV["爱购_交易密码"] = lua_exit
      end
    end
  end
  lua_exit = rw_zdfs
  if lua_exit == "便捷法术1" then
    _ENV["人物攻击"] = "法术"
    _ENV["UI_zhandou_师门"] = "百级技能"
  end
  lua_exit = rw_zdfs
  if lua_exit == "普通攻击" then
    _ENV["人物攻击"] = "普攻"
    _ENV["UI_zhandou_师门"] = "百级普攻"
  end
  lua_exit = bb_zdfs
  if lua_exit == "攻宝宝" then
    _ENV["宠物攻击"] = "普攻"
    UI_zhandou_BB_model = "宝宝攻击"
  end
  lua_exit = bb_zdfs
  if lua_exit == "法宝宝" then
    _ENV["宠物攻击"] = "法术"
    UI_zhandou_BB_model = "宝宝技能"
  end
  lua_exit = _ENV["补血道具"]
  if lua_exit == "包子1" then
    _ENV["双击血"] = "双击包子1"
  end
  lua_exit = _ENV["补蓝道具"]
  if lua_exit == "佛手1" then
    _ENV["双击蓝"] = "双击佛手1"
  end
  lua_exit = _ENV["补血道具"]
  if lua_exit == "红罗羹" then
    _ENV["双击血"] = "双击红罗羹"
  end
  lua_exit = _ENV["补蓝道具"]
  if lua_exit == "绿罗羹" then
    _ENV["双击蓝"] = "双击绿罗羹"
  end
  lua_exit = _ENV["补血道具"]
  if lua_exit == "包子1" then
    _ENV["押镖检测"] = "包子"
  end
  lua_exit = _ENV["补血道具"]
  if lua_exit == "红罗羹" then
    _ENV["押镖检测"] = "红罗羹"
  end
  lua_exit = _ENV["UI_角色回复"]
  if lua_exit == "50%" then
    _ENV["UI_角色通用回复"] = "50%"
  else
    lua_exit = _ENV["UI_角色回复"]
    if lua_exit == "70%" then
      _ENV["UI_角色通用回复"] = "70%"
    else
      lua_exit = _ENV["UI_角色回复"]
      if lua_exit == "90%" then
        _ENV["UI_角色通用回复"] = "90%"
      end
    end
  end
  lua_exit = _ENV["UI_宠物回复"]
  if lua_exit == "50%" then
    _ENV["UI_宠物通用回复"] = "50%"
  else
    lua_exit = _ENV["UI_宠物回复"]
    if lua_exit == "70%" then
      _ENV["UI_宠物通用回复"] = "70%"
    else
      lua_exit = _ENV["UI_宠物回复"]
      if lua_exit == "90%" then
        _ENV["UI_宠物通用回复"] = "90%"
      end
    end
  end
  lua_exit = _ENV["UI_回复方式"]
  if lua_exit == "道具" then
    _ENV["UI_角色状态回复"] = "吃碗"
    _ENV["UI_宠物状态回复"] = "吃碗"
  else
    lua_exit = _ENV["UI_回复方式"]
    if lua_exit == "住店/巫医" then
      _ENV["UI_角色状态回复"] = "住店"
      _ENV["UI_宠物状态回复"] = "巫医"
    end
  end
  lua_exit = _ENV["UI_卡顿掉帧"]
  if lua_exit == "" then
    _ENV["UI_卡顿掉帧"] = "1"
  end
  lua_exit = _ENV["UI_跑商票数"]
  if lua_exit == "" then
    _ENV["UI_跑商票数"] = "999"
  end
  lua_exit = _ENV["UI_转移二药间隔"]
  if lua_exit == "" then
    _ENV["UI_转移二药间隔"] = "999"
  end
  lua_exit = _ENV["UI_青龙次数"]
  if lua_exit == "" then
    _ENV["UI_青龙次数"] = "9999"
  end
  lua_exit = _ENV["UI_跑商刷价等待"]
  if lua_exit == "" then
    _ENV["UI_跑商刷价等待"] = "12"
  end
  lua_exit = tonumber
  Color = _ENV["UI_跑商刷价等待"]
  lua_exit = lua_exit(Color)
  _ENV["跑商刷价等待"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_跑商票数"]
  lua_exit = lua_exit(Color)
  _ENV["目标跑商票数"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_转移二药间隔"]
  lua_exit = lua_exit(Color)
  _ENV["转移二药间隔"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_青龙次数"]
  lua_exit = lua_exit(Color)
  _ENV["目标青龙次数"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_卡顿掉帧"]
  lua_exit = lua_exit(Color)
  _ENV["卡顿掉帧"] = lua_exit
  lua_exit = _ENV["UI_二药频率"]
  if lua_exit == "不买二药" then
    _ENV["UI_帮贡换二药"] = false
  else
    _ENV["UI_帮贡换二药"] = true
  end
  lua_exit = _ENV["UI_车夫寻找方式"]
  if lua_exit == "自动识别" then
  else
    lua_exit = _ENV["UI_车夫x"]
    if lua_exit ~= "" then
      lua_exit = _ENV["UI_车夫y"]
      if lua_exit ~= "" then
        lua_exit = _ENV["UI_出口x"]
        if lua_exit ~= "" then
          lua_exit = _ENV["UI_出口y"]
          if lua_exit == "" then
            end
            end
            end
            end
            lua_exit = Gameorder1
            if lua_exit == "跑商" then
            lua_exit = dialog
            Color = "请输入帮派坐标"
            time = time
            lua_exit(Color, time)
            lua_exit = lua_exit
            lua_exit()
            end
            end
          else
            lua_exit = Gameorder1
            if lua_exit == "青龙" then
            lua_exit = _ENV["UI_走入青龙"]
            if lua_exit then
            lua_exit = _ENV["UI_青龙x"]
            if lua_exit ~= "" then
            lua_exit = _ENV["UI_青龙y"]
            if lua_exit ~= "" then
            goto lbl_213
          end
        end
      end
      lua_exit = dialog
      Color = "请输入帮派青龙入口坐标（此坐标是走入的坐标，坐标是你站在仓库门口，填入你所在位置的坐标+2或-2，可以调试一下）"
      time = time
      lua_exit(Color, time)
      lua_exit = lua_exit
      lua_exit()
      lua_exit = _ENV["UI_走入青龙中转"]
      if lua_exit then
        lua_exit = _ENV["UI_青龙中转x"]
        if lua_exit ~= "" then
          lua_exit = _ENV["UI_青龙中转y"]
        end
        if lua_exit == "" then
          lua_exit = dialog
          Color = "勾选了（步行中转）但是没输入坐标。部分地图需要中转一下才能步行进入仓库"
          time = time
          lua_exit(Color, time)
          lua_exit = lua_exit
          lua_exit()
        end
      end
    else
      lua_exit = _ENV["UI_车夫寻找方式"]
      if lua_exit == "自动识别" then
      else
        lua_exit = _ENV["UI_车夫x"]
        if lua_exit ~= "" then
          lua_exit = _ENV["UI_车夫y"]
          if lua_exit == "" then
            end
            end
            lua_exit = dialog
            Color = "请输入帮派车夫坐标"
            time = time
            lua_exit(Color, time)
            lua_exit = lua_exit
            lua_exit()
            end
            end
            end
          else
            lua_exit = tonumber
            Color = _ENV["UI_青龙x"]
            lua_exit = lua_exit(Color)
            _ENV["青龙x"] = lua_exit
            lua_exit = tonumber
            Color = _ENV["UI_青龙y"]
            lua_exit = lua_exit(Color)
            _ENV["青龙y"] = lua_exit
            lua_exit = tonumber
            Color = _ENV["UI_青龙中转x"]
            lua_exit = lua_exit(Color)
            _ENV["青龙中转x"] = lua_exit
            lua_exit = tonumber
            Color = _ENV["UI_青龙中转y"]
            lua_exit = lua_exit(Color)
            _ENV["青龙中转y"] = lua_exit
            lua_exit = _ENV["UI_家具放弃方式"]
            if lua_exit == "等待3分钟放弃" then
            lua_exit = _ENV["UI_帮派总管x"]
            if lua_exit ~= "" then
            lua_exit = _ENV["UI_帮派总管y"]
            if lua_exit ~= "" then
            goto lbl_274
          end
      end
    end
    lua_exit = dialog
    Color = "如需等待放弃,请输入帮派聚义堂总管坐标"
    time = time
    lua_exit(Color, time)
  end
  lua_exit = tonumber
  Color = _ENV["UI_车夫x"]
  lua_exit = lua_exit(Color)
  _ENV["车夫x"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_车夫y"]
  lua_exit = lua_exit(Color)
  _ENV["车夫y"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_出口x"]
  lua_exit = lua_exit(Color)
  _ENV["出口x"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_出口y"]
  lua_exit = lua_exit(Color)
  _ENV["出口y"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_卖药坐标X"]
  lua_exit = lua_exit(Color)
  _ENV["_卖药坐标_x"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_卖药坐标Y"]
  lua_exit = lua_exit(Color)
  _ENV["_卖药坐标_y"] = lua_exit
  lua_exit = tonumber
  Color = _ENV["UI_卖二药价格"]
  lua_exit = lua_exit(Color)
  _ENV["_卖二药价格"] = lua_exit
  lua_exit = _ENV["UI_跑商等级"]
  if lua_exit == "40" then
    _ENV["初始金额"] = 40000
    _ENV["交票金额"] = 100000
    _ENV["回城金额"] = 85000
  else
    _ENV["初始金额"] = 42000
    _ENV["交票金额"] = 150000
    _ENV["回城金额"] = 130000
  end
  lua_exit = _ENV["卖体间隔time"]
  if lua_exit == "" then
    _ENV["卖体间隔time"] = "99999"
  end
  lua_exit = tonumber
  Color = _ENV["卖体间隔time"]
  lua_exit = lua_exit(Color)
  _ENV["卖体间隔tim"] = lua_exit
  lua_exit = _ENV["循环上号"]
  if lua_exit == "单号模式" then
    _ENV["UI_当前循环"] = "当前循环"
  end
  lua_exit = _ENV["UI_活力处置"]
  if lua_exit == "换修业" then
    _ENV["UI_修业"] = true
  end
  lua_exit = _ENV["不买超过一万召唤兽"]
  if lua_exit == "不买超过一万召唤兽" then
    _ENV["买召唤兽循环"] = 1
  else
    _ENV["买召唤兽循环"] = 2
  end
  lua_exit = _ENV["循环上号"]
  if lua_exit == "文本循环" then
    lua_exit = isFileExist
    Color = userPath
    Color = Color()
    time = "/res/userData.txt"
    Color = Color .. time
    lua_exit = lua_exit(Color)
    if lua_exit then
      while true do
        lua_exit = os
        lua_exit = lua_exit.date
        Color = "%Y"
        time = os
        time = time.time
        time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = time()
        lua_exit = lua_exit(Color, time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
        Color = os
        Color = Color.date
        time = "%m"
        L0_2 = os
        L0_2 = L0_2.time
        L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = L0_2()
        Color = Color(time, L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
        time = os
        time = time.date
        L0_2 = "%d"
        os = os
        os = os.time
        os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = os()
        time = time(L0_2, os, GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
        L0_2 = readFile
        os = userPath
        os = os()
        GOGAME = "/res/userData.txt"
        os = os .. GOGAME
        L0_2 = L0_2(os)
        os = 1
        GOGAME = #L0_2
        os = 1
        for mSleep = os, GOGAME, os do
          os = L0_2[mSleep]
          if os ~= "\n" then
            os = L0_2[mSleep]
            if os == "\r" then
              end
              end
              os = removeFirstLine
              L0_2 = userPath
              L0_2 = L0_2()
              Color = "/res/userData.txt"
              L0_2 = L0_2 .. Color
              Color = 1
              os(L0_2, Color)
              os = table
              os = os.remove
              L0_2 = L0_2
              Color = mSleep
              os(L0_2, Color)
              goto lbl_551
            else
              ju_shehunxiang = 0
              BXTASK = true
              os = GameLogin
              L0_2 = L0_2[mSleep]
              os(L0_2)
              os = GOGAME
              os()
              os = removeFirstLine
              L0_2 = userPath
              L0_2 = L0_2()
              Color = "/res/userData.txt"
              L0_2 = L0_2 .. Color
              Color = 1
              os(L0_2, Color)
              os = writeFileString
              L0_2 = userPath
              L0_2 = L0_2()
              Color = "/res/userData.txt"
              L0_2 = L0_2 .. Color
              Color = L0_2[mSleep]
              Color = "a"
              false = 1
              os(L0_2, Color, Color, false)
              while true do
              os = _print
              L0_2 = "退出游戏"
              os(L0_2)
              os = getColour
              L0_2 = colorList
              Color = "团队副本"
              os = os(L0_2, Color)
              if os then
              os = randomClick
              L0_2 = 2
              Color = 300
              Color = 1593
              false = 82
              os(L0_2, Color, Color, false)
              else
              os = getColour
              L0_2 = GameFB
              Color = "打开系统"
              os = os(L0_2, Color)
              if os then
              os = randomClick
              L0_2 = 0
              Color = 500
              Color = 1404
              false = 897
              time = 1544
              os(L0_2, Color, Color, false, time, L14_2)
              else
              os = getColour
              L0_2 = GameFB
              Color = "退出游戏"
              os = os(L0_2, Color)
              if os then
              os = randomClick
              L0_2 = 0
              Color = 1500
              Color = 1088
              false = 612
              time = 1232
              os(L0_2, Color, Color, false, time, L14_2)
              break
              else
              os = getColour
              L0_2 = GameFB
              Color = "打开成就"
              os = os(L0_2, Color)
              if os then
              os = randomClick
              L0_2 = 2
              Color = 1300
              Color = 1657
              false = 43
              os(L0_2, Color, Color, false)
              else
              os = getColour
              L0_2 = _ENV["商人鬼魂"]
              Color = "关闭对话框"
              os = os(L0_2, Color)
              if os then
              os = randomClick
              L0_2 = 2
              Color = 300
              Color = 1873
              false = 797
              os(L0_2, Color, Color, false)
              else
              os = randomTap
              L0_2 = 670
              Color = 983
              Color = 10
              os(L0_2, Color, Color)
              os = mSleep
              L0_2 = math
              L0_2 = L0_2.random
              Color = 500
              Color = 1000
              L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = L0_2(Color, Color)
              os(L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
            end
                    end
                  end
                end
              end
            end
            os = mSleep
            L0_2 = 100
            os(L0_2)
          end
          os = os
          os = os.time
          os = os()
          while true do
            L0_2 = _find
            Color = Color
            Color = Color["红尘"]
            Color = Color["登录界面1"]
            L0_2 = L0_2(Color)
            if L0_2 then
              L0_2 = _ENV["_随机延时"]
              Color = 811
              L0_2(Color)
              break
            end
            L0_2 = os
            L0_2 = L0_2.time
            L0_2 = L0_2()
            L0_2 = L0_2 - os
            if 30 < L0_2 then
              L0_2 = os
              L0_2 = L0_2.time
              L0_2 = L0_2()
              os = L0_2
              L0_2 = _print
              Color = "卡了？"
              L0_2(Color)
              L0_2 = closeApp
              Color = "com.netease.mhxyhtb"
              L0_2(Color)
              L0_2 = mSleep
              Color = 2000
              L0_2(Color)
              L0_2 = os
              L0_2 = L0_2.execute
              Color = "input keyevent KEYCODE_HOME"
              L0_2(Color)
              L0_2 = mSleep
              Color = 2000
              L0_2(Color)
              L0_2 = runApp
              Color = "com.netease.mhxyhtb"
              L0_2(Color)
            end
          end
        end
        os = closeApp
        GOGAME = "com.netease.mhxyhtb"
        os(GOGAME)
        while true do
          os = os
          os = os.date
          GOGAME = "%Y"
          os = os
          os = os.time
          os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = os()
          os = os(GOGAME, os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
          GOGAME = os
          GOGAME = GOGAME.date
          os = "%m"
          mSleep = os
          mSleep = mSleep.time
          mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = mSleep()
          GOGAME = GOGAME(os, mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
          os = os
          os = os.date
          mSleep = "%d"
          os = os
          os = os.time
          os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2 = os()
          os = os(mSleep, os, L0_2, Color, Color, false, time, L14_2, os, L16_2, os, L18_2)
          mSleep = printLog
          os = "完成所有角色："
          L0_2 = lua_exit
          Color = "."
          Color = Color
          false = "."
          time = time
          os = os
          os = os.date
          os = os
          os = os.time
          os, L18_2 = os()
          os = os(L16_2, os, L18_2)
          os = os .. L0_2 .. Color .. Color .. false .. time .. L14_2 .. os
          mSleep(os)
          mSleep = os - time
          if mSleep == 1 then
            break
          end
          mSleep = GOGAME - Color
          if mSleep == 1 then
            break
          end
          mSleep = os - lua_exit
          if mSleep == 1 then
            break
          end
          mSleep = mSleep
          os = 1000
          mSleep(os)
        end
      end
    else
      lua_exit = dialog
      Color = "请先添加账号文件再使用文本循环登录。"
      lua_exit(Color)
      lua_exit = luaExit
      lua_exit()
    end
  else
    lua_exit = _ENV["循环上号"]
    if lua_exit == "普通循环" then
      lua_exit = _ENV["角色数量"]
      if lua_exit == "" then
        lua_exit = dialog
        Color = "循环模式需填写数量"
        time = time
        lua_exit(Color, time)
      end
      lua_exit = tonumber
      Color = _ENV["角色数量"]
      lua_exit = lua_exit(Color)
      Color = 1
      time = lua_exit
      L0_2 = 1
      for os = Color, time, L0_2 do
        BXTASK = true
        GOGAME = _ENV["_游戏"]
        GOGAME = GOGAME["循环登录"]
        GOGAME()
        GOGAME = GOGAME
        GOGAME()
        GOGAME = _ENV["_游戏"]
        GOGAME = GOGAME["退出到登录界面"]
        GOGAME()
      end
      Color = closeApp
      time = "com.netease.mhxyhtb"
      Color(time)
      Color = UI_msg
      if Color == "响铃" then
        Color = setVolumeLevel
        time = 1
        Color(time)
        Color = playAudio
        time = userPath
        time = time()
        L0_2 = "/res/GameGX.mp3"
        time = time .. L0_2
        Color(time)
      end
      Color = _ENV["没点角色"]
      if Color == "" then
        Color = dialog
        time = "完成任务"
        Color(time)
      else
        Color = dialog
        time = "完成任务，"
        L0_2 = _ENV["没点角色"]
        os = "欠费了"
        time = time .. L0_2 .. os
        Color(time)
      end
    else
      BXTASK = true
      lua_exit = _ENV["_游戏"]
      lua_exit = lua_exit["进入2"]
      lua_exit()
      lua_exit = GOGAME
      lua_exit()
      lua_exit = _ENV["_游戏"]
      lua_exit = lua_exit["退出到登录界面"]
      lua_exit()
      lua_exit = UI_msg
      if lua_exit == "响铃" then
        lua_exit = setVolumeLevel
        Color = 1
        lua_exit(Color)
        lua_exit = playAudio
        Color = userPath
        Color = Color()
        time = "/res/GameGX.mp3"
        Color = Color .. time
        lua_exit(Color)
      end
      lua_exit = _ENV["没点角色"]
      if lua_exit == "" then
        lua_exit = dialog
        Color = "完成任务"
        lua_exit(Color)
      else
        lua_exit = dialog
        Color = "完成任务，"
        time = _ENV["没点角色"]
        L0_2 = "欠费了"
        Color = Color .. time .. L0_2
        lua_exit(Color)
      end
    end
  end
end

start = start

function start()
  local lua_exit, Color, time, L0_2, os
  lua_exit = getTarget
  lua_exit = lua_exit()
  if lua_exit == "" then
    lua_exit = openProps
    lua_exit()
    lua_exit = isItemExist
    Color = _ENV["押镖检测"]
    lua_exit = lua_exit(Color)
    if lua_exit == false then
      lua_exit = isItemExist
      Color = "包子"
      lua_exit = lua_exit(Color)
      if lua_exit == false then
        lua_exit = _ENV["_购买"]
        lua_exit = lua_exit["包子"]
        lua_exit()
      end
    end
    lua_exit = _ENV["宠物忠诚玲珑"]
    lua_exit()
  end
  lua_exit = ""
  Color = award
  if Color == "现金" then
    lua_exit = "cash"
  else
    Color = award
    if Color == "储备" then
      lua_exit = "reserve"
    end
  end
  Color = level1
  if Color == "1级镖" then
    travelMod = "walk"
    Color = startEscorting
    time = 1
    L0_2 = lua_exit
    Color(time, L0_2)
  else
    Color = level1
    if Color == "2级镖" then
      travelMod = "walk"
      Color = startEscorting
      time = 2
      L0_2 = lua_exit
      Color(time, L0_2)
    else
      Color = level1
      if Color == "3级镖" then
        travelMod = "walk"
        Color = startEscorting
        time = 3
        L0_2 = lua_exit
        Color(time, L0_2)
      else
        Color = level1
        if Color == "4级镖" then
          travelMod = "walk"
          Color = startEscorting
          time = 4
          L0_2 = lua_exit
          Color(time, L0_2)
        end
      end
    end
  end
end

_ENV["押镖任务"] = start

function start()
  local lua_exit, Color, time
end

_ENV["卖体转钱任务"] = start

function start()
  local lua_exit, Color, time
  lua_exit = _ENV["UI_签到"]
  if lua_exit then
    lua_exit = _ENV["自动签到"]
    lua_exit()
  end
  lua_exit = _ENV["UI_跑商模式"]
  if lua_exit == "联动跑商" then
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["识别服务器名"]
    lua_exit()
  end
  lua_exit = startTrading
  lua_exit()
end

_ENV["跑商任务"] = start

function start()
  local lua_exit, Color, time
  _ENV["当前内政已刷完"] = false
  _ENV["青龙_次数"] = 0
  lua_exit = _ENV["_青龙"]
  lua_exit = lua_exit["识别任务"]
  lua_exit()
end

_ENV["青龙任务"] = start

function start()
  local lua_exit, Color, time, L0_2
  lua_exit = _ENV["UI_定时买药"]
  if lua_exit == "" then
    lua_exit = dialog
    Color = "定时时间未填写，暂时默认当前为刷药时间"
    time = 3
    lua_exit(Color, time)
    lua_exit = os
    lua_exit = lua_exit.date
    Color = "*t"
    time = os
    time = time.time
    time, L0_2 = time()
    lua_exit = lua_exit(Color, time, L0_2)
    nowTime = lua_exit
    lua_exit = nowTime
    lua_exit = lua_exit.min
    lua_exit = lua_exit + 1
    _ENV["定时买药time"] = lua_exit
  else
    lua_exit = tonumber
    Color = _ENV["UI_定时买药"]
    lua_exit = lua_exit(Color)
    lua_exit = lua_exit + 1
    _ENV["定时买药time"] = lua_exit
  end
  _ENV["现在"] = true
  while true do
    lua_exit = os
    lua_exit = lua_exit.date
    Color = "*t"
    time = os
    time = time.time
    time, L0_2 = time()
    lua_exit = lua_exit(Color, time, L0_2)
    nowTime = lua_exit
    lua_exit = nowTime
    lua_exit = lua_exit.min
    Color = _ENV["定时买药time"]
    if lua_exit ~= Color then
      lua_exit = _ENV["现在"]
      if lua_exit then
        end
        end
        _ENV["现在"] = false
        lua_exit = _ENV["_游戏"]
        lua_exit = lua_exit["进入2"]
        lua_exit()
        lua_exit = _ENV["存物品"]
        lua_exit()
        lua_exit = _ENV["_前往"]
        lua_exit = lua_exit["书院"]
        lua_exit()
        lua_exit = _ENV["_青龙"]
        lua_exit = lua_exit["进入买药界面"]
        lua_exit = lua_exit()
        if lua_exit then
        lua_exit = _ENV["买药品"]
        lua_exit()
        end
        lua_exit = _ENV["_游戏"]
        lua_exit = lua_exit["退出到登录界面"]
        lua_exit()
        lua_exit = mSleep
        Color = 120000
        lua_exit(Color)
      else
        lua_exit = mSleep
        Color = 5000
        lua_exit(Color)
        lua_exit = toast
        Color = "等待帮派刷新药品"
        time = 2
        lua_exit(Color, time)
      end
  end
end

_ENV["定时买药任务"] = start

function start()
  local lua_exit, Color, time, L0_2, os
  _ENV["取药页数"] = 1
  lua_exit = _ENV["检测提示框"]
  lua_exit()
  while true do
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["各地仓库"]
    Color = _ENV["UI_卖二药地址"]
    lua_exit(Color)
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["仓库取药"]
    lua_exit()
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["前往准确坐标"]
    Color = _ENV["UI_卖二药地址"]
    time = _ENV["_卖药坐标_x"]
    L0_2 = _ENV["_卖药坐标_y"]
    lua_exit(Color, time, L0_2)
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["摆摊"]
    lua_exit()
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["摆摊输入价格"]
    Color = _ENV["_卖二药价格"]
    lua_exit(Color)
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["摆摊上架"]
    Color = _ENV["_卖二药价格"]
    lua_exit(Color)
    while true do
      lua_exit = _find
      Color = Color
      Color = Color["摆摊"]
      Color = Color["普通摆摊J"]
      lua_exit = lua_exit(Color)
      if lua_exit then
        lua_exit = _find
        Color = Color
        Color = Color["摆摊"]
        Color = Color["正在出售"]
        lua_exit = lua_exit(Color)
        if lua_exit == false then
          break
        end
      end
      lua_exit = mSleep
      Color = 500
      lua_exit(Color)
    end
    repeat
      lua_exit = _find
      Color = Color
      Color = Color["摆摊"]
      Color = Color["收摊A"]
      lua_exit = lua_exit(Color)
      if lua_exit then
        lua_exit = _Sleep
        Color = 300
        time = 500
        lua_exit(Color, time)
      end
      lua_exit = mSleep
      Color = 100
      lua_exit(Color)
      lua_exit = _cmp_tb
      Color = Color
      Color = Color["主界面"]
      lua_exit = lua_exit(Color)
    until lua_exit
    lua_exit = _ENV["没有药了"]
    if lua_exit then
      lua_exit = dialog
      Color = "完成任务"
      time = time
      lua_exit(Color, time)
      break
    end
  end
end

_ENV["摆摊卖二药"] = start

function start()
  local lua_exit, Color, time, L0_2
  lua_exit = _ENV["UI_添加好友"]
  if lua_exit then
    lua_exit = _ENV["加好友"]
    lua_exit()
  end
  _ENV["取药页数"] = 1
  while true do
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["各地仓库"]
    Color = _ENV["UI_卖二药地址"]
    lua_exit(Color)
    lua_exit = _ENV["_通用"]
    lua_exit = lua_exit["仓库取药"]
    lua_exit()
    lua_exit = _ENV["转移二药"]
    lua_exit()
    lua_exit = _ENV["通用功能"]
    lua_exit = lua_exit["关闭"]
    lua_exit()
    lua_exit = _ENV["没有药了"]
    if lua_exit then
      lua_exit = _ENV["UI_转移二药"]
      if lua_exit == "每X票转移" then
        lua_exit = _ENV["_前往"]
        lua_exit = lua_exit["长安城"]
        lua_exit()
        break
      end
      lua_exit = dialog
      Color = "完成任务"
      time = time
      lua_exit(Color, time)
      lua_exit = lua_exit
      lua_exit()
      break
    end
  end
end

_ENV["转移二药总流程"] = start

function start()
  local lua_exit, Color, time
  lua_exit = _ENV["_通用"]
  lua_exit = lua_exit["各地仓库"]
  Color = _ENV["UI_卖二药地址"]
  lua_exit(Color)
  lua_exit = _ENV["加好友"]
  lua_exit()
  repeat
    _ENV["钱够继续买图"] = false
    lua_exit = _ENV["购买宝图全部流程"]
    lua_exit = lua_exit()
    if lua_exit then
      _ENV["钱够继续买图"] = true
    end
    lua_exit = _ENV["_前往"]
    lua_exit = lua_exit["建邺城"]
    lua_exit()
    lua_exit = _ENV["转移宝图"]
    lua_exit()
    lua_exit = _ENV["钱够继续买图"]
  until not lua_exit
end

_ENV["买图转移"] = start
start = start
start()
