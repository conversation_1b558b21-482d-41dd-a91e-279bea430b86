local L0_1, L1_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
  L0_2 = 1
  L1_2 = 20
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["其他"]
    L5_2 = L5_2["展开充值"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 300
      L6_2 = 400
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["其他"]
    L5_2 = L5_2["打开充值"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 300
      L6_2 = 400
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["其他"]
    L5_2 = L5_2["已打开充值"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = tap
      L5_2 = 433
      L6_2 = 710
      L4_2(L5_2, L6_2)
      L4_2 = _Sleep
      L5_2 = 500
      L6_2 = 600
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["其他"]
    L5_2 = L5_2["打开充值下一步"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 500
      L6_2 = 600
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["其他"]
    L5_2 = L5_2["充值账号界面"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 500
      L6_2 = 600
      L4_2(L5_2, L6_2)
      L4_2 = tsOcrText
      L5_2 = _ENV["ocr_邮箱"]
      L6_2 = 1065
      L7_2 = 246
      L8_2 = 1564
      L9_2 = 293
      L10_2 = "273552 , 151315"
      L11_2 = 96
      L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      rett = L4_2
      L4_2 = _ENV["UI_测试识别邮箱"]
      if L4_2 then
        L4_2 = dialog
        L5_2 = rett
        L6_2 = 10
        L4_2(L5_2, L6_2)
      end
      L4_2 = rett
      _ENV["爱购_充值邮箱"] = L4_2
      L4_2 = _ENV["通用功能"]
      L4_2 = L4_2["关闭"]
      L4_2()
      break
    end
  end
end

_ENV["获取充值账号"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = _ENV["爱购_账号"]
  if L0_2 then
    L0_2 = _ENV["爱购_密码"]
    if L0_2 then
      goto lbl_11
    end
  end
  L0_2 = dialog
  L1_2 = "自动充值：没有输入账号或密码"
  L2_2 = time
  L0_2(L1_2, L2_2)
  ::lbl_11::
  L0_2 = {}
  L0_2.tstab = 1
  L0_2.timeOut = 90
  L0_2.urlEnCode = false
  L1_2 = "https://aigosky.youyuwang.cn/frontend/auth/login/password"
  L2_2 = "username="
  L3_2 = _ENV["爱购_账号"]
  L4_2 = "&password="
  L5_2 = _ENV["爱购_密码"]
  L2_2 = L2_2 .. L3_2 .. L4_2 .. L5_2
  L3_2 = httpPost
  L4_2 = L1_2
  L5_2 = L2_2
  L6_2 = L0_2
  L3_2 = L3_2(L4_2, L5_2, L6_2)
  str = L3_2
  L3_2 = str
  if L3_2 then
    L3_2 = nLog
    L4_2 = str
    L3_2(L4_2)
    L3_2 = pcall
    L4_2 = json
    L4_2 = L4_2.decode
    L5_2 = str
    L3_2, L4_2 = L3_2(L4_2, L5_2)
    if L3_2 then
      L5_2 = L4_2.code
      if L5_2 == 0 then
        L5_2 = L4_2.data
        L5_2 = L5_2.token
        _ENV["爱购_token"] = L5_2
        L5_2 = true
        return L5_2
      end
      L5_2 = L4_2.message
      if L5_2 == "请求过于频繁" then
        L5_2 = mSleep
        L6_2 = 10000
        L5_2(L6_2)
        L5_2 = false
        return L5_2
      end
      L5_2 = dialog
      L6_2 = "自动充值："
      L7_2 = L4_2.message
      L6_2 = L6_2 .. L7_2
      L7_2 = time
      L5_2(L6_2, L7_2)
      L5_2 = lua_exit
      L5_2()
    end
    L5_2 = dialog
    L6_2 = "自动充值：未知错误1"
    L7_2 = time
    L5_2(L6_2, L7_2)
    L5_2 = lua_exit
    L5_2()
  end
  L3_2 = dialog
  L4_2 = "自动充值：未知错误2"
  L5_2 = time
  L3_2(L4_2, L5_2)
  L3_2 = lua_exit
  L3_2()
end

_ENV["爱购登录"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  L0_2 = require
  L1_2 = "tsnet"
  L0_2(L1_2)
  L0_2 = "https://aigosky.youyuwang.cn/frontend/purchase/submit-order"
  L1_2 = {}
  L2_2 = _ENV["爱购_token"]
  L1_2.FToken = L2_2
  L1_2["Content-Type"] = "application/json"
  header_send = L1_2
  L1_2 = {}
  L2_2 = _ENV["爱购_交易密码"]
  L1_2.deal_password = L2_2
  L1_2.quantity = 5
  L1_2.product_id = 1003
  L2_2 = {}
  L3_2 = _ENV["爱购_充值邮箱"]
  L2_2.charge_account = L3_2
  L2_2.game = "-1"
  L2_2.charge_mode = "1"
  L1_2.charge_params = L2_2
  body_send = L1_2
  L1_2 = json
  L1_2 = L1_2.encode
  L2_2 = body_send
  L1_2 = L1_2(L2_2)
  a = L1_2
  L1_2 = http
  L1_2 = L1_2.post
  L2_2 = L0_2
  L3_2 = {}
  L4_2 = header_send
  L3_2.headers = L4_2
  L4_2 = a
  L3_2.postdata = L4_2
  L4_2 = {}
  L4_2.charset = "utf-8"
  L3_2.opts = L4_2
  L1_2, L2_2, L3_2 = L1_2(L2_2, L3_2)
  content = L3_2
  header = L2_2
  status = L1_2
  L1_2 = status
  if L1_2 == 200 then
    L1_2 = nLog
    L2_2 = content
    L1_2(L2_2)
    L1_2 = pcall
    L2_2 = json
    L2_2 = L2_2.decode
    L3_2 = content
    L1_2, L2_2 = L1_2(L2_2, L3_2)
    if L1_2 then
      L3_2 = L2_2.message
      if L3_2 == "您的会话已失效，请重新登录" then
        L3_2 = false
        return L3_2
      else
        L3_2 = L2_2.message
        if L3_2 == "交易密码错误" then
          L3_2 = dialog
          L4_2 = "交易密码错误"
          L5_2 = time
          L3_2(L4_2, L5_2)
          L3_2 = lua_exit
          L3_2()
        else
          L3_2 = L2_2.message
          if L3_2 == "缺少充值账号参数" then
            L3_2 = dialog
            L4_2 = "自动充值：缺少充值账号"
            L5_2 = time
            L3_2(L4_2, L5_2)
            L3_2 = lua_exit
            L3_2()
          end
        end
      end
      L3_2 = L2_2.code
      if L3_2 == 0 then
        L3_2 = L2_2.data
        L3_2 = L3_2.order_id
        _ENV["爱购_订单id"] = L3_2
        L3_2 = true
        return L3_2
      end
    end
    L3_2 = dialog
    L4_2 = "自动充值：未知错误4"
    L5_2 = time
    L3_2(L4_2, L5_2)
    L3_2 = lua_exit
    L3_2()
  end
  L1_2 = dialog
  L2_2 = "自动充值：未知错误3"
  L3_2 = time
  L1_2(L2_2, L3_2)
  L1_2 = lua_exit
  L1_2()
end

_ENV["爱购充值"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = {}
  L0_2.tstab = "tstab"
  L1_2 = {}
  L2_2 = _ENV["爱购_token"]
  L1_2.FToken = L2_2
  L0_2.header_send = L1_2
  L1_2 = {}
  L0_2.body_send = L1_2
  L0_2.format = "gbk"
  tab = L0_2
  L0_2 = ts
  L0_2 = L0_2.httpPost
  L1_2 = "aigosky.youyuwang.cn/frontend/orders/"
  L2_2 = _ENV["爱购_订单id"]
  L1_2 = L1_2 .. L2_2
  L2_2 = tab
  L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2)
  body_resp = L2_2
  header_resp = L1_2
  code = L0_2
  L0_2 = code
  if L0_2 == 200 then
    L0_2 = nLog
    L1_2 = body_resp
    L0_2(L1_2)
    L0_2 = pcall
    L1_2 = json
    L1_2 = L1_2.decode
    L2_2 = body_resp
    L0_2, L1_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L2_2 = L1_2.message
      if L2_2 ~= "您的会话已失效，请重新登录" then
        L2_2 = L1_2.message
        if L2_2 ~= "加载失败，订单不存在" then
          goto lbl_42
        end
      end
      L2_2 = false
      do return L2_2 end
      ::lbl_42::
      L2_2 = L1_2.data
      if L2_2 ~= nil then
        L2_2 = L1_2.data
        L3_2 = null
        if L2_2 ~= L3_2 then
          L2_2 = L1_2.data
          if L2_2 ~= "" then
            goto lbl_54
          end
        end
      end
      L2_2 = false
      do return L2_2 end
      ::lbl_54::
      L2_2 = L1_2.data
      L2_2 = L2_2.status_info
      if L2_2 ~= nil then
        L2_2 = L1_2.data
        L2_2 = L2_2.status_info
        L3_2 = null
        if L2_2 ~= L3_2 then
          L2_2 = L1_2.data
          L2_2 = L2_2.status_info
          if L2_2 ~= "" then
            goto lbl_69
          end
        end
      end
      L2_2 = false
      do return L2_2 end
      ::lbl_69::
      L2_2 = L1_2.data
      L2_2 = L2_2.status_info
      if L2_2 == "交易成功" then
        L2_2 = true
        return L2_2
      end
      L2_2 = L1_2.data
      L2_2 = L2_2.status_info
      if L2_2 ~= "通行证帐号不存在或不可用" then
        L2_2 = L1_2.data
        L2_2 = L2_2.status_info
        if L2_2 ~= "账号必须是邮箱格式" then
          L2_2 = L1_2.data
          L2_2 = L2_2.status_info
          if L2_2 ~= "通行证账号 格式有错" then
            goto lbl_89
          end
        end
      end
      L2_2 = false
      return L2_2
    end
  end
  ::lbl_89::
end

_ENV["爱购查询"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = _ENV["获取充值账号"]
  L0_2()
  L0_2 = _ENV["爱购登录"]
  L0_2()
  L0_2 = 1
  L1_2 = 3
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _ENV["爱购充值"]
    L4_2 = L4_2()
    if L4_2 then
      break
    end
    L4_2 = _ENV["爱购登录"]
    L4_2()
    L4_2 = mSleep
    L5_2 = 5000
    L4_2(L5_2)
    if L3_2 == 3 then
      L4_2 = dialog
      L5_2 = "充值错误次数过多"
      L6_2 = time
      L4_2(L5_2, L6_2)
      L4_2 = lua_exit
      L4_2()
    end
  end
  L0_2 = 1
  L1_2 = 10
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _ENV["爱购查询"]
    L4_2 = L4_2()
    if L4_2 then
      break
    end
    L4_2 = _ENV["爱购登录"]
    L4_2()
    L4_2 = mSleep
    L5_2 = 10000
    L4_2(L5_2)
    if L3_2 == 10 then
      L4_2 = dialog
      L5_2 = "查询次数过多"
      L6_2 = time
      L4_2(L5_2, L6_2)
      L4_2 = lua_exit
      L4_2()
    end
  end
end

_ENV["自动充值流程"] = L0_1
Bool = false

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  while true do
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "子女"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = printLog
      L1_2 = "识别到点开子女"
      L0_2(L1_2)
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 800
      L3_2 = 1657
      L4_2 = 192
      L5_2 = 1709
      L6_2 = 322
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    else
      break
    end
  end
end

_ENV["关闭子女"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  while "x" do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 15756852
    L7_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
    L8_2 = 90
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A2_2
    L12_2 = A3_2
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    y = L6_2
    x = L5_2
    L5_2 = x
    if 0 < L5_2 then
      L5_2 = printLog
      L6_2 = "前往挖图地点"
      L5_2(L6_2)
      L5_2 = randomTap
      L6_2 = x
      L7_2 = y
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 1000
      L8_2 = 2000
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      if A4_2 == "大唐国境" or A4_2 == "五庄观" then
        L5_2 = randomTap
        L6_2 = x
        L7_2 = y
        L5_2(L6_2, L7_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 1000
        L8_2 = 2000
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      end
      L5_2 = randomTap
      L6_2 = 1600
      L7_2 = 86
      L8_2 = 10
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 1000
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      Bool = true
      break
    else
      L5_2 = {}
      L6_2 = "0000600000001f00000001f80000007fe000001fffffff1ffffffffdffffffffeffffc000efe00000001f0000001800000001e3c3ff000f1e3fffe078f3ffff83c79f0ffe1c3cf03effe1e781e3ff0f3c0f0ff079e0783f83cf03c1fc1e781e0fc1fffff9fe1ffffffff0ffffffffc7ffffff9e1ffe1f80f079e0780783cf03c03c1e781e00f0f3c0f007879e07803c3cf03c01e1e781e0078f3fff003c79fff801e3c3ff800f1e0ff0007800000003c00000001c00000000400000000000000000380000001fe0000003fe1fffffffe1fffffff81ffffffe00ffffff800781e070003c0f038001e0781c000f03c0e000781e070003c0f038001e0781c000f03c0e000781e070003e1f87e001ffffffff8ffffffffc7ffffffff3fffffe001e0781c000f03c0e000781e070063c0f038071e0781c03cf03c0e00e781e07007bc0f03803de0781c01ef03c0f01f7fffefc3fbffffffff8ffffffff83fffffff8$使用$1376$37$76"
      L5_2[1] = L6_2
      L6_2 = addTSOcrDictEx
      L7_2 = L5_2
      L6_2 = L6_2(L7_2)
      L7_2 = tsFindText
      L8_2 = L6_2
      L9_2 = "使用"
      L10_2 = 553
      L11_2 = 479
      L12_2 = 795
      L13_2 = 748
      L14_2 = "7BA2D3 , 1E1F2D # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # D4DCE1 , 3B3530"
      L15_2 = 90
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      y = L8_2
      x = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 9793044
      L9_2 = "-20|-1|0x82934c,-33|2|0x0a2e64,-34|8|0x003595,-25|30|0xc4860d,-11|14|0x0058e1,8|-3|0x2a4b70,8|-6|0x003ba5,-10|17|0x003595,-28|13|0x03193e"
      L10_2 = 90
      L11_2 = 299
      L12_2 = 278
      L13_2 = 424
      L14_2 = 401
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yy = L8_2
      xx = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 15329795
      L9_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
      L10_2 = 90
      L11_2 = 298
      L12_2 = 494
      L13_2 = 377
      L14_2 = 545
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yx = L8_2
      xy = L7_2
      L7_2 = x
      if 0 < L7_2 then
        L7_2 = xx
        if 0 < L7_2 then
          L7_2 = xy
          if 0 < L7_2 then
            L7_2 = randomTap
            L8_2 = x
            L9_2 = y
            L10_2 = 10
            L7_2(L8_2, L9_2, L10_2)
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 300
            L10_2 = 700
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            L7_2 = printLog
            L8_2 = "点击使用"
            L7_2(L8_2)
            L7_2 = os
            L7_2 = L7_2.date
            L8_2 = "%S"
            L9_2 = os
            L9_2 = L9_2.time
            L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2()
            L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            while true do
              L8_2 = os
              L8_2 = L8_2.date
              L9_2 = "%S"
              L10_2 = os
              L10_2 = L10_2.time
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2()
              L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              L8_2 = L7_2 - L8_2
              L9_2 = printLog
              L10_2 = "蓝色"
              L9_2(L10_2)
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15756852
              L11_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
              L12_2 = 90
              L13_2 = A0_2
              L14_2 = A1_2
              L15_2 = A2_2
              L16_2 = A3_2
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = _ENV["_蓝色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              elseif L8_2 < -10 or L8_2 < 50 and 1 < L8_2 then
                L9_2 = _ENV["_蓝色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              end
            end
        end
      end
      else
        L7_2 = findMultiColorInRegionFuzzy
        L8_2 = 19157
        L9_2 = "-4|7|0x0f3b83,6|1|0x0051e4,-10|-17|0x002f87,-2|-18|0x0043c6,11|-15|0x051e46,1|-10|0x00338f,39|-18|0x024ddb,37|-29|0x0039a3,22|6|0x0957df"
        L10_2 = 90
        L11_2 = 891
        L12_2 = 242
        L13_2 = 1572
        L14_2 = 805
        L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        y = L8_2
        x = L7_2
        L7_2 = x
        if 0 < L7_2 then
          L7_2 = randomTap
          L8_2 = x
          L9_2 = y
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 300
          L10_2 = 700
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L7_2 = printLog
          L8_2 = "点击飞行棋"
          L7_2(L8_2)
        else
          L7_2 = randomTap
          L8_2 = 1230
          L9_2 = 203
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 500
          L10_2 = 800
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          while true do
            L7_2 = {}
            L8_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
            L7_2[1] = L8_2
            L8_2 = addTSOcrDictEx
            L9_2 = L7_2
            L8_2 = L8_2(L9_2)
            L9_2 = tsFindText
            L10_2 = L8_2
            L11_2 = "移动"
            L12_2 = 183
            L13_2 = 370
            L14_2 = 518
            L15_2 = 1056
            L16_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
            L17_2 = 90
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            _ENV["动"] = L10_2
            _ENV["移"] = L9_2
            L9_2 = findMultiColorInRegionFuzzy
            L10_2 = 15329795
            L11_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
            L12_2 = 90
            L13_2 = 298
            L14_2 = 494
            L15_2 = 377
            L16_2 = 545
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            yx = L10_2
            xy = L9_2
            L9_2 = _ENV["移"]
            if 0 < L9_2 then
              L9_2 = xy
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = _ENV["移"]
                L10_2 = L10_2 + 50
                L11_2 = _ENV["动"]
                L11_2 = L11_2 + 20
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = os
                L9_2 = L9_2.date
                L10_2 = "%S"
                L11_2 = os
                L11_2 = L11_2.time
                L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L11_2()
                L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                while "n" do
                  L10_2 = os
                  L10_2 = L10_2.date
                  L11_2 = "%S"
                  L12_2 = os
                  L12_2 = L12_2.time
                  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2()
                  L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  L10_2 = L9_2 - L10_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 5004448
                  L13_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
                  L14_2 = 90
                  L15_2 = 890
                  L16_2 = 811
                  L17_2 = 1057
                  L18_2 = 895
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  y = L12_2
                  n = L11_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 19157
                  L13_2 = "-4|7|0x0f3b83,6|1|0x0051e4,-10|-17|0x002f87,-2|-18|0x0043c6,11|-15|0x051e46,1|-10|0x00338f,39|-18|0x024ddb,37|-29|0x0039a3,22|6|0x0957df"
                  L14_2 = 90
                  L15_2 = 891
                  L16_2 = 242
                  L17_2 = 1572
                  L18_2 = 805
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  _ENV["色"] = L12_2
                  _ENV["蓝"] = L11_2
                  L11_2 = _ENV["蓝"]
                  if 0 < L11_2 then
                    L11_2 = n
                    if L11_2 < 0 then
                      L11_2 = _ENV["_蓝色飞行棋"]
                      L12_2 = A0_2
                      L13_2 = A1_2
                      L14_2 = A2_2
                      L15_2 = A3_2
                      L16_2 = A4_2
                      return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  end
                  elseif L10_2 < -10 or L10_2 < 50 and 1 < L10_2 then
                    L11_2 = _ENV["_蓝色飞行棋"]
                    L12_2 = A0_2
                    L13_2 = A1_2
                    L14_2 = A2_2
                    L15_2 = A3_2
                    L16_2 = A4_2
                    return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  else
                    L11_2 = n
                    if 0 < L11_2 then
                      L11_2 = randomTap
                      L12_2 = 986
                      L13_2 = 206
                      L14_2 = 10
                      L11_2(L12_2, L13_2, L14_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 200
                      L14_2 = 500
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                end
            end
            else
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 19157
              L11_2 = "-4|7|0x0f3b83,6|1|0x0051e4,-10|-17|0x002f87,-2|-18|0x0043c6,11|-15|0x051e46,1|-10|0x00338f,39|-18|0x024ddb,37|-29|0x0039a3,22|6|0x0957df"
              L12_2 = 90
              L13_2 = 891
              L14_2 = 242
              L15_2 = 1572
              L16_2 = 805
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = x
                L11_2 = y
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 200
                L12_2 = 500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              else
                L9_2 = dialog
                L10_2 = "找不到蓝色飞行棋。。。"
                L9_2(L10_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["_蓝色飞行棋"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  while true do
    L5_2 = _ENV["关闭子女"]
    L5_2()
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 15756852
    L7_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
    L8_2 = 90
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A2_2
    L12_2 = A3_2
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    if 0 < L5_2 then
      L7_2 = randomClick
      L8_2 = 2
      L9_2 = 500
      L10_2 = 102
      L11_2 = 947
      L7_2(L8_2, L9_2, L10_2, L11_2)
      L7_2 = randomTap
      L8_2 = L5_2
      L9_2 = L6_2
      L7_2(L8_2, L9_2)
      L7_2 = mSleep
      L8_2 = math
      L8_2 = L8_2.random
      L9_2 = 800
      L10_2 = 1500
      L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L8_2(L9_2, L10_2)
      L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
      L7_2 = randomClick
      L8_2 = 2
      L9_2 = 500
      L10_2 = 1604
      L11_2 = 84
      L7_2(L8_2, L9_2, L10_2, L11_2)
      while true do
        L7_2 = getColour
        L8_2 = _ENV["红尘试炼"]
        L9_2 = "打开背包"
        L7_2 = L7_2(L8_2, L9_2)
        if L7_2 then
          L7_2 = randomClick
          L8_2 = 2
          L9_2 = 500
          L10_2 = 1604
          L11_2 = 84
          L7_2(L8_2, L9_2, L10_2, L11_2)
        else
          L7_2 = findMultiColorInRegionFuzzy
          L8_2 = 15756852
          L9_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
          L10_2 = 90
          L11_2 = A0_2
          L12_2 = A1_2
          L13_2 = A2_2
          L14_2 = A3_2
          L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
          if 0 < L7_2 then
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 500
            L12_2 = 102
            L13_2 = 947
            L9_2(L10_2, L11_2, L12_2, L13_2)
            L9_2 = randomTap
            L10_2 = L7_2
            L11_2 = L8_2
            L9_2(L10_2, L11_2)
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 800
            L12_2 = 1500
            L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
          else
            return
          end
        end
      end
    else
      L7_2 = getColour
      L8_2 = colorList
      L9_2 = "打开背包"
      L7_2 = L7_2(L8_2, L9_2)
      if L7_2 then
        L7_2 = {}
        L8_2 = "0000600000001f00000001f80000007fe000001fffffff1ffffffffdffffffffeffffc000efe00000001f0000001800000001e3c3ff000f1e3fffe078f3ffff83c79f0ffe1c3cf03effe1e781e3ff0f3c0f0ff079e0783f83cf03c1fc1e781e0fc1fffff9fe1ffffffff0ffffffffc7ffffff9e1ffe1f80f079e0780783cf03c03c1e781e00f0f3c0f007879e07803c3cf03c01e1e781e0078f3fff003c79fff801e3c3ff800f1e0ff0007800000003c00000001c00000000400000000000000000380000001fe0000003fe1fffffffe1fffffff81ffffffe00ffffff800781e070003c0f038001e0781c000f03c0e000781e070003c0f038001e0781c000f03c0e000781e070003e1f87e001ffffffff8ffffffffc7ffffffff3fffffe001e0781c000f03c0e000781e070063c0f038071e0781c03cf03c0e00e781e07007bc0f03803de0781c01ef03c0f01f7fffefc3fbffffffff8ffffffff83fffffff8$使用$1376$37$76"
        L7_2[1] = L8_2
        L8_2 = addTSOcrDictEx
        L9_2 = L7_2
        L8_2 = L8_2(L9_2)
        L9_2 = tsFindText
        L10_2 = L8_2
        L11_2 = "使用"
        L12_2 = 553
        L13_2 = 479
        L14_2 = 795
        L15_2 = 748
        L16_2 = "7BA2D3 , 1E1F2D # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # D4DCE1 , 3B3530"
        L17_2 = 90
        L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L6_2 = L10_2
        L5_2 = L9_2
        L9_2 = findMultiColorInRegionFuzzy
        L10_2 = 14627088
        L11_2 = "-16|0|0xd41008,-20|-9|0x700807,-27|-20|0xe2ab1d,-24|-28|0x980708,-8|-30|0x890b02,10|-29|0xb51206,16|-26|0xd18b07,7|-15|0xa51109,-12|0|0xe40d0e"
        L12_2 = 90
        L13_2 = 300
        L14_2 = 278
        L15_2 = 427
        L16_2 = 403
        L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
        yy = L10_2
        xx = L9_2
        L9_2 = findMultiColorInRegionFuzzy
        L10_2 = 15329795
        L11_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
        L12_2 = 90
        L13_2 = 298
        L14_2 = 494
        L15_2 = 377
        L16_2 = 545
        L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
        yx = L10_2
        xy = L9_2
        if 0 < L5_2 then
          L9_2 = xx
          if 0 < L9_2 then
            L9_2 = xy
            if 0 < L9_2 then
              L9_2 = randomTap
              L10_2 = L5_2
              L11_2 = L6_2
              L12_2 = 10
              L9_2(L10_2, L11_2, L12_2)
              L9_2 = mSleep
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 300
              L12_2 = 700
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L10_2(L11_2, L12_2)
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
              L9_2 = os
              L9_2 = L9_2.date
              L10_2 = "%S"
              L11_2 = os
              L11_2 = L11_2.time
              L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L11_2()
              L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
              while true do
                L10_2 = os
                L10_2 = L10_2.date
                L11_2 = "%S"
                L12_2 = os
                L12_2 = L12_2.time
                L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2()
                L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                L10_2 = L9_2 - L10_2
                L11_2 = findMultiColorInRegionFuzzy
                L12_2 = 15756852
                L13_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L14_2 = 90
                L15_2 = A0_2
                L16_2 = A1_2
                L17_2 = A2_2
                L18_2 = A3_2
                L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L6_2 = L12_2
                L5_2 = L11_2
                if 0 < L5_2 then
                  L11_2 = _ENV["_红色飞行棋"]
                  L12_2 = A0_2
                  L13_2 = A1_2
                  L14_2 = A2_2
                  L15_2 = A3_2
                  L16_2 = A4_2
                  return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                elseif L10_2 < -10 or L10_2 < 50 and 1 < L10_2 then
                  L11_2 = _ENV["_红色飞行棋"]
                  L12_2 = A0_2
                  L13_2 = A1_2
                  L14_2 = A2_2
                  L15_2 = A3_2
                  L16_2 = A4_2
                  return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                end
              end
          end
        end
        else
          L9_2 = findMultiColorInRegionFuzzy
          L10_2 = 14422029
          L11_2 = "20|-18|0xea100f,-1|-8|0x4c0802,-6|-6|0x2f0201,-8|3|0xd41008,-5|-12|0xa10907,18|-18|0xd70d0b,-5|3|0xdd0f0b,20|-17|0xe8130e,-7|-19|0xd00b0a"
          L12_2 = 90
          L13_2 = 881
          L14_2 = 244
          L15_2 = 1589
          L16_2 = 816
          L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          L6_2 = L10_2
          L5_2 = L9_2
          L9_2 = findMultiColorInRegionFuzzy
          L10_2 = 11931914
          L11_2 = "0|-10|0xb6080a,25|-9|0xa12d09,40|-7|0xcc8404,48|2|0xc5240d,39|11|0xb2620b,34|22|0xd8360e,10|32|0x900a03,6|29|0xc17604,7|20|0xc40c08"
          L12_2 = 90
          L13_2 = 893
          L14_2 = 255
          L15_2 = 1579
          L16_2 = 807
          L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          d = L10_2
          c = L9_2
          if 0 < L5_2 then
            L9_2 = randomTap
            L10_2 = L5_2
            L11_2 = L6_2
            L12_2 = 10
            L9_2(L10_2, L11_2, L12_2)
            L9_2 = mSleep
            L10_2 = math
            L10_2 = L10_2.random
            L11_2 = 300
            L12_2 = 700
            L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L10_2(L11_2, L12_2)
            L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
          else
            L9_2 = c
            if 0 < L9_2 then
              L9_2 = randomTap
              L10_2 = c
              L11_2 = d
              L12_2 = 10
              L9_2(L10_2, L11_2, L12_2)
              L9_2 = mSleep
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 300
              L12_2 = 700
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L10_2(L11_2, L12_2)
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            else
              L9_2 = randomTap
              L10_2 = 1230
              L11_2 = 203
              L12_2 = 10
              L9_2(L10_2, L11_2, L12_2)
              L9_2 = mSleep
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 500
              L12_2 = 800
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L10_2(L11_2, L12_2)
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
              while true do
                L9_2 = _ENV["关闭子女"]
                L9_2()
                L9_2 = {}
                L10_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
                L9_2[1] = L10_2
                L10_2 = addTSOcrDictEx
                L11_2 = L9_2
                L10_2 = L10_2(L11_2)
                L11_2 = tsFindText
                L12_2 = L10_2
                L13_2 = "移动"
                L14_2 = 183
                L15_2 = 370
                L16_2 = 518
                L17_2 = 1056
                L18_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
                L19_2 = 90
                L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
                _ENV["动"] = L12_2
                _ENV["移"] = L11_2
                L11_2 = findMultiColorInRegionFuzzy
                L12_2 = 15329795
                L13_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
                L14_2 = 90
                L15_2 = 298
                L16_2 = 494
                L17_2 = 377
                L18_2 = 545
                L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                yx = L12_2
                xy = L11_2
                L11_2 = _ENV["移"]
                if 0 < L11_2 then
                  L11_2 = xy
                  if 0 < L11_2 then
                    L11_2 = randomTap
                    L12_2 = _ENV["移"]
                    L12_2 = L12_2 + 50
                    L13_2 = _ENV["动"]
                    L13_2 = L13_2 + 20
                    L11_2(L12_2, L13_2)
                    L11_2 = mSleep
                    L12_2 = math
                    L12_2 = L12_2.random
                    L13_2 = 800
                    L14_2 = 1500
                    L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
                    L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    L11_2 = randomTap
                    L12_2 = 986
                    L13_2 = 206
                    L14_2 = 10
                    L11_2(L12_2, L13_2, L14_2)
                    L11_2 = mSleep
                    L12_2 = math
                    L12_2 = L12_2.random
                    L13_2 = 800
                    L14_2 = 1500
                    L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
                    L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    L11_2 = randomTap
                    L12_2 = 986
                    L13_2 = 206
                    L14_2 = 10
                    L11_2(L12_2, L13_2, L14_2)
                    L11_2 = mSleep
                    L12_2 = math
                    L12_2 = L12_2.random
                    L13_2 = 800
                    L14_2 = 1500
                    L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
                    L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    L11_2 = os
                    L11_2 = L11_2.date
                    L12_2 = "%S"
                    L13_2 = os
                    L13_2 = L13_2.time
                    L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L13_2()
                    L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    while "n" do
                      L12_2 = os
                      L12_2 = L12_2.date
                      L13_2 = "%S"
                      L14_2 = os
                      L14_2 = L14_2.time
                      L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L14_2()
                      L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                      L12_2 = L11_2 - L12_2
                      L13_2 = findMultiColorInRegionFuzzy
                      L14_2 = 5004448
                      L15_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
                      L16_2 = 90
                      L17_2 = 890
                      L18_2 = 811
                      L19_2 = 1057
                      L20_2 = 895
                      L13_2, L14_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                      L6_2 = L14_2
                      n = L13_2
                      L13_2 = findMultiColorInRegionFuzzy
                      L14_2 = 14422029
                      L15_2 = "20|-18|0xea100f,-1|-8|0x4c0802,-6|-6|0x2f0201,-8|3|0xd41008,-5|-12|0xa10907,18|-18|0xd70d0b,-5|3|0xdd0f0b,20|-17|0xe8130e,-7|-19|0xd00b0a"
                      L16_2 = 90
                      L17_2 = 881
                      L18_2 = 244
                      L19_2 = 1589
                      L20_2 = 816
                      L13_2, L14_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                      _ENV["色1"] = L14_2
                      _ENV["红1"] = L13_2
                      L13_2 = findMultiColorInRegionFuzzy
                      L14_2 = 11931914
                      L15_2 = "0|-10|0xb6080a,25|-9|0xa12d09,40|-7|0xcc8404,48|2|0xc5240d,39|11|0xb2620b,34|22|0xd8360e,10|32|0x900a03,6|29|0xc17604,7|20|0xc40c08"
                      L16_2 = 90
                      L17_2 = 893
                      L18_2 = 255
                      L19_2 = 1579
                      L20_2 = 807
                      L13_2, L14_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                      _ENV["色2"] = L14_2
                      _ENV["红2"] = L13_2
                      L13_2 = _ENV["红1"]
                      if 0 < L13_2 then
                        L13_2 = n
                        if L13_2 < 0 then
                          goto lbl_400
                        end
                      end
                      L13_2 = _ENV["红2"]
                      if 0 < L13_2 then
                        L13_2 = n
                        if L13_2 < 0 then
                          ::lbl_400::
                          L13_2 = _ENV["_红色飞行棋"]
                          L14_2 = A0_2
                          L15_2 = A1_2
                          L16_2 = A2_2
                          L17_2 = A3_2
                          L18_2 = A4_2
                          return L13_2(L14_2, L15_2, L16_2, L17_2, L18_2)
                      end
                      elseif L12_2 < -10 or L12_2 < 50 and 1 < L12_2 then
                        L13_2 = _ENV["_红色飞行棋"]
                        L14_2 = A0_2
                        L15_2 = A1_2
                        L16_2 = A2_2
                        L17_2 = A3_2
                        L18_2 = A4_2
                        return L13_2(L14_2, L15_2, L16_2, L17_2, L18_2)
                      else
                        L13_2 = n
                        if 0 < L13_2 then
                          L13_2 = randomTap
                          L14_2 = 986
                          L15_2 = 206
                          L16_2 = 10
                          L13_2(L14_2, L15_2, L16_2)
                          L13_2 = mSleep
                          L14_2 = math
                          L14_2 = L14_2.random
                          L15_2 = 200
                          L16_2 = 500
                          L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L14_2(L15_2, L16_2)
                          L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                        end
                      end
                    end
                end
                else
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 14422029
                  L13_2 = "20|-18|0xea100f,-1|-8|0x4c0802,-6|-6|0x2f0201,-8|3|0xd41008,-5|-12|0xa10907,18|-18|0xd70d0b,-5|3|0xdd0f0b,20|-17|0xe8130e,-7|-19|0xd00b0a"
                  L14_2 = 90
                  L15_2 = 881
                  L16_2 = 244
                  L17_2 = 1589
                  L18_2 = 816
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  d = L12_2
                  c = L11_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 11931914
                  L13_2 = "0|-10|0xb6080a,25|-9|0xa12d09,40|-7|0xcc8404,48|2|0xc5240d,39|11|0xb2620b,34|22|0xd8360e,10|32|0x900a03,6|29|0xc17604,7|20|0xc40c08"
                  L14_2 = 90
                  L15_2 = 893
                  L16_2 = 255
                  L17_2 = 1579
                  L18_2 = 807
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  L6_2 = L12_2
                  L5_2 = L11_2
                  if 0 < L5_2 then
                    L11_2 = randomTap
                    L12_2 = L5_2
                    L13_2 = L6_2
                    L11_2(L12_2, L13_2)
                    L11_2 = mSleep
                    L12_2 = math
                    L12_2 = L12_2.random
                    L13_2 = 200
                    L14_2 = 500
                    L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
                    L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                  else
                    L11_2 = c
                    if 0 < L11_2 then
                      L11_2 = randomTap
                      L12_2 = c
                      L13_2 = d
                      L11_2(L12_2, L13_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 200
                      L14_2 = 500
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
                    else
                      L11_2 = dialog
                      L12_2 = "找不到红色飞行棋。。。"
                      L11_2(L12_2)
                    end
                  end
                end
              end
            end
          end
        end
      else
        L7_2 = getColour
        L8_2 = _ENV["商人鬼魂"]
        L9_2 = "关闭对话框"
        L7_2 = L7_2(L8_2, L9_2)
        if L7_2 then
          L7_2 = randomClick
          L8_2 = 2
          L9_2 = 300
          L10_2 = 1873
          L11_2 = 797
          L7_2(L8_2, L9_2, L10_2, L11_2)
        else
          L7_2 = printLog
          L8_2 = "打开背包"
          L7_2(L8_2)
          L7_2 = randomClick
          L8_2 = 2
          L9_2 = 300
          L10_2 = 1695
          L11_2 = 985
          L12_2 = 10
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
        end
      end
    end
  end
end

_ENV["_红色飞行棋"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  while "x" do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 15756852
    L7_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
    L8_2 = 90
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A2_2
    L12_2 = A3_2
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    y = L6_2
    x = L5_2
    L5_2 = x
    if 0 < L5_2 then
      L5_2 = randomTap
      L6_2 = x
      L7_2 = y
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = printLog
      L6_2 = "前往挖图地点"
      L5_2(L6_2)
      if A4_2 == "大唐国境" or A4_2 == "五庄观" then
        L5_2 = randomTap
        L6_2 = x
        L7_2 = y
        L5_2(L6_2, L7_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 500
        L8_2 = 800
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      end
      L5_2 = randomTap
      L6_2 = 1600
      L7_2 = 86
      L8_2 = 10
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      Bool = true
      break
    else
      L5_2 = {}
      L6_2 = "0000600000001f00000001f80000007fe000001fffffff1ffffffffdffffffffeffffc000efe00000001f0000001800000001e3c3ff000f1e3fffe078f3ffff83c79f0ffe1c3cf03effe1e781e3ff0f3c0f0ff079e0783f83cf03c1fc1e781e0fc1fffff9fe1ffffffff0ffffffffc7ffffff9e1ffe1f80f079e0780783cf03c03c1e781e00f0f3c0f007879e07803c3cf03c01e1e781e0078f3fff003c79fff801e3c3ff800f1e0ff0007800000003c00000001c00000000400000000000000000380000001fe0000003fe1fffffffe1fffffff81ffffffe00ffffff800781e070003c0f038001e0781c000f03c0e000781e070003c0f038001e0781c000f03c0e000781e070003e1f87e001ffffffff8ffffffffc7ffffffff3fffffe001e0781c000f03c0e000781e070063c0f038071e0781c03cf03c0e00e781e07007bc0f03803de0781c01ef03c0f01f7fffefc3fbffffffff8ffffffff83fffffff8$使用$1376$37$76"
      L5_2[1] = L6_2
      L6_2 = addTSOcrDictEx
      L7_2 = L5_2
      L6_2 = L6_2(L7_2)
      L7_2 = tsFindText
      L8_2 = L6_2
      L9_2 = "使用"
      L10_2 = 553
      L11_2 = 479
      L12_2 = 795
      L13_2 = 748
      L14_2 = "7BA2D3 , 1E1F2D # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # D4DCE1 , 3B3530"
      L15_2 = 90
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      y = L8_2
      x = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 15786766
      L9_2 = "-11|-10|0x776403,-14|-18|0xe4cd00,-10|-23|0xa26f3a,20|-21|0xc4a813,30|-23|0x650dc6,31|-29|0xa98039,25|-8|0x98545d,3|-10|0x1b1803,-4|-4|0xddc403"
      L10_2 = 90
      L11_2 = 303
      L12_2 = 281
      L13_2 = 429
      L14_2 = 403
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yy = L8_2
      xx = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 15329795
      L9_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
      L10_2 = 90
      L11_2 = 298
      L12_2 = 494
      L13_2 = 377
      L14_2 = 545
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yx = L8_2
      xy = L7_2
      L7_2 = x
      if 0 < L7_2 then
        L7_2 = xx
        if 0 < L7_2 then
          L7_2 = xy
          if 0 < L7_2 then
            L7_2 = randomTap
            L8_2 = x
            L9_2 = y
            L10_2 = 10
            L7_2(L8_2, L9_2, L10_2)
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 300
            L10_2 = 700
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            L7_2 = printLog
            L8_2 = "点击使用"
            L7_2(L8_2)
            L7_2 = os
            L7_2 = L7_2.date
            L8_2 = "%S"
            L9_2 = os
            L9_2 = L9_2.time
            L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2()
            L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            while true do
              L8_2 = os
              L8_2 = L8_2.date
              L9_2 = "%S"
              L10_2 = os
              L10_2 = L10_2.time
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2()
              L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              L8_2 = L7_2 - L8_2
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15756852
              L11_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
              L12_2 = 90
              L13_2 = A0_2
              L14_2 = A1_2
              L15_2 = A2_2
              L16_2 = A3_2
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = _ENV["_黄色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              elseif L8_2 < -10 or L8_2 < 50 and 1 < L8_2 then
                L9_2 = _ENV["_黄色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              end
            end
        end
      end
      else
        L7_2 = findMultiColorInRegionFuzzy
        L8_2 = 15127052
        L9_2 = "-7|1|0x211c01,-26|-2|0xc6a200,-13|-9|0xf0e907,-2|-23|0xba9116,8|-9|0xedde0a,4|-12|0xe8d709,32|-11|0xdfc52f,4|4|0xcd9b00,-7|9|0xf0ec18"
        L10_2 = 90
        L11_2 = 892
        L12_2 = 253
        L13_2 = 1580
        L14_2 = 810
        L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        y = L8_2
        x = L7_2
        L7_2 = x
        if 0 < L7_2 then
          L7_2 = randomTap
          L8_2 = x
          L9_2 = y
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 300
          L10_2 = 700
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L7_2 = printLog
          L8_2 = "点击飞行棋"
          L7_2(L8_2)
        else
          L7_2 = randomTap
          L8_2 = 1230
          L9_2 = 203
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 500
          L10_2 = 800
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          while true do
            L7_2 = {}
            L8_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
            L7_2[1] = L8_2
            L8_2 = addTSOcrDictEx
            L9_2 = L7_2
            L8_2 = L8_2(L9_2)
            L9_2 = tsFindText
            L10_2 = L8_2
            L11_2 = "移动"
            L12_2 = 183
            L13_2 = 370
            L14_2 = 518
            L15_2 = 1056
            L16_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
            L17_2 = 90
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            _ENV["动"] = L10_2
            _ENV["移"] = L9_2
            L9_2 = findMultiColorInRegionFuzzy
            L10_2 = 15329795
            L11_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
            L12_2 = 90
            L13_2 = 298
            L14_2 = 494
            L15_2 = 377
            L16_2 = 545
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            yx = L10_2
            xy = L9_2
            L9_2 = _ENV["移"]
            if 0 < L9_2 then
              L9_2 = xy
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = _ENV["移"]
                L10_2 = L10_2 + 50
                L11_2 = _ENV["动"]
                L11_2 = L11_2 + 20
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = os
                L9_2 = L9_2.date
                L10_2 = "%S"
                L11_2 = os
                L11_2 = L11_2.time
                L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L11_2()
                L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                while "n" do
                  L10_2 = os
                  L10_2 = L10_2.date
                  L11_2 = "%S"
                  L12_2 = os
                  L12_2 = L12_2.time
                  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2()
                  L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  L10_2 = L9_2 - L10_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 5004448
                  L13_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
                  L14_2 = 90
                  L15_2 = 890
                  L16_2 = 811
                  L17_2 = 1057
                  L18_2 = 895
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  y = L12_2
                  n = L11_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 15127052
                  L13_2 = "-7|1|0x211c01,-26|-2|0xc6a200,-13|-9|0xf0e907,-2|-23|0xba9116,8|-9|0xedde0a,4|-12|0xe8d709,32|-11|0xdfc52f,4|4|0xcd9b00,-7|9|0xf0ec18"
                  L14_2 = 90
                  L15_2 = 892
                  L16_2 = 253
                  L17_2 = 1580
                  L18_2 = 810
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  _ENV["色"] = L12_2
                  _ENV["黄"] = L11_2
                  L11_2 = _ENV["黄"]
                  if 0 < L11_2 then
                    L11_2 = n
                    if L11_2 < 0 then
                      L11_2 = _ENV["_黄色飞行棋"]
                      L12_2 = A0_2
                      L13_2 = A1_2
                      L14_2 = A2_2
                      L15_2 = A3_2
                      L16_2 = A4_2
                      return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  end
                  elseif L10_2 < -10 or L10_2 < 50 and 1 < L10_2 then
                    L11_2 = _ENV["_黄色飞行棋"]
                    L12_2 = A0_2
                    L13_2 = A1_2
                    L14_2 = A2_2
                    L15_2 = A3_2
                    L16_2 = A4_2
                    return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  else
                    L11_2 = n
                    if 0 < L11_2 then
                      L11_2 = randomTap
                      L12_2 = 986
                      L13_2 = 206
                      L14_2 = 10
                      L11_2(L12_2, L13_2, L14_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 200
                      L14_2 = 500
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                end
            end
            else
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15127052
              L11_2 = "-7|1|0x211c01,-26|-2|0xc6a200,-13|-9|0xf0e907,-2|-23|0xba9116,8|-9|0xedde0a,4|-12|0xe8d709,32|-11|0xdfc52f,4|4|0xcd9b00,-7|9|0xf0ec18"
              L12_2 = 90
              L13_2 = 892
              L14_2 = 253
              L15_2 = 1580
              L16_2 = 810
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = x
                L11_2 = y
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 200
                L12_2 = 500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              else
                L9_2 = dialog
                L10_2 = "找不到黄色飞行棋。。。"
                L9_2(L10_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["_黄色飞行棋"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  while "x" do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 15756852
    L7_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
    L8_2 = 90
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A2_2
    L12_2 = A3_2
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    y = L6_2
    x = L5_2
    L5_2 = x
    if 0 < L5_2 then
      L5_2 = randomTap
      L6_2 = x
      L7_2 = y
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = printLog
      L6_2 = "前往挖图地点"
      L5_2(L6_2)
      if A4_2 == "大唐国境" or A4_2 == "五庄观" then
        L5_2 = randomTap
        L6_2 = x
        L7_2 = y
        L5_2(L6_2, L7_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 500
        L8_2 = 800
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      end
      L5_2 = randomTap
      L6_2 = 1600
      L7_2 = 86
      L8_2 = 10
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      Bool = true
      break
    else
      L5_2 = {}
      L6_2 = "0000600000001f00000001f80000007fe000001fffffff1ffffffffdffffffffeffffc000efe00000001f0000001800000001e3c3ff000f1e3fffe078f3ffff83c79f0ffe1c3cf03effe1e781e3ff0f3c0f0ff079e0783f83cf03c1fc1e781e0fc1fffff9fe1ffffffff0ffffffffc7ffffff9e1ffe1f80f079e0780783cf03c03c1e781e00f0f3c0f007879e07803c3cf03c01e1e781e0078f3fff003c79fff801e3c3ff800f1e0ff0007800000003c00000001c00000000400000000000000000380000001fe0000003fe1fffffffe1fffffff81ffffffe00ffffff800781e070003c0f038001e0781c000f03c0e000781e070003c0f038001e0781c000f03c0e000781e070003e1f87e001ffffffff8ffffffffc7ffffffff3fffffe001e0781c000f03c0e000781e070063c0f038071e0781c03cf03c0e00e781e07007bc0f03803de0781c01ef03c0f01f7fffefc3fbffffffff8ffffffff83fffffff8$使用$1376$37$76"
      L5_2[1] = L6_2
      L6_2 = addTSOcrDictEx
      L7_2 = L5_2
      L6_2 = L6_2(L7_2)
      L7_2 = tsFindText
      L8_2 = L6_2
      L9_2 = "使用"
      L10_2 = 553
      L11_2 = 479
      L12_2 = 795
      L13_2 = 748
      L14_2 = "7BA2D3 , 1E1F2D # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # D4DCE1 , 3B3530"
      L15_2 = 90
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      y = L8_2
      x = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 3718656
      L9_2 = "-9|5|0x38de00,5|10|0x32c800,23|2|0x8c89aa,23|-23|0x38ad00,8|-24|0x8ab606,-12|-26|0x84b007,-24|-25|0x2b9300,-24|-17|0x369a02,-17|-7|0x29b100"
      L10_2 = 90
      L11_2 = 295
      L12_2 = 277
      L13_2 = 427
      L14_2 = 402
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yy = L8_2
      xx = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 15329795
      L9_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
      L10_2 = 90
      L11_2 = 298
      L12_2 = 494
      L13_2 = 377
      L14_2 = 545
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yx = L8_2
      xy = L7_2
      L7_2 = x
      if 0 < L7_2 then
        L7_2 = xx
        if 0 < L7_2 then
          L7_2 = xy
          if 0 < L7_2 then
            L7_2 = randomTap
            L8_2 = x
            L9_2 = y
            L10_2 = 10
            L7_2(L8_2, L9_2, L10_2)
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 300
            L10_2 = 700
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            L7_2 = printLog
            L8_2 = "点击使用"
            L7_2(L8_2)
            L7_2 = os
            L7_2 = L7_2.date
            L8_2 = "%S"
            L9_2 = os
            L9_2 = L9_2.time
            L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2()
            L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            while true do
              L8_2 = os
              L8_2 = L8_2.date
              L9_2 = "%S"
              L10_2 = os
              L10_2 = L10_2.time
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2()
              L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              L8_2 = L7_2 - L8_2
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15756852
              L11_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
              L12_2 = 90
              L13_2 = A0_2
              L14_2 = A1_2
              L15_2 = A2_2
              L16_2 = A3_2
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = _ENV["_绿色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              elseif L8_2 < -10 or L8_2 < 50 and 1 < L8_2 then
                L9_2 = _ENV["_绿色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              end
            end
        end
      end
      else
        L7_2 = findMultiColorInRegionFuzzy
        L8_2 = 3725568
        L9_2 = "14|11|0x409b07,8|16|0x38eb00,-2|20|0x28b100,26|4|0x41ed00,29|-4|0x3beb00,37|-11|0x3bad04,-2|-5|0x349400,17|12|0x38aa00,19|-3|0x3cc500"
        L10_2 = 90
        L11_2 = 892
        L12_2 = 253
        L13_2 = 1580
        L14_2 = 810
        L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        y = L8_2
        x = L7_2
        L7_2 = x
        if 0 < L7_2 then
          L7_2 = randomTap
          L8_2 = x
          L9_2 = y
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 300
          L10_2 = 700
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L7_2 = printLog
          L8_2 = "点击飞行棋"
          L7_2(L8_2)
        else
          L7_2 = randomTap
          L8_2 = 1230
          L9_2 = 203
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 500
          L10_2 = 800
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          while true do
            L7_2 = {}
            L8_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
            L7_2[1] = L8_2
            L8_2 = addTSOcrDictEx
            L9_2 = L7_2
            L8_2 = L8_2(L9_2)
            L9_2 = tsFindText
            L10_2 = L8_2
            L11_2 = "移动"
            L12_2 = 183
            L13_2 = 370
            L14_2 = 518
            L15_2 = 1056
            L16_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
            L17_2 = 90
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            _ENV["动"] = L10_2
            _ENV["移"] = L9_2
            L9_2 = findMultiColorInRegionFuzzy
            L10_2 = 15329795
            L11_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
            L12_2 = 90
            L13_2 = 298
            L14_2 = 494
            L15_2 = 377
            L16_2 = 545
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            yx = L10_2
            xy = L9_2
            L9_2 = _ENV["移"]
            if 0 < L9_2 then
              L9_2 = xy
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = _ENV["移"]
                L10_2 = L10_2 + 50
                L11_2 = _ENV["动"]
                L11_2 = L11_2 + 20
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = os
                L9_2 = L9_2.date
                L10_2 = "%S"
                L11_2 = os
                L11_2 = L11_2.time
                L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L11_2()
                L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                while "n" do
                  L10_2 = os
                  L10_2 = L10_2.date
                  L11_2 = "%S"
                  L12_2 = os
                  L12_2 = L12_2.time
                  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2()
                  L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  L10_2 = L9_2 - L10_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 5004448
                  L13_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
                  L14_2 = 90
                  L15_2 = 890
                  L16_2 = 811
                  L17_2 = 1057
                  L18_2 = 895
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  y = L12_2
                  n = L11_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 3725568
                  L13_2 = "14|11|0x409b07,8|16|0x38eb00,-2|20|0x28b100,26|4|0x41ed00,29|-4|0x3beb00,37|-11|0x3bad04,-2|-5|0x349400,17|12|0x38aa00,19|-3|0x3cc500"
                  L14_2 = 90
                  L15_2 = 892
                  L16_2 = 253
                  L17_2 = 1580
                  L18_2 = 810
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  _ENV["色"] = L12_2
                  _ENV["绿"] = L11_2
                  L11_2 = _ENV["绿"]
                  if 0 < L11_2 then
                    L11_2 = n
                    if L11_2 < 0 then
                      L11_2 = _ENV["_绿色飞行棋"]
                      L12_2 = A0_2
                      L13_2 = A1_2
                      L14_2 = A2_2
                      L15_2 = A3_2
                      L16_2 = A4_2
                      return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  end
                  elseif L10_2 < -10 or L10_2 < 50 and 1 < L10_2 then
                    L11_2 = _ENV["_绿色飞行棋"]
                    L12_2 = A0_2
                    L13_2 = A1_2
                    L14_2 = A2_2
                    L15_2 = A3_2
                    L16_2 = A4_2
                    return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  else
                    L11_2 = n
                    if 0 < L11_2 then
                      L11_2 = randomTap
                      L12_2 = 986
                      L13_2 = 206
                      L14_2 = 10
                      L11_2(L12_2, L13_2, L14_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 200
                      L14_2 = 500
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                end
            end
            else
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 3725568
              L11_2 = "14|11|0x409b07,8|16|0x38eb00,-2|20|0x28b100,26|4|0x41ed00,29|-4|0x3beb00,37|-11|0x3bad04,-2|-5|0x349400,17|12|0x38aa00,19|-3|0x3cc500"
              L12_2 = 90
              L13_2 = 892
              L14_2 = 253
              L15_2 = 1580
              L16_2 = 810
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = x
                L11_2 = y
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 200
                L12_2 = 500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              else
                L9_2 = dialog
                L10_2 = "找不到绿色飞行棋。。。"
                L9_2(L10_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["_绿色飞行棋"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  while "x" do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = 15756852
    L7_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
    L8_2 = 90
    L9_2 = A0_2
    L10_2 = A1_2
    L11_2 = A2_2
    L12_2 = A3_2
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    y = L6_2
    x = L5_2
    L5_2 = x
    if 0 < L5_2 then
      L5_2 = randomTap
      L6_2 = x
      L7_2 = y
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = printLog
      L6_2 = "前往挖图地点"
      L5_2(L6_2)
      if A4_2 == "大唐国境" or A4_2 == "五庄观" then
        L5_2 = randomTap
        L6_2 = x
        L7_2 = y
        L5_2(L6_2, L7_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 500
        L8_2 = 800
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      end
      L5_2 = randomTap
      L6_2 = 1600
      L7_2 = 86
      L8_2 = 10
      L5_2(L6_2, L7_2, L8_2)
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 500
      L8_2 = 800
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      Bool = true
      break
    else
      L5_2 = {}
      L6_2 = "0000600000001f00000001f80000007fe000001fffffff1ffffffffdffffffffeffffc000efe00000001f0000001800000001e3c3ff000f1e3fffe078f3ffff83c79f0ffe1c3cf03effe1e781e3ff0f3c0f0ff079e0783f83cf03c1fc1e781e0fc1fffff9fe1ffffffff0ffffffffc7ffffff9e1ffe1f80f079e0780783cf03c03c1e781e00f0f3c0f007879e07803c3cf03c01e1e781e0078f3fff003c79fff801e3c3ff800f1e0ff0007800000003c00000001c00000000400000000000000000380000001fe0000003fe1fffffffe1fffffff81ffffffe00ffffff800781e070003c0f038001e0781c000f03c0e000781e070003c0f038001e0781c000f03c0e000781e070003e1f87e001ffffffff8ffffffffc7ffffffff3fffffe001e0781c000f03c0e000781e070063c0f038071e0781c03cf03c0e00e781e07007bc0f03803de0781c01ef03c0f01f7fffefc3fbffffffff8ffffffff83fffffff8$使用$1376$37$76"
      L5_2[1] = L6_2
      L6_2 = addTSOcrDictEx
      L7_2 = L5_2
      L6_2 = L6_2(L7_2)
      L7_2 = tsFindText
      L8_2 = L6_2
      L9_2 = "使用"
      L10_2 = 553
      L11_2 = 479
      L12_2 = 795
      L13_2 = 748
      L14_2 = "7BA2D3 , 1E1F2D # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # 85ACDD , 282937 # D4DCE1 , 3B3530"
      L15_2 = 90
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      y = L8_2
      x = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 12958586
      L9_2 = "11|-9|0xc0b998,28|-21|0xc09a3c,29|-26|0xb38e36,6|-30|0xe6d128,-4|-30|0xcf8402,-11|-8|0xd0d4b2,-11|13|0x9699b2,4|3|0xc5b144,22|-9|0xa89c77"
      L10_2 = 90
      L11_2 = 300
      L12_2 = 273
      L13_2 = 426
      L14_2 = 402
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yy = L8_2
      xx = L7_2
      L7_2 = findMultiColorInRegionFuzzy
      L8_2 = 15329795
      L9_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
      L10_2 = 90
      L11_2 = 298
      L12_2 = 494
      L13_2 = 377
      L14_2 = 545
      L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      yx = L8_2
      xy = L7_2
      L7_2 = x
      if 0 < L7_2 then
        L7_2 = xx
        if 0 < L7_2 then
          L7_2 = xy
          if 0 < L7_2 then
            L7_2 = randomTap
            L8_2 = x
            L9_2 = y
            L10_2 = 10
            L7_2(L8_2, L9_2, L10_2)
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 500
            L10_2 = 800
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            L7_2 = os
            L7_2 = L7_2.date
            L8_2 = "%S"
            L9_2 = os
            L9_2 = L9_2.time
            L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2()
            L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
            while true do
              L8_2 = os
              L8_2 = L8_2.date
              L9_2 = "%S"
              L10_2 = os
              L10_2 = L10_2.time
              L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2()
              L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              L8_2 = L7_2 - L8_2
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15756852
              L11_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
              L12_2 = 90
              L13_2 = A0_2
              L14_2 = A1_2
              L15_2 = A2_2
              L16_2 = A3_2
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = _ENV["_白色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              elseif L8_2 < -10 or L8_2 < 50 and 1 < L8_2 then
                L9_2 = _ENV["_白色飞行棋"]
                L10_2 = A0_2
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                return L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
              end
            end
        end
      end
      else
        L7_2 = findMultiColorInRegionFuzzy
        L8_2 = 15265245
        L9_2 = "-14|-8|0xc1bb96,-11|-21|0xceccb5,3|-23|0xe8e8d0,22|-33|0xc8c7ae,23|-20|0xf0f8ef,7|-9|0xb6b08b,-1|-1|0xe8eedd,16|-12|0xeef6e9,0|-2|0xecf4e4"
        L10_2 = 90
        L11_2 = 892
        L12_2 = 253
        L13_2 = 1580
        L14_2 = 810
        L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        y = L8_2
        x = L7_2
        L7_2 = x
        if 0 < L7_2 then
          L7_2 = randomTap
          L8_2 = x
          L9_2 = y
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 300
          L10_2 = 700
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L7_2 = printLog
          L8_2 = "点击飞行棋"
          L7_2(L8_2)
        else
          L7_2 = randomTap
          L8_2 = 1230
          L9_2 = 203
          L10_2 = 10
          L7_2(L8_2, L9_2, L10_2)
          L7_2 = mSleep
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = 500
          L10_2 = 800
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2(L9_2, L10_2)
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          while true do
            L7_2 = {}
            L8_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
            L7_2[1] = L8_2
            L8_2 = addTSOcrDictEx
            L9_2 = L7_2
            L8_2 = L8_2(L9_2)
            L9_2 = tsFindText
            L10_2 = L8_2
            L11_2 = "移动"
            L12_2 = 183
            L13_2 = 370
            L14_2 = 518
            L15_2 = 1056
            L16_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
            L17_2 = 90
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            _ENV["动"] = L10_2
            _ENV["移"] = L9_2
            L9_2 = findMultiColorInRegionFuzzy
            L10_2 = 15329795
            L11_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
            L12_2 = 90
            L13_2 = 298
            L14_2 = 494
            L15_2 = 377
            L16_2 = 545
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
            yx = L10_2
            xy = L9_2
            L9_2 = _ENV["移"]
            if 0 < L9_2 then
              L9_2 = xy
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = _ENV["移"]
                L10_2 = L10_2 + 50
                L11_2 = _ENV["动"]
                L11_2 = L11_2 + 20
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = randomTap
                L10_2 = 986
                L11_2 = 206
                L12_2 = 10
                L9_2(L10_2, L11_2, L12_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 800
                L12_2 = 1500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                L9_2 = os
                L9_2 = L9_2.date
                L10_2 = "%S"
                L11_2 = os
                L11_2 = L11_2.time
                L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L11_2()
                L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                while "n" do
                  L10_2 = os
                  L10_2 = L10_2.date
                  L11_2 = "%S"
                  L12_2 = os
                  L12_2 = L12_2.time
                  L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2()
                  L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  L10_2 = L9_2 - L10_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 5004448
                  L13_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
                  L14_2 = 90
                  L15_2 = 890
                  L16_2 = 811
                  L17_2 = 1057
                  L18_2 = 895
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  y = L12_2
                  n = L11_2
                  L11_2 = findMultiColorInRegionFuzzy
                  L12_2 = 15265245
                  L13_2 = "-14|-8|0xc1bb96,-11|-21|0xceccb5,3|-23|0xe8e8d0,22|-33|0xc8c7ae,23|-20|0xf0f8ef,7|-9|0xb6b08b,-1|-1|0xe8eedd,16|-12|0xeef6e9,0|-2|0xecf4e4"
                  L14_2 = 90
                  L15_2 = 892
                  L16_2 = 253
                  L17_2 = 1580
                  L18_2 = 810
                  L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                  _ENV["色"] = L12_2
                  _ENV["白"] = L11_2
                  L11_2 = _ENV["白"]
                  if 0 < L11_2 then
                    L11_2 = n
                    if L11_2 < 0 then
                      L11_2 = _ENV["_白色飞行棋"]
                      L12_2 = A0_2
                      L13_2 = A1_2
                      L14_2 = A2_2
                      L15_2 = A3_2
                      L16_2 = A4_2
                      return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  end
                  elseif L10_2 < -10 or L10_2 < 50 and 1 < L10_2 then
                    L11_2 = _ENV["_白色飞行棋"]
                    L12_2 = A0_2
                    L13_2 = A1_2
                    L14_2 = A2_2
                    L15_2 = A3_2
                    L16_2 = A4_2
                    return L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                  else
                    L11_2 = n
                    if 0 < L11_2 then
                      L11_2 = randomTap
                      L12_2 = 986
                      L13_2 = 206
                      L14_2 = 10
                      L11_2(L12_2, L13_2, L14_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 200
                      L14_2 = 500
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                end
            end
            else
              L9_2 = findMultiColorInRegionFuzzy
              L10_2 = 15265245
              L11_2 = "-14|-8|0xc1bb96,-11|-21|0xceccb5,3|-23|0xe8e8d0,22|-33|0xc8c7ae,23|-20|0xf0f8ef,7|-9|0xb6b08b,-1|-1|0xe8eedd,16|-12|0xeef6e9,0|-2|0xecf4e4"
              L12_2 = 90
              L13_2 = 892
              L14_2 = 253
              L15_2 = 1580
              L16_2 = 810
              L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
              y = L10_2
              x = L9_2
              L9_2 = x
              if 0 < L9_2 then
                L9_2 = randomTap
                L10_2 = x
                L11_2 = y
                L9_2(L10_2, L11_2)
                L9_2 = mSleep
                L10_2 = math
                L10_2 = L10_2.random
                L11_2 = 200
                L12_2 = 500
                L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L10_2(L11_2, L12_2)
                L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
              else
                L9_2 = dialog
                L10_2 = "找不到白色飞行棋。。。"
                L9_2(L10_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["_白色飞行棋"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2)
  local L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L6_2 = randomTap
  L7_2 = 1230
  L8_2 = 203
  L9_2 = 10
  L6_2(L7_2, L8_2, L9_2)
  L6_2 = mSleep
  L7_2 = math
  L7_2 = L7_2.random
  L8_2 = 500
  L9_2 = 800
  L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L7_2(L8_2, L9_2)
  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
  while true do
    L6_2 = {}
    L7_2 = "381c00f800f0f007e007c3e1ff801f1fdffe007fffffffc1ffffffffe7ffffffff9ffffffffe7ffffffff1f1fff8000f83fff0003e0f1fc000707c1e000087e01800001f1e070380f87c3c1e07e1f1f0787f07c7c1e3f81e7e079ff0fdf83effe3ffe0fbffdfffc7c7dfffffdf1f7fffdffc7cfffe3fe0f1fff87f83c7f3e0fc0f1f8783f03dfe1e1f80fff0787e03ff81f7f00ffc07ff801fc01ffc007e007fe0008001fe00000007e000000000000000000000000000000000000000001e078007e0781f007fc1e07c0fff07c1f07ffc1f07ffffe07c1fffff81f07ffc7c07c1ffc1f01f07f007c07c1f001f01f07c027807c1f03fe01f07c1ff80781f07ff01e07c07ff8701f00ffe00778007f801e0001fe0078001ff001e001ff800780fff8003f0fffc03ffffffde1ffffff83cffffff00fbffffe001e7ffe00007801e00001f007800007c01e00001f007c0001f801f0001fe007ffffff001ffffff8007fffffc000fffff80000ff0000@00$移动$1506$38$77"
    L6_2[1] = L7_2
    L7_2 = addTSOcrDictEx
    L8_2 = L6_2
    L7_2 = L7_2(L8_2)
    L8_2 = tsFindText
    L9_2 = L7_2
    L10_2 = "移动"
    L11_2 = 183
    L12_2 = 370
    L13_2 = 518
    L14_2 = 1056
    L15_2 = "0FD513 , 0F3E13 # 0FD513 , 0F3E13 # 0FD513 , 0F3E13 # E7EDEF , 2C2724 # E7EDEF , 2C2724 # C2CCD2 , 524842"
    L16_2 = 90
    L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
    _ENV["动"] = L9_2
    _ENV["移"] = L8_2
    L8_2 = findMultiColorInRegionFuzzy
    L9_2 = 15329795
    L10_2 = "-1|5|0xf6f702,-4|7|0xeaeb03,-7|17|0xeeef03,7|8|0xe0e204,-1|0|0xe9ea03,-31|11|0xf8f902,-37|15|0x343c20,-34|7|0xe5e605,-30|-4|0xcacc07"
    L11_2 = 90
    L12_2 = 298
    L13_2 = 494
    L14_2 = 377
    L15_2 = 545
    L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    yx = L9_2
    xy = L8_2
    L8_2 = _ENV["移"]
    if 0 < L8_2 then
      L8_2 = xy
      if 0 < L8_2 then
        L8_2 = randomTap
        L9_2 = _ENV["移"]
        L9_2 = L9_2 + 50
        L10_2 = _ENV["动"]
        L10_2 = L10_2 + 20
        L8_2(L9_2, L10_2)
        L8_2 = mSleep
        L9_2 = math
        L9_2 = L9_2.random
        L10_2 = 800
        L11_2 = 1500
        L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L9_2(L10_2, L11_2)
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L8_2 = randomTap
        L9_2 = 986
        L10_2 = 206
        L11_2 = 10
        L8_2(L9_2, L10_2, L11_2)
        L8_2 = mSleep
        L9_2 = math
        L9_2 = L9_2.random
        L10_2 = 800
        L11_2 = 1500
        L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L9_2(L10_2, L11_2)
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L8_2 = randomTap
        L9_2 = 986
        L10_2 = 206
        L11_2 = 10
        L8_2(L9_2, L10_2, L11_2)
        L8_2 = mSleep
        L9_2 = math
        L9_2 = L9_2.random
        L10_2 = 800
        L11_2 = 1500
        L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L9_2(L10_2, L11_2)
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L8_2 = os
        L8_2 = L8_2.date
        L9_2 = "%S"
        L10_2 = os
        L10_2 = L10_2.time
        L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L10_2()
        L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        while "n" do
          L9_2 = os
          L9_2 = L9_2.date
          L10_2 = "%S"
          L11_2 = os
          L11_2 = L11_2.time
          L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2()
          L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          L9_2 = L8_2 - L9_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 5004448
          L12_2 = "-59|-7|0x485ca0,-90|8|0x5060a0,-99|34|0x5466a8,27|30|0x5064a8,20|-6|0x485ca0,-4|34|0x5466a8,-46|28|0x5064a8,-36|7|0xffffff,-34|26|0xf5f6fa"
          L13_2 = 90
          L14_2 = 890
          L15_2 = 811
          L16_2 = 1057
          L17_2 = 895
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          y = L11_2
          n = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 15265245
          L12_2 = "-14|-8|0xc1bb96,-11|-21|0xceccb5,3|-23|0xe8e8d0,22|-33|0xc8c7ae,23|-20|0xf0f8ef,7|-9|0xb6b08b,-1|-1|0xe8eedd,16|-12|0xeef6e9,0|-2|0xecf4e4"
          L13_2 = 90
          L14_2 = 892
          L15_2 = 253
          L16_2 = 1580
          L17_2 = 810
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色"] = L11_2
          _ENV["白"] = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 19157
          L12_2 = "-4|7|0x0f3b83,6|1|0x0051e4,-10|-17|0x002f87,-2|-18|0x0043c6,11|-15|0x051e46,1|-10|0x00338f,39|-18|0x024ddb,37|-29|0x0039a3,22|6|0x0957df"
          L13_2 = 90
          L14_2 = 891
          L15_2 = 242
          L16_2 = 1572
          L17_2 = 805
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色"] = L11_2
          _ENV["蓝"] = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 14422029
          L12_2 = "20|-18|0xea100f,-1|-8|0x4c0802,-6|-6|0x2f0201,-8|3|0xd41008,-5|-12|0xa10907,18|-18|0xd70d0b,-5|3|0xdd0f0b,20|-17|0xe8130e,-7|-19|0xd00b0a"
          L13_2 = 90
          L14_2 = 881
          L15_2 = 244
          L16_2 = 1589
          L17_2 = 816
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色1"] = L11_2
          _ENV["红1"] = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 11931914
          L12_2 = "0|-10|0xb6080a,25|-9|0xa12d09,40|-7|0xcc8404,48|2|0xc5240d,39|11|0xb2620b,34|22|0xd8360e,10|32|0x900a03,6|29|0xc17604,7|20|0xc40c08"
          L13_2 = 90
          L14_2 = 893
          L15_2 = 255
          L16_2 = 1579
          L17_2 = 807
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色2"] = L11_2
          _ENV["红2"] = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 15127052
          L12_2 = "-7|1|0x211c01,-26|-2|0xc6a200,-13|-9|0xf0e907,-2|-23|0xba9116,8|-9|0xedde0a,4|-12|0xe8d709,32|-11|0xdfc52f,4|4|0xcd9b00,-7|9|0xf0ec18"
          L13_2 = 90
          L14_2 = 892
          L15_2 = 253
          L16_2 = 1580
          L17_2 = 810
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色"] = L11_2
          _ENV["黄"] = L10_2
          L10_2 = findMultiColorInRegionFuzzy
          L11_2 = 3725568
          L12_2 = "14|11|0x409b07,8|16|0x38eb00,-2|20|0x28b100,26|4|0x41ed00,29|-4|0x3beb00,37|-11|0x3bad04,-2|-5|0x349400,17|12|0x38aa00,19|-3|0x3cc500"
          L13_2 = 90
          L14_2 = 892
          L15_2 = 253
          L16_2 = 1580
          L17_2 = 810
          L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          _ENV["色"] = L11_2
          _ENV["绿"] = L10_2
          L10_2 = _ENV["蓝"]
          if 0 < L10_2 then
            L10_2 = n
            if L10_2 < 0 and A0_2 == "蓝色" then
              L10_2 = _ENV["_蓝色飞行棋"]
              L11_2 = A1_2
              L12_2 = A2_2
              L13_2 = A3_2
              L14_2 = A4_2
              L15_2 = A5_2
              return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
          end
          else
            L10_2 = _ENV["红1"]
            if 0 < L10_2 then
              L10_2 = n
              if L10_2 < 0 and A0_2 == "红色" then
                goto lbl_210
              end
            end
            L10_2 = _ENV["红2"]
            if 0 < L10_2 then
              L10_2 = n
              if L10_2 < 0 and A0_2 == "红色" then
                ::lbl_210::
                L10_2 = _ENV["_红色飞行棋"]
                L11_2 = A1_2
                L12_2 = A2_2
                L13_2 = A3_2
                L14_2 = A4_2
                L15_2 = A5_2
                return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
            end
            else
              L10_2 = _ENV["黄"]
              if 0 < L10_2 then
                L10_2 = n
                if L10_2 < 0 and A0_2 == "黄色" then
                  L10_2 = _ENV["_黄色飞行棋"]
                  L11_2 = A1_2
                  L12_2 = A2_2
                  L13_2 = A3_2
                  L14_2 = A4_2
                  L15_2 = A5_2
                  return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
              end
              else
                L10_2 = _ENV["绿"]
                if 0 < L10_2 then
                  L10_2 = n
                  if L10_2 < 0 and A0_2 == "绿色" then
                    L10_2 = _ENV["_绿色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                end
                else
                  L10_2 = _ENV["白"]
                  if 0 < L10_2 then
                    L10_2 = n
                    if L10_2 < 0 and A0_2 == "白色" then
                      L10_2 = _ENV["_白色飞行棋"]
                      L11_2 = A1_2
                      L12_2 = A2_2
                      L13_2 = A3_2
                      L14_2 = A4_2
                      L15_2 = A5_2
                      return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  end
                  elseif L9_2 < -15 or L9_2 < 45 and 1 < L9_2 and A0_2 == "红色" then
                    L10_2 = _ENV["_红色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  elseif L9_2 < -15 or L9_2 < 45 and 1 < L9_2 and A0_2 == "蓝色" then
                    L10_2 = _ENV["_蓝色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  elseif L9_2 < -15 or L9_2 < 45 and 1 < L9_2 and A0_2 == "黄色" then
                    L10_2 = _ENV["_黄色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  elseif L9_2 < -15 or L9_2 < 45 and 1 < L9_2 and A0_2 == "绿色" then
                    L10_2 = _ENV["_绿色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  elseif L9_2 < -15 or L9_2 < 45 and 1 < L9_2 and A0_2 == "白色" then
                    L10_2 = _ENV["_白色飞行棋"]
                    L11_2 = A1_2
                    L12_2 = A2_2
                    L13_2 = A3_2
                    L14_2 = A4_2
                    L15_2 = A5_2
                    return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  else
                    L10_2 = n
                    if 0 < L10_2 then
                      L10_2 = randomTap
                      L11_2 = 986
                      L12_2 = 206
                      L13_2 = 10
                      L10_2(L11_2, L12_2, L13_2)
                      L10_2 = mSleep
                      L11_2 = math
                      L11_2 = L11_2.random
                      L12_2 = 200
                      L13_2 = 500
                      L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2(L12_2, L13_2)
                      L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                    end
                  end
                end
              end
            end
          end
        end
    end
    else
      L8_2 = _ENV["识别飞行旗颜色"]
      L9_2 = A0_2
      L8_2(L9_2)
    end
  end
end

_ENV["移动飞行棋到背包"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  if A0_2 == "蓝色" then
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 19157
    L3_2 = "-4|7|0x0f3b83,6|1|0x0051e4,-10|-17|0x002f87,-2|-18|0x0043c6,11|-15|0x051e46,1|-10|0x00338f,39|-18|0x024ddb,37|-29|0x0039a3,22|6|0x0957df"
    L4_2 = 90
    L5_2 = 891
    L6_2 = 242
    L7_2 = 1572
    L8_2 = 805
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    y = L2_2
    x = L1_2
    L1_2 = x
    if 0 < L1_2 then
      L1_2 = randomTap
      L2_2 = x
      L3_2 = y
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 200
      L4_2 = 500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    else
      L1_2 = dialog
      L2_2 = "找不到蓝色飞行棋。。。"
      L1_2(L2_2)
    end
  elseif A0_2 == "红色" then
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 14422029
    L3_2 = "20|-18|0xea100f,-1|-8|0x4c0802,-6|-6|0x2f0201,-8|3|0xd41008,-5|-12|0xa10907,18|-18|0xd70d0b,-5|3|0xdd0f0b,20|-17|0xe8130e,-7|-19|0xd00b0a"
    L4_2 = 90
    L5_2 = 881
    L6_2 = 244
    L7_2 = 1589
    L8_2 = 816
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    d = L2_2
    c = L1_2
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 11931914
    L3_2 = "0|-10|0xb6080a,25|-9|0xa12d09,40|-7|0xcc8404,48|2|0xc5240d,39|11|0xb2620b,34|22|0xd8360e,10|32|0x900a03,6|29|0xc17604,7|20|0xc40c08"
    L4_2 = 90
    L5_2 = 893
    L6_2 = 255
    L7_2 = 1579
    L8_2 = 807
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    y = L2_2
    x = L1_2
    L1_2 = x
    if 0 < L1_2 then
      L1_2 = randomTap
      L2_2 = x
      L3_2 = y
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 200
      L4_2 = 500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    else
      L1_2 = c
      if 0 < L1_2 then
        L1_2 = randomTap
        L2_2 = c
        L3_2 = d
        L1_2(L2_2, L3_2)
        L1_2 = mSleep
        L2_2 = math
        L2_2 = L2_2.random
        L3_2 = 200
        L4_2 = 500
        L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
        L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      else
        L1_2 = dialog
        L2_2 = "找不到红色飞行棋。。。"
        L1_2(L2_2)
      end
    end
  elseif A0_2 == "黄色" then
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 15127052
    L3_2 = "-7|1|0x211c01,-26|-2|0xc6a200,-13|-9|0xf0e907,-2|-23|0xba9116,8|-9|0xedde0a,4|-12|0xe8d709,32|-11|0xdfc52f,4|4|0xcd9b00,-7|9|0xf0ec18"
    L4_2 = 90
    L5_2 = 892
    L6_2 = 253
    L7_2 = 1580
    L8_2 = 810
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    y = L2_2
    x = L1_2
    L1_2 = x
    if 0 < L1_2 then
      L1_2 = randomTap
      L2_2 = x
      L3_2 = y
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 200
      L4_2 = 500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    else
      L1_2 = dialog
      L2_2 = "找不到黄色飞行棋。。。"
      L1_2(L2_2)
    end
  elseif A0_2 == "绿色" then
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 3725568
    L3_2 = "14|11|0x409b07,8|16|0x38eb00,-2|20|0x28b100,26|4|0x41ed00,29|-4|0x3beb00,37|-11|0x3bad04,-2|-5|0x349400,17|12|0x38aa00,19|-3|0x3cc500"
    L4_2 = 90
    L5_2 = 892
    L6_2 = 253
    L7_2 = 1580
    L8_2 = 810
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    y = L2_2
    x = L1_2
    L1_2 = x
    if 0 < L1_2 then
      L1_2 = randomTap
      L2_2 = x
      L3_2 = y
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 200
      L4_2 = 500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    else
      L1_2 = dialog
      L2_2 = "找不到绿色飞行棋。。。"
      L1_2(L2_2)
    end
  elseif A0_2 == "白色" then
    L1_2 = findMultiColorInRegionFuzzy
    L2_2 = 15265245
    L3_2 = "-14|-8|0xc1bb96,-11|-21|0xceccb5,3|-23|0xe8e8d0,22|-33|0xc8c7ae,23|-20|0xf0f8ef,7|-9|0xb6b08b,-1|-1|0xe8eedd,16|-12|0xeef6e9,0|-2|0xecf4e4"
    L4_2 = 90
    L5_2 = 892
    L6_2 = 253
    L7_2 = 1580
    L8_2 = 810
    L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    y = L2_2
    x = L1_2
    L1_2 = x
    if 0 < L1_2 then
      L1_2 = randomTap
      L2_2 = x
      L3_2 = y
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 200
      L4_2 = 500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    else
      L1_2 = dialog
      L2_2 = "找不到白色飞行棋。。。"
      L1_2(L2_2)
    end
  end
end

_ENV["识别飞行旗颜色"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2
  L3_2 = global
  L3_2 = L3_2["使用飞行符"]
  L4_2 = A0_2
  L5_2 = A1_2
  L3_2(L4_2, L5_2)
end

_ENV["飞行"] = L0_1
