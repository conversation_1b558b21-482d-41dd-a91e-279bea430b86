local L0_1, L1_1, L2_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["_功能"]
  L0_2 = L0_2["屏蔽"]
  L1_2 = 3
  L0_2(L1_2)
end

_ENV["隐藏窗口按钮"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while "x" do
    L0_2 = findMultiColorInRegionFuzzy
    L1_2 = 15325917
    L2_2 = "15|10|0x7a2538,16|26|0x9d6373,20|10|0xa9717d,5|22|0xb9929b,9|11|0x701428,-5|18|0xfbf9fa,34|2|0x701428,26|18|0x681428,-1|22|0xb9929b"
    L3_2 = 90
    L4_2 = 576
    L5_2 = 322
    L6_2 = 737
    L7_2 = 386
    L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
    y = L1_2
    x = L0_2
    L0_2 = x
    if 0 < L0_2 then
      break
    end
    L0_2 = randomTap
    L1_2 = 168
    L2_2 = 83
    L3_2 = 50
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = mSleep
    L1_2 = math
    L1_2 = L1_2.random
    L2_2 = 550
    L3_2 = 750
    L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  end
end

_ENV["打开长寿地图"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while "x" do
    L0_2 = findMultiColorInRegionFuzzy
    L1_2 = 14206512
    L2_2 = "0|-2|0xe7d533,1|13|0xebd834,6|24|0xccbc2d,14|-2|0xd8c730,22|2|0xdfce32,22|0|0xdecd32,22|16|0xe6d333,23|17|0xdbca31,22|13|0xe4d233"
    L3_2 = 90
    L4_2 = 460
    L5_2 = 169
    L6_2 = 1392
    L7_2 = 808
    L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
    y = L1_2
    x = L0_2
    L0_2 = x
    if 0 < L0_2 then
      L0_2 = x
      L1_2 = ","
      L2_2 = y
      L0_2 = L0_2 .. L1_2 .. L2_2
      ret = L0_2
      L0_2 = ret
      return L0_2
    else
      L0_2 = _ENV["打开长寿地图"]
      L0_2()
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 550
      L3_2 = 750
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L0_2 = randomTap
      L1_2 = 1081
      L2_2 = 522
      L3_2 = 50
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 350
      L3_2 = 550
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L0_2 = tap
      L1_2 = 1260
      L2_2 = 548
      L0_2(L1_2, L2_2)
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 2550
      L3_2 = 3350
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L0_2 = randomTap
      L1_2 = 1382
      L2_2 = 109
      L3_2 = 10
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 550
      L3_2 = 750
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L0_2 = _ENV["隐藏窗口按钮"]
      L0_2()
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 350
      L3_2 = 550
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    end
  end
end

_ENV["到巫医附近"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = getCurMap
    L0_2 = L0_2()
    if L0_2 == "CSC" then
      L0_2 = mSleep
      L1_2 = 500
      L0_2(L1_2)
      break
    else
      L0_2 = useFlyRuneTo
      L1_2 = constPos
      L1_2 = L1_2.runeCCCpoint
      L0_2(L1_2)
    end
  end
  L0_2 = _ENV["隐藏窗口按钮"]
  L0_2()
  while "x" do
    L0_2 = findMultiColorInRegionFuzzy
    L1_2 = 8755619
    L2_2 = "0|1|0xafbcc3,0|2|0xe7ebed,0|3|0xebeef0,-1|3|0xf2f3f5,-3|5|0xfbfcfc,-5|6|0xb8c2c9,-7|8|0x385468,-7|10|0x385468,-7|12|0xa4b1ba"
    L3_2 = 90
    L4_2 = 1407
    L5_2 = 473
    L6_2 = 1504
    L7_2 = 526
    L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
    y = L1_2
    x = L0_2
    L0_2 = x
    if 0 < L0_2 then
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 250
      L3_2 = 350
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L0_2 = randomTap
      L1_2 = x
      L2_2 = y
      L3_2 = 20
      L0_2(L1_2, L2_2, L3_2)
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 250
      L3_2 = 350
      L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      break
    else
      L0_2 = _ENV["到巫医附近"]
      L0_2 = L0_2()
      ret = L0_2
      L0_2 = ret
      if L0_2 ~= nil then
        L0_2 = strSplit
        L1_2 = ret
        L2_2 = ","
        L0_2 = L0_2(L1_2, L2_2)
        era = L0_2
        L0_2 = era
        L1_2 = era
        L1_2 = L1_2[1]
        L1_2 = L1_2 + 65
        L0_2[1] = L1_2
        L0_2 = era
        L1_2 = era
        L1_2 = L1_2[2]
        L1_2 = L1_2 - 91
        L0_2[2] = L1_2
        L0_2 = randomTap
        L1_2 = era
        L1_2 = L1_2[1]
        L2_2 = era
        L2_2 = L2_2[2]
        L3_2 = 10
        L0_2(L1_2, L2_2, L3_2)
        L0_2 = mSleep
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 350
        L3_2 = 450
        L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
        L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      end
    end
  end
end

_ENV["前往长寿巫医"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  repeat
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["通用"]
    L1_2 = L1_2["展开充值"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 300
      L2_2 = 400
      L0_2(L1_2, L2_2)
    end
    L0_2 = myBlockcolor
    L1_2 = sevenColor
    L1_2 = L1_2["点活动"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _print
      L1_2 = "hd"
      L0_2(L1_2)
      L0_2 = tap
      L1_2 = x
      L2_2 = y
      L0_2(L1_2, L2_2)
      L0_2 = _Sleep
      L1_2 = 600
      L2_2 = 700
      L0_2(L1_2, L2_2)
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["进入游戏"]
    L1_2 = L1_2["到签到界面"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = _cmp
      L1_2 = Color
      L1_2 = L1_2["进入游戏"]
      L1_2 = L1_2["到签到界面1"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_54
      end
    end
    L0_2 = _print
    L1_2 = "qd"
    L0_2(L1_2)
    L0_2 = tap
    L1_2 = 1531
    L2_2 = 918
    L0_2(L1_2, L2_2)
    L0_2 = _Sleep
    L1_2 = 600
    L2_2 = 700
    L0_2(L1_2, L2_2)
    ::lbl_54::
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["进入游戏"]
    L1_2 = L1_2["梦幻精灵"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _print
      L1_2 = "mhjl"
      L0_2(L1_2)
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 100
      L3_2 = 1839
      L4_2 = 69
      L5_2 = 1880
      L6_2 = 102
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
      L0_2 = _Sleep
      L1_2 = 600
      L2_2 = 700
      L0_2(L1_2, L2_2)
    end
    L0_2 = myBlockcoloraa
    L1_2 = sevenColor
    L1_2 = L1_2["指引开启"]
    L0_2 = L0_2(L1_2)
    if not L0_2 then
      L0_2 = myBlockcoloraa
      L1_2 = sevenColor
      L1_2 = L1_2["指引开启1"]
      L0_2 = L0_2(L1_2)
      if not L0_2 then
        goto lbl_101
      end
    end
    L0_2 = _print
    L1_2 = "zy"
    L0_2(L1_2)
    L0_2 = mSleep
    L1_2 = 800
    L0_2(L1_2)
    L0_2 = tap
    L1_2 = 1857
    L2_2 = 16
    L0_2(L1_2, L2_2)
    L0_2 = mSleep
    L1_2 = 800
    L0_2(L1_2)
    ::lbl_101::
    L0_2 = _find_tb
    L1_2 = Color
    L1_2 = L1_2["进入游戏"]
    L1_2 = L1_2["新版签到"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _Sleep
      L1_2 = 500
      L2_2 = 1000
      L0_2(L1_2, L2_2)
    end
    L0_2 = mSleep
    L1_2 = 300
    L0_2(L1_2)
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["进入游戏"]
    L1_2 = L1_2["每日签到界面1"]
    L0_2 = L0_2(L1_2)
  until L0_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["进入游戏"]
  L1_2 = L1_2["每日签到界面1"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = randomClick
    L1_2 = 0
    L2_2 = 300
    L3_2 = 1452
    L4_2 = 463
    L5_2 = 1498
    L6_2 = 679
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    L0_2 = mSleep
    L1_2 = 400
    L0_2(L1_2)
  end
  L0_2 = 1
  L1_2 = 50
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["提示框"]
    L4_2, L5_2, L6_2 = L4_2(L5_2)
    if L4_2 then
      if not (530 <= L5_2 and 120 <= L6_2 and L5_2 <= 550 and L6_2 <= 140) then
        goto lbl_159
      end
      do break end
      ::lbl_159::
      L7_2 = touchDown
      L8_2 = 0
      L9_2 = L5_2
      L10_2 = L6_2
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 350
      L7_2(L8_2)
      L7_2 = touchMove
      L8_2 = 0
      L9_2 = 540
      L10_2 = 134
      L7_2(L8_2, L9_2, L10_2)
      L7_2 = mSleep
      L8_2 = 250
      L7_2(L8_2)
      L7_2 = touchUp
      L8_2 = 0
      L9_2 = 540
      L10_2 = 134
      L7_2(L8_2, L9_2, L10_2)
      break
    end
    if L3_2 == 20 then
      L7_2 = _cmp
      L8_2 = Color
      L8_2 = L8_2["进入游戏"]
      L8_2 = L8_2["每日签到界面1"]
      L7_2 = L7_2(L8_2)
      if L7_2 then
        L7_2 = randomClick
        L8_2 = 0
        L9_2 = 300
        L10_2 = 1452
        L11_2 = 463
        L12_2 = 1498
        L13_2 = 679
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L7_2 = mSleep
        L8_2 = 400
        L7_2(L8_2)
      end
    end
    L7_2 = mSleep
    L8_2 = 40
    L7_2(L8_2)
  end
  L0_2 = _find
  L1_2 = Color
  L1_2 = L1_2["进入游戏"]
  L1_2 = L1_2["开始答题1"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = mSleep
    L1_2 = 500
    L0_2(L1_2)
  end
  L0_2 = _find
  L1_2 = Color
  L1_2 = L1_2["进入游戏"]
  L1_2 = L1_2["无需答题"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
  else
    L0_2 = os
    L0_2 = L0_2.time
    L0_2 = L0_2()
    L1_2 = _find_cx
    L2_2 = Color
    L2_2 = L2_2["进入游戏"]
    L2_2 = L2_2["开始答题"]
    L3_2 = {}
    L4_2 = 80
    L5_2 = 30
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    L1_2 = L1_2(L2_2, L3_2)
    if L1_2 then
      L1_2 = _find_cx
      L2_2 = Color
      L2_2 = L2_2["进入游戏"]
      L2_2 = L2_2["答题界面"]
      L3_2 = {}
      L4_2 = 90
      L5_2 = 50
      L3_2[1] = L4_2
      L3_2[2] = L5_2
      L1_2 = L1_2(L2_2, L3_2)
      if L1_2 then
        L1_2 = 0
        repeat
          L2_2 = os
          L2_2 = L2_2.time
          L2_2 = L2_2()
          L2_2 = L2_2 - L0_2
          if 150 < L2_2 then
            L2_2 = _ENV["通用功能"]
            L2_2 = L2_2["关闭"]
            L2_2()
            break
          end
          L2_2 = _find
          L3_2 = Color
          L3_2 = L3_2["进入游戏"]
          L3_2 = L3_2["答题界面"]
          L2_2 = L2_2(L3_2)
          if L2_2 then
            L2_2 = _find_tb
            L3_2 = Color
            L3_2 = L3_2["进入游戏"]
            L3_2 = L3_2["错误答案"]
            L2_2 = L2_2(L3_2)
            if L2_2 then
              L2_2 = {}
              L3_2 = {}
              L4_2 = 682
              L5_2 = 555
              L6_2 = 789
              L7_2 = 622
              L3_2[1] = L4_2
              L3_2[2] = L5_2
              L3_2[3] = L6_2
              L3_2[4] = L7_2
              L4_2 = {}
              L5_2 = 1264
              L6_2 = 551
              L7_2 = 1382
              L8_2 = 616
              L4_2[1] = L5_2
              L4_2[2] = L6_2
              L4_2[3] = L7_2
              L4_2[4] = L8_2
              L5_2 = {}
              L6_2 = 673
              L7_2 = 722
              L8_2 = 780
              L9_2 = 785
              L5_2[1] = L6_2
              L5_2[2] = L7_2
              L5_2[3] = L8_2
              L5_2[4] = L9_2
              L6_2 = {}
              L7_2 = 1265
              L8_2 = 721
              L9_2 = 1378
              L10_2 = 784
              L6_2[1] = L7_2
              L6_2[2] = L8_2
              L6_2[3] = L9_2
              L6_2[4] = L10_2
              L2_2[1] = L3_2
              L2_2[2] = L4_2
              L2_2[3] = L5_2
              L2_2[4] = L6_2
              L3_2 = 1
              L4_2 = 4
              L5_2 = 1
              for L6_2 = L3_2, L4_2, L5_2 do
                L7_2 = _find_tb
                L8_2 = Color
                L8_2 = L8_2["进入游戏"]
                L8_2 = L8_2["错误答案"]
                L9_2 = {}
                L10_2 = 0
                L11_2 = 0
                L9_2[1] = L10_2
                L9_2[2] = L11_2
                L10_2 = {}
                L11_2 = L2_2[L6_2]
                L11_2 = L11_2[1]
                L12_2 = L2_2[L6_2]
                L12_2 = L12_2[2]
                L13_2 = L2_2[L6_2]
                L13_2 = L13_2[3]
                L14_2 = L2_2[L6_2]
                L14_2 = L14_2[4]
                L10_2[1] = L11_2
                L10_2[2] = L12_2
                L10_2[3] = L13_2
                L10_2[4] = L14_2
                L7_2 = L7_2(L8_2, L9_2, L10_2)
                if L7_2 == false then
                  L7_2 = randomClick
                  L8_2 = 0
                  L9_2 = 500
                  L10_2 = L2_2[L6_2]
                  L10_2 = L10_2[1]
                  L11_2 = L2_2[L6_2]
                  L11_2 = L11_2[2]
                  L12_2 = L2_2[L6_2]
                  L12_2 = L12_2[3]
                  L13_2 = L2_2[L6_2]
                  L13_2 = L13_2[4]
                  L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                end
                L7_2 = mSleep
                L8_2 = 100
                L7_2(L8_2)
              end
            else
              L2_2 = _find
              L3_2 = Color
              L3_2 = L3_2["进入游戏"]
              L3_2 = L3_2["答题界面"]
              L2_2 = L2_2(L3_2)
              if L2_2 then
                L2_2 = math
                L2_2 = L2_2.random
                L3_2 = 1
                L4_2 = 2
                L2_2 = L2_2(L3_2, L4_2)
                if L2_2 == 1 then
                  L3_2 = tap
                  L4_2 = 529
                  L5_2 = 591
                  L3_2(L4_2, L5_2)
                  L3_2 = mSleep
                  L4_2 = 1300
                  L3_2(L4_2)
                else
                  L3_2 = tap
                  L4_2 = 1119
                  L5_2 = 600
                  L3_2(L4_2, L5_2)
                  L3_2 = mSleep
                  L4_2 = 1300
                  L3_2(L4_2)
                end
              end
            end
          end
          L1_2 = L1_2 + 1
          if L1_2 == 5 then
            L1_2 = 0
            L2_2 = _find
            L3_2 = Color
            L3_2 = L3_2["进入游戏"]
            L3_2 = L3_2["答题界面"]
            L2_2 = L2_2(L3_2)
            if L2_2 then
              L2_2 = math
              L2_2 = L2_2.random
              L3_2 = 1
              L4_2 = 2
              L2_2 = L2_2(L3_2, L4_2)
              if L2_2 == 1 then
                L3_2 = tap
                L4_2 = 529
                L5_2 = 591
                L3_2(L4_2, L5_2)
                L3_2 = mSleep
                L4_2 = 1300
                L3_2(L4_2)
              else
                L3_2 = tap
                L4_2 = 1119
                L5_2 = 600
                L3_2(L4_2, L5_2)
                L3_2 = mSleep
                L4_2 = 1300
                L3_2(L4_2)
              end
            end
          end
          L2_2 = mSleep
          L3_2 = 40
          L2_2(L3_2)
          L2_2 = _cmp
          L3_2 = Color
          L3_2 = L3_2["进入游戏"]
          L3_2 = L3_2["每日签到界面1"]
          L2_2 = L2_2(L3_2)
        until L2_2
      end
    end
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["自动签到"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["屏蔽"]
  L1_2 = L1_2["取消"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 800
    L0_2(L1_2, L2_2)
  end
  L0_2 = _cmp
  L1_2 = Color
  L1_2 = L1_2["进入游戏"]
  L1_2 = L1_2["梦幻精灵"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _tap
    L1_2 = 1855
    L2_2 = 85
    L0_2(L1_2, L2_2)
    L0_2 = _Sleep
    L1_2 = 500
    L2_2 = 800
    L0_2(L1_2, L2_2)
  end
  L0_2 = 1
  L1_2 = os
  L1_2 = L1_2.time
  L1_2 = L1_2()
  L2_2 = 1
  L3_2 = 20
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 == false then
      L0_2 = 0
    end
    if L0_2 == 0 then
      L6_2 = _find_tb
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["屏蔽"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        L6_2 = mSleep
        L7_2 = 300
        L6_2(L7_2)
        L6_2 = os
        L6_2 = L6_2.time
        L6_2 = L6_2()
        L1_2 = L6_2
      end
    end
    L6_2 = _find
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["隐藏界面"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = mSleep
      L7_2 = 500
      L6_2(L7_2)
      L6_2 = _find
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["取消隐藏界面"]
      L6_2(L7_2)
      L6_2 = mSleep
      L7_2 = 500
      L6_2(L7_2)
    end
    L6_2 = _find
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if not L6_2 then
      L6_2 = _find
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["取消隐藏界面"]
      L6_2 = L6_2(L7_2)
      if not L6_2 then
        goto lbl_94
      end
    end
    L6_2 = mSleep
    L7_2 = 300
    L6_2(L7_2)
    ::lbl_94::
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["未隐藏界面"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        return
      end
    end
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["高亮隐藏界面"]
      L6_2 = L6_2(L7_2)
      if L6_2 == false then
        return
      end
    end
    L6_2 = os
    L6_2 = L6_2.time
    L6_2 = L6_2()
    L6_2 = L6_2 - L1_2
    if 10 < L6_2 then
      L0_2 = 1
      L6_2 = os
      L6_2 = L6_2.time
      L6_2 = L6_2()
      L1_2 = L6_2
    end
    L6_2 = mSleep
    L7_2 = 10
    L6_2(L7_2)
  end
end

_ENV["刷新屏蔽界面"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = 1
  L1_2 = 20
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["通用"]
    L5_2 = L5_2["展开充值"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 300
      L6_2 = 400
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["通用"]
    L5_2 = L5_2["打开活动"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 300
      L6_2 = 400
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["通用"]
    L5_2 = L5_2["打开日常任务"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _Sleep
      L5_2 = 500
      L6_2 = 600
      L4_2(L5_2, L6_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["通用"]
    L5_2 = L5_2["已打开日常任务"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _ENV["通用功能"]
      L4_2 = L4_2["关闭"]
      L4_2()
      break
    end
    L4_2 = mSleep
    L5_2 = 50
    L4_2(L5_2)
  end
end

_ENV["刷新签到界面"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = 1
  L1_2 = os
  L1_2 = L1_2.time
  L1_2 = L1_2()
  L2_2 = 1
  L3_2 = 20
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 == false then
      L0_2 = 0
    end
    if L0_2 == 0 then
      L6_2 = _find_tb
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["屏蔽"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        L6_2 = mSleep
        L7_2 = 300
        L6_2(L7_2)
        L6_2 = os
        L6_2 = L6_2.time
        L6_2 = L6_2()
        L1_2 = L6_2
      end
    end
    L6_2 = _find
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if not L6_2 then
      L6_2 = _find
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["取消隐藏界面"]
      L6_2 = L6_2(L7_2)
      if not L6_2 then
        goto lbl_50
      end
    end
    L6_2 = mSleep
    L7_2 = 300
    L6_2(L7_2)
    ::lbl_50::
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["未隐藏界面"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        return
      end
    end
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏全部摊位"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["高亮隐藏界面"]
      L6_2 = L6_2(L7_2)
      if L6_2 == false then
        return
      end
    end
    L6_2 = os
    L6_2 = L6_2.time
    L6_2 = L6_2()
    L6_2 = L6_2 - L1_2
    if 7 < L6_2 then
      L0_2 = 1
      L6_2 = os
      L6_2 = L6_2.time
      L6_2 = L6_2()
      L1_2 = L6_2
    end
    L6_2 = mSleep
    L7_2 = 10
    L6_2(L7_2)
  end
end

_ENV["押镖屏蔽"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = 1
  L1_2 = os
  L1_2 = L1_2.time
  L1_2 = L1_2()
  L2_2 = 1
  L3_2 = 20
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["只看队长2"]
    L6_2 = L6_2(L7_2)
    if L6_2 == false and L0_2 == 1 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["取消"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        L6_2 = _Sleep
        L7_2 = 300
        L8_2 = 1000
        L6_2(L7_2, L8_2)
      end
      L0_2 = 0
    end
    if L0_2 == 0 then
      L6_2 = _find_tb
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["屏蔽"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        L6_2 = _Sleep
        L7_2 = 300
        L8_2 = 1000
        L6_2(L7_2, L8_2)
      end
      L0_2 = 1
      L6_2 = mSleep
      L7_2 = 300
      L6_2(L7_2)
      L6_2 = os
      L6_2 = L6_2.time
      L6_2 = L6_2()
      L1_2 = L6_2
    end
    L6_2 = _find
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["隐藏玩家"]
    L6_2 = L6_2(L7_2)
    if not L6_2 then
      L6_2 = _find
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["隐藏摊位"]
      L6_2 = L6_2(L7_2)
      if not L6_2 then
        L6_2 = _find
        L7_2 = Color
        L7_2 = L7_2["屏蔽"]
        L7_2 = L7_2["取消隐藏界面"]
        L6_2 = L6_2(L7_2)
        if not L6_2 then
          goto lbl_75
        end
      end
    end
    L6_2 = mSleep
    L7_2 = 300
    L6_2(L7_2)
    ::lbl_75::
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["已隐藏玩家"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _cmp
      L7_2 = Color
      L7_2 = L7_2["屏蔽"]
      L7_2 = L7_2["已隐藏摊位"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        L6_2 = _cmp
        L7_2 = Color
        L7_2 = L7_2["屏蔽"]
        L7_2 = L7_2["未隐藏界面"]
        L6_2 = L6_2(L7_2)
        if L6_2 then
          return
        end
      end
    end
    L6_2 = os
    L6_2 = L6_2.time
    L6_2 = L6_2()
    L6_2 = L6_2 - L1_2
    if 7 < L6_2 then
      L0_2 = 1
      L6_2 = os
      L6_2 = L6_2.time
      L6_2 = L6_2()
      L1_2 = L6_2
    end
    L6_2 = mSleep
    L7_2 = 10
    L6_2(L7_2)
  end
end

_ENV["跑商屏蔽"] = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  if A0_2 == 1 then
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 200
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    taskPos = L2_2
  elseif A0_2 == 2 then
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 340
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    taskPos = L2_2
  elseif A0_2 == 3 then
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 480
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    taskPos = L2_2
  elseif A0_2 == 4 then
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 620
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    taskPos = L2_2
  end
  if A1_2 == "cash" then
    adwardType = "现金镖"
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 530
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    awardPos = L2_2
  elseif A1_2 == "reserve" then
    adwardType = "储备镖"
    L2_2 = {}
    L3_2 = 1612
    L4_2 = 660
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    awardPos = L2_2
  end
  L2_2 = scrnLog
  L3_2 = "靠近郑镖头"
  L2_2(L3_2)
  L2_2 = _ENV["押镖屏蔽"]
  L2_2()
  repeat
    repeat
      L2_2 = _cmp_tb
      L3_2 = Color
      L3_2 = L3_2["店小二"]
      L3_2 = L3_2["主界面"]
      L2_2 = L2_2(L3_2)
      if L2_2 == false then
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.VERINF
        L3_2 = L3_2.VERmoveWord
        L2_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L2_2 = _ENV["_验证"]
          L2_2 = L2_2["漂浮2"]
          L2_2 = L2_2()
          if L2_2 then
            return
          else
            L2_2 = _ENV["_验证"]
            L2_2 = L2_2["表情"]
            L2_2 = L2_2()
            if L2_2 then
              return
            end
          end
        else
          L2_2 = findFeat
          L3_2 = color
          L3_2 = L3_2.VERINF
          L3_2 = L3_2.VERidiom
          L2_2 = L2_2(L3_2)
          if 0 < L2_2 then
            L2_2 = _ENV["_验证"]
            L2_2 = L2_2["成语"]
            L2_2 = L2_2["验证"]
            L2_2()
          else
          end
        end
      end
      L2_2 = _ENV["_返回"]
      L2_2 = L2_2["押镖坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
      if not (L2_2 <= 33) then
        L2_2 = y
        if not (L2_2 <= 14) then
          goto lbl_127
        end
      end
      L2_2 = randomClick
      L3_2 = 0
      L4_2 = 500
      L5_2 = 1182
      L6_2 = 514
      L7_2 = 1492
      L8_2 = 624
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["关闭"]
      L2_2()
      L2_2 = mSleep
      L3_2 = math
      L3_2 = L3_2.random
      L4_2 = 450
      L5_2 = 550
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2(L4_2, L5_2)
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
      L2_2 = _ENV["moveJudge押镖"]
      L2_2()
      L2_2 = _ENV["_卡区延迟"]
      L2_2()
      ::lbl_127::
      L2_2 = _ENV["_返回"]
      L2_2 = L2_2["押镖坐标"]
      L2_2, L3_2 = L2_2()
      y = L3_2
      x = L2_2
      L2_2 = x
    until 33 < L2_2
    L2_2 = y
  until 14 <= L2_2
  while true do
    L2_2 = _ENV["_返回"]
    L2_2 = L2_2["押镖坐标"]
    L2_2, L3_2 = L2_2()
    y = L3_2
    x = L2_2
    L2_2 = _ENV["_卡区延迟"]
    L2_2()
    L2_2 = x
    if 43 < L2_2 then
      L2_2 = randomClick
      L3_2 = 0
      L4_2 = 500
      L5_2 = 1302
      L6_2 = 732
      L7_2 = 1452
      L8_2 = 843
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L2_2 = mSleep
      L3_2 = math
      L3_2 = L3_2.random
      L4_2 = 450
      L5_2 = 550
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2(L4_2, L5_2)
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
      L2_2 = _ENV["moveJudge押镖"]
      L2_2()
      L2_2 = _ENV["_卡区延迟"]
      L2_2()
    else
      L2_2 = x
      if not (L2_2 <= 33) then
        L2_2 = y
        if not (L2_2 <= 14) then
          goto lbl_196
        end
      end
      L2_2 = randomClick
      L3_2 = 0
      L4_2 = 500
      L5_2 = 1182
      L6_2 = 514
      L7_2 = 1492
      L8_2 = 624
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L2_2 = mSleep
      L3_2 = math
      L3_2 = L3_2.random
      L4_2 = 450
      L5_2 = 550
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2(L4_2, L5_2)
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
      L2_2 = _ENV["通用功能"]
      L2_2 = L2_2["叉叉"]
      L2_2()
      L2_2 = moveJudge
      L2_2()
      L2_2 = _ENV["_卡区延迟"]
      L2_2()
    end
    ::lbl_196::
    L2_2 = _ENV["_返回"]
    L2_2 = L2_2["押镖坐标"]
    L2_2, L3_2 = L2_2()
    y = L3_2
    x = L2_2
    L2_2 = x
    if L2_2 == nil then
      L2_2 = x
      if L2_2 == "" then
        goto lbl_232
      end
    end
    L2_2 = _ENV["_计算"]
    L2_2 = L2_2["取目标屏幕坐标"]
    L3_2 = "长风镖局"
    L4_2 = x
    L5_2 = y
    L6_2 = 37
    L7_2 = 19
    L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
    _ENV["老板坐标y"] = L3_2
    _ENV["老板坐标x"] = L2_2
    L2_2 = tap
    L3_2 = _ENV["老板坐标x"]
    L4_2 = _ENV["老板坐标y"]
    L2_2(L3_2, L4_2)
    L2_2 = _find_cx
    L3_2 = Color
    L3_2 = L3_2["起号"]
    L3_2 = L3_2["出现对话框"]
    L4_2 = {}
    L5_2 = 20
    L6_2 = 60
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    L2_2 = L2_2(L3_2, L4_2)
    if L2_2 then
    end
    ::lbl_232::
    L2_2 = scrnLog
    L3_2 = "接镖：选择"
    L4_2 = adwardType
    L3_2 = L3_2 .. L4_2
    L2_2(L3_2)
    L2_2 = _ENV["_卡区延迟"]
    L2_2()
    L2_2 = findFeat
    L3_2 = color
    L3_2 = L3_2.VERINF
    L3_2 = L3_2.VERmoveWord
    L2_2 = L2_2(L3_2)
    if 0 < L2_2 then
      L2_2 = _ENV["_验证"]
      L2_2 = L2_2["漂浮2"]
      L2_2 = L2_2()
      if L2_2 then
        return
      else
        L2_2 = _ENV["_验证"]
        L2_2 = L2_2["表情"]
        L2_2 = L2_2()
        if L2_2 then
          return
        end
      end
    else
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.VERINF
      L3_2 = L3_2.VERidiom
      L2_2 = L2_2(L3_2)
      if 0 < L2_2 then
        L2_2 = _ENV["_验证"]
        L2_2 = L2_2["成语"]
        L2_2 = L2_2["验证"]
        L2_2()
      else
        L2_2 = _find
        L3_2 = Color
        L3_2 = L3_2["起号"]
        L3_2 = L3_2["出现对话框"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = singleTap
          L3_2 = taskPos
          L4_2 = 30
          L2_2(L3_2, L4_2)
          L2_2 = mSleep
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 350
          L5_2 = 450
          L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2(L4_2, L5_2)
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          L2_2 = _ENV["_卡区延迟"]
          L2_2()
          L2_2 = _find_cx
          L3_2 = Color
          L3_2 = L3_2["起号"]
          L3_2 = L3_2["出现对话框"]
          L4_2 = {}
          L5_2 = 30
          L6_2 = 100
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
          end
          L2_2 = singleTap
          L3_2 = awardPos
          L4_2 = 30
          L2_2(L3_2, L4_2)
          L2_2 = _ENV["_卡区延迟"]
          L2_2()
          L2_2 = mSleep
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 350
          L5_2 = 450
          L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2(L4_2, L5_2)
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          L2_2 = _find_cx
          L3_2 = Color
          L3_2 = L3_2["起号"]
          L3_2 = L3_2["出现对话框"]
          L4_2 = {}
          L5_2 = 30
          L6_2 = 100
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
          end
          L2_2 = findFeat
          L3_2 = color
          L3_2 = L3_2.endOfEscorter
          L2_2 = L2_2(L3_2)
          if 0 < L2_2 then
            L2_2 = closeAllDialog
            L2_2()
            _ENV["押镖完毕"] = true
            L2_2 = true
            return L2_2
          end
          L2_2 = closeAllDialog
          L2_2()
          L2_2 = _find_tb_cx
          L3_2 = Color
          L3_2 = L3_2["起号"]
          L3_2 = L3_2["绘卷"]
          L4_2 = {}
          L5_2 = 20
          L6_2 = 30
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L2_2 = L2_2(L3_2, L4_2)
          if L2_2 then
          end
        end
      end
    end
    L2_2 = _cmp_tb
    L3_2 = Color
    L3_2 = L3_2["店小二"]
    L3_2 = L3_2["主界面"]
    L2_2 = L2_2(L3_2)
    if L2_2 == false then
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.VERINF
      L3_2 = L3_2.VERmoveWord
      L2_2 = L2_2(L3_2)
      if 0 < L2_2 then
        L2_2 = _ENV["_验证"]
        L2_2 = L2_2["漂浮2"]
        L2_2 = L2_2()
        if L2_2 then
          return
        else
          L2_2 = _ENV["_验证"]
          L2_2 = L2_2["表情"]
          L2_2 = L2_2()
          if L2_2 then
            return
          end
        end
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.VERINF
        L3_2 = L3_2.VERidiom
        L2_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L2_2 = _ENV["_验证"]
          L2_2 = L2_2["成语"]
          L2_2 = L2_2["验证"]
          L2_2()
        else
        end
      end
    end
    L2_2 = getTarget
    L2_2 = L2_2()
    if L2_2 ~= "" then
      travelMod = "walk"
      L2_2 = closeScrnLog
      L3_2 = "接镖：选择"
      L4_2 = adwardType
      L3_2 = L3_2 .. L4_2
      L2_2(L3_2)
      L2_2 = true
      return L2_2
    end
  end
end

getEscort = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = scrnLog
  L1_2 = "正在获取目标"
  L0_2(L1_2)
  while true do
    L0_2 = _cmp_tb
    L1_2 = Color
    L1_2 = L1_2["初始化"]
    L1_2 = L1_2["已展开任务栏"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = tsOcrText
      L1_2 = taskMasterName
      L2_2 = 1587
      L3_2 = 320
      L4_2 = 1894
      L5_2 = 408
      L6_2 = "EB0505 , 1C0606"
      L7_2 = 91
      L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
      L1_2 = #L0_2
      if 4 <= L1_2 then
        L1_2 = #L0_2
        L1_2 = L1_2 % 2
        if L1_2 == 0 then
          L1_2 = string
          L1_2 = L1_2.sub
          L2_2 = L0_2
          L3_2 = 1
          L4_2 = #L0_2
          L4_2 = L4_2 / 2
          L1_2 = L1_2(L2_2, L3_2, L4_2)
          L2_2 = string
          L2_2 = L2_2.sub
          L3_2 = L0_2
          L4_2 = #L0_2
          L4_2 = L4_2 / 2
          L4_2 = L4_2 + 1
          L5_2 = #L0_2
          L2_2 = L2_2(L3_2, L4_2, L5_2)
          if L1_2 == L2_2 then
            L0_2 = L1_2
          end
        end
      end
      L1_2 = closeScrnLog
      L2_2 = "正在获取目标"
      L1_2(L2_2)
      return L0_2
    else
      L0_2 = cleanupINF
      L0_2()
      L0_2 = fightSystem
      L0_2()
      L0_2 = _cmp_tb
      L1_2 = Color
      L1_2 = L1_2["初始化"]
      L1_2 = L1_2["展开任务栏"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = mSleep
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 450
        L3_2 = 550
        L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2(L2_2, L3_2)
        L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      end
    end
  end
end

getTarget = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  while true do
    L0_2 = scrnLog
    L1_2 = "离开镖局"
    L0_2(L1_2)
    L0_2 = getCurMap
    L0_2 = L0_2()
    if L0_2 == "CFBJ" then
      L0_2 = {}
      L1_2 = 910
      L2_2 = 816
      L0_2[1] = L1_2
      L0_2[2] = L2_2
      L1_2 = singleTap
      L2_2 = L0_2
      L3_2 = 30
      L1_2(L2_2, L3_2)
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 1500
      L4_2 = 2000
      L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2)
      L1_2 = _ENV["_卡区延迟"]
      L1_2()
    else
      L0_2 = getCurMap
      L0_2 = L0_2()
      if L0_2 == "" then
        L0_2 = fightSystem
        L0_2()
        L0_2 = cleanupINF
        L0_2()
      else
        L0_2 = closeScrnLog
        L1_2 = "离开镖局"
        L0_2(L1_2)
        L0_2 = true
        return L0_2
      end
    end
  end
end

goOutOfCFBJ = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2
  if A0_2 == "CYJ" then
    L1_2 = goToCYJF
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "KDCS" then
    L1_2 = goToCJG
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "QQ" then
    L1_2 = goToQQF
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "YJ" then
    L1_2 = goToLXBD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "LJ" then
    L1_2 = goToLXBD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "EDW" then
    L1_2 = goToDXD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "SDW" then
    L1_2 = goToLDD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "DDW" then
    L1_2 = goToSWD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "ZYDX" then
    L1_2 = goToQKD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "HSN" then
    L1_2 = goToPSD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "BBGN" then
    L1_2 = goToPSD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "DZW" then
    L1_2 = goToDZWF
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "GYJJ" then
    L1_2 = goToCYD
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "NMW" then
    L1_2 = goToMWJ
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "DHLW" then
    L1_2 = goToSJG
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "SPP" then
    L1_2 = goToNECCZJ
    L2_2 = "walk"
    return L1_2(L2_2)
  elseif A0_2 == "PTZS" then
    L1_2 = goToLTG
    L2_2 = "walk"
    return L1_2(L2_2)
  end
end

goToMaster = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = scrnLog
  L2_2 = "寻找目标身影"
  L1_2(L2_2)
  if A0_2 == "GYJJ" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.GYJJ
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.GYJJ
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 688
        L7_2 = 206
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 1500
        L9_2 = 2000
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "BBGN" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.BBGN
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.BBGN
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 935
        L7_2 = 241
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 3500
        L9_2 = 4000
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "LJ" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.LJ
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.LJ
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 440
        L7_2 = 335
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 4500
        L9_2 = 5000
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "PTZS" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.PTZS
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.PTZS
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 1327
        L7_2 = 280
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 2500
        L9_2 = 3000
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "QQ" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.QQ
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.QQ
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 818
        L7_2 = 382
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 300
        L9_2 = 3500
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "DHLW" then
    L1_2 = mSleep
    L2_2 = math
    L2_2 = L2_2.random
    L3_2 = 1500
    L4_2 = 2500
    L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L2_2(L3_2, L4_2)
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.DHLW
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.DHLW
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 1001
        L7_2 = 550
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 4500
        L9_2 = 5500
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "SDW" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.SDW
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.SDW
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 906
        L7_2 = 349
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 2000
        L9_2 = 2500
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  elseif A0_2 == "YJ" then
    while true do
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.masterPos
      L2_2 = L2_2.YJ
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = mSleep
      L4_2 = 300
      L3_2(L4_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2.YJ
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 and L1_2 == L3_2 and L2_2 == L4_2 then
        L5_2 = closeScrnLog
        L6_2 = "寻找目标身影"
        L5_2(L6_2)
        L5_2 = true
        return L5_2
      elseif L1_2 < 0 and L3_2 < 0 then
        L5_2 = fightSystem
        L5_2()
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = 656
        L7_2 = 492
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 2500
        L9_2 = 3000
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      end
    end
  else
    L1_2 = closeScrnLog
    L2_2 = "寻找目标身影"
    L1_2(L2_2)
    L1_2 = true
    return L1_2
  end
end

approach = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  while true do
    L2_2 = getTarget
    L2_2 = L2_2()
    if L2_2 == "" then
      travelMod = "fly"
      L3_2 = closeScrnLog
      L4_2 = "给予镖物"
      L3_2(L4_2)
      L3_2 = true
      return L3_2
    else
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.masterPos
      L4_2 = L4_2[A0_2]
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L3_2 then
        L5_2 = scrnLog
        L6_2 = "给予镖物"
        L5_2(L6_2)
        L5_2 = _ENV["押镖屏蔽"]
        L5_2()
        L5_2 = {}
        L6_2 = L3_2 + 40
        L7_2 = L4_2 - 90
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = openGiveINF
        L6_2()
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 5
        L6_2(L7_2, L8_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 350
        L9_2 = 400
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
        if A1_2 == 2 then
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.props
          L7_2 = L7_2.escort2
          L8_2 = "singleTap"
          L9_2 = 10
          L6_2(L7_2, L8_2, L9_2)
          L6_2 = mSleep
          L7_2 = math
          L7_2 = L7_2.random
          L8_2 = 350
          L9_2 = 400
          L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
          L6_2(L7_2, L8_2, L9_2, L10_2)
        elseif A1_2 == 4 then
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.props
          L7_2 = L7_2.escort4
          L8_2 = "singleTap"
          L9_2 = 10
          L6_2(L7_2, L8_2, L9_2)
          L6_2 = mSleep
          L7_2 = math
          L7_2 = L7_2.random
          L8_2 = 350
          L9_2 = 400
          L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
          L6_2(L7_2, L8_2, L9_2, L10_2)
        elseif A1_2 == 3 then
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.props
          L7_2 = L7_2.escort3
          L8_2 = "singleTap"
          L9_2 = 10
          L6_2(L7_2, L8_2, L9_2)
          L6_2 = mSleep
          L7_2 = math
          L7_2 = L7_2.random
          L8_2 = 350
          L9_2 = 400
          L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
          L6_2(L7_2, L8_2, L9_2, L10_2)
        elseif A1_2 == 1 then
          L6_2 = findAndTap
          L7_2 = color
          L7_2 = L7_2.props
          L7_2 = L7_2.escort1
          L8_2 = "singleTap"
          L9_2 = 10
          L6_2(L7_2, L8_2, L9_2)
          L6_2 = mSleep
          L7_2 = math
          L7_2 = L7_2.random
          L8_2 = 350
          L9_2 = 400
          L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
          L6_2(L7_2, L8_2, L9_2, L10_2)
        end
        L6_2 = findAndTap
        L7_2 = color
        L7_2 = L7_2.btn
        L7_2 = L7_2.giveSure
        L8_2 = "singleTap"
        L9_2 = 10
        L6_2(L7_2, L8_2, L9_2)
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 350
        L9_2 = 400
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
        L6_2 = fightSystem
        L6_2()
        L6_2 = cleanupINF
        L6_2()
        L6_2 = mSleep
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = 350
        L9_2 = 400
        L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
        L6_2(L7_2, L8_2, L9_2, L10_2)
      else
        L5_2 = approach
        L6_2 = A0_2
        L5_2(L6_2)
      end
    end
    L3_2 = findAndTap
    L4_2 = color
    L4_2 = L4_2.btn
    L4_2 = L4_2.giveINF
    L5_2 = "singleTap"
    L6_2 = 30
    L3_2 = L3_2(L4_2, L5_2, L6_2)
    if L3_2 then
      L3_2 = mSleep
      L4_2 = math
      L4_2 = L4_2.random
      L5_2 = 350
      L6_2 = 400
      L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L4_2(L5_2, L6_2)
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
    end
  end
end

giveEscort = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  _ENV["押镖完毕"] = false
  L2_2 = _ENV["UI_押镖次数"]
  if L2_2 == "" then
    _ENV["UI_押镖次数"] = "990"
  end
  L2_2 = tonumber
  L3_2 = _ENV["UI_押镖次数"]
  L2_2 = L2_2(L3_2)
  _ENV["UI_押镖次数"] = L2_2
  _ENV["押镖次数"] = 0
  L2_2 = ""
  L3_2 = _ENV["UI_次数"]
  if L3_2 == "" then
    _ENV["UI_次数"] = "5"
  end
  _ENV["押镖频率"] = -1
  L3_2 = tonumber
  L4_2 = _ENV["UI_次数"]
  L3_2 = L3_2(L4_2)
  _ENV["UI_次数"] = L3_2
  while true do
    L3_2 = getTarget
    L3_2 = L3_2()
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["绘卷"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find
      L5_2 = Color
      L5_2 = L5_2["玲珑"]
      L5_2 = L5_2["没有任务"]
      L4_2 = L4_2(L5_2)
      if L4_2 and L3_2 == "" then
        L4_2 = _ENV["押镖频率"]
        L4_2 = L4_2 + 1
        _ENV["押镖频率"] = L4_2
        L4_2 = _ENV["押镖频率"]
        L5_2 = _ENV["UI_次数"]
        if L4_2 >= L5_2 then
          _ENV["押镖频率"] = 0
          L4_2 = openProps
          L4_2()
          L4_2 = isItemExist
          L5_2 = _ENV["押镖检测"]
          L4_2 = L4_2(L5_2)
          if L4_2 == false then
            L4_2 = _ENV["遍历_包子数量"]
            L4_2 = L4_2()
            if L4_2 < 2 then
              L4_2 = _ENV["_购买"]
              L4_2 = L4_2["包子"]
              L4_2()
            end
          end
          L4_2 = _ENV["宠物忠诚玲珑"]
          L4_2()
        end
        L4_2 = _ENV["押镖次数"]
        L4_2 = L4_2 + 1
        _ENV["押镖次数"] = L4_2
        L4_2 = _ENV["押镖次数"]
        L5_2 = _ENV["UI_押镖次数"]
        if L4_2 > L5_2 then
          L4_2 = true
          return L4_2
        end
        L4_2 = goToCFBJ
        L5_2 = "fly"
        L4_2(L5_2)
        L4_2 = getEscort
        L5_2 = A0_2
        L6_2 = A1_2
        L4_2(L5_2, L6_2)
        L4_2 = getTarget
        L4_2 = L4_2()
        L3_2 = L4_2
      end
    end
    L4_2 = MyGetRunningAccess
    L4_2()
    L4_2 = goOutOfCFBJ
    L4_2()
    L4_2 = _ENV["押镖完毕"]
    if L4_2 then
      L4_2 = true
      return L4_2
    end
    L4_2 = goToMaster
    L5_2 = L3_2
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = approach
      L5_2 = L3_2
      L4_2(L5_2)
      L4_2 = giveEscort
      L5_2 = L3_2
      L6_2 = A0_2
      L4_2(L5_2, L6_2)
    end
  end
end

startEscorting = L0_1
